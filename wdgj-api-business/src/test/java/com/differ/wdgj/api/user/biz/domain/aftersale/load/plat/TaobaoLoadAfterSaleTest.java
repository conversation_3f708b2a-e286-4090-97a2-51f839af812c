package com.differ.wdgj.api.user.biz.domain.aftersale.load.plat;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.LoadAfterSaleFacade;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import org.junit.Assert;
import org.junit.Test;

/**
 * 淘宝下载售后单
 *
 * <AUTHOR>
 * @date 2025/4/10 下午7:02
 */
public class TaobaoLoadAfterSaleTest extends AbstractSpringTest {
    /**
     * 京东
     */
    @Test
    public void taobaoTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1000);
        param.setTriggerType(TriggerTypeEnum.HAND);
        param.setCreator("cxd单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }

}
