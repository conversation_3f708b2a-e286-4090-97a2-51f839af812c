package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct;

import java.math.BigDecimal;
import java.util.List;

/**
 * 菠萝派下载商品 商品级返回对象
 *
 * <AUTHOR>
 * @date 2024-03-22 9:46
 */
public class BusinessDownloadProductResponseGoodInfo {

    /**
    * 商品ID
    */
    public int goodsId;

    /**
    * 平台商品ID
    */
    public String platProductId;

    /**
    * 商品名称
    */
    public String name;

    /**
    * 外部商家编码。
    */
    public String outerId;

    /**
    * 商品价格
    */
    public BigDecimal price;

    /**
    * 商品数量
    */
    public int num;

    /**
    * 图片URL
    */
    public String pictureUrl;

    /**
    * 商品所在仓库编号。
    */
    public String whseCode;

    /**
    * 商品所在仓库编号。
    */
    public String propertyAlias;

    /**
    * 商品链接。
    */
    public String detailUrl;

    /**
    * 规格集合
    */
    public List<BusinessDownloadProductResponseGoodInfoSku> skus;


    //region get/set
    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public String getPlatProductId() {
        return platProductId;
    }

    public void setPlatProductId(String platProductId) {
        this.platProductId = platProductId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public String getWhseCode() {
        return whseCode;
    }

    public void setWhseCode(String whseCode) {
        this.whseCode = whseCode;
    }

    public String getPropertyAlias() {
        return propertyAlias;
    }

    public void setPropertyAlias(String propertyAlias) {
        this.propertyAlias = propertyAlias;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public List<BusinessDownloadProductResponseGoodInfoSku> getSkus() {
        return skus;
    }

    public void setSkus(List<BusinessDownloadProductResponseGoodInfoSku> skus) {
        this.skus = skus;
    }
    //endregion
}
