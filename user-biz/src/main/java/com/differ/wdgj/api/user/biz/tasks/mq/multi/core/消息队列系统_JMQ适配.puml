'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

interface JMQSimpleReceive <<架构代码：JMQ组件接收接口>>
abstract class AbstractJMQSender <<架构代码：JMQ组件发送接口>>

interface JmqAdapterAnnotation <<JMQ注解>>
interface ApiMultiMQ <<多队列注解>>
interface BeanDefinitionRegistryPostProcessor <<spring的bean注册器>> {
    void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry)
}

interface SubQueueFactory <<子队列创建工厂接口>>{
     String getEngineQueueCode(String multiQueueCode) : 获取队列引擎对应的队列代码
     SubQueue createSubQueue(SubQueueContext subQueueContext, Class<T> dataClazz) : 创建子队列
}

class ByteBuddy <<动态代理组件：字节码增强技术>>
class JmqBeanDynamicDefinitionRegistry <<jmq的bean动态生成的注册器>> implements BeanDefinitionRegistryPostProcessor {
    + static JmqAdapter getJmqAdapter(String jmqCode)
}
class JmqDynamicBeanFactory <<JMQ动态Bean工厂>>
class JmqAdapter <<JMQ适配器>> {

    + void initListener(Class<T> dataClazz, Function<QueueResult, T, QueueHeader> funListener) : 初始化监听器
    + JMQResult consume(JMQMessage<String> message) ： JMQ消费方法
    + void send(String message, QueueHeader header) ： 发送消息到JMQ,并受信号量限制
}
class JmqProxy <<JMQ动态代理类>>
class JmqSubQueue <<JMQ子队列>>
class SubQueueJmqFactory <<JMQ子队列工厂>>

enum SubQueueEnum <<子队列枚举>> {
    SubQueueFactory factory : 队列创建工厂
}

AbstractJMQSender <|-- JmqAdapter :继承
JMQSimpleReceive <|.. JmqAdapter :继承
SubQueueFactory <|.. SubQueueJmqFactory :继承

JmqAdapter <.. JmqSubQueue :依赖（代码体现：成员变量）
JmqDynamicBeanFactory <.. JmqBeanDynamicDefinitionRegistry :依赖（代码体现：内部变量）
JmqAdapter  <--o JmqBeanDynamicDefinitionRegistry :聚合（代码体现：创建JmqAdapter并聚合）
JmqProxy <.. JmqDynamicBeanFactory :依赖（代码体现：内部变量）
JmqAdapterAnnotation <.. JmqProxy :依赖（代码体现：内部变量）
ApiMultiMQ <.. JmqProxy :依赖（代码体现：内部变量）
ByteBuddy <.. JmqProxy :依赖（代码体现：内部变量）

JmqAdapter <.. SubQueueJmqFactory
JmqBeanDynamicDefinitionRegistry <.. SubQueueJmqFactory
JmqSubQueue <.. SubQueueJmqFactory

SubQueueFactory  <.. SubQueueEnum

@enduml