'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

interface WaitLine<T extends AbstractWaitEntity> <<排队接口抽象>>{
    boolean putWaitOrExec(T entity) : 入队或执行
    boolean isWaiting(String waitUniqueId) : 是否在排队
    void receiveMessageHandleNext() : next消息处理
    void callbackResultOnAsync(String waitUniqueId) : 异步任务回调结果
    boolean cancel(String waitUniqueId): 取消
}

interface UserWaitLine<T extends AbstractWaitEntity> <<带用户的排队接口抽象>>{
}

interface WaitStrategy <<排队任务数据存储策略>>{
    isWaitAndPut(): 入队或执行
    pullNextWait(): next消息处理
    deleteWaitOrExec
    batchDeleteWait()
    batchIsWait()
    waitMonitor()
    checkFinally(): 兜底检查
}

abstract class AbstractWaitLine {
}

abstract class BaseCommonWaitLineHandler {
}

class CommonUserWaitLine {
}

class RedisSimpleWaitStrategy <<redis存储的简单去重策略>>{
}
class RedisPriorityWaitStrategy <<redis存储支持优先级的策略>>{
}

class DBSimpleWaitStrategy <<数据库存储策略>>{
}

WaitLine <|.. AbstractWaitLine :继承（接口）

AbstractWaitLine <|-- BaseCommonWaitLineHandler :继承（类）

AbstractWaitLine <|-- PluginA :继承（类）
AbstractWaitLine <|-- PluginB :继承（类）

AbstractWaitLine <|-- CommonUserWaitLine :继承（类）

UserWaitLine <|.. BaseUserWaitLineHandler :继承（接口）

WaitLine <--* UserWaitLine :组合（成员集合变量）
BaseUserWaitLineHandler <|-- UserPluginA :继承（类）
BaseUserWaitLineHandler <|-- UserPluginB :继承（类）

WaitStrategy <-- WaitLine :关联（类成员变量）

WaitStrategy <|-- RedisSimpleWaitStrategy :继承（类）

WaitStrategy <|-- DBSimpleWaitStrategy :继承（类）


@enduml