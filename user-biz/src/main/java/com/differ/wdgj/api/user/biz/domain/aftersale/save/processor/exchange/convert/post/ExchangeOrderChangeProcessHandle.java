package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.post;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleChangeTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.handle.TargetErpAfterSaleChangeItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle.plugins.ErpAfterSaleChangeNoticeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPostProcessOrderHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundStatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 换货单单变化处理
 *
 * <AUTHOR>
 * @date 2024/8/8 下午1:22
 */
public class ExchangeOrderChangeProcessHandle  extends AbstractPostProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangeOrderChangeProcessHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 售后单后置处理
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult processOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 获取售后单变化
        Set<TargetErpAfterSaleChangeItem> changeTypes = getAfterSaleOrderChange(sourceOrder, targetOrder);

        // 插入通知
        if (CollectionUtils.isNotEmpty(changeTypes)) {
            ErpAfterSaleChangeNoticeHandle erpAfterSaleChangeNoticeHandle = new ErpAfterSaleChangeNoticeHandle(context, targetOrder.getAfterSaleOrder().getRefundId(), targetOrder.getAfterSaleOrder().getBillId(), changeTypes);
            targetOrder.getNotices().add(erpAfterSaleChangeNoticeHandle);
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "换货单单变化处理";
    }
    //endregion

    //region 私有方法
    /**
     * 获取售后单变化
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 售后单变化列表
     */
    private Set<TargetErpAfterSaleChangeItem> getAfterSaleOrderChange(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 变化信息
        Set<TargetErpAfterSaleChangeItem> changeTypes = new HashSet<>();

        // 检测变化
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        if (dbOrder != null) {
            // 历史售后单数据
            ApiReturnListDO oldOrder = dbOrder.getAfterSaleOrder();
            List<ApiReturnDetailDO> oldReturnGoodsList = dbOrder.getReturnGoods();

            // 已递交的售后单，且退货商品信息不为空
            if (oldOrder != null && oldOrder.getTradeID() != null && oldOrder.getTradeID() > 0 && CollectionUtils.isNotEmpty(oldReturnGoodsList)) {
                // 新售后单数据
                ApiReturnListDO newOrder = targetOrder.getAfterSaleOrder();
                // 物流变化
                if (!StringUtils.equals(newOrder.getLogisticNo(), oldOrder.getLogisticNo())) {
                    String changeInfo = String.format("售后单物流信息变化：%s -> %s", oldOrder.getLogisticNo(), newOrder.getLogisticNo());
                    changeTypes.add(new TargetErpAfterSaleChangeItem(AfterSaleChangeTypeEnum.LOGISTICS_CHANGE, changeInfo));
                }
                // 售后单取消
                if (!StringUtils.equals(newOrder.getReturnStatus(), oldOrder.getReturnStatus()) &&
                        StringUtils.equals(PolyRefundStatusEnum.REFUND_CLOSED.getWdgjValue(), newOrder.getReturnStatus())) {
                    String changeInfo = "售后单取消";
                    changeTypes.add(new TargetErpAfterSaleChangeItem(AfterSaleChangeTypeEnum.AFTER_SALE_CANCEL, changeInfo));
                }
            }
        }
        return changeTypes;
    }
    //endregion
}
