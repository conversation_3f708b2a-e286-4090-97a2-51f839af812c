/**
 * 业务领域层，复杂的业务写在此层，简单的查询不要在此层，直接通过service层调仓储等
 * domain层强制不支持Spring的bean扫描，建议使用面向对象的经典写法
 *
 * 总体调用原则：
 * controller -> service -> domain/tasks -> repository :业务复杂的通过领域 (tasks,manager等同领域服务的位置)
 * controller -> service -> repository : 简单查询service可直接调repository
 * tasks -> domain/infrastructure : 简单查询service可直接调repository
 *
 * <AUTHOR>
 * @date 2024/1/26 11:02
 */
package com.differ.wdgj.api.user.biz.domain;