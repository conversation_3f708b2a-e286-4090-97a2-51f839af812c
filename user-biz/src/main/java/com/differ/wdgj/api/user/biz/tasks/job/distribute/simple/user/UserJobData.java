package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.user;



import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.SimpleDistributeJobData;

import java.util.Objects;

/**
 * 会员任务数据
 *
 * <AUTHOR>
 * @date 2021-11-22 15:31
 */
public class UserJobData implements SimpleDistributeJobData {

    /**
     * 会员名
     *
     * @param memberName 会员名
     */
    public UserJobData(String memberName) {
        this.memberName = memberName;
    }

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 获取唯一标识
     *
     * @return 唯一标识
     */
    @Override
    public String uniqueSign() {
        return this.memberName;
    }

    // region getter & setter

    public String getMemberName() {
        return this.memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserJobData that = (UserJobData) o;
        return Objects.equals(this.uniqueSign(), that.uniqueSign());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.uniqueSign());
    }

    // endregion
}
