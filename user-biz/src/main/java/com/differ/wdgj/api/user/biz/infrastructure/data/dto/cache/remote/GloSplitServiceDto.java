package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

import java.time.LocalDateTime;

/**
 * 菠萝派网关配置
 * </p>对应表 glo_splitService
 *
 * <AUTHOR>
 * @date 2024/8/19 下午2:13
 */
public class GloSplitServiceDto {
    /**
     * 唯一标识SplitserviceID。
     */
    private int splitserviceID;

    /**
     * 全局分类Id。
     */
    private int categoryId;

    /**
     * 类型：全局0、灰度1、正式2。
     */
    private int func;

    /**
     * 会员名(如api2017)，多个会员之间#隔开。
     */
    private String outAccount;

    /**
     * 平台值，多个之间使用#隔开。
     */
    private String platValue;

    /**
     * 接口方法枚举值，多个值#隔开。
     */
    private String methodValue;

    /**
     * 阿里地址，多个地址#隔开。
     */
    private String aliyunUrl;

    /**
     * 京东地址，多个地址#隔开。
     */
    private String jdUrl;

    /**
     * 优先级。
     */
    private int priority;

    /**
     * 是否启用，默认启用。
     */
    private boolean isEnable;

    /**
     * 备注。
     */
    private String remark;

    /**
     * 创建时间。
     */
    private LocalDateTime createTime;

    /**
     * 最后修改时间。
     */
    private LocalDateTime lastModifyTime;

    //region get/set
    public int getSplitserviceID() {
        return splitserviceID;
    }

    public void setSplitserviceID(int splitserviceID) {
        this.splitserviceID = splitserviceID;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public int getFunc() {
        return func;
    }

    public void setFunc(int func) {
        this.func = func;
    }

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public String getPlatValue() {
        return platValue;
    }

    public void setPlatValue(String platValue) {
        this.platValue = platValue;
    }

    public String getMethodValue() {
        return methodValue;
    }

    public void setMethodValue(String methodValue) {
        this.methodValue = methodValue;
    }

    public String getAliyunUrl() {
        return aliyunUrl;
    }

    public void setAliyunUrl(String aliyunUrl) {
        this.aliyunUrl = aliyunUrl;
    }

    public String getJdUrl() {
        return jdUrl;
    }

    public void setJdUrl(String jdUrl) {
        this.jdUrl = jdUrl;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public void setEnable(boolean enable) {
        isEnable = enable;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    //endregion
}
