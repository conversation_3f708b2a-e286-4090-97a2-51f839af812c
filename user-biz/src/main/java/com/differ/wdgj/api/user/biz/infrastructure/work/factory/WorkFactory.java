package com.differ.wdgj.api.user.biz.infrastructure.work.factory;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.DataOperateContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.event.handler.WorkEventHandler;
import com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.check.WorkInterruptChecker;
import com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.heart.WorkHeart;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.WorkMutex;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import com.differ.wdgj.api.user.biz.infrastructure.work.template.WorkExecTemplate;

/**
 * 抽象工厂接口
 *
 * <AUTHOR>
 * @date 2024/6/25 10:51
 */
public interface WorkFactory<T extends WorkData<?>> {
    /**
     * 创建模板(核心)
     *
     * @return 模板
     */
    WorkExecTemplate<T> createTemplate();

    /**
     * 创建任务数据操作类
     *
     * @return 任务数据操作类
     */
    WorkDataOperate createDataOperate();

    /**
     * 创建心跳
     *
     * @param context 上下文
     * @return 心跳
     */
    WorkHeart createWorkHeart(DataOperateContext context);

    /**
     * +
     * 创建中断检测器
     *
     * @return 中断检测器
     */
    WorkInterruptChecker createWorkInterrupt();

    /**
     * 创建互斥处理器
     *
     * @return 互斥处理器
     */
    WorkMutex createWorkMutex();

    /**
     * 创建事件处理器
     *
     * @return 事件处理器
     */
    WorkEventHandler createEventHandler();

    /**
     * 获取工作任务的参数类型
     * com.differ.jackyun.omsapi.user.biz.infrastructure.work.data.WorkData<T extends WorkArgs>
     *
     * @return T的类型
     */
    Class<?> getWorkDataArgClass();
}
