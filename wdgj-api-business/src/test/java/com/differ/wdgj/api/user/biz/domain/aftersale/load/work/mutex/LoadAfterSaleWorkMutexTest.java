package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * LoadAfterSaleWorkMutex 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午2:30
 */
public class LoadAfterSaleWorkMutexTest extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 测试消息推送不互斥场景
     * 当触发类型为MESSAGE_NOTIFICATION且加载类型为AFTER_SALE_NO时，直接创建任务
     */
    @Test
    public void testCreate_MessageNotificationWithAfterSaleNo_ShouldCreateDirectly() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION,
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        CreateResult expectedResult = CreateResult.successResult("test-task-id");

        // 模拟依赖行为
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "test-task-id", result.getTaskId());

        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试消息推送但加载类型不是AFTER_SALE_NO的场景
     * 应该继续执行后续逻辑而不是直接创建
     */
    @Test
    public void testCreate_MessageNotificationWithOtherLoadType_ShouldContinue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION,
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );

        // 模拟父类方法返回null（不存在未完成任务，会创建任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果 - 应该通过父类逻辑创建任务
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试非消息推送触发类型的场景
     * 应该继续执行后续逻辑
     */
    @Test
    public void testCreate_NonMessageNotification_ShouldContinue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND,
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );

        // 模拟父类方法返回null（不存在未完成任务，会创建任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果 - 应该通过父类逻辑创建任务
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试基类创建逻辑返回非空结果的场景
     * 当父类create方法返回非空结果时，应该直接返回该结果
     */
    @Test
    public void testCreate_SuperCreateReturnsResult_ShouldReturnSuperResult() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND,
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        CreateResult expectedResult = CreateResult.successResult("super-task-id");

        // 模拟父类方法返回结果（存在未完成任务时返回null，不存在时创建任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试存在未完成任务且不存在超时任务的场景
     * 当存在未完成任务但配置的超时时间为0（默认值）时，应该返回null
     */
    @Test
    public void testCreate_ExistsUnCompleteTask_ShouldReturnNull() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO,
            AfterSaleLoadTypeEnum.TIME_RANGE
        );

        // 模拟父类方法返回null（存在未完成任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);

        // 执行测试 - 由于配置键不存在，默认超时时间为0，不会检查超时任务
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果
        Assert.assertNull("结果应为空", result);

        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        // 由于超时时间为0，不应该检查超时任务
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
        Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
    }

    /**
     * 测试不存在未完成任务的场景
     * 当不存在未完成任务时，父类应该创建新任务
     */
    @Test
    public void testCreate_NoUnCompleteTask_ShouldCreateNewTask() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO,
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        CreateResult expectedResult = CreateResult.successResult("new-task-id");

        // 模拟父类方法返回结果（不存在未完成任务，创建新任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "new-task-id", result.getTaskId());

        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }



    /**
     * 测试args为null的边界情况
     * 当AfterSaleLoadArgs为null时，应该跳过消息推送检查，继续执行后续逻辑
     */
    @Test
    public void testCreate_ArgsNull_ShouldSkipMessageCheck() {
        // 准备测试数据
        WorkContext workContext = WorkContext.of(
            "test-member",
            WorkEnum.LOAD_AFTER_SALE,
            1001,
            TriggerTypeEnum.MESSAGE_NOTIFICATION,
            "test-creator"
        );
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, null);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");

        // 模拟父类方法返回结果（不存在未完成任务，创建新任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果 - 应该通过父类逻辑创建任务
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试所有枚举值的覆盖
     * 确保所有AfterSaleLoadTypeEnum和TriggerTypeEnum的组合都能正常处理
     */
    @Test
    public void testCreate_AllEnumCombinations() {
        // 测试所有触发类型和加载类型的组合
        TriggerTypeEnum[] triggerTypes = {TriggerTypeEnum.HAND, TriggerTypeEnum.AUTO, TriggerTypeEnum.MESSAGE_NOTIFICATION};
        AfterSaleLoadTypeEnum[] loadTypes = {AfterSaleLoadTypeEnum.SHOP_LOAD, AfterSaleLoadTypeEnum.TIME_RANGE, AfterSaleLoadTypeEnum.AFTER_SALE_NO};

        for (TriggerTypeEnum triggerType : triggerTypes) {
            for (AfterSaleLoadTypeEnum loadType : loadTypes) {
                // 重置mock
                Mockito.reset(mockDataOperate);

                WorkData<AfterSaleLoadArgs> workData = createWorkData(triggerType, loadType);

                if (triggerType == TriggerTypeEnum.MESSAGE_NOTIFICATION && loadType == AfterSaleLoadTypeEnum.AFTER_SALE_NO) {
                    // 消息推送且按售后单号下载，应该直接创建
                    CreateResult expectedResult = CreateResult.successResult("direct-task-id");
                    Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

                    CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

                    Assert.assertNotNull("消息推送+售后单号应该直接创建", result);
                    Assert.assertTrue("应该创建成功", result.success());
                    Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                } else {
                    // 其他情况，模拟不存在未完成任务，应该通过父类逻辑创建
                    CreateResult expectedResult = CreateResult.successResult("super-task-id");
                    Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
                    Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

                    CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

                    Assert.assertNotNull("其他情况应该通过父类创建", result);
                    Assert.assertTrue("应该创建成功", result.success());
                    Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
                    Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                }
            }
        }
    }

    /**
     * 创建测试用的WorkData对象
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "test-member",
            WorkEnum.LOAD_AFTER_SALE,
            1001,
            triggerType,
            "test-creator"
        );

        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);

        return WorkData.of(workContext, args);
    }
}
