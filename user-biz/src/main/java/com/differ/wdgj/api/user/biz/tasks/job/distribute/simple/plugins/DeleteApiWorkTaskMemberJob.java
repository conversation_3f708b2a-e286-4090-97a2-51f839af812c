package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiWorkTaskMapper;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.WorkFacade;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.AbstractUserSimpleDistributeJob;
import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import org.apache.commons.lang3.math.NumberUtils;

import java.text.MessageFormat;

/**
 * 自动删除过期任务数据 定时任务
 *
 * <AUTHOR>
 * @date 2025/04/16 上午11:30
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "DeleteApiWorkTaskMemberJob",
        cron = "0 0 2 * * ?" //每天凌晨2点执行一次
)
public class DeleteApiWorkTaskMemberJob extends AbstractUserSimpleDistributeJob {
    //region 变量

    /**
     * 标题
     */
    private final String CAPTION = "自动删除过期任务数据";

    /**
     * API业务工作任务仓储
     */
    private ApiWorkTaskMapper bizWorkTaskMapper;

    /**
     * 工作任务门面
     */
    private final WorkFacade workFacade = new WorkFacade();

    // endregion

    //region 实现基类方法

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return CAPTION;
    }

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {
        StringBuilder infoLogStr = new StringBuilder();
        try {
            //是否开启 总开关
            if (!ConfigKeyUtils.isActionApiBoolean(ConfigKeyEnum.ISACTION_DELETE_EXPIRED_DATA)) {
                return;
            }

            // 遍历 taskType 枚举
            for (WorkEnum taskType : WorkEnum.values()) {
                // 检测工作任务中断
                workFacade.cleanComplete(memberName, taskType);
                Integer resultAllCount = 0;
                try {
                    //获取 taskType 配置键过期天数。未配置时默认15天
                    String taskTypeExpiredDay = ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.TASK_CONFIG_TASKTYPE_EXPIRED_DAY, taskType.toString(), "15");

                    //分页删除
                    Integer resultPageCount = 1000;
                    while (resultPageCount >= 1000) {
                        resultPageCount = DBSwitchUtil.doDBWithUser(memberName, () -> getBizWorkTaskMapper().DeleteExpiredData(taskType.getValue(), NumberUtils.toInt(taskTypeExpiredDay)));
                        resultAllCount += resultPageCount;
                    }

                    // 记录日志
                    infoLogStr.append(MessageFormat.format("【{0}】删除【{1}】过期任务 - 全部成功，影响总行数：{2}", memberName, taskType.getCaption(), resultAllCount)).append(System.lineSeparator());
                } catch (Exception e) {
                    infoLogStr.append(MessageFormat.format("【{0}】删除【{1}】过期任务 - 失败，影响总行数：{2}，异常信息：{3}", memberName, taskType.getCaption(), resultAllCount, e.getMessage())).append(System.lineSeparator());
                    LogFactory.error("【{%s} - {%s}】 {}：", memberName, CAPTION, e);
                }
            }
        } finally {
            LogFactory.info(CAPTION, memberName, () -> infoLogStr);
        }
    }

    //endregion

    //region 私有方法

    /**
     * 获取工作任务仓储
     *
     * @return 工作任务仓储
     */
    private ApiWorkTaskMapper getBizWorkTaskMapper() {
        if (this.bizWorkTaskMapper == null) {
            bizWorkTaskMapper = BeanContextUtil.getBean(ApiWorkTaskMapper.class);
        }
        return bizWorkTaskMapper;
    }

    //endregion

}
