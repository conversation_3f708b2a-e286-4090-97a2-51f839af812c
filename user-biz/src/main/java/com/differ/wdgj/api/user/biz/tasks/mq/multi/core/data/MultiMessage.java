package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data;

/**
 * 多队列消息体,包含头信息和业务对象
 *
 * <AUTHOR>
 * @date 2024/4/3 17:04
 */
public class MultiMessage {

    /**
     * 业务数据
     */
    private String data;
    /**
     * 头信息
     */
    private QueueHeader header;

    public MultiMessage(String data, QueueHeader header) {
        this.data = data;
        this.header = header;
    }

    public String getData() {
        return data;
    }

    public QueueHeader getHeader() {
        return header;
    }
}
