package com.differ.wdgj.api.component.wait.data;

/**
 * @Description 排队消息
 * <AUTHOR>
 * @Date 2021/9/10 15:01
 */
public class BaseWaitMessage {

    /**
     * 消息处理类型
     */
    private HandlerMode mode;

    /**
     * 排队类型
     */
    private String waitType;

    /**
     * 用户排队时的用户
     */
    @Deprecated
    private String user;

    /**
     * 排队键
     */
    private String key;

    public BaseWaitMessage() {
    }

    public BaseWaitMessage(String waitType) {
        this.waitType = waitType;
        mode = HandlerMode.DEFAULT;
    }

    public BaseWaitMessage(HandlerMode mode, String waitType) {
        this.mode = mode;
        this.waitType = waitType;
    }

    public BaseWaitMessage(String waitType, String key) {
        this.waitType = waitType;
        this.user = key;
        this.key = key;
        mode = HandlerMode.USER;
    }

    public BaseWaitMessage(HandlerMode mode, String waitType, String key) {
        this.mode = mode;
        this.waitType = waitType;
        this.user = key;
        this.key = key;
    }

    public String getWaitType() {
        return waitType;
    }

    public void setWaitType(String waitType) {
        this.waitType = waitType;
    }

    @Deprecated
    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public HandlerMode getMode() {
        return mode;
    }

    public void setMode(HandlerMode mode) {
        this.mode = mode;
    }
}
