package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.work;

import com.differ.wdgj.api.component.task.single.core.JobExecTimeStrategy;
import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.work.WorkFacade;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkConstant;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.AbstractUserSimpleDistributeJob;
import org.slf4j.Logger;

/**
 * 工作任务的分布式检测任务
 *
 * <AUTHOR>
 * @date 2024/10/28 上午11:17
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "WorkCheckDistributeJob",
        cron = "0/10 * * * * ?"
)
public class WorkCheckDistributeJob extends AbstractUserSimpleDistributeJob {
    //region 常量
    /**
     * 工作任务门面
     */
    private final WorkFacade workFacade = new WorkFacade();

    /**
     * 日志
     */
    private final Logger log = LogFactory.get(WorkCheckDistributeJob.class);
    //endregion

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return "工作任务检测模块";
    }

    /**
     * 设置执行时间策略
     *
     * @return 执行执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        // 为保证任务心跳检测正常情况下通过校验，需要将检测任务的执行频率设置在 心跳最小间隔时间的1/3
        int workMinHeartIntervalSecond = (int) WorkConstant.WORK_MIN_HEART_INTERVAL / 1000;
        int frequency = workMinHeartIntervalSecond / 3;
        execTimeStrategy.setRunFrequency(frequency);
        return execTimeStrategy;
    }

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {
        // 检测所有工作任务中断
        for (WorkEnum workEnum : WorkEnum.values()) {
            try {
                // 检测工作任务中断
                workFacade.checkInterrupt(memberName, workEnum);
            } catch (Throwable e) {
                log.error(String.format("%s检测工作任务是否被中断异常:%s", memberName, workEnum), e);
            }
        }
    }
}
