package com.differ.wdgj.api.user.biz.infrastructure.work;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberRunnable;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.WorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.check.WorkInterruptChecker;
import com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.heart.HeartBeatCenter;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.WorkMutex;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 工作任务的门面入口类
 * 设计文档：https://s.jkyun.biz/UVOt64q 通用工作任务（带前端进度）的开发设计
 * 外部定时任务协作类：WorkHeartJob,WorkCheckDistributeJob
 * 排队场景处理示例类：DemoWorkUserClusterQueueJob
 *
 * <AUTHOR>
 * @date 2024/6/24 17:02
 */
public class WorkFacade {

    private static final Logger LOG = LoggerFactory.getLogger(WorkFacade.class);

    /**
     * 直接同步执行工作任务，用于小任务的场景
     *
     * @param workData 任务信息
     * @return 执行结果
     */
    public WorkResult execWorkSyncDirect(WorkData<?> workData) {
        CreateResult createResult = createWork(workData);
        if (!createResult.success()) {
            return createResult;
        }
        return execWork(workData, createResult.getTaskId(), false);
    }

    /**
     * 创建工作任务
     *
     * @param workData 任务信息
     */
    public CreateResult createWork(WorkData<?> workData) {
        try {
            // 校验参数
            String validError = workData.validArgs();
            if (StringUtils.isNotBlank(validError)) {
                return CreateResult.failResult(validError);
            }

            // 获取工厂
            WorkEnum workType = workData.getWorkType();
            WorkFactory workFactory = workType.getWorkFactory();
            // 任务创建操作对象
            WorkDataOperate dataOperate = workFactory.createDataOperate();
            // 互斥处理对象，创建任务
            WorkMutex workMutex = workFactory.createWorkMutex();
            return workMutex.createIfNoExists(workData, dataOperate);
        } catch (Exception e) {
            LOG.error(String.format("添加任务失败:%s", JsonUtils.toJson(workData)), e);
        }
        return null;
    }

    /**
     * 执行工作任务
     * 异步执行结果返回null
     *
     * @param member   会员名
     * @param workEnum 任务类型
     * @param taskId   已创建任务的任务ID
     * @param async    true:异步执行（WorkEnum对应的线程池） false:同步执行
     */
    public WorkResult execWork(String member, WorkEnum workEnum, String taskId, boolean async) {
        WorkFactory workFactory = workEnum.getWorkFactory();
        if (!async) {
            // 同步执行
            return workFactory.createTemplate().run(member, taskId);
        }

        // 异步执行
        workEnum.getTaskEnum().execute(new MemberRunnable(member) {
            @Override
            public void run() {
                workFactory.createTemplate().run(member, taskId);
            }
        });
        return null;
    }

    /**
     * 执行工作任务
     * 异步执行结果返回null
     *
     * @param workData 任务信息
     * @param taskId   已创建任务的任务ID
     * @param async    true:异步执行（WorkEnum对应的线程池） false:同步执行
     */
    public WorkResult execWork(WorkData<?> workData, String taskId, boolean async) {
        WorkEnum workEnum = workData.getWorkType();
        WorkFactory workFactory = workEnum.getWorkFactory();
        if (!async) {
            // 同步执行
            return workFactory.createTemplate().run(workData, taskId);
        }

        // 异步执行
        workEnum.getTaskEnum().execute(new MemberRunnable(workData.getMemberName()) {
            @Override
            public void run() {
                workFactory.createTemplate().run(workData, taskId);
            }
        });
        return null;
    }

    /**
     * 检测工作任务是否被中断,并将中断任务加入执行线程池
     *
     * @param member   会员名
     * @param workEnum 任务类型
     */
    public void checkInterrupt(String member, WorkEnum workEnum) {
        WorkFactory workFactory = workEnum.getWorkFactory();
        // 创建中断检测器
        WorkInterruptChecker workInterruptChecker = workFactory.createWorkInterrupt();
        // 检测并处理中断
        workInterruptChecker.checkInterruptAndHandle(member);
    }

    /**
     * 通过外部定时任务调用来刷新心跳
     */
    public void doHeart() {
        HeartBeatCenter.singleton().doHeart();
    }

    /**
     * 清理已完成的超过一定时间的任务
     */
    public void cleanComplete(String member, WorkEnum workEnum) {
        try {
            WorkFactory workFactory = workEnum.getWorkFactory();
            WorkDataOperate dataOperate = workFactory.createDataOperate();
            int deleteCount = dataOperate.cleanExpired(member, workEnum);
            LogFactory.info("【{%s}】清理已完成的工作", member, () -> ExtUtils.stringBuilderAppend(String.format("清理已完成的工作，任务类型：%s，清理数量：%d", workEnum, deleteCount)));
        } catch (Throwable e) {
            LogFactory.error("【{%s}】清理已完成的工作失败：", member, e);
        }
    }

    /**
     * 工作任务删除
     *
     * @param member   会员名
     * @param taskId   任务ID
     * @param workType 工作类型
     */
    public void workComplete(String member, String taskId, WorkEnum workType, WorkResult workResult) {
        // 获取工厂
        WorkFactory workFactory = workType.getWorkFactory();
        // 任务创建操作对象
        WorkDataOperate dataOperate = workFactory.createDataOperate();
        dataOperate.workComplete(member, taskId, workResult);
    }
}
