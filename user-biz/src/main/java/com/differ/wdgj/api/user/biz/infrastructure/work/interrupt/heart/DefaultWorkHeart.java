package com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.heart;

import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.WorkRunningCache;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.DataOperateContext;

/**
 * 默认心跳器
 *
 * <AUTHOR>
 * @date 2024/7/9 10:28
 */
public class DefaultWorkHeart extends AbstractWorkHeart {

    /**
     * 工作任务数据
     */
    private DataOperateContext context;

    /**
     * 工作任务操作缓存
     */
    private WorkRunningCache workRunningCache;

    /**
     * 心跳的最小间隔时间(毫秒)
     */
    private long minHeartInterval = 600000L;

    public DefaultWorkHeart(DataOperateContext context) {
        super(context.getMember(), String.format("%s_%s", context.getWorkEnum(), context.getTaskId()), context.getWorkEnum().getTaskEnum());
        this.context = context;
    }

    @Override
    public boolean isRunning() {
        return this.getWorkRunningCache().hasHeart(minHeartInterval);
    }

    @Override
    public void run() {
        this.getWorkRunningCache().refreshHeart();
    }

    /**
     * 工作任务缓存操作
     *
     * @return
     */
    private WorkRunningCache getWorkRunningCache() {
        if (this.workRunningCache == null) {
            this.workRunningCache = WorkRunningCache.build(context.getMember(), context.getWorkEnum(), context.getTaskId());
        }
        return this.workRunningCache;
    }
}
