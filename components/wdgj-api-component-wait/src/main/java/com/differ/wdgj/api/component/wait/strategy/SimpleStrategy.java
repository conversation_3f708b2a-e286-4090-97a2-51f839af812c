package com.differ.wdgj.api.component.wait.strategy;


import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.wait.AbstractWaitEntity;
import com.differ.wdgj.api.component.wait.WaitStrategy;
import com.differ.wdgj.api.component.wait.data.*;
import com.differ.wdgj.api.component.wait.impl.WaitRedisScripter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 排队抽象类, 实际使用的子类必须在构造中初始化上下文
 * 执行中的key使用时间后缀的原因：
 * 1.场景：当发生前面有个执行中任务，一直没有回调结果时，且后续一直有任务进入到执行中时,
 * 2.目的：也能自动超时过期,避免执行中的任务个数被一直占坑，这时通过容错检查，可以调整回正确的执行中总数
 * <AUTHOR>
 * @Date 2021/9/10 15:02
 */
public class SimpleStrategy implements WaitStrategy {

    protected static Logger log = LoggerFactory.getLogger(SimpleStrategy.class);

    /**
     * redis操作
     */
    protected MultiRedis multiRedis;
    /**
     * 排队上下文
     */
    protected WaitContext waitContext;
    /**
     * 缓存键前缀
     */
    public static final String WAIT_CACHE_KEY_PREFIX = "wait";
    /**
     * 消息处理类型
     */
    protected HandlerMode mode = HandlerMode.DEFAULT;

    /**
     * 初始化，任务执行方法
     *
     * @param context
     */
    @Override
    public void init(WaitContext context) {
        this.waitContext = context;
        this.multiRedis = context.getMultiRedis();
    }

    @Override
    public WaitContext getWaitContext() {
        return waitContext;
    }

    /**
     * 刷新配置
     *
     * @param args
     */
    @Override
    public void refreshArgs(WaitConfigArgs args) {
        this.waitContext.refreshArgs(args);
    }

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return true:放入排队, false:已直接执行
     */
    @Override
    public boolean putWaitOrExec(AbstractWaitEntity entity) {
        WaitStatusEnum waitStatus = putAndGetStatus(entity);
        return WaitStatusEnum.WAIT_ADD == waitStatus || WaitStatusEnum.WAIT_EXISTS == waitStatus;
    }

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return 排队状态 {@link WaitStatusEnum}
     */
    @Override
    public WaitStatusEnum putAndGetStatus(AbstractWaitEntity entity) {
        // 1.是否进入排队
        WaitStatusEnum waitStatus = isWaitAndPut(entity.getWaitUniqueId(), entity.getDataString());
        this.writeDebug("[{}-排队添加]{}:{}{}", this.getWaitGroup(), waitStatus.getDesc(), entity.getWaitUniqueId(), entity.getDataString());
        if (WaitStatusEnum.WAIT_ADD == waitStatus || WaitStatusEnum.WAIT_EXISTS == waitStatus) {
            // 保守模式时,再发起一次事件
            if (this.waitContext.isInsureMode()) {
                this.publishPullNext();
            }
            return waitStatus;
        }

        if (WaitStatusEnum.EXEC_EXISTS == waitStatus) {
            return waitStatus;
        }
        // 2.直接执行任务
        this.exec(entity.getWaitUniqueId(), entity.getDataString(), false);
        this.writeDebug("[{}-排队添加]直接执行完成:{}{}", this.getWaitGroup(), entity.getWaitUniqueId(), entity.getDataString());
        // 3.同步任务时,完成后触发下一个MQ事件
        if (!this.waitContext.isAsyncCallbackResult()) {
            this.callbackResult(entity.getWaitUniqueId());
            this.writeDebug("[{}-排队添加]同步任务直接执行完成后触发下一个:{}{}", this.getWaitGroup(), entity.getWaitUniqueId(), entity.getDataString());
        }
        return WaitStatusEnum.EXEC_ADD;
    }

    /**
     * 收到消息：处理下一个MQ事件
     * @return true:取到任务并且处理，false:没有取到任务
     */
    @Override
    public boolean receiveMessageHandleNext() {
        /*
            1.取排队任务,lua脚本实现
            2.有任务时，消费任务,完成后触发下一个MQ事件，
            3.没有任务时，终止
         */
        this.writeDebug("[{}-排队处理下一个]收到下一个消息", this.getWaitGroup());
        Map<String, String> nextWaitData = doPullNextWait();
        if (MapUtils.isEmpty(nextWaitData)) {
            return false;
        }

        for (Map.Entry<String, String> next : nextWaitData.entrySet()) {
            this.writeDebug("[{}-排队处理下一个]拉到下一个:{}{}", this.getWaitGroup(), next.getKey(), next.getValue());
            this.exec(next.getKey(), next.getValue(), true);
            this.writeDebug("[{}-排队处理下一个]执行下一个完成:{}{}", this.getWaitGroup(), next.getKey(), next.getValue());
            // 同步任务时,完成排队任务后，删除执行中的排队任务信息，触发下一个MQ事件
            if (!this.waitContext.isAsyncCallbackResult()) {
                this.writeDebug("[{}-排队处理下一个]同步任务执行下一个完成再次触发下一个:{}{}", this.getWaitGroup(), next.getKey(), next.getValue());
                this.callbackResult(next.getKey());
            }
        }
        return true;
    }

    /**
     * 执行异步任务完成后回调结果
     *
     * @param waitUniqueId 任务唯一ID
     */
    @Override
    public void callbackResultOnAsync(String waitUniqueId) {
        // 异步时才回调
        if (this.waitContext.isAsyncCallbackResult()) {
            this.writeDebug("[{}-排队异步回调]任务:{}", this.getWaitGroup(), waitUniqueId);
            this.callbackResult(waitUniqueId);
        }
    }

    /**
     * 检查并修复数据容错
     *
     * @return 修复的记录数
     */
    @Override
    public int checkAndRepair() {
        int repair = this.checkData();
        if (repair > 0) {
            for (int i = 0; i < repair; i++) {
                this.publishPullNext();
            }
        }
        this.writeDebug("[{}-排队兜底容错]：修复{}", this.getWaitGroup(), repair);
        return repair;
    }

    /**
     * 任务完成后回调结果
     *
     * @param waitUniqueId 任务唯一ID
     */
    protected void callbackResult(String waitUniqueId) {
        // 完成排队任务后，删除执行中的排队任务信息
        boolean result = this.deleteWaitOrExec(waitUniqueId, false);
        this.writeDebug("[{}-排队回调结果]完成{}后，删除排队任务:{}", this.getWaitGroup(), waitUniqueId, result);

        try {
            // 预防一直占用的风险
            Thread.sleep(1);
        } catch (InterruptedException e) {
            log.error(String.format("%s-排队回调结果,响应中断", this.getWaitGroup()), e);
            Thread.interrupted();
            return;
        }

        // 拉取下一个排队任务
        boolean publishResult = this.publishPullNext();
        this.writeDebug("[{}-排队回调结果]完成{}后，发布下个事件{}", this.getWaitGroup(), waitUniqueId, publishResult);
    }

    /**
     * 取消排队
     *
     * @param waitUniqueId 任务ID
     * @return 返回是否有任务被取消掉
     */
    @Override
    public boolean cancel(String waitUniqueId) {
        return this.deleteWaitOrExec(waitUniqueId, true);
    }

    /**
     * 取消排队
     *
     * @param waitUniqueIds 任务IDs
     * @return
     */
    @Override
    public int cancel(List<String> waitUniqueIds) {
        return this.deleteWaitOrExec(waitUniqueIds, true);
    }

    /**
     * 是否排队中或查询排队状态
     *
     * @param waitUniqueId
     * @return
     */
    @Override
    public boolean isWaiting(String waitUniqueId) {
        return multiRedis.hashExist(this.getContentWaitKey(), waitUniqueId);
    }

    /**
     * 是否排队中或查询排队状态
     *
     * @param waitUniqueIds
     * @return
     */
    @Override
    public Map<String, Boolean> isWaiting(List<String> waitUniqueIds) {
        // 任务排队数据
        String contentWaitKey = getContentWaitKey();

        // 设置lua参数,支持集群
        List<String> keys = Arrays.asList(contentWaitKey);
        Object luaResult = multiRedis.executeScript(
                WaitRedisScripter.singleton().redisScriptBatchIsWait(),
                // Lua脚本中的Key列表
                keys,
                // Lua脚本args列表
                waitUniqueIds
        );
        if (luaResult == null) {
            return null;
        }

        List<Long> listResult = (List<Long>) luaResult;
        Map<String, Boolean> result = new HashMap<>();
        int size = waitUniqueIds.size();
        if (listResult != null && listResult.size() == size) {
            for (int i = 0; i < size; i++) {
                result.put(waitUniqueIds.get(i), 1L == listResult.get(i));
            }
        }
        return result;
    }

    /**
     * 拉取下一个排队任务,发布MQ事件
     */
    protected boolean publishPullNext() {
        try {
            this.waitContext.getWaitMessageSubscriber().publishNext(new BaseWaitMessage(this.mode, this.waitContext.getWaitType()));
            return true;
        } catch (Exception ex) {
            log.error(String.format("%s排队触发：拉取下一个失败", this.getWaitGroup()), ex);
        }
        return false;
    }

    /**
     * 执行任务
     *
     * @param waitUniqueId
     * @param data
     */
    protected void exec(String waitUniqueId, String data, boolean fromWait) {
        try {
            // 执行任务
            this.waitContext.getExecutor().exec(waitUniqueId, data, fromWait);
        } catch (Exception ex) {
            log.error(String.format("%s排队执行败,任务:%s", this.getWaitGroup(), data), ex);
        }
    }

    /**
     * 是否进入排队
     *
     * @param uniqueId
     * @param data
     * @return
     */
    protected WaitStatusEnum isWaitAndPut(String uniqueId, String data) {
        try {
            // 执行中数量的Key
            String execCountKey = getExecCountKey();
            // 任务排队数据
            String contentWaitKey = getContentWaitKey();
            // 任务排队队列
            String contentWaitListKey = getContentWaitListKey();
            // 任务执行中队列
            String executingKey = this.getExecutingKey();

            // 执行中数量上限
            String maxCapacity = String.valueOf(this.getMaxExecCount());
            // 排队任务缓存键过期时间
            String waitDataExpire = String.valueOf(this.waitContext.getWaitDataExpire());
            int executingExpire = this.waitContext.getExecutingExpire();
            String executingKeyTail = String.valueOf(getExecutingKeyTail(executingExpire));
            // 执行超时时间
            String executingExpireStr = String.valueOf(executingExpire);
            this.writeDebug("[{}-添加排队]-排队hash键：{}，执行过期时间：{}", this.waitContext.getWaitType(), uniqueId, executingExpireStr);
            // 设置lua参数,支持集群
            List<String> keys = Arrays.asList(execCountKey, contentWaitKey, contentWaitListKey, executingKey);
            List<String> values = Arrays.asList(maxCapacity, data, uniqueId, waitDataExpire, executingKeyTail, executingExpireStr);
            Object luaResult = multiRedis.executeScript(
                    WaitRedisScripter.singleton().redisScriptIsWaitAndPut(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            return WaitStatusEnum.convertObjectToEnum(luaResult);
        } catch (Exception ex) {
            log.error(String.format("%s是否要排队执行失败,uniqueId:%s", this.getWaitGroup(), uniqueId), ex);
        }
        // 异常时不排队，直接执行
        return WaitStatusEnum.EXEC_ADD;
    }

    /**
     * 拉取下一个排队数据
     * 1.取排队任务,lua脚本实现
     * 2.有任务时，消费任务，计数不增不减,完成后触发下一个MQ事件，
     * 3.没有任务时，lua脚本中会减少执行中任务数
     */
    protected Map<String, String> doPullNextWait() {
        // 任务排队数据
        String contentWaitKey = getContentWaitKey();
        // 任务排队队列
        String contentWaitListKey = getContentWaitListKey();
        // 任务排队队列
        String executeKey = getExecutingKey();

        // 执行过期时间
        int executingExpire = this.waitContext.getExecutingExpire();
        this.writeDebug("[{}-拉取下一个]-执行过期时间：{}", this.waitContext.getWaitType(), executingExpire);
        long executingKeyTail = getExecutingKeyTail(executingExpire);
        // 执行中数量上限
        String maxCapacity = String.valueOf(this.getMaxExecCount());
        // 设置lua参数,支持集群
        List<String> keys = Arrays.asList(contentWaitKey, contentWaitListKey, executeKey);
        List<String> values = Arrays.asList(String.valueOf(executingExpire), String.valueOf(executingKeyTail), maxCapacity);
        Object luaResult = multiRedis.executeScript(
                WaitRedisScripter.singleton().redisScriptPullNextWait(),
                // Lua脚本中的Key列表
                keys,
                // Lua脚本args列表
                values
        );
        if (luaResult == null) {
            return null;
        }

        List<String> listResult = (List<String>) luaResult;
        Map<String, String> result = new HashMap<>();
        if (listResult != null && listResult.size() == 2) {
            result.put(listResult.get(0), listResult.get(1));
        }

        return result;
    }

    /**
     * 删除排队中任务
     *
     * @param waitUniqueIds
     * @param cancel
     * @return true:有任务被删除，false:没有任务存在
     */
    protected int deleteWaitOrExec(List<String> waitUniqueIds, boolean cancel) {
        try {
            // 任务排队数据
            String contentWaitKey = getContentWaitKey();
            // 任务排队队列
            String contentWaitListKey = getContentWaitListKey();
            // 任务执行中队列
            String executeKey = getExecutingKey();
            // 执行中数量的Key
            String execCountKey = getExecCountKey();

            // 计数过期时间
            String cancelArg = cancel ? "1" : "0";
            // 执行中任务数据的后缀
            long executingKeyTail = this.getExecutingKeyTail(this.waitContext.getExecutingExpire());

            // 设置lua参数,支持集群
            List<String> keys = Arrays.asList(contentWaitKey, contentWaitListKey, executeKey, execCountKey);
            List<String> values = new ArrayList<>(waitUniqueIds);
            values.add(0, String.valueOf(executingKeyTail));
            values.add(0, cancelArg);
            Object luaResult = multiRedis.executeScript(
                    WaitRedisScripter.singleton().redisScriptBatchDeleteWait(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            return luaResult == null ? 0 : Integer.parseInt(luaResult.toString());
        } catch (Exception ex) {
            log.error(String.format("%s批量删除排队中任务失败", this.getWaitGroup()), ex);
        }
        return 0;
    }

    /**
     * 删除排队和执行中任务
     *
     * @param waitUniqueId
     * @param cancel
     * @return true:有任务被删除，false:没有任务存在
     */
    protected boolean deleteWaitOrExec(String waitUniqueId, boolean cancel) {
        try {
            // 任务排队数据
            String contentWaitKey = getContentWaitKey();
            // 任务排队队列
            String contentWaitListKey = getContentWaitListKey();
            // 任务执行中队列
            String executeKey = getExecutingKey();
            // 执行中数量的Key
            String execCountKey = getExecCountKey();

            // 计数过期时间
            String cancelArg = cancel ? "1" : "0";
            // 执行中任务数据的后缀
            long executingKeyTail = this.getExecutingKeyTail(this.waitContext.getExecutingExpire());

            // 设置lua参数,支持集群
            List<String> keys = Arrays.asList(contentWaitKey, contentWaitListKey, executeKey, execCountKey);
            List<String> values = Arrays.asList(waitUniqueId, cancelArg, String.valueOf(executingKeyTail));
            Object luaResult = multiRedis.executeScript(
                    WaitRedisScripter.singleton().redisScriptDeleteWaitOrExec(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            return luaResult != null && Integer.parseInt(luaResult.toString()) > 0;
        } catch (Exception ex) {
            log.error(String.format("%s删除排队中任务失败,uniqueId:%s", this.getWaitGroup(), waitUniqueId), ex);
        }
        return false;
    }

    /**
     * 排队监控信息查询
     *
     * @return
     */
    @Override
    public WaitMonitorResult getMonitorInfo(MonitorQueryArgs queryArgs) {
        WaitMonitorResult monitorInfo = null;
        try {
            // 任务排队队列
            String contentWaitListKey = this.getContentWaitListKey();
            // 任务执行中队列
            String executingKey = this.getExecutingKey();

            if (queryArgs == null) {
                queryArgs = new MonitorQueryArgs();
            }
            int pageIndex = queryArgs.getPageIndex();
            if (pageIndex < 1) {
                pageIndex = 1;
            }

            // 排队数据开始
            int start = (pageIndex - 1) * queryArgs.getPageSize();
            // 排队数据结束
            int stop = start + queryArgs.getPageSize();
            // 设置执行中参数后缀
            String executingKeyTail = String.valueOf(getExecutingKeyTail(this.waitContext.getExecutingExpire()));

            // 设置lua参数,支持集群
            List<String> keys = Arrays.asList(contentWaitListKey, executingKey);
            List<String> values = Arrays.asList(String.valueOf(start), String.valueOf(stop), executingKeyTail);
            Object luaResult = multiRedis.executeScript(
                    WaitRedisScripter.singleton().redisScriptWaitMonitor(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            List<String> listResult = (List<String>) luaResult;
            if (listResult != null) {
                int size = listResult.size();
                if (size >= 2) {
                    int nextCursor = 0;
                    monitorInfo = new WaitMonitorResult();
                    // 执行中总数量
                    monitorInfo.setExecCount(Integer.parseInt(listResult.get(nextCursor)));
                    nextCursor++;
                    // 排队中总数量
                    monitorInfo.setWaitCount(Integer.parseInt(listResult.get(nextCursor)));
                    nextCursor++;
                    // 没有后续数据，则直接返回
                    if (nextCursor >= size) {
                        return monitorInfo;
                    }
                    // 当前页执行中数据获取
                    int execCurCount = Integer.parseInt(listResult.get(nextCursor));
                    nextCursor++;
                    List<String> execData = new ArrayList<>();
                    for (int i = 0; i < execCurCount; i++) {
                        execData.add(listResult.get(nextCursor));
                        nextCursor++;
                    }
                    monitorInfo.setExecData(execData);
                    // 没有后续数据，则直接返回
                    if (nextCursor >= size) {
                        return monitorInfo;
                    }
                    // 当前页排队中数据获取
                    int waitCurCount = Integer.parseInt(listResult.get(nextCursor));
                    nextCursor++;
                    List<String> waitData = new ArrayList<>();
                    for (int i = 0; i < waitCurCount; i++) {
                        waitData.add(listResult.get(nextCursor));
                        nextCursor++;
                    }
                    monitorInfo.setWaitData(waitData);
                }
            }
        } catch (Exception ex) {
            log.error(String.format("%s排队监控信息查询失败", this.getWaitGroup()), ex);
        }
        return monitorInfo;
    }

    /**
     * 兜底检查数据
     *
     * @return
     */
    protected int checkData() {
        try {
            // 执行中数量的Key
            String execCountKey = getExecCountKey();
            // 任务执行中队列
            String executingKey = this.getExecutingKey();
            // 任务排队队列
            String contentWaitListKey = getContentWaitListKey();
            // 执行过期时间
            int executingExpire = this.waitContext.getExecutingExpire();
            long executingKeyTail = getExecutingKeyTail(executingExpire);
            // 执行中数量上限
            String maxCapacity = String.valueOf(this.getMaxExecCount());

            // 设置lua参数,支持集群
            List<String> keys = Arrays.asList(execCountKey, executingKey, contentWaitListKey);
            List<String> values = Arrays.asList(String.valueOf(executingExpire), String.valueOf(executingKeyTail), maxCapacity);
            Object luaResult = multiRedis.executeScript(
                    WaitRedisScripter.singleton().redisScriptCheckData(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            return Integer.parseInt(luaResult.toString());
        } catch (Exception ex) {
            log.error(String.format("%s兜底检查数据失败", this.getWaitGroup()), ex);
        }
        return -1;
    }

    /**
     * 获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    @Override
    public Map<String, String> getWaitData(List<String> waitUniqueIds) {
        // 任务排队数据
        String contentWaitKey = getContentWaitKey();

        // 设置lua参数,支持集群
        List<String> keys = Arrays.asList(contentWaitKey);
        Object luaResult = multiRedis.executeScript(
                WaitRedisScripter.singleton().redisScriptGetWaitData(),
                // Lua脚本中的Key列表
                keys,
                // Lua脚本args列表
                waitUniqueIds
        );
        if (luaResult == null) {
            return null;
        }

        List<String> listResult = (List<String>) luaResult;
        Map<String, String> result = new HashMap<>();
        int size = waitUniqueIds.size();
        if (listResult.size() == size) {
            for (int i = 0; i < size; i++) {
                result.put(waitUniqueIds.get(i), listResult.get(i));
            }
        }
        return result;
    }

    /**
     * 获取排队任务总数
     *
     * @return
     */
    @Override
    public Long getWaitSum() {
        // 排队列表key
        String contentWaitListKey = getContentWaitListKey();

        return this.multiRedis.getRedisTemplate(contentWaitListKey).opsForList().size(contentWaitListKey);
    }

    /**
     * 获取执行中任务总数
     *
     * @return
     */
    @Override
    public Long getExecSum() {
        // 排队列表key
        String execCountKey = getExecCountKey();

        return NumberUtils.toLong(this.multiRedis.get(execCountKey), 0) ;
    }

    /**
     * 从末端分页获取排队任务数据
     *
     * @param bizData 请求参数
     * @return
     */
    @Override
    public Map<String, String> getWaitData (WaitQueryRequestBiaData bizData) {
        Map<String, String> resultMap = new LinkedHashMap<>();
        // 排队列表key
        String contentWaitListKey = getContentWaitListKey();
        long end = -1 - (long) bizData.getPageIndex() * bizData.getPageSize();
        long start = (long) - (bizData.getPageIndex() + 1 ) * bizData.getPageSize();
        // 从末端获取排队键
        List<String> waitUniqueIds = this.multiRedis.getRedisTemplate(contentWaitListKey).opsForList().range(contentWaitListKey, start, end);

        if (CollectionUtils.isEmpty(waitUniqueIds)) {
            return resultMap;
        }
        // 排队列表key
        String contentWaitKey = getContentWaitKey();
        Map<String, String> sourceMap = this.multiRedis.hashGetStrValues(contentWaitKey, waitUniqueIds);
        for (int i = waitUniqueIds.size() - 1; i >= 0; i--) {
            // 倒序插入
            resultMap.put(waitUniqueIds.get(i), sourceMap.get(waitUniqueIds.get(i)));
        }
        return resultMap;
    }

    /**
     * 倒序获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    @Override
    public LinkedHashMap<String, String> getWaitDataDesc (List<String> waitUniqueIds) {
        // 排队列表key
        String contentWaitKey = getContentWaitKey();
        Map<String, String> sourceMap = this.multiRedis.hashGetStrValues(contentWaitKey, waitUniqueIds);
        LinkedHashMap<String, String> resultMap = new LinkedHashMap<>();
        for (int i = waitUniqueIds.size() - 1; i >= 0; i--) {
            // 倒序插入
            resultMap.put(waitUniqueIds.get(i), sourceMap.get(waitUniqueIds.get(i)));
        }
        return resultMap;
    }

    /**
     * 获取排队任务key
     *
     * @return
     */
    @Override
    public List<String> getWaitKeys(long start, long end) {
        // 排队列表key
        String contentWaitListKey = getContentWaitListKey();
        return this.multiRedis.getRedisTemplate(contentWaitListKey).opsForList().range(contentWaitListKey, start, end);
    }

    /**
     * 获取执行中任务key
     *
     * @return
     */
    @Override
    public List<String> getExecKeys() {
        // 排队列表key
        long executingKeyTail = getExecutingKeyTail(this.waitContext.getExecutingExpire());
        String executingKey = getExecutingKey() + executingKeyTail;
        String executingKeyLast = getExecutingKey() + (executingKeyTail - 1);
        Set<Object> values = new HashSet<>();
        values.addAll(this.multiRedis.getRedisTemplate(executingKey).opsForHash().keys(executingKey));
        values.addAll(this.multiRedis.getRedisTemplate(executingKey).opsForHash().keys(executingKeyLast));
        if (CollectionUtils.isEmpty(values)) {
            return Collections.emptyList();
        }
        return values.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.toList());
    }

    /**
     * 删除排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    @Override
    public boolean deleteWaitData (List<String> waitUniqueIds) {
        if (CollectionUtils.isEmpty(waitUniqueIds)) {
            return true;
        }
        // 先删除排队任务再删除执行任务，防止删除时排队任务进入执行中
        return deleteWaitOrExec(waitUniqueIds, true) + deleteWaitOrExec(waitUniqueIds, false) > 0;
    }

    /**
     * 缓存键：任务数据
     *
     * @return
     */
    protected String getContentWaitKey() {
        return String.format("{%s:%s}:wait_data", WAIT_CACHE_KEY_PREFIX, getWaitGroup());
    }

    /**
     * 缓存键：任务排队
     *
     * @return
     */
    protected String getContentWaitListKey() {
        return String.format("{%s:%s}:wait_list", WAIT_CACHE_KEY_PREFIX, getWaitGroup());
    }

    /**
     * 缓存键：任务执行中数量
     *
     * @return
     */
    protected String getExecCountKey() {
        return String.format("{%s:%s}:exec_count", WAIT_CACHE_KEY_PREFIX, getWaitGroup());
    }

    /**
     * 缓存键：任务执行中数据
     *
     * @return
     */
    protected String getExecutingKey() {
        return String.format("{%s:%s}:exec", WAIT_CACHE_KEY_PREFIX, getWaitGroup());
    }

    /**
     * 缓存键后缀：执行中任务数据的后缀，根据任务执行超时时间和当前时间拆分，实际Key过期时间为 executingExpire *2,
     *
     * @return
     */
    protected long getExecutingKeyTail(int executingExpire) {
        // 当前时间戳（秒）
        long now = Instant.now().getEpochSecond();
        return now / executingExpire;
    }

    protected String getWaitGroup() {
        return this.waitContext.getWaitType();
    }

    /**
     * 根据开关写调试日志
     *
     * @param format
     * @param arguments
     */
    protected void writeDebug(String format, Object... arguments) {
        if (this.isDebug()) {
            log.info(format, arguments);
        }
    }

    protected boolean isDebug() {
        return this.waitContext.isDebug();
    }

    protected int getMaxExecCount() {
        return this.waitContext.getMaxExec();
    }
}
