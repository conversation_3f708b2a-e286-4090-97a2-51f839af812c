package com.differ.wdgj.api.user.biz.infrastructure.config;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.data.SiteTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.SystemEnvTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.netflix.eureka.CloudEurekaInstanceConfig;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/6/28 19:38
 */
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "system")
public class SystemAppConfig {

    /**
     * MQ的服务器IP和端口
     */
    private String siteType;

    /**
     * 系统环境类别
     */
    private String env;

    /**
     * 是否内网测试环境
     */
    private Boolean isLocalTest;

    /**
     * 获取本地系统应用基础配置。
     *
     * @return 系统应用基础配置
     */
    public static SystemAppConfig get() {
        return BeanContextUtil.getBean(SystemAppConfig.class);
    }

    /**
     * 获取站点枚举
     *
     * @return
     */
    public SiteTypeEnum getSiteTypeEnum() {
        return SiteTypeEnum.convert(this.siteType);
    }

    /**
     * 获取系统环境枚举
     *
     * @return
     */
    public SystemEnvTypeEnum getSystemEnvTypeEnum() {
        return SystemEnvTypeEnum.convert(this.env);
    }

    /**
     * 获取服务器的IP和端口地址
     *
     * @return IP和端口地址
     */
    public String getIpPort() {
        CloudEurekaInstanceConfig instanceConfig = BeanContextUtil.getBean(EurekaRegistration.class).getInstanceConfig();
        return instanceConfig.getInstanceId();
    }
    /**
     * 获取服务名
     *
     * @return 服务名
     */
    public String getServiceName() {
        CloudEurekaInstanceConfig instanceConfig = BeanContextUtil.getBean(EurekaRegistration.class).getInstanceConfig();
        return instanceConfig.getAppname();
    }

    /**
     * 是否支持在本站点运行
     *
     * @param sitesToRun 可运行的站点集合，由注解参数设置
     * @return
     */
    public boolean runOnSite(String[] sitesToRun) {
        if (sitesToRun == null || sitesToRun.length <= 0) {
            return false;
        }

        // 判断全部站点支持
        List<String> sites = Arrays.asList(sitesToRun);
        if (sites.contains(SiteTypeCodeConst.ALL)) {
            return true;
        }

        // 判断是否包含本站代码
        String serviceCode = this.getSiteTypeEnum().getServiceCode();
        return sites.contains(serviceCode);
    }

    public static String getClusterNo() {
        return "full";
    }

    /**
     * 是否本地测试环境
     *
     * @return 结果
     */
    public Boolean isLocalTest() {
        if(StringUtils.equals(this.getEnv(), SystemEnvTypeEnum.DEV.toString()) && isLocalTest != null){
            return isLocalTest;
        }

        return StringUtils.equals(this.getEnv(), SystemEnvTypeEnum.DEV.toString());
    }

    /**
     * 是否测试环境
     *
     * @return 结果
     */
    public Boolean isTest() {
        return StringUtils.equals(this.getEnv(), SystemEnvTypeEnum.DEV.toString()) ||
                StringUtils.equals(this.getEnv(), SystemEnvTypeEnum.MODULE_TEST.toString()) ||
                StringUtils.equals(this.getEnv(), SystemEnvTypeEnum.SYSTEM_TEST.toString());
    }

    public void setLocalTest(Boolean localTest) {
        isLocalTest = localTest;
    }

    //region get/set
    public String getSiteType() {
        return siteType;
    }

    public void setSiteType(String siteType) {
        this.siteType = siteType;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }
    //endregion
}
