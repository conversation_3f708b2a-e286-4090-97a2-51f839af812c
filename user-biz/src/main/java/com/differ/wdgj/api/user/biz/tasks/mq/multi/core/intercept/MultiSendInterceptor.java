package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.intercept;

import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueueContext;

/**
 * 发送拦截器
 *
 * <AUTHOR>
 * @date 2024/4/7 11:09
 */
public interface MultiSendInterceptor<T> {

    /**
     * 发送消息前置拦截处理
     *
     * @param context
     * @param message
     * @param messageStr
     * @param header
     */
    void beforeSendMulti(SubQueueContext context, T message, String messageStr, QueueHeader header);

    /**
     * 发送消息后置拦截处理
     *
     * @param message
     * @param messageStr
     * @param header
     * @param e
     */
    void afterSendMulti(boolean sendResult, SubQueueContext context, T message, String messageStr, QueueHeader header, Throwable e);
}
