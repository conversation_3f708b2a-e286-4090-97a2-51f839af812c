<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
<!--    <turboFilter class = "com.differ.wdgj.api.user.biz.infrastructure.initializer.log.CustomLogInitializer"></turboFilter>-->
    <!-- kafka日志类配置 -->
    <appender name="logstash" class="com.differ.jackyun.framework.component.log.kafka.LogbackKafkaAppender">

        <!-- 主题（固定） -->
        <topic>logstash-kafka-system-topic</topic>

        <!-- 开发，集成kafka地址配置 -->
        <producerConfig>bootstrap.servers=192.168.88.182:9092,192.168.88.183:9092,192.168.88.184:9092</producerConfig>

    </appender>

    <!-- 默认日志等级 -->
    <root level="INFO">
        <appender-ref ref="logstash"/>
    </root>
</configuration>