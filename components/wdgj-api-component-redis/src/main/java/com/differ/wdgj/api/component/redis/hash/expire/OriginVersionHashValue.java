package com.differ.wdgj.api.component.redis.hash.expire;

/**
 * @Description
 * <AUTHOR>
 * @date 2021/12/2 21:25
 */
public class OriginVersionHashValue extends HashExpireValue{

    /**
     * 更新前的数据版本
     */
    private String OriginVersion;

    public OriginVersionHashValue(long expire, String realValue, String version, String originVersion) {
        super(expire, realValue, version);
        OriginVersion = originVersion;
    }
}
