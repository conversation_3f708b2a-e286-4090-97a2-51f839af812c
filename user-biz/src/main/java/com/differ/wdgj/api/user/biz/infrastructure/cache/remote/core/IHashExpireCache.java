package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;

import java.util.Map;
import java.util.function.Consumer;

/**
 * HASH缓存接口（支持字段过期）
 *
 * <AUTHOR> wangz
 * @since 2019-12-24  15:01
 */
public interface IHashExpireCache<T> {

    /**
     * 查询缓存
     * <p>
     * 1、单个查询
     * 2、缓存不存在时从数据源获取并同步到缓存
     *
     * @param hashField 字段
     * @return 值
     */
    T getAndSyncIfAbsent(String hashField);

    /**
     * 查询缓存
     * <p>
     * 1、批量查询
     * 2、缓存不存在时从数据源获取并同步到缓存
     *
     * @param hashFields 字段集合
     * @return 值集合
     */
    Map<String, T> getAndSyncIfAbsent(String... hashFields);

    /**
     * 分页获取某个Hash下所有缓存，并且处理
     *
     * @param pageSize 分页大小，每页scan的数据量
     * @param handler  处理方法
     */
    void scanAll(int pageSize, Consumer<T> handler);

    /**
     * 更新缓存（支持字段过期）
     *
     * @param hashField 字段
     * @param hashValue 值
     * @return 是否成功
     */
    boolean hashExpireSet(String hashField, T hashValue);

    /**
     * 批量更新缓存（支持字段过期）
     *
     * @param hashValues 值集合
     * @return 成功数量
     */
    int hashExpireMultiSet(Map<String, T> hashValues);

    /**
     * 删除缓存
     *
     * @param hashFields 字段集合
     * @return 删除个数
     */
    int remove(String... hashFields);
}
