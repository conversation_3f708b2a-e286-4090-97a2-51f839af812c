package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.differ.wdgj.api.component.util.enums.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 下载售后单拆分策略
 *
 * <AUTHOR>
 * @date 2024/9/19 下午4:58
 */
public enum LoadOrderSplitTypeEnum implements CodeEnum, ValueEnum {

    /**
     * 无需拆分
     */
    NO_SPLIT(0, "NO_SPLIT"),

    /**
     * 固定时间拆分
     */
    FIX_TIME_SPLIT(1, "FIX_TIME_SPLIT"),

    /**
     * 动态时间拆分
     */
    DYNAMIC_TIME_SPLIT(2, "DYNAMIC_TIME_SPLIT"),

    ;
    // region 变量

    /**
     * 枚举code值
     */
    private String code;

    /**
     * 枚举值
     */
    private int value;


    // endregion

    // region 构造器

    LoadOrderSplitTypeEnum(int value, String code) {
        this.value = value;
        this.code = code;
    }

    // endregion

    // region getter

    /**
     * 获取 枚举code
     *
     * @return 枚举code
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 获取 枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    // endregion

    // region 公共方法

    /**
     * 枚举转换
     *
     * @param code code值
     * @return 返回结果
     */
    public static LoadOrderSplitTypeEnum convert(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return EnumConvertCacheUtil.convert(code, LoadOrderSplitTypeEnum.class, EnumConvertType.CODE);
    }

    // endregion
}
