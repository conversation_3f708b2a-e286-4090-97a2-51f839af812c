package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.hash.membercontrol.plugins;

import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.hash.membercontrol.AbstractMemberControlCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 负载均衡定时任务会员控制缓存
 *
 * <AUTHOR>
 * @date 2023-12-12 23:45
 */
public class ApiBalanceJobMemberControlCache extends AbstractMemberControlCache {

    // region 构造方法

    /**
     * 构造方法
     *
     * @param jobCode 任务编码
     */
    public ApiBalanceJobMemberControlCache(String jobCode) {
        this.jobCode = jobCode;

        try {
            this.afterPropertiesSet();
        } catch (Exception e) {
            log.error("初始化API负载均衡定时任务会员控制缓存实例失败", e);
        }
    }

    // endregion

    // region 变量

    /**
     * 日志
     */
    protected Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 重试类型
     */
    private final String jobCode;

    // endregion

    // region 重写基类方法

    /**
     * 业务类型
     *
     * @return 业务类型
     */
    @Override
    public String businessType() {
        return String.format("balance.%s", this.jobCode);
    }

    /**
     * 清除标记限制（超过约定次数后删除会员）
     *
     * @return 最大次数
     */
    @Override
    public long clearSignLimit() {
        return 10L;
    }

    // endregion

    // region 公共方法

    /**
     * 获取缓存代理对象
     *
     * @return 缓存代理对象
     */
    public static ApiBalanceJobMemberControlCache build(String jobCode) {
        return new ApiBalanceJobMemberControlCache(jobCode);
    }

    /**
     * 会员是否开启
     *
     * @param memberName 会员名
     * @return 结果
     */
    public boolean actionMember(String memberName) {
        return this.cacher.hashExist(this.getCacheKey(), memberName);
    }

    // endregion
}