package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plat;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.ISyncStockShopResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.TaoBaoActivityStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.TaoBaoCStoreCheckStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.TaoBaoCStoreFlagStockResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.TaoBaoFxStockResultProcessor;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝库存同步处理类
 *
 * <AUTHOR>
 * @date 2024-03-22 15:58
 */
public class TaoBaoSyncStockProcessor extends BaseSyncStockProcessor {
    //region 构造
    /**
     * 构造函数
     *
     * @param context 全局上下文
     */
    public TaoBaoSyncStockProcessor(StockSyncContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取库存同步结果处理器
     *
     * @return 结果
     */
    @Override
    protected List<ISyncStockShopResultProcessor> getPlatResultProcessors(){
        List<ISyncStockShopResultProcessor> resultProcessors = new ArrayList<>();
        // 淘宝活动商品结果处理
        resultProcessors.add(new TaoBaoActivityStockResultProcessor());
        // 淘宝分销商品结果处理
        resultProcessors.add(new TaoBaoFxStockResultProcessor());
        // 淘宝淘宝C店商品审核变更处理
        resultProcessors.add(new TaoBaoCStoreCheckStockResultProcessor());
        // 淘宝C店数据打标
        resultProcessors.add(new TaoBaoCStoreFlagStockResultProcessor());
        return resultProcessors;
    }
}
