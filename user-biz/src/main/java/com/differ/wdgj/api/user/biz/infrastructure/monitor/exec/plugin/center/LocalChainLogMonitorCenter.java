package com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.plugin.center;

import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.AbstractExecuteQueueMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.TimeoutQueue;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ExecuteEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * @Description 本地日志链路执行监控中心,相关定时任务:LocalExecuteMonitorJob，设计：https://s.jkyun.biz/PtwivXO 执行监控基础组件
 * <AUTHOR>
 * @Date 2023/12/14 17:14
 */
public class LocalChainLogMonitorCenter extends AbstractExecuteQueueMonitorCenter {

    private static final Logger LOG = LoggerFactory.getLogger(LocalChainLogMonitorCenter.class);

    /**
     * 当完成一个任务链时，执行处理
     *
     * @param queue
     * @param success
     */
    @Override
    protected void doFinish(TimeoutQueue queue, boolean success) {
        if (success) {
            return;
        }
        ConcurrentLinkedQueue<ExecuteEvent> queueEvent = queue.getQueue();
        ExecuteEvent executeEvent = queueEvent.poll();
        while (executeEvent != null) {
            LOG.warn(String.format("[链路跟踪]-[失败]时间：%s,%s,%s", executeEvent.getEventTime(), executeEvent.getStatus(), executeEvent.getEventData()));
            executeEvent = queueEvent.poll();
        }
        LOG.warn(String.format("[链路跟踪]-[完成]忽略日志数：%d", queue.getIgnoreCount()));
    }

    /**
     * 是否超时，用于外部定时任务检测
     * 注意：当超时的时候，如果不想清除队列中的数据，业务处理中不要使用queue.getQueue().poll(),应使用queue.getQueue().toArray()处理数据
     *
     * @param queue 执行队列
     * @param maxTimeoutSeconds 超时的阈值
     */
    @Override
    protected void doTimeout(TimeoutQueue queue, int maxTimeoutSeconds) {
        ConcurrentLinkedQueue<ExecuteEvent> queueEvent = queue.getQueue();
        ExecuteEvent executeEvent = queueEvent.poll();
        while (executeEvent != null) {
            LOG.warn(String.format("[链路跟踪]-[超时],时间：%s,%s,%s", executeEvent.getEventTime(), executeEvent.getStatus(), executeEvent.getEventData()));
            executeEvent = queueEvent.poll();
        }
        LOG.warn(String.format("[链路跟踪]-[超时]忽略日志数：%d", queue.getIgnoreCount()));
    }
}
