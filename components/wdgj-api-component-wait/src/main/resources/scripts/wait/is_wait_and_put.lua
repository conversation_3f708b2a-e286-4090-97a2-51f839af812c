-- 是否要排队，排队时加入排队任务
-- User: chuff
-- Date: 2021/9/13 10:45
--

-- 执行中数量的Key
local execCountKey = KEYS[1]
-- 排队任务数据（Hash）
local contentWaitKey = KEYS[2]
-- 任务排队队列
local contentWaitListKey = KEYS[3]
-- 排队任务执行中的Key
local executeKey = KEYS[4]
-- 执行中数量上限
local maxCapacity = tonumber(ARGV[1])
-- 任务内容
local content = ARGV[2]
-- 任务内容hashkey
local contentHashKey = ARGV[3]
-- 排队任务缓存键过期时间，每次加排队任务会重设过期时间
local waitDataExpire = tonumber(ARGV[4])
-- 执行中的key后缀
local executeKeyTail = tonumber(ARGV[5])
-- 执行任务超时时间
local executeExpire = tonumber(ARGV[6])

local executeKeyWithTail = executeKey .. executeKeyTail
local executeKeyWithBefore = executeKey .. (executeKeyTail -1)

-- 执行中的不再排队
if 1 == redis.call("hexists", executeKeyWithTail, contentHashKey) or 1 == redis.call("hexists", executeKeyWithBefore, contentHashKey)  then
    return 2
end

-- 执行中的数量
local currentCount = redis.call("hlen", executeKeyWithTail) + redis.call("hlen", executeKeyWithBefore)

-- 是否要排队
if currentCount < maxCapacity then
    -- 不用排队，添加到执行中,并重设过期时间，
    redis.call("hset", executeKeyWithTail, contentHashKey, content)
    redis.call("expire", executeKeyWithTail, executeExpire)
    -- 添加计数
    redis.call("setex", execCountKey, executeExpire, currentCount + 1)
    return 0
else
    if 1 == redis.call("hexists", contentWaitKey, contentHashKey) then
        return 3
    end
    -- 要排队
    redis.call("lpush", contentWaitListKey, contentHashKey)
    redis.call("hset", contentWaitKey, contentHashKey, content)
    -- 设置hashkey过期时间，防止一直占用
    redis.call("expire", contentWaitKey, waitDataExpire)
    redis.call("expire", contentWaitListKey, waitDataExpire)
    return 1
end