package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.wdgj.MatchOrderGoodsResultDto;
import org.apache.ibatis.annotations.Param;

/**
 * 网店管家存储过程仓储
 *
 * <AUTHOR>
 * @date 2024-07-04 20:26
 */
public interface WdgjStoredProcedureMapper {

    /**
     * 商品匹配
     *
     * @param shopId       店铺Id
     * @param tradeGoodsNo 商品编码
     * @param goodsName    商品名称
     * @param goodsSpec    商品规格
     */
    MatchOrderGoodsResultDto matchOrderGoods(@Param("shopId") int shopId, @Param("tradeGoodsNo") String tradeGoodsNo, @Param("goodsName") String goodsName, @Param("goodsSpec") String goodsSpec);

    /**
     * 售后单变更通知
     *
     * @param billId      售后单Id
     * @param noticeTypes 消息通知类别，多个用逗号分割
     */
    void afterSaleChangeNotice(@Param("billId") int billId, @Param("noticeTypes") String noticeTypes);
}
