package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen;

import com.differ.jackyun.framework.component.jmq.core.annotation.JChild;
import com.differ.jackyun.framework.component.jmq.rabbit.annotation.JMQRabbit;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen.strategy.EndWithMatchListenStrategy;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * JMQ批量监听注解
 *
 * <AUTHOR>
 * @date 2024-03-19 13:52
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
@Inherited
@JMQRabbit
@JChild
public @interface JmqBatchListenAnnotation {

    String value() default "";

    Class<? extends MatchListenStrategy> jmqMatchListenStrategy() default EndWithMatchListenStrategy.class;

}
