<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.GCfgSysMapper" >
  <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.GCfgSysDO" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="RecID" property="recId" jdbcType="INTEGER" />
    <result column="CfgKey" property="cfgKey" jdbcType="VARCHAR" />
    <result column="cfgvalue" property="cfgValue" jdbcType="VARCHAR" />
    <result column="Lastmodified" property="lastModified" jdbcType="TIMESTAMP" />
  </resultMap>

  <select id="selectByConfigKey"
            resultType="java.lang.String"
            parameterType="java.lang.String">
    select cfgvalue from  g_cfg_sys where cfgkey = #{cfgkey}
  </select>

  <select id="getSysAutoId" resultType="java.lang.String" statementType="CALLABLE">
    {call G_GetSysID(#{tableName,mode=IN},#{fieldName,mode=IN})}
  </select>

</mapper>