package com.differ.wdgj.api;

import com.differ.wdgj.api.business.BusinessApplication;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description spring的测试基类
 * <AUTHOR>
 * @Date 2021/1/6 15:46
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {BusinessApplication.class})
public abstract class AbstractSpringTest {
}
