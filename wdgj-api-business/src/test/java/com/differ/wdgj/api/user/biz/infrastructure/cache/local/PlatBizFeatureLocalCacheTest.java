package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.plat.PlatBizFeatureLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.PlatBizFeatureCompositeDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature.PlatBizFeatureTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 平台业务特性内存缓存
 *
 * <AUTHOR>
 * @date 2024-06-21 14:51
 */
@Ignore
public class PlatBizFeatureLocalCacheTest  extends AbstractSpringTest {

    /**
     * 获取业务特性
     */
    @Test
    public void getFeatureTest(){

        PlatBizFeatureTypeEnum loadAfterSales = PlatBizFeatureTypeEnum.LOAD_AFTER_SALES;

        // 默认数据
        PlatBizFeatureCompositeDto defaultComposite = PlatBizFeatureLocalCache.singleton().getFeature(PolyPlatEnum.NONE, loadAfterSales);

        // 平台级数据
        PlatBizFeatureCompositeDto platComposite = PlatBizFeatureLocalCache.singleton().getFeature(PolyPlatEnum.BUSINESS_DouDianSupermarket, loadAfterSales);

        // 无平台级数据
        PlatBizFeatureCompositeDto noPlatComposite = PlatBizFeatureLocalCache.singleton().getFeature(PolyPlatEnum.BUSINESS_Taobao, loadAfterSales);

    }
}
