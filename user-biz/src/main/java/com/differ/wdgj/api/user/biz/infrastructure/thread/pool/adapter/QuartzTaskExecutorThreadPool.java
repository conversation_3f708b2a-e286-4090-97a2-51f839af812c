package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.adapter;


import com.differ.wdgj.api.component.util.system.ThreadUtil;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.AlarmOperator;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.AlarmContent;
import com.differ.wdgj.api.user.biz.infrastructure.common.TimeSegmentCounter;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerConfigException;
import org.quartz.core.JobRunShell;
import org.quartz.spi.ThreadPool;
import org.quartz.spi.TriggerFiredBundle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * Quartz定时任务线程池
 *
 * <AUTHOR>
 * @date 2024/2/26 14:45
 */
public class QuartzTaskExecutorThreadPool implements ThreadPool {

    protected final Logger log = LoggerFactory.getLogger(QuartzTaskExecutorThreadPool.class);
    /**
     * 线程休眠时间
     */
    private static final long SLEEP_INTERVAL = 1000L;
    /**
     * quartz线程池
     */
    private final TaskEnum taskEnum = TaskEnum.API_QUARTZ_EXEC;
    /**
     * 触发4次
     */
    private static final int EVENT_COUNT = 20;
    /**
     * 触发条件：总共分20段，每段3秒，触发20次
     */
    private final TimeSegmentCounter timeSegmentCounter = new TimeSegmentCounter(3, TimeUnit.SECONDS, EVENT_COUNT, EVENT_COUNT);
    /**
     * 是否关闭
     */
    private boolean isShutdown = false;

    @Override
    public boolean runInThread(Runnable runnable) {
        if (runnable == null) {
            return false;
        }

        try {
            this.taskEnum.execute(getExecRuntime(runnable));
            return true;
        } catch (RejectedExecutionException ex) {
            log.error("quartz执行线程被拒绝", ex);
            return false;
        }
    }

    /**
     * 当没有可用线程时，阻塞等待可用线程
     *
     * @return
     */
    @Override
    public int blockForAvailableThreads() {
        int count = this.taskEnum.getCoreThreadIdleCount();
        while (count <= 0 && !this.isShutdown) {
            ThreadUtil.sleep(SLEEP_INTERVAL);
            count = this.taskEnum.getCoreThreadIdleCount();

            // 时间区间段计数器：判断是否触发报警
            if (count <= 0 && timeSegmentCounter.checkOnEvent()) {
                String alarmText = String.format("定时任务触发的可用线程不足,每分钟告警%d次", EVENT_COUNT);
                String alarmKey = SystemAppConfig.get().getIpPort();
                AlarmOperator.singleton().alarmInterval(AlarmIntervalTypeEnum.QUARTZ_TASK, alarmKey, AlarmContent.build(AlarmIntervalTypeEnum.QUARTZ_TASK, alarmText));
            }
        }

        return count;
    }

    @Override
    public void initialize() throws SchedulerConfigException {

    }

    @Override
    public void shutdown(boolean waitForJobsToComplete) {
        this.isShutdown = true;
        this.taskEnum.getPoolExecutor().shutdown();
    }

    @Override
    public int getPoolSize() {
        return this.taskEnum.getCorePoolSize();
    }

    @Override
    public void setInstanceId(String schedInstId) {

    }

    @Override
    public void setInstanceName(String schedName) {

    }

    /**
     * 获取执行任务,支持描述信息的显示
     *
     * @param runnable
     * @return
     */
    private Runnable getExecRuntime(Runnable runnable) {

        if (runnable instanceof JobRunShell) {
            return new Runnable() {
                @Override
                public void run() {
                    runnable.run();
                }

                @Override
                public String toString() {
                    // 显示任务描述
                    JobRunShell runShell = (JobRunShell) runnable;
                    String jobName = getJobDescription(runShell);
                    if (StringUtils.isNotBlank(jobName)) {
                        return jobName;
                    }
                    return super.toString();
                }
            };
        }

        return runnable;
    }


    /**
     * 获取任务描述
     *
     * @param runShell 任务
     * @return 任务描述
     */
    private String getJobDescription(JobRunShell runShell) {
        if (runShell == null) {
            return null;
        }

        Field field = ReflectionUtils.findField(runShell.getClass(), "firedTriggerBundle");
        ReflectionUtils.makeAccessible(field); // 关键是这一行，设置可访问
        Object firedTriggerBundleObject = ReflectionUtils.getField(field, runShell);
        TriggerFiredBundle firedTriggerBundle = (TriggerFiredBundle) firedTriggerBundleObject;
        if (firedTriggerBundle == null) {
            return null;
        }
        return firedTriggerBundle.getJobDetail().getKey() + "." + firedTriggerBundle.getFireTime();
    }
}