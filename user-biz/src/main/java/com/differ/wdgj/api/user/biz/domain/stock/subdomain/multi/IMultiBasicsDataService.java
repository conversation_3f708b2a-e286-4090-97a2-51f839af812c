package com.differ.wdgj.api.user.biz.domain.stock.subdomain.multi;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.MappingSetTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncInvalidStoreResult;

import java.util.List;

/**
 * 多仓基础数据服务
 *
 * <AUTHOR>
 * @date 2024-03-19 10:31
 */
public interface IMultiBasicsDataService {

    /**
     * 删除多门店匹配信息
     * @param vipUser 会员名
     * @param operator 操作人
     * @param mappingSetType 匹配类型
     * @param invalidStoreResults 无效店铺结果列表
     */
    void delMultiStoreMatch(String vipUser, String operator, MappingSetTypeEnum mappingSetType, List<StockSyncInvalidStoreResult> invalidStoreResults);
}
