package com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig;

import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.data.BizConfigSyncResult;

/**
 * 业务配置同步基类
 *
 * <AUTHOR>
 * @since 2024-07-26 上午 11:17
 */
public class BaseBizConfigSyncProcessor implements IBizConfigSyncProcessor {


    /**
     * 同步配置信息
     *
     * @param bizConfig 业务配置信息
     * @return 同步结果
     */
    @Override
    public BizConfigSyncResult syncConfig(String bizConfig) {

        BizConfigSyncResult result = new BizConfigSyncResult();
        result.setErrorMsg("未实现该业务的同步方法");

        return result;
    }
}
