package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 系统日志
 * <AUTHOR>
 * @date 2024-04-09 10:55
 */
@Ignore
public class GloLogMapperTest extends AbstractSpringTest {

    @Autowired(required = false)
    private GloLogMapper mapper;

    /**
     * 新增系统
     */
    @Test
    public void InsertTest(){
        GloLogDO gloLogDO = new GloLogDO();
        DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(),()->mapper.insert(gloLogDO));
    }
}
