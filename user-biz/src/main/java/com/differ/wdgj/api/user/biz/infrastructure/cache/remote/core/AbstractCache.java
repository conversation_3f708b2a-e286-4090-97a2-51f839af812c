package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;

import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import org.springframework.beans.factory.InitializingBean;

/**
 * Redis缓存顶层抽象类
 *
 * <AUTHOR>
 * @date 2020/12/22 15:24
 */
public abstract class AbstractCache implements InitializingBean {

    /**
     * 缓存实例对象createCacheKeyArgument
     */
    protected MultiRedis cacher;

    @Override
    public void afterPropertiesSet() throws Exception {
        this.cacher = BeanContextUtil.getBean(MultiRedis.class);
    }

}
