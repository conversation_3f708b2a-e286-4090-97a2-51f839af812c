package com.differ.wdgj.api.user.biz.domain.aftersale.save.operation;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolySpecialRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.AfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * RefundTypeCovertOperation 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午6:00
 */
public class RefundTypeCovertOperationTest {

    @Mock
    private AfterSaleSaveContext mockContext;

    private RefundTypeCovertOperation refundTypeCovertOperation;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        refundTypeCovertOperation = RefundTypeCovertOperation.singleton();
    }

    /**
     * 测试退货退款单转换
     * 当菠萝派售后单类型为JH_04（退货退款单）时，应该转换为REFUND
     */
    @Test
    public void testCovertApiAfterSaleType_RefundType_ShouldReturnRefund() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_04);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("退货退款单应该转换为REFUND", ApiAfterSaleTypeEnum.REFUND, result);
    }

    /**
     * 测试退款单转换
     * 当菠萝派售后单类型为JH_03（退款单）时，应该转换为REFUND_PAY
     */
    @Test
    public void testCovertApiAfterSaleType_RefundPayType_ShouldReturnRefundPay() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("退款单应该转换为REFUND_PAY", ApiAfterSaleTypeEnum.REFUND_PAY, result);
    }

    /**
     * 测试补寄单转换     * 当菠萝派售后单类型为JH_SUPPLEMENT_SEND（补寄）时，应该转换为REFUND_SUPPLEMENT
     */
    @Test
    public void testCovertApiAfterSaleType_SupplementType_ShouldReturnRefundSupplement() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_SUPPLEMENT_SEND);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("补寄单应该转换为REFUND_SUPPLEMENT", ApiAfterSaleTypeEnum.REFUND_SUPPLEMENT, result);
    }

    /**
     * 测试价保订单转换
     * 当菠萝派售后单类型为JH_10（价保订单）时，应该转换为REFUND_BJ
     */
    @Test
    public void testCovertApiAfterSaleType_PriceProtectType_ShouldReturnRefundBj() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_10);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("价保订单应该转换为REFUND_BJ", ApiAfterSaleTypeEnum.REFUND_BJ, result);
    }

    /**
     * 测试维修单转换     * 当菠萝派售后单类型为JH_WEIXIU（维修单）时，应该转换为REPAIR
     */
    @Test
    public void testCovertApiAfterSaleType_RepairType_ShouldReturnRepair() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_WEIXIU);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("维修单应该转换为REPAIR", ApiAfterSaleTypeEnum.REPAIR, result);
    }

    /**
     * 测试特殊售后单转换- 保价
     * 当特殊售后单类型为JH_PRICE_PROTECT（保价）时，应该转换为REFUND_BJ
     */
    @Test
    public void testCovertApiAfterSaleType_SpecialPriceProtect_ShouldReturnRefundBj() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_SPECIALREFUND);
        ployOrder.setSpecialRefundType(PolySpecialRefundTypeEnum.JH_PRICE_PROTECT.getCode());
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("特殊售后类型保价应该转换为REFUND_BJ", ApiAfterSaleTypeEnum.REFUND_BJ, result);
    }


    // ==================== 配置相关的测试场景 ====================

    /**
     * 测试bPolyRefundPayRegardedRefundPaySend为true时，JH_03退款单转换为REFUND_PAY_SEND
     * 当配置了将菠萝派仅退款单视为已发货仅退款且退款类型为JH_03时，应该直接转换为REFUND_PAY_SEND
     */
    @Test
    public void testCovertApiAfterSaleType_PolyRefundPayRegardedSend_JH03_ShouldReturnRefundPaySend() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 模拟配置：启用将菠萝派仅退款单视为已发货仅退款
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBPolyRefundPayRegardedRefundPaySend(true);
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("启用bPolyRefundPayRegardedRefundPaySend时，JH_03应该转换为REFUND_PAY_SEND",
                           ApiAfterSaleTypeEnum.REFUND_PAY_SEND, result);
    }

    /**
     * 测试bPolyRefundPayRegardedRefundPaySend为true时，JH_01退款单转换为REFUND_PAY_SEND
     * 当配置了将菠萝派仅退款单视为已发货仅退款且退款类型为JH_01时，应该直接转换为REFUND_PAY_SEND
     */
    @Test
    public void testCovertApiAfterSaleType_PolyRefundPayRegardedSend_JH01_ShouldReturnRefundPaySend() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_01);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 模拟配置：启用将菠萝派仅退款单视为已发货仅退款
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBPolyRefundPayRegardedRefundPaySend(true);
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("启用bPolyRefundPayRegardedRefundPaySend时，JH_01应该转换为REFUND_PAY_SEND",
                           ApiAfterSaleTypeEnum.REFUND_PAY_SEND, result);
    }

    /**
     * 测试bPolyRefundPayRegardedRefundPaySend为false时的默认行为
     * 当未配置将菠萝派仅退款单视为已发货仅退款时，应该按照原有逻辑处理
     */
    @Test
    public void testCovertApiAfterSaleType_PolyRefundPayRegardedSend_Disabled_ShouldUseDefaultLogic() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 模拟配置：未启用将菠萝派仅退款单视为已发货仅退款
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBPolyRefundPayRegardedRefundPaySend(false);
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - 应该按照原有逻辑转换为REFUND_PAY
        Assert.assertEquals("未启用bPolyRefundPayRegardedRefundPaySend时，JH_03应该转换为REFUND_PAY",
                           ApiAfterSaleTypeEnum.REFUND_PAY, result);
    }

    /**
     * 测试bDistinguishShippedAndUnshipped为true时，REFUND_PAY根据发货状态进一步转换
     * 当基础转换结果为REFUND_PAY且启用区分已发货/未发货时，应该根据发货状态进一步转换
     */
    @Test
    public void testCovertApiAfterSaleType_DistinguishShippedEnabled_RefundPayWithSendStatus() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrderWithSendStatus(true); // 已发货

        // 模拟配置：启用区分已发货/未发货，但不启用bPolyRefundPayRegardedRefundPaySend
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBDistinguishShippedAndUnshipped(true);
        configContent.setBPolyRefundPayRegardedRefundPaySend(false); // 确保不走第一层逻辑
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - 应该根据发货状态转换为REFUND_PAY_SEND
        Assert.assertEquals("启用bDistinguishShippedAndUnshipped且已发货时，应该转换为REFUND_PAY_SEND",
                           ApiAfterSaleTypeEnum.REFUND_PAY_SEND, result);
    }

    /**
     * 测试bDistinguishShippedAndUnshipped为true时，REFUND_PAY根据未发货状态转换
     * 当基础转换结果为REFUND_PAY且启用区分已发货/未发货且未发货时，应该转换为REFUND_PAY_NOT_SEND
     */
    @Test
    public void testCovertApiAfterSaleType_DistinguishShippedEnabled_RefundPayNotSend() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrderWithSendStatus(false); // 未发货

        // 模拟配置：启用区分已发货/未发货，但不启用bPolyRefundPayRegardedRefundPaySend
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBDistinguishShippedAndUnshipped(true);
        configContent.setBPolyRefundPayRegardedRefundPaySend(false); // 确保不走第一层逻辑
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - 应该根据发货状态转换为REFUND_PAY_NOT_SEND
        Assert.assertEquals("启用bDistinguishShippedAndUnshipped且未发货时，应该转换为REFUND_PAY_NOT_SEND",
                           ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND, result);
    }

    /**
     * 测试bDistinguishShippedAndUnshipped为false时不进行发货状态转换
     * 当未启用区分已发货/未发货时，不应该进行发货状态的进一步转换
     */
    @Test
    public void testCovertApiAfterSaleType_DistinguishShippedDisabled_NoSendStatusConversion() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrderWithSendStatus(true); // 已发货

        // 模拟配置：未启用区分已发货/未发货
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBDistinguishShippedAndUnshipped(false);
        configContent.setBPolyRefundPayRegardedRefundPaySend(false);
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - 应该保持REFUND_PAY，不进行发货状态转换
        Assert.assertEquals("未启用bDistinguishShippedAndUnshipped时，应该保持REFUND_PAY",
                           ApiAfterSaleTypeEnum.REFUND_PAY, result);
    }

    /**
     * 测试两个配置同时启用时的优先级
     * 当bPolyRefundPayRegardedRefundPaySend和bDistinguishShippedAndUnshipped都启用时，
     * bPolyRefundPayRegardedRefundPaySend应该优先生效（在polyRefundTypeCovert中处理）
     */
    @Test
    public void testCovertApiAfterSaleType_BothConfigsEnabled_PolyRefundPayRegardedTakesPriority() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrderWithSendStatus(false); // 未发货

        // 模拟配置：同时启用两个配置
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBDistinguishShippedAndUnshipped(true);
        configContent.setBPolyRefundPayRegardedRefundPaySend(true);
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - bPolyRefundPayRegardedRefundPaySend优先生效，返回REFUND_PAY_SEND
        Assert.assertEquals("两个配置同时启用时，bPolyRefundPayRegardedRefundPaySend应该优先生效",
                           ApiAfterSaleTypeEnum.REFUND_PAY_SEND, result);
    }

    /**
     * 测试配置为null时的默认行为
     * 当配置对象为null时，应该按照原有逻辑处理
     */
    @Test
    public void testCovertApiAfterSaleType_ConfigNull_ShouldUseDefaultLogic() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 模拟配置：配置对象为null
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(null);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - 应该按照原有逻辑转换为REFUND_PAY
        Assert.assertEquals("配置为null时，JH_03应该转换为REFUND_PAY",
                           ApiAfterSaleTypeEnum.REFUND_PAY, result);
    }

    /**
     * 测试非退款类型不受配置影响
     * 当售后类型不是退款类型时，不应该受到配置的影响
     */
    @Test
    public void testCovertApiAfterSaleType_NonRefundPayType_NotAffectedByConfigs() {
        // 准备测试数据 - 使用退货退款单
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_04);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 模拟配置：启用所有配置
        AfterSalesConfigContent configContent = new AfterSalesConfigContent();
        configContent.setBDistinguishShippedAndUnshipped(true);
        configContent.setBPolyRefundPayRegardedRefundPaySend(true);
        Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.DEFAULT.getCode()))
               .thenReturn(configContent);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
            mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果 - 退货退款单不受配置影响，仍然转换为REFUND
        Assert.assertEquals("退货退款单不受配置影响",
                           ApiAfterSaleTypeEnum.REFUND, result);
    }

    //region 私有方法
    /**
     * 创建测试用的菠萝派订单对象
     *
     * @param refundType 退款类别
     * @return 菠萝派订单对象
     */
    private BusinessGetRefundOrderResponseOrderItem createPlolyOrder(PolyRefundTypeEnum refundType) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = new BusinessGetRefundOrderResponseOrderItem();
        ployOrder.setRefundType(refundType.getCode());
        ployOrder.setRefundNo("test-refund-id-001");
        ployOrder.setPlatOrderNo("test-order-id-001");
        ployOrder.setRefundAmount(new BigDecimal("100.00"));

        // 添加退款商品信息
        List<BusinessGetRefundResponseRefundGoodInfo> refundGoods = new ArrayList<>();
        BusinessGetRefundResponseRefundGoodInfo goodInfo = new BusinessGetRefundResponseRefundGoodInfo();
        goodInfo.setPlatProductId("1");
        goodInfo.setProductName("测试商品");
        goodInfo.setRefundProductNum(1);
        goodInfo.setStatus(PolyRefundGoodsStatusEnum.BUYER_RECEIVED.getCode());
        refundGoods.add(goodInfo);
        ployOrder.setRefundGoods(refundGoods);

        return ployOrder;
    }

    /**
     * 创建测试用的数据库订单对
     *
     * @return 数据库订单对
     */
    private DbAfterSaleOrderItem createDbOrder() {
        DbAfterSaleOrderItem dbOrder = new DbAfterSaleOrderItem();

        // 创建ApiTrade
        ApiTradeDO apiTrade = new ApiTradeDO();
        apiTrade.setBillId(1001);
        apiTrade.setTradeNo("test-trade-no-001");
        apiTrade.setShopId(2001);
        apiTrade.setbSplit(0);
        apiTrade.setSynStatus(4); // SUCCESS
        dbOrder.setApiTrade(apiTrade);

        // 创建ApiTradeGoods列表
        List<ApiTradeGoodsDO> apiTradeGoodsList = new ArrayList<>();
        ApiTradeGoodsDO apiTradeGoods = new ApiTradeGoodsDO();
        apiTradeGoods.setRecId(3001);
        apiTradeGoods.setGoodsId(1);
        apiTradeGoods.setPlatGoodsID("1");
        apiTradeGoods.setTradeGoodsName("测试商品");
        apiTradeGoods.setGoodsCount(new BigDecimal("1"));
        apiTradeGoods.setbSend(2); // SUCCESS
        apiTradeGoodsList.add(apiTradeGoods);
        dbOrder.setApiTradeGoodsList(apiTradeGoodsList);

        return dbOrder;
    }

    /**
     * 创建带有发货状态的测试用数据库订单对象
     *
     * @param allSent 是否全部发货
     * @return 数据库订单对象
     */
    private DbAfterSaleOrderItem createDbOrderWithSendStatus(boolean allSent) {
        DbAfterSaleOrderItem dbOrder = new DbAfterSaleOrderItem();

        // 创建ApiTrade
        ApiTradeDO apiTrade = new ApiTradeDO();
        apiTrade.setBillId(1001);
        apiTrade.setTradeNo("test-trade-no-001");
        apiTrade.setShopId(2001);
        apiTrade.setbSplit(0);
        // 设置发货状态：4=SUCCESS（已发货），1=WAIT_SYNC（待发货）
        apiTrade.setSynStatus(allSent ? 4 : 1);
        dbOrder.setApiTrade(apiTrade);

        // 创建ApiTradeGoods列表
        List<ApiTradeGoodsDO> apiTradeGoodsList = new ArrayList<>();
        ApiTradeGoodsDO apiTradeGoods = new ApiTradeGoodsDO();
        apiTradeGoods.setRecId(3001);
        apiTradeGoods.setGoodsId(1);
        apiTradeGoods.setPlatGoodsID("1");
        apiTradeGoods.setTradeGoodsName("测试商品");
        apiTradeGoods.setGoodsCount(new BigDecimal("1"));
        // 设置商品发货状态：2=SUCCESS（已发货），1=WAIT_SYNC（待发货）
        apiTradeGoods.setbSend(allSent ? 2 : 1);
        apiTradeGoodsList.add(apiTradeGoods);
        dbOrder.setApiTradeGoodsList(apiTradeGoodsList);

        return dbOrder;
    }
    //endregion
}
