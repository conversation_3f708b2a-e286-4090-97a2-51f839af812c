package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderExtItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.BaseSaveExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.*;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import static com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum.BUSINESS_KuaiShouShop;

/**
 * 拼多多 - 保存换货单处理器
 *
 * <AUTHOR>
 * @date 2025/5/8 下午4:50
 */
public class KuaiShouShopSaveExchangeOrderProcessor extends BaseSaveExchangeOrderProcessor {
    //region 常量
    /**
     * 拼多多密文类型（和下载普通订单保持一致）
     */
    private static final int PDD_ENCRYPT_TYPE = 2;
    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public KuaiShouShopSaveExchangeOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 密文处理
        AfterSaleHandleResult desensitizationResult = processDesensitization(sourceOrder, targetOrder);
        if (desensitizationResult.isFailed()) {
            return desensitizationResult;
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 转换换货商品信息
     *
     * @param sourceOrder   原始售后单数据
     * @param sourceGoods   原始售后换货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult exchangeGoodsConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        // 供子类重写
        return GoodsConvertHandleResult.success();
    }

    //region 私有方法

    /**
     * 密文处理
     * <p>XQ202404160106</p>
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    private AfterSaleHandleResult processDesensitization(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础信息
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderExtItem dbOrderExt = sourceOrder.getDbOrderExt();
        ApiReturnListDO newExchangeOrder = targetOrder.getAfterSaleOrder();
        final String nameSep = "#";
        final String phoneSep = "$";
        final String adrSep = "~";
        final String flag = "ksxd";

        // 密文处理
        // 基础信息处理
        newExchangeOrder.setEncryptType(BUSINESS_KuaiShouShop.getApiPlatType());
        // 获取密文索引
        String receiverNameIndex = AfterSaleCovertUtils.getCiphertextIndex(newExchangeOrder.getChgSndTo(), nameSep, 0);
        String mobileIndex = AfterSaleCovertUtils.getCiphertextIndex(newExchangeOrder.getChgTel(), phoneSep, 0);
        String addressIndex = AfterSaleCovertUtils.getCiphertextIndex(newExchangeOrder.getChangeAdr(), adrSep, 0);
        // 密文格式化
        newExchangeOrder.setChgSndTo(AfterSaleCovertUtils.formatDesensitizationInfo(receiverNameIndex, flag));
        newExchangeOrder.setChgTel(AfterSaleCovertUtils.formatDesensitizationInfo(mobileIndex, flag));
        newExchangeOrder.setChangeAdr(AfterSaleCovertUtils.formatDesensitizationInfo(addressIndex, flag));
        // 密文表处理
        targetOrder.setEncryptTrade(AfterSaleCovertUtils.covertEncryptTrade(context, ployOrder,  dbOrderExt.getApiEncryptTrade(), targetOrder.getEncryptTrade()));

        return AfterSaleHandleResult.success();
    }
    //endregion
}
