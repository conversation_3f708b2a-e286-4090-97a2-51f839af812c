package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 售后单日志仓储 (g_api_return_log)
 *
 * <AUTHOR>
 * @date 2024-07-01 10:08
 */
@Ignore
public class ApiReturnLogMapperTest extends AbstractSpringTest {
    @Autowired
    ApiReturnLogMapper mapper;

    /**
     * 批量新增
     */
    @Test
    public void batchInsertTest(){

        List<ApiReturnLogDO> list = new ArrayList<>();
        ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
        apiReturnLogOne.setBillId(12921);
        apiReturnLogOne.setOperator("[新API]");
        apiReturnLogOne.setLogDetail("测试1");
        apiReturnLogOne.setLogTime(LocalDateTime.now());
        list.add(apiReturnLogOne);
        ApiReturnLogDO apiReturnLogTwo = new ApiReturnLogDO();
        apiReturnLogTwo.setBillId(12921);
        apiReturnLogTwo.setOperator("[新API]");
        apiReturnLogTwo.setLogDetail("测试2");
        apiReturnLogTwo.setLogTime(LocalDateTime.now());
        list.add(apiReturnLogTwo);

        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.batchInsert(list));
    }
}
