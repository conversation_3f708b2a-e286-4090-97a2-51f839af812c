package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund;

import java.util.List;

/**
 * 淘宝物流拦截信息
 *
 * <AUTHOR>
 * @date 2025/4/10 上午11:23
 */
public class TbRefundLogisticsInterceptDto {
    /**
     * 快递是否自动拦截
     */
    private boolean bexInterceptAuto;

    /**
     * 快递拦截出资方
     */
    private String exInterceptInvestor;

    /**
     * 快递拦截状态
     */
    List<TbRefundAutoInterceptStatusDto> interceptStatusList;

    //region get/set
    public boolean getBexInterceptAuto() {
        return bexInterceptAuto;
    }

    public void setBexInterceptAuto(boolean bexInterceptAuto) {
        this.bexInterceptAuto = bexInterceptAuto;
    }

    public String getExInterceptInvestor() {
        return exInterceptInvestor;
    }

    public void setExInterceptInvestor(String exInterceptInvestor) {
        this.exInterceptInvestor = exInterceptInvestor;
    }

    public List<TbRefundAutoInterceptStatusDto> getInterceptStatusList() {
        return interceptStatusList;
    }

    public void setInterceptStatusList(List<TbRefundAutoInterceptStatusDto> interceptStatusList) {
        this.interceptStatusList = interceptStatusList;
    }
    //endregion
}
