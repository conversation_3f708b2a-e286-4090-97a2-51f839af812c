package com.differ.wdgj.api.user.biz.infrastructure.work.mutex;


import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.DistributedLockCache;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkErrorCodeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;

/**
 * 支持工作任务互斥的模式实现
 *
 * <AUTHOR>
 * @date 2024/7/10 9:58
 */
public class DefaultWorkMutex implements WorkMutex {
    /**
     * 创建任务，当已存在互斥的任务未完成时，返回false
     *
     * @param workData    工作数据
     * @param dataOperate 数据操作工具
     * @return 创建结果
     */
    @Override
    public CreateResult createIfNoExists(WorkData<?> workData, WorkDataOperate dataOperate) {
        String mutexKey = getMutexKey(workData);
        // 基于分布式锁创建任务
        CreateResult createResult = DistributedLockCache.singleton().tryLockResult(mutexKey, () ->
                create(workData, dataOperate));
        if (createResult == null) {
            createResult = CreateResult.failResult(WorkErrorCodeEnum.WORK_MUTEX.getCode());
        }
        return createResult;
    }

    /**
     * 获取互斥key
     *
     * @param workData 工作数据
     * @return 互斥key
     */
    protected String getMutexKey(WorkData<?> workData) {
        return String.format("%s_%s_%d", workData.getWorkType(), workData.getMemberName(), workData.getShopId());
    }

    /**
     * 创建任务
     *
     * @param workData    工作数据
     * @param dataOperate 数据操作工具
     * @return 创建结果
     */
    protected CreateResult create(WorkData<?> workData, WorkDataOperate dataOperate) {
        if (!dataOperate.existsUnComplete(workData)) {
            return dataOperate.create(workData);
        }
        return null;
    }
}
