package com.differ.wdgj.api.user.biz.tasks.mq.jmq.core;

import com.differ.jackyun.framework.component.jmq.core.JMQInfo;
import com.differ.jackyun.framework.component.jmq.core.QueueEnabled;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * API MQ 开启
 *
 * <AUTHOR>
 * @date 2022-04-13 17:05
 */
public class ApiMqEnabled implements QueueEnabled {

    protected Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 是否开启生产者
     * <p>
     * 说明：生产者默认开启
     *
     * @param jmsInfo 配置
     * @return 是否开启
     */
    @Override
    public boolean enabledSender(JMQInfo jmsInfo) {
        try {

            // 获取额外属性
            Object param = jmsInfo.getExtraValue("sitesToSend");
            if (param == null) {
                return true;
            }

            // 发送站点
            List<String> sitesToSend = Arrays.asList((String[]) param);
            if (CollectionUtils.isEmpty(sitesToSend)) {
                return true;
            }

            // 配置文件读取当前站点类型
            String serviceCode = SystemAppConfig.get().getSiteTypeEnum().getServiceCode();

            // 是否开启生产者
            return sitesToSend.contains(serviceCode);
        } catch (Exception ex) {
            log.error("判断当前站点是否生产", ex);
            return false;
        }
    }

    /**
     * 是否开启消费者
     * <p>
     * 说明：消费者默认关闭
     *
     * @param jmsInfo 配置
     * @return 是否开启
     */
    @Override
    public boolean enabledReceiver(JMQInfo jmsInfo) {
        try {

            // 获取额外属性
            Object param = jmsInfo.getExtraValue("sitesToReceive");
            if (param == null) {
                return false;
            }

            // 发送站点
            List<String> sitesToReceive = Arrays.asList((String[]) param);
            if (CollectionUtils.isEmpty(sitesToReceive)) {
                return false;
            }

            // 配置文件读取当前站点类型
            String serviceCode = SystemAppConfig.get().getSiteTypeEnum().getServiceCode();

            // 是否开启消费者
            return sitesToReceive.contains(serviceCode);
        } catch (Exception ex) {
            log.error("判断当前站点是否消费", ex);
            return false;
        }
    }
}
