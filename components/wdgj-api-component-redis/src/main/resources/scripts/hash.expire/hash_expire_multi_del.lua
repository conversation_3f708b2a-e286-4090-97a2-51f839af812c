-- 支持过期的hash批量删除
-- User: chuff
-- Date: 2021/11/29 14:25
--

local cacheKey = KEYS[1]

-- 缓存键过期时间
local cacheKeyExpire = tonumber(ARGV[1])
-- 当前时间
local currentTime = tonumber(ARGV[2])

-- 缓存键的key后缀
local cacheKeyTail = math.floor(currentTime / cacheKeyExpire)

local cacheKeyWithTail = cacheKey .. cacheKeyTail
local cacheKeyWithBefore = cacheKey .. (cacheKeyTail - 1)

local len = #ARGV
local ret = 0
for i = 3, len, 1 do
    local count = redis.call("hdel", cacheKeyWithTail, ARGV[i])
    if count == 0 then
        count = redis.call("hdel", cacheKeyWithBefore, ARGV[i])
    end
    ret = ret + count
end
return ret

