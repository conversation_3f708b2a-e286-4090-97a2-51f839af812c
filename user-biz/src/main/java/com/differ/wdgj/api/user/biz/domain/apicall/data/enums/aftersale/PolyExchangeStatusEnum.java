package com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;

/**
 * 菠萝派换货状态
 *
 * <AUTHOR>
 * @date 2024/8/8 上午10:36
 */
public enum PolyExchangeStatusEnum implements CodeEnum {
    /**
     * 买家已经申请换货等待卖家同意
     */
    JH_01("买家已经申请换货等待卖家同意", "JH_01"),

    /**
     * 卖家已经同意换货等待买家换货
     */
    JH_02("卖家已经同意换货等待买家换货", "JH_02"),

    /**
     * 买家已经退货等待卖家确认收货
     */
    JH_03("买家已经退货等待卖家确认收货", "JH_03"),

    /**
     * 卖家拒绝换货
     */
    JH_04("卖家拒绝换货", "JH_04"),

    /**
     * 换货关闭
     */
    JH_05("换货关闭", "JH_05"),

    /**
     * 换货成功
     */
    JH_06("换货成功", "JH_06"),

    /**
     * 等待卖家发出换货商品
     */
    JH_07("等待卖家发出换货商品", "JH_07"),

    /**
     * 待买家收货
     */
    JH_08("待买家收货", "JH_08"),

    /**
     * 退货退款不换货，还未退款
     */
    JH_09("退货退款不换货，还未退款", "JH_09"),

    /**
     * 退货退款不换货，退款成功
     */
    JH_10("退货退款不换货，退款成功", "JH_10"),

    /**
     * 买家已经申请换货等待卖家同意(二次审核)
     */
    JH_11("买家已经申请换货等待卖家同意(二次审核)", "JH_11"),

    /**
     * 卖家拒绝换货(二次拒绝)
     */
    JH_12("卖家拒绝换货(二次拒绝)", "JH_12"),

    /**
     * 其他
     */
    JH_99("其他", "JH_99");

    private final String wdgjValue;
    private final String code;

    /**
     * 构造
     *
     * @param wdgjValue 云端值
     * @param code      菠萝派值
     */
    PolyExchangeStatusEnum(String wdgjValue, String code) {
        this.wdgjValue = wdgjValue;
        this.code = code;
    }

    /**
     * 获取云端值
     *
     * @return 云端值
     */
    public String getWdgjValue() {
        return wdgjValue;
    }

    /**
     * 获取菠萝派值
     *
     * @return 菠萝派值
     */
    @Override
    public String getCode() {
        return code;
    }
    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static PolyExchangeStatusEnum create(String value) {
        return EnumConvertCacheUtil.convert(PolyExchangeStatusEnum.class, value, EnumConvertType.CODE);
    }
}
