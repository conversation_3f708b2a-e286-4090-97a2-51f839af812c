package com.differ.wdgj.api.user.biz.domain.apicall.adapter;


import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallPlatAuthorize;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopAuthDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.auth.ShopAuthCustomParam;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.cryptography.WdgjCryptography;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 适配器 - 平台授权
 *
 * <AUTHOR>
 * @date 2022-03-09 19:25
 */
public class PlatAuthorizeAdapter {

    // region 公共方法

    /**
     * 获取平台授权信息
     *
     * @param apiType    接口类型
     * @param plat       平台值
     * @param memberName 会员名
     * @param shopId     店铺Id
     * @return 平台授权信息
     */
    public ApiCallPlatAuthorize getPlatAuthorize(PolyAPITypeEnum apiType, PolyPlatEnum plat, String memberName, int shopId) {
        // AppKey & AppSecret
        ApiCallPlatAuthorize appKeyAndSecret;

        // 菠萝派商城
        if (PolyPlatEnum.BUSINESS_PolyMall.equals(plat) && !PolyAPITypeEnum.COMMOM_BUSINESSPLATFEATURES.equals(apiType)) {
            appKeyAndSecret = this.getAppInfoByPoly(memberName, shopId);
        }
        // 默认
        else {
            appKeyAndSecret = this.getAppInfoByDefault();
        }

        return appKeyAndSecret;
    }

    // endregion

    // region 私有方法

    /**
     * 获取AppKey和AppSecret - 菠萝派商城
     *
     * @param memberName 会员名
     * @param shopId     店铺Id
     * @return 结果
     */
    private ApiCallPlatAuthorize getAppInfoByPoly(String memberName, Integer shopId) {
        // 校验会员名和店铺Id
        if (StringUtils.isEmpty(memberName) || shopId == null) {
            return null;
        }

        // 获取店铺授权信息
        ApiShopAuthDto shopInfo = ShopInfoUtils.singleAuthByOutShopId(memberName, shopId);
        if (shopInfo == null) {
            return null;
        }

        // 解析自用型参数
        return this.resolvePolySelfUseAuth(shopInfo);
    }

    /**
     * 解析菠萝派商城自用型参数
     *
     * @param shopInfo 店铺信息
     * @return AppKey & AppSecret
     */
    private ApiCallPlatAuthorize resolvePolySelfUseAuth(ApiShopAuthDto shopInfo) {
        // 自用型参数解析
        List<ShopAuthCustomParam> shopAuthCustomParams = ShopInfoUtils.DeserializationAuthCustomParams(shopInfo.getCustomParms());
        // 解密
        shopAuthCustomParams.forEach(x -> x.setValue(WdgjCryptography.singleton().decrypt(x.getValue())));

        // AppKey
        ShopAuthCustomParam appKeyItem = shopAuthCustomParams.stream().filter(x -> StringUtils.equals("AppKey", x.geteKey())).findFirst().orElse(null);
        if(appKeyItem == null){
            return null;
        }
        // AppSecret
        ShopAuthCustomParam appSecretItem = shopAuthCustomParams.stream().filter(x -> StringUtils.equals("AppSecret", x.geteKey())).findFirst().orElse(null);
        if(appSecretItem == null){
            return null;
        }
        // token
        ShopAuthCustomParam sessionKeyItem = shopAuthCustomParams.stream().filter(x -> StringUtils.equals("SessionKey", x.geteKey())).findFirst().orElse(null);
        if(sessionKeyItem == null){
            return null;
        }

        return new ApiCallPlatAuthorize(sessionKeyItem.getValue(), appKeyItem.getValue(), appSecretItem.getValue());
    }

    /**
     * 获取AppKey和AppSecret - 默认
     *
     * @return 结果
     */
    private ApiCallPlatAuthorize getAppInfoByDefault() {
        String appKey = ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.API_PolyAPI_AppKey);
        String appSecret = ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.API_PolyAPI_AppSecret);
        return new ApiCallPlatAuthorize(StringUtils.EMPTY, appKey, appSecret);
    }

    // endregion
}
