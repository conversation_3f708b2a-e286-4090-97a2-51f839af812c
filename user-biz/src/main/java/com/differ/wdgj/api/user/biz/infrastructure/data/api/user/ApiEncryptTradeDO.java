package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.TradeEncryptTypeEnum;

import java.time.LocalDateTime;

/**
 * 原始密文表[g_api_encrypttradelist]
 *
 * <AUTHOR>
 * @date 2024-07-01 10:48
 */
public class ApiEncryptTradeDO {
    /**
     * 获取或设置 Id
     */
    private Integer id;

    /**
     * 获取或设置 订单密文类型
     *
     * @see TradeEncryptTypeEnum
     */
    private Integer type;

    /**
     * 获取或设置 原始单号
     */
    private String tradeNo;

    /**
     * 获取或设置 店铺id
     */
    private Integer shopId;

    /**
     * 获取或设置 买家账号
     */
    private String customerAccount;

    /**
     * 获取或设置 收货人
     */
    private String receiverName;

    /**
     * 获取或设置 手机
     */
    private String mobile;

    /**
     * 获取或设置 电话
     */
    private String phone;

    /**
     * 获取或设置 省
     */
    private String province;

    /**
     * 获取或设置 市
     */
    private String city;

    /**
     * 获取或设置 区
     */
    private String area;

    /**
     * 获取或设置 镇
     */
    private String town;

    /**
     * 获取或设置 详细地址
     */
    private String address;

    /**
     * 获取或设置 详细地址
     */
    private String addressTwo;

    /**
     * 获取或设置 发票抬头
     */
    private String invoiceTitle;

    /**
     * 获取或设置 税号（纳税人识别号）
     */
    private String invoiceCode;

    /**
     * 获取或设置 发票联系人电话
     */
    private String invoicePhone;

    /**
     * 获取或设置 增票注册电话
     */
    private String invoiceregPhone;

    /**
     * 获取或设置 增票银行账户
     */
    private String invoiceBankAccount;

    /**
     * 获取或设置 增票收票地址
     */
    private String invoiceUserAddress;

    /**
     * 获取或设置 增票收票电话
     */
    private String invoiceUserPhone;

    /**
     * 获取或设置 支付单号
     */
    private String payNo;

    /**
     * 获取或设置 证件姓名
     */
    private String idCardName;

    /**
     * 获取或设置 证件号码
     */
    private String idCardNo;

    /**
     * 获取或设置 支付申报订单号
     */
    private String innerTransactionId;

    /**
     * 获取或设置 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 获取或设置 最后更新时间
     */
    private LocalDateTime gmtModified;

    /**
     * 获取或设置 店铺token
     */
    private String polyApiToken;

    /**
     * 获取或设置 不包含省市区的地址（拼多多）
     */
    private String receiverAddress;

    /**
     * 获取或设置 自提信息
     */
    private String selffetchInfo;

    /**
     * 获取或设置 平台店铺Id
     */
    private String platShopId;

    /**
     * 获取或设置 订单收件人信息
     */
    private String receiverMaskId;

    /**
     * 获取或设置 门店预约人加密ID
     */
    private String storeConsigneeCID;

    /**
     * 获取或设置 发票收件人加密ID
     */
    private String invoiceReceiverCID;

    /**
     * 获取或设置 收件人
     */
    private String email;

    // Getters and setters

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getCustomerAccount() {
        return customerAccount;
    }

    public void setCustomerAccount(String customerAccount) {
        this.customerAccount = customerAccount;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoicePhone() {
        return invoicePhone;
    }

    public void setInvoicePhone(String invoicePhone) {
        this.invoicePhone = invoicePhone;
    }

    public String getInvoiceregPhone() {
        return invoiceregPhone;
    }

    public void setInvoiceregPhone(String invoiceregPhone) {
        this.invoiceregPhone = invoiceregPhone;
    }

    public String getInvoiceBankAccount() {
        return invoiceBankAccount;
    }

    public void setInvoiceBankAccount(String invoiceBankAccount) {
        this.invoiceBankAccount = invoiceBankAccount;
    }

    public String getInvoiceUserAddress() {
        return invoiceUserAddress;
    }

    public void setInvoiceUserAddress(String invoiceUserAddress) {
        this.invoiceUserAddress = invoiceUserAddress;
    }

    public String getInvoiceUserPhone() {
        return invoiceUserPhone;
    }

    public void setInvoiceUserPhone(String invoiceUserPhone) {
        this.invoiceUserPhone = invoiceUserPhone;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getIdCardName() {
        return idCardName;
    }

    public void setIdCardName(String idCardName) {
        this.idCardName = idCardName;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getInnerTransactionId() {
        return innerTransactionId;
    }

    public void setInnerTransactionId(String innerTransactionId) {
        this.innerTransactionId = innerTransactionId;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getPolyApiToken() {
        return polyApiToken;
    }

    public void setPolyApiToken(String polyApiToken) {
        this.polyApiToken = polyApiToken;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getSelffetchInfo() {
        return selffetchInfo;
    }

    public void setSelffetchInfo(String selffetchInfo) {
        this.selffetchInfo = selffetchInfo;
    }

    public String getPlatShopId() {
        return platShopId;
    }

    public void setPlatShopId(String platShopId) {
        this.platShopId = platShopId;
    }

    public String getReceiverMaskId() {
        return receiverMaskId;
    }

    public void setReceiverMaskId(String receiverMaskId) {
        this.receiverMaskId = receiverMaskId;
    }

    public String getStoreConsigneeCID() {
        return storeConsigneeCID;
    }

    public void setStoreConsigneeCID(String storeConsigneeCID) {
        this.storeConsigneeCID = storeConsigneeCID;
    }

    public String getInvoiceReceiverCID() {
        return invoiceReceiverCID;
    }

    public void setInvoiceReceiverCID(String invoiceReceiverCID) {
        this.invoiceReceiverCID = invoiceReceiverCID;
    }

    public String getAddressTwo() {
        return addressTwo;
    }

    public void setAddressTwo(String addressTwo) {
        this.addressTwo = addressTwo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
