package com.differ.wdgj.api.user.biz.domain.order.common.data;

import java.math.BigDecimal;

/**
 * 订单商品发货同步结果
 *
 * <AUTHOR>
 * @date 2025/6/19 15:04
 */
public class OrderGoodsSendInfoDto {
    /**
     * 自增主键
     */
    private int recId;

    /**
     * 是否已经全部发货
     */
    private boolean bAllSend;

    /**
     * 待发货同步数量
     */
    private BigDecimal waitSendCount;

    /**
     * 发货同步成功数量
     */
    private BigDecimal successSendCount;

    /**
     * 发货失败成功数量
     */
    private BigDecimal failSendCount;

    //region get/set
    public int getRecId() {
        return recId;
    }

    public void setRecId(int recId) {
        this.recId = recId;
    }

    public boolean isbAllSend() {
        return bAllSend;
    }

    public void setbAllSend(boolean bAllSend) {
        this.bAllSend = bAllSend;
    }

    public BigDecimal getWaitSendCount() {
        return waitSendCount;
    }

    public void setWaitSendCount(BigDecimal waitSendCount) {
        this.waitSendCount = waitSendCount;
    }

    public BigDecimal getSuccessSendCount() {
        return successSendCount;
    }

    public void setSuccessSendCount(BigDecimal successSendCount) {
        this.successSendCount = successSendCount;
    }

    public BigDecimal getFailSendCount() {
        return failSendCount;
    }

    public void setFailSendCount(BigDecimal failSendCount) {
        this.failSendCount = failSendCount;
    }
    //endregion
}
