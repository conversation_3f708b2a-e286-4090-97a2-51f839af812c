package com.differ.wdgj.api.user.biz.infrastructure.work.operate;


import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;

/**
 * 工作任务的操作
 *
 * <AUTHOR>
 * @date 2024/6/24 10:41
 */
public interface WorkDetailOperate<S extends SubTask> {

    /**
     * 设置进度值
     *
     * @param value 进度值
     */
    void setProgress(int value);

    /**
     * 增量更新进度值
     *
     * @param value 进度值
     */
    void increProgress(int value);

    /**
     * 更新子任务信息和动态状态数据
     *
     * @param subTask 子任务
     */
    void updateSubTask(S subTask);

    /**
     * 更新子任务信息和动态状态数据
     *
     * @param subTask            子任务
     * @param increProgressValue 动态状态数据
     */
    void updateSubTask(S subTask, int increProgressValue);

    /**
     * 初始化工作任务为执行状态
     *
     * @param member 会员名
     * @param taskId 任务id
     */
    void initToExecute(String member, String taskId);

    /**
     * 完成工作任务
     *
     * @param member     会员名
     * @param taskId     任务id
     * @param workResult 结果
     */
    void complete(String member, String taskId, WorkResult workResult);

    /**
     * 获取子任务的动态状态数据
     *
     * @param subTypeKey 子任务标识
     * @return 子任务
     */
    S getSubTaskBreakData(String subTypeKey, Class<? extends S> clazz);
}
