package com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.*;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 平台业务特性 - 配置约束 - 特殊配置键模式枚举
 *
 * <AUTHOR>
 * @date 2024-06-24 12:04
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum PlatBizFeatureRestraintKeyModeEnum implements ValueEnum {
    /**
     * 单入参模式
     */
    SINGLE("单入参模式", 1),


    ;

    /**
     * 分类名称
     */
    private final String name;
    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造方法
     *
     * @param name  名称
     * @param value 值
     */
    PlatBizFeatureRestraintKeyModeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 获取枚举值
     *
     * @return 结果
     */
    @Override
    @JsonValue
    public Integer getValue() {
        return value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    @JSONCreator
    public static PlatBizFeatureTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, PlatBizFeatureTypeEnum.class, EnumConvertType.VALUE);
    }
}
