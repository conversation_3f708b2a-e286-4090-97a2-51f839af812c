package com.differ.wdgj.api.user.biz.domain.stock.trigger.operation;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.StockSyncResponseOperation;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 库存同步响应操作
 * <AUTHOR>
 * @date 2024-03-18 14:04
 */
@Ignore
public class StockSyncResponseOperationTest {

    /**
     * 构建库存同步结果
     */
    @Test
    public void buildStockSyncResultTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockSyncResponseOperationTest\\StockSyncContext.json")));
        String str2 = new String(Files.readAllBytes(Paths.get("D:\\esapi-java\\管家java测试数据\\StockSyncResponseOperationTest\\StockSyncResultComposite.json")));
        StockSyncContext context = JsonUtils.deJson(str, StockSyncContext.class);
        StockSyncResultComposite resultComposite = JsonUtils.deJson(str2, StockSyncResultComposite.class);
        Map<MatchIdEnhance, StockSyncResultComposite> responseComposites = new HashMap<>();
        responseComposites.put(MatchIdEnhance.convert(resultComposite.getMatchEnhance()), resultComposite);

        StockSyncResponseOperation operation = new StockSyncResponseOperation(context, new BaseSyncStockProcessor(context));
        operation.buildStockSyncResult(responseComposites);
    }
}
