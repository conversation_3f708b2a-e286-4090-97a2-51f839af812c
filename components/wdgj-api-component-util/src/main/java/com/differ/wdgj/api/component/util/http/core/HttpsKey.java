package com.differ.wdgj.api.component.util.http.core;

import java.util.Objects;

/**
 * RestTemplate的工厂类型关键字
 *
 * <AUTHOR>
 * @date 2024/8/13 16:13
 */
public class HttpsKey {

    /**
     * TLS 安全传输层协议版本
     */
    private HttpTlsEnum tslEnum;

    /**
     * 超时时间
     */
    private int timeOut;

    public HttpsKey(HttpTlsEnum tslEnum, int timeOut) {
        this.tslEnum = tslEnum;
        this.timeOut = timeOut;
    }

    public HttpTlsEnum getTslEnum() {
        return tslEnum;
    }

    public int getTimeOut() {
        return timeOut;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HttpsKey httpsKey = (HttpsKey) o;
        return timeOut == httpsKey.timeOut && tslEnum == httpsKey.tslEnum;
    }

    @Override
    public int hashCode() {
        return Objects.hash(tslEnum, timeOut);
    }
}
