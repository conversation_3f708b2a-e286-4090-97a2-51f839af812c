//package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.items;
//
//import com.differ.newapi.base.infrastructure.component.cache.ICacheDataItem;
//import com.differ.newapi.proxy.business.data.pojo.esapi.GloServer;
//
///**
// * 缓存项
// * <AUTHOR>
// * @date 2020-05-14 15:25
// */
//public class ServerCacheItem extends GloServer implements ICacheDataItem {
//
//    @Override
//    public String getDataVersion() {
//        return this.getDataversion().toString();
//    }
//
//    @Override
//    public String getHashKey() {
//        return this.getServerip();
//    }
//}
