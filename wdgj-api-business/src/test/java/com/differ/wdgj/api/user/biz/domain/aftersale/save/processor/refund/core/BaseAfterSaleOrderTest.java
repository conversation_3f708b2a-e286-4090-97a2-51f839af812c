package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.DownloadOrderShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.DownloadOrderShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;

import java.time.LocalDateTime;

/**
 * 售后转换逻辑单元测试的公共方法
 *
 * <AUTHOR>
 * @date 2024/8/12 下午3:23
 */
@Ignore
public abstract class BaseAfterSaleOrderTest extends AbstractSpringTest {
    /**
     * 创建上下文
     *
     * @param memberName 会员名
     * @param outShopId  外部店铺Id
     * @return 上下文
     */
    protected AfterSaleSaveContext getContext(String memberName, int outShopId) {
        AfterSalesShopConfig afterSalesConfig = AfterSalesShopConfigUtils.singleByShopId(memberName, outShopId);
        DownloadOrderShopConfig downloadOrderConfig = DownloadOrderShopConfigUtils.singleByShopId(memberName, outShopId);
        ApiShopBaseDto apiShopBase = ShopInfoUtils.singleByOutShopId(memberName, outShopId);

        AfterSaleSaveContext context = new AfterSaleSaveContext();
        context.setMemberName(memberName);
        context.setShopId(outShopId);
        context.setPlat(apiShopBase.getPlat());
        context.setOrderTriggerType(OrderTriggerTypeEnum.MANUAL);
        context.setOperatorName(StringUtils.EMPTY);
        context.setWriteBusinessLog(false);
        context.setPolyApiRequestId("678209738626042881P3114M0232V13E216");
        context.setLoadTime(LocalDateTime.now());
        context.setApiShopBaseInfo(apiShopBase);
        context.setAfterSalesShopConfig(afterSalesConfig);
        context.setDownLoadOrderShopConfig(downloadOrderConfig);

        return context;
    }
}
