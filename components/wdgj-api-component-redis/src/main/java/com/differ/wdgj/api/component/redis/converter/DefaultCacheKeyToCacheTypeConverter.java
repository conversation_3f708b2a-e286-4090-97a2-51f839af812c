package com.differ.wdgj.api.component.redis.converter;

/**
 * 默认：缓存键到缓存类型的转换器
 * <AUTHOR>
 * @date 2020/12/14 14:52
 */
public class DefaultCacheKeyToCacheTypeConverter implements CacheKeyToCacheTypeConverter{

    /**
     * 默认类型
     */
    public static final String DEFAULT_TYPE = "default_template";

    @Override
    public String defaultType() {
        return DEFAULT_TYPE;
    }

    @Override
    public String toCacheType(String cacheKey) {
        return DEFAULT_TYPE;
    }
}
