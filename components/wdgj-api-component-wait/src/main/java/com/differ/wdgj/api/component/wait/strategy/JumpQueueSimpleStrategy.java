package com.differ.wdgj.api.component.wait.strategy;

import com.differ.wdgj.api.component.wait.AbstractWaitEntity;
import com.differ.wdgj.api.component.wait.data.JumpQueueWaitEntity;
import com.differ.wdgj.api.component.wait.data.WaitStatusEnum;
import com.differ.wdgj.api.component.wait.impl.WaitRedisScripter;

import java.util.Arrays;
import java.util.List;

/**
 * @Description 支持插队的排队
 * <AUTHOR>
 * @Date 2021/12/29 10:39
 */
public class JumpQueueSimpleStrategy extends SimpleStrategy {

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return 排队状态 {@link WaitStatusEnum}
     */
    @Override
    public WaitStatusEnum putAndGetStatus(AbstractWaitEntity entity) {
        // 1.是否进入排队
        boolean jumpQueue = false;
        if (entity instanceof JumpQueueWaitEntity) {
            jumpQueue = ((JumpQueueWaitEntity) entity).isJumpQueue();
        }
        WaitStatusEnum waitStatus = isWaitAndPut(entity.getWaitUniqueId(), entity.getDataString(), jumpQueue);
        this.writeDebug("[{}-排队添加]{}:{}{}", this.waitContext.getWaitType(), waitStatus.getDesc(), entity.getWaitUniqueId(), entity.getDataString());
        if (WaitStatusEnum.WAIT_ADD == waitStatus || WaitStatusEnum.WAIT_EXISTS == waitStatus) {
            // 保守模式时,再发起一次事件
            if (this.waitContext.isInsureMode()) {
                this.publishPullNext();
            }
            return waitStatus;
        }

        if (WaitStatusEnum.EXEC_EXISTS == waitStatus) {
            return waitStatus;
        }
        // 2.直接执行任务
        this.exec(entity.getWaitUniqueId(), entity.getDataString(), false);
        this.writeDebug("[{}-排队添加]直接执行完成:{}{}", this.waitContext.getWaitType(), entity.getWaitUniqueId(), entity.getDataString());
        // 3.同步任务时,完成后触发下一个MQ事件
        if (!this.waitContext.isAsyncCallbackResult()) {
            this.callbackResult(entity.getWaitUniqueId());
            this.writeDebug("[{}-排队添加]同步任务直接执行完成后触发下一个:{}{}", this.waitContext.getWaitType(), entity.getWaitUniqueId(), entity.getDataString());
        }
        return WaitStatusEnum.EXEC_ADD;
    }

    protected WaitStatusEnum isWaitAndPut(String uniqueId, String data, boolean jumpQueue) {
        {
            try {
                // 执行中数量的Key
                String execCountKey = getExecCountKey();
                // 任务排队数据
                String contentWaitKey = getContentWaitKey();
                // 任务排队队列
                String contentWaitListKey = getContentWaitListKey();
                // 任务执行中队列
                String executingKey = this.getExecutingKey();

                // 执行中数量上限
                String maxCapacity = String.valueOf(this.getMaxExecCount());
                // 排队任务缓存键过期时间
                String waitDataExpire = String.valueOf(this.waitContext.getWaitDataExpire());
                int executingExpire = this.waitContext.getExecutingExpire();
                String executingKeyTail = String.valueOf(getExecutingKeyTail(executingExpire));
                // 执行超时时间
                String executingExpireStr = String.valueOf(executingExpire);
                this.writeDebug("[{}-添加排队]-排队hash键：{}，执行过期时间：{}", this.waitContext.getWaitType(), uniqueId, executingExpireStr);
                // 设置lua参数,支持集群
                List<String> keys = Arrays.asList(execCountKey, contentWaitKey, contentWaitListKey, executingKey);
                List<String> values = Arrays.asList(maxCapacity, data, uniqueId, waitDataExpire, executingKeyTail, executingExpireStr, jumpQueue ? "1" : "0");
                Object luaResult = multiRedis.executeScript(
                        WaitRedisScripter.singleton().redisScriptIsWaitAndPutJumpQueue(),
                        // Lua脚本中的Key列表
                        keys,
                        // Lua脚本args列表
                        values
                );
                return WaitStatusEnum.convertObjectToEnum(luaResult);
            } catch (Exception ex) {
                log.error(String.format("%s是否要排队执行失败,uniqueId:%s", this.waitContext.getWaitType(), uniqueId), ex);
            }
            // 异常时不排队，直接执行
            return WaitStatusEnum.EXEC_ADD;
        }
    }
}
