package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.NameEnum;

/**
 * 匹配类型
 * 表g_cfg_mappingSet字段MapName
 *
 * <AUTHOR>
 * @date 2024-03-19 10:34
 */
public enum MappingSetTypeEnum implements NameEnum {

    /**
     * 有赞门店
     */
    YouZanShop("YouZanShop", 0),

    /**
     * 京东到家门店
     */
    JDO2OShop("JDO2OShop", 1),

    /**
     * 微盟多门店
     */
    WeimobShop("WeimobShop", 2),

    /**
     * 菠萝派商城多门店
     */
    PolyMallShop("PolyMallShop", 3),

    /**
     * 卖家地址库
     */
    PDDSellerAddress("PDDSellerAddress", 4),

    /**
     * 唯品会JIT仓库类型
     */
    JITWarehouseType("JITWarehouseType", 5),

    /**
     * 小鹅拼拼卖家地址
     */
    XiaoEPinPinSellerAddress("XiaoEPinPinSellerAddress", 6),

    ;

    /**
     * 构造
     * @param mapName 匹配类型名
     * @param value 匹配类型值
     */
    private MappingSetTypeEnum(String mapName, Integer value){
        this.mapName = mapName;
        this.value = value;
    }

    /**
     * 匹配类型名
     */
    private String mapName;

    /**
     * 匹配类型值
     */
    private Integer value;


    /**
     * 获取 匹配类型名
     *
     * @return 匹配类型名
     */
    @Override
    public String getName() {
        return this.mapName;
    }
}
