package com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.impl;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.BaseBizConfigSyncProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.data.BizConfigSyncResult;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.data.ClearShopConfigLocalCacheEntity;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopConfigLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopConfigKey;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 清除内存缓存广播通知处理类
 *
 * <AUTHOR>
 * @date 2025/2/24 下午4:14
 */
public class ClearShopConfigLocalCacheProcessor extends BaseBizConfigSyncProcessor {
    /**
     * 同步配置信息
     *
     * @param bizConfig 业务配置信息
     * @return 同步结果
     */
    @Override
    public BizConfigSyncResult syncConfig(String bizConfig) {
        BizConfigSyncResult result = new BizConfigSyncResult();
        String memberName = "DEFAULT";
        try {
            ClearShopConfigLocalCacheEntity entity = JsonUtils.deJson(bizConfig, ClearShopConfigLocalCacheEntity.class);
            if (entity == null) {
                result.setErrorMsg("业务配置信息解析失败");
                return result;
            }

            if (StringUtils.isAllBlank(entity.getMemberName())) {
                result.setErrorMsg("会员名为空");
                return result;
            }

            memberName = entity.getMemberName();
            int shopId = entity.getShopId();
            if (shopId <= 0) {
                result.setErrorMsg("店铺id值无效");
                return result;
            }

            // 构建需要清除的内存缓存Key列表
            List<ApiShopConfigKey> apiShopConfigKeys = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(entity.getBizTypes())){
                entity.getBizTypes().forEach(bizTypeStr -> {
                    if(bizTypeStr != null){
                        ApiShopConfigBizTypes bizType = ApiShopConfigBizTypes.create(bizTypeStr.toString());
                        ApiShopConfigKey e = ApiShopConfigKey.create(entity.getMemberName(), shopId, bizType);
                        apiShopConfigKeys.add(e);
                    }

                });
            }

            // 设置本地缓存无效
            if(CollectionUtils.isNotEmpty(apiShopConfigKeys)){
                ApiShopConfigLocalCache.singleton().invalidate(apiShopConfigKeys);
            }

            result.setSuccess(true);
        } catch (Exception e) {
            result.setErrorMsg(e.getMessage());
        } finally {
            LogFactory.info("清除内存缓存广播通知处理", memberName, () -> ExtUtils.stringBuilderAppend(String.format("请求信息：%s;结果：%s", bizConfig, JsonUtils.toJson(result))));
        }

        return result;
    }

    /**
     * 构建业务处理实体Json字符串
     *
     * @param memberName 会员名
     * @param shopId     店铺Id
     * @param bizTypes    业务类型
     * @return 业务处理实体Json字符串
     */
    public static String buildBizEntity(String memberName, int shopId, List<ApiShopConfigBizTypes> bizTypes) {
        ClearShopConfigLocalCacheEntity entity = new ClearShopConfigLocalCacheEntity();
        entity.setMemberName(memberName);
        entity.setShopId(shopId);
        entity.setBizTypes(bizTypes.stream().map(ApiShopConfigBizTypes::getValue).collect(Collectors.toList()));
        return JsonUtils.toJson(entity);
    }
}
