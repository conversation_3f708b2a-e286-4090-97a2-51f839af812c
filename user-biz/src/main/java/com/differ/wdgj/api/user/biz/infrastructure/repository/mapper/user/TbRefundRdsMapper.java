package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbRefundRdsDo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 淘宝推送库 - 退货退款单 仓储
 * sys_info.jdp_tb_refund
 *
 * <AUTHOR>
 * @date 2025/4/7 下午2:06
 */
public interface TbRefundRdsMapper extends BasicOperateMapper<TbRefundRdsDo> {
    /**
     * 查询淘宝退货退款单列表
     *
     * @param dtStartTime 开始时间
     * @param dtEndTime   结束时间
     * @param sellNick    昵称
     * @param start       起始下载位置
     * @param pageSize    页大小
     * @return 淘宝退货退款单列表
     */
    List<TbRefundRdsDo> getRDSRefundOrderDataByModified(@Param("dtStartTime") LocalDateTime dtStartTime, @Param("dtEndTime") LocalDateTime dtEndTime, @Param("sellNick") String sellNick, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 查询淘宝退货退款单列表总数
     *
     * @param dtStartTime 开始时间
     * @param dtEndTime   结束时间
     * @param sellNick    昵称
     * @return 淘宝退货退款单列表
     */
    int getRefundsCountByModified(@Param("dtStartTime") LocalDateTime dtStartTime, @Param("dtEndTime") LocalDateTime dtEndTime, @Param("sellNick") String sellNick);

}
