package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueWriteSerializer;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.HandBizTransmitTypeEnum;

/**
 * 业务手动操作转发java mq对象
 *
 * <AUTHOR>
 * @date 2024/10/28 下午5:07
 */
public class HandBizTransmitMqDto {
    /**
     * 会员名
     */
    private String outAccount;

    /**
     * 店铺id
     */
    private int outShopId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 转发类型
     */
    @JSONField(deserializeUsing = EnumCodeValueDeserializer.class, serializeUsing = EnumCodeValueWriteSerializer.class)
    private HandBizTransmitTypeEnum transmitType;

    /**
     * 手动下载业务参数
     */
    private String handBizRequest;

    //region get/set
    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public int getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(int outShopId) {
        this.outShopId = outShopId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public HandBizTransmitTypeEnum getTransmitType() {
        return transmitType;
    }

    public void setTransmitType(HandBizTransmitTypeEnum transmitType) {
        this.transmitType = transmitType;
    }

    public String getHandBizRequest() {
        return handBizRequest;
    }

    public void setHandBizRequest(String handBizRequest) {
        this.handBizRequest = handBizRequest;
    }
    //endregion
}
