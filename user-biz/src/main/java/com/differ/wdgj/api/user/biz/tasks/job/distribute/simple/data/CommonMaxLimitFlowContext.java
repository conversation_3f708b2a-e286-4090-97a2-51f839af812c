package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.data;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 通用执行上限流式任务上下文
 *
 * <AUTHOR>
 * @date 2022/9/6 15:15
 */
public class CommonMaxLimitFlowContext extends UserFlowJobContext {

    /**
     * 已迭代执行次数
     */
    private final AtomicInteger totalTimeHasRun = new AtomicInteger(0);

    /**
     * 已执行的业务数据总数
     */
    private final AtomicInteger totalBizDataHasRun = new AtomicInteger(0);

    /**
     * 最新单次执行业务数据数量
     */
    private int lastOnceRunBizDataNum = 0;

    /**
     * 单次任务执行完成
     *
     * @param lastOnceRunBizDataNum 最新单次执行业务数据数量
     */
    public void onceRunCompleted(int lastOnceRunBizDataNum) {

        // 已迭代执行次数 +1
        this.totalTimeHasRun.incrementAndGet();

        // 已执行的业务数据总数累加
        this.totalBizDataHasRun.addAndGet(lastOnceRunBizDataNum);

        // 最新单次执行业务数据数量更新
        this.lastOnceRunBizDataNum = lastOnceRunBizDataNum;
    }

    // region getter

    public AtomicInteger getTotalTimeHasRun() {
        return totalTimeHasRun;
    }

    public AtomicInteger getTotalBizDataHasRun() {
        return totalBizDataHasRun;
    }

    public int getLastOnceRunBizDataNum() {
        return lastOnceRunBizDataNum;
    }

    // endregion
}
