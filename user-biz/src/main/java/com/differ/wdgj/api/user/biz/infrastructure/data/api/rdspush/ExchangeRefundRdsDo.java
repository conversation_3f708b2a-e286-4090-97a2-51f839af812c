package com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush;

import java.time.LocalDateTime;

/**
 * 淘宝推送库 - 换货单
 * ys_info.jdp_exchange_refund
 *
 * <AUTHOR>
 * @date 2025/4/10 下午9:23
 */
public class ExchangeRefundRdsDo {
    /**
     * 纠纷ID（主键）
     */
    private Long disputeId;

    /**
     * 卖家昵称
     */
    private String sellerNick;

    /**
     * 买家昵称
     */
    private String buyerNick;

    /**
     * 退款状态
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;

    /**
     * JDP哈希值
     */
    private String jdpHashcode;

    /**
     * JDP响应内容
     */
    private String jdpResponse;

    /**
     * JDP创建时间
     */
    private LocalDateTime jdpCreated;

    /**
     * JDP修改时间
     */
    private LocalDateTime jdpModified;

    //region get/set
    public Long getDisputeId() {
        return disputeId;
    }

    public void setDisputeId(Long disputeId) {
        this.disputeId = disputeId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreated() {
        return created;
    }

    public void setCreated(LocalDateTime created) {
        this.created = created;
    }

    public LocalDateTime getModified() {
        return modified;
    }

    public void setModified(LocalDateTime modified) {
        this.modified = modified;
    }

    public String getJdpHashcode() {
        return jdpHashcode;
    }

    public void setJdpHashcode(String jdpHashcode) {
        this.jdpHashcode = jdpHashcode;
    }

    public String getJdpResponse() {
        return jdpResponse;
    }

    public void setJdpResponse(String jdpResponse) {
        this.jdpResponse = jdpResponse;
    }

    public LocalDateTime getJdpCreated() {
        return jdpCreated;
    }

    public void setJdpCreated(LocalDateTime jdpCreated) {
        this.jdpCreated = jdpCreated;
    }

    public LocalDateTime getJdpModified() {
        return jdpModified;
    }

    public void setJdpModified(LocalDateTime jdpModified) {
        this.jdpModified = jdpModified;
    }
    //endregion
}
