<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.CfgMappingSetMapper" >

    <!-- 根据平台店铺Id删除管家匹配信息 -->
    <delete id="deleteByPlatStoreId">
        delete from g_cfg_mappingSet where MapName=#{mapName} And MapSrcID1 in
        <foreach collection="platStoreIds" item="platStoreId" open="(" close=")" separator=",">
            #{platStoreId}
        </foreach>
    </delete>

</mapper>