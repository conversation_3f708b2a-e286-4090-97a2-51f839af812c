package com.differ.wdgj.api.component.redis.config;

import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.redis.MultiRedisCacher;
import com.differ.wdgj.api.component.redis.converter.CacheKeyToCacheTypeConverter;
import com.differ.wdgj.api.component.redis.creator.MultiRedisPropertiesCreator;
import com.differ.wdgj.api.component.redis.creator.RedisConnectionFactoryCreator;
import com.differ.wdgj.api.component.redis.trace.DefaultRedisTraceImpl;
import com.differ.wdgj.api.component.redis.trace.RedisTrace;
import com.differ.wdgj.api.component.redis.util.RedisJsonUtil;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自动装配redis多库
 * <AUTHOR>
 * @date 2020/12/14 11:37
 */
@Configuration
@ConditionalOnBean({MultiRedisPropertiesCreator.class, CacheKeyToCacheTypeConverter.class})
@AutoConfigureAfter(BeforeMultiRedisAutoConfigure.class)
public class MultiRedisAutoConfigure {

    @Bean
    @ConditionalOnMissingBean
    public RedisTrace redisTrace() {
        return new DefaultRedisTraceImpl();
    }

    @Bean
    public MultiRedis multiRedis(CacheKeyToCacheTypeConverter keyToCacheTypeConverter, MultiRedisPropertiesCreator propertiesCreator, RedisTrace redisTrace, RedisJsonUtil jsonUtil) {
        RedisConnectionFactoryCreator redisConnectionFactoryCreator = new RedisConnectionFactoryCreator(propertiesCreator);
        return new MultiRedisCacher(keyToCacheTypeConverter, redisConnectionFactoryCreator, redisTrace, jsonUtil);
    }

}
