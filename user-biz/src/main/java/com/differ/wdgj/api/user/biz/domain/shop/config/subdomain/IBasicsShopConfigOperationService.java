package com.differ.wdgj.api.user.biz.domain.shop.config.subdomain;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;

/**
 * 店铺配置基础操作实现
 *
 * <AUTHOR>
 * @date 2025/2/24 上午10:14
 */
public interface IBasicsShopConfigOperationService {
    /**
     * 更新店铺配置
     *
     * @param shopId      erp店铺id
     * @param bizType     业务类型
     * @param configValue 配置值
     * @return 结果
     */
    boolean updateShopConfig(String memberName, int shopId, ApiShopConfigBizTypes bizType, String configValue);

    /**
     * 更新api店铺Id
     *
     * @param shopId    erp店铺id
     * @param apiShopId api店铺Id
     * @return 结果
     */
    boolean updateApiShopId(String memberName, int shopId, int apiShopId);
}
