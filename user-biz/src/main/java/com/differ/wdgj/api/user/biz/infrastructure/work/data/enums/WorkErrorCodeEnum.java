package com.differ.wdgj.api.user.biz.infrastructure.work.data.enums;


import com.differ.wdgj.api.component.util.enums.CodeEnum;

public enum WorkErrorCodeEnum implements CodeEnum {

    /**
     * 存在互斥任务
     */
    WORK_MUTEX("WORK_MUTEX"),

    /**
     * 工作任务添加失败
     */
    WORK_ADD_FAIL("WORK_ADD_FAIL"),

    /**
     * 错误
     */
    ERROR("ERROR"),
    ;
    // region 构造器

    WorkErrorCodeEnum(String code) {
        this.code = code;
    }

    // endregion

    // region 变量

    /**
     * 错误码
     */
    private String code;

    // endregion

    // region getter

    @Override
    public String getCode() {
        return code;
    }


    // endregion
}
