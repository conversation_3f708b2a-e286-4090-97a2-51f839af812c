package com.differ.wdgj.api.user.biz.infrastructure.work.data.enums;


import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

/**
 * 触发方式（手动，自动）
 *
 * <AUTHOR>
 * @date 2024/6/25 10:50
 */
public enum TriggerTypeEnum implements ValueEnum {

    /**
     * 手动
     */
    HAND(1),

    /**
     * 自动
     */
    AUTO(2),

    /**
     * 消息通知
     */
    MESSAGE_NOTIFICATION(3),
    ;

    /**
     * 类型值
     */
    private final Integer value;


    TriggerTypeEnum(Integer value) {
        this.value = value;
    }


    @Override
    public Integer getValue() {
        return value;
    }

    public Byte getByteValue() {
        return value.byteValue();
    }


    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static TriggerTypeEnum create(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return EnumConvertCacheUtil.convert(value, TriggerTypeEnum.class, EnumConvertType.VALUE);
    }
}
