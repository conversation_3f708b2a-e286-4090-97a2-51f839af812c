<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiTradeListMapper">

    <!--原始单表数据映射-->
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO">
        <id property="billId" column="BillID" jdbcType="INTEGER"/>
        <result property="tradeNo" column="TradeNO" jdbcType="VARCHAR"/>
        <result property="shopId" column="ShopID" jdbcType="INTEGER"/>
        <result property="curStatus" column="CurStatus" jdbcType="INTEGER"/>
        <result property="tradeStatus" column="TradeStatus" jdbcType="VARCHAR"/>
        <result property="tradeTime" column="TradeTime" jdbcType="TIMESTAMP"/>
        <result property="customerID" column="CustomerID" jdbcType="VARCHAR"/>
        <result property="customerName" column="CustomerName" jdbcType="VARCHAR"/>
        <result property="payId" column="PayID" jdbcType="VARCHAR"/>
        <result property="payAccount" column="PayAccount" jdbcType="VARCHAR"/>
        <result property="synStatus" column="SynStatus" jdbcType="INTEGER"/>
        <result property="country" column="Country" jdbcType="VARCHAR"/>
        <result property="province" column="Province" jdbcType="VARCHAR"/>
        <result property="city" column="City" jdbcType="VARCHAR"/>
        <result property="town" column="Town" jdbcType="VARCHAR"/>
        <result property="street" column="Street" jdbcType="VARCHAR"/>
        <result property="adr" column="Adr" jdbcType="VARCHAR"/>
        <result property="zip" column="Zip" jdbcType="VARCHAR"/>
        <result property="phone" column="Phone" jdbcType="VARCHAR"/>
        <result property="mobile" column="Mobile" jdbcType="VARCHAR"/>
        <result property="email" column="Email" jdbcType="VARCHAR"/>
        <result property="tradeId" column="TradeID" jdbcType="INTEGER"/>
        <result property="customerRemark" column="CustomerRemark" jdbcType="VARCHAR"/>
        <result property="remark" column="Remark" jdbcType="VARCHAR"/>
        <result property="postFee" column="PostFee" jdbcType="DECIMAL"/>
        <result property="goodsFee" column="GoodsFee" jdbcType="DECIMAL"/>
        <result property="taxTotal" column="TaxTotal" jdbcType="DECIMAL"/>
        <result property="totalMoney" column="TotalMoney" jdbcType="DECIMAL"/>
        <result property="favourableMoney" column="FavourableMoney" jdbcType="DECIMAL"/>
        <result property="commissionValue" column="CommissionValue" jdbcType="DECIMAL"/>
        <result property="platShareTotal" column="PlatShareTotal" jdbcType="DECIMAL"/>
        <result property="sndStyle" column="SndStyle" jdbcType="VARCHAR"/>
        <result property="chargeType" column="ChargeType" jdbcType="VARCHAR"/>
        <result property="seller" column="Seller" jdbcType="VARCHAR"/>
        <result property="qq" column="QQ" jdbcType="VARCHAR"/>
        <result property="drawBack" column="DrawBack" jdbcType="DECIMAL"/>
        <result property="payTime" column="PayTime" jdbcType="TIMESTAMP"/>
        <result property="getTime" column="GetTime" jdbcType="TIMESTAMP"/>
        <result property="dTimeStamp" column="DTimeStamp" jdbcType="FLOAT"/>
        <result property="bFx" column="BFX" jdbcType="BIT"/>
        <result property="invoiceKind" column="InvoiceKind" jdbcType="TINYINT"/>
        <result property="invoiceTitle" column="InvoiceTitle" jdbcType="VARCHAR"/>
        <result property="fromID" column="FromID" jdbcType="INTEGER"/>
        <result property="sndTime" column="SndTime" jdbcType="TIMESTAMP"/>
        <result property="codServiceFee" column="CODServiceFee" jdbcType="DECIMAL"/>
        <result property="synCause" column="SynCause" jdbcType="VARCHAR"/>
        <result property="bDelayForRemark" column="BDelayForRemark" jdbcType="TINYINT"/>
        <result property="bDelayForOrder" column="BDelayForOrder" jdbcType="TINYINT"/>
        <result property="summary" column="Summary" jdbcType="VARCHAR"/>
        <result property="tradeType" column="TradeType" jdbcType="VARCHAR"/>
        <result property="apiType" column="APIType" jdbcType="INTEGER"/>
        <result property="fxTId" column="FXTid" jdbcType="VARCHAR"/>
        <result property="remind" column="Remind" jdbcType="TINYINT"/>
        <result property="bwlb" column="Bwlb" jdbcType="TINYINT"/>
        <result property="bbrandSale" column="Bbrand_sale" jdbcType="TINYINT"/>
        <result property="bSoldOver" column="BSoldOver" jdbcType="INTEGER"/>
        <result property="bTBJHS" column="BTBJHS" jdbcType="TINYINT"/>
        <result property="apiSendTime" column="APISendTime" jdbcType="TIMESTAMP"/>
        <result property="tbFlag" column="TBFLAG" jdbcType="VARCHAR"/>
        <result property="couponPrice" column="Coupon_price" jdbcType="DECIMAL"/>
        <result property="flagID" column="FlagID" jdbcType="INTEGER"/>
        <result property="bSplit" column="BSplit" jdbcType="INTEGER"/>
        <result property="postCode" column="PostCode" jdbcType="VARCHAR"/>
        <result property="currencyType" column="Currency_type" jdbcType="VARCHAR"/>
        <result property="cardType" column="CardType" jdbcType="INTEGER"/>
        <result property="cardName" column="CardName" jdbcType="VARCHAR"/>
        <result property="idCard" column="IDCard" jdbcType="VARCHAR"/>
        <result property="sndDeadLine" column="SndDeadLine" jdbcType="TIMESTAMP"/>
        <result property="payMethod" column="PayMethod" jdbcType="VARCHAR"/>
        <result property="bDeleteGoods" column="BDeleteGoods" jdbcType="TINYINT"/>
        <result property="encryptType" column="EncryptType" jdbcType="INTEGER"/>
        <result property="appendRemark" column="AppendRemark" jdbcType="VARCHAR"/>
        <result property="waitPostTime" column="WaitPostTime" jdbcType="TIMESTAMP"/>
        <result property="receiverMaskId" column="ReceiverMaskID" jdbcType="VARCHAR"/>
        <result property="guid" column="Guid" jdbcType="VARCHAR"/>
        <result property="oaidSourceCode" column="OaidSourceCode" jdbcType="VARCHAR"/>
        <result property="nickUId" column="NickUID" jdbcType="VARCHAR"/>
    </resultMap>

    <!--原始单His表数据映射-->
    <resultMap id="BaseHisResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO">
        <id property="billId" column="BillID" jdbcType="INTEGER"/>
        <result property="tradeNo" column="TradeNO" jdbcType="VARCHAR"/>
        <result property="shopId" column="ShopID" jdbcType="INTEGER"/>
        <result property="curStatus" column="CurStatus" jdbcType="INTEGER"/>
        <result property="tradeStatus" column="TradeStatus" jdbcType="VARCHAR"/>
        <result property="tradeTime" column="TradeTime" jdbcType="TIMESTAMP"/>
        <result property="customerID" column="CustomerID" jdbcType="VARCHAR"/>
        <result property="customerName" column="CustomerName" jdbcType="VARCHAR"/>
        <result property="payId" column="PayID" jdbcType="VARCHAR"/>
        <result property="payAccount" column="PayAccount" jdbcType="VARCHAR"/>
        <result property="synStatus" column="SynStatus" jdbcType="INTEGER"/>
        <result property="country" column="Country" jdbcType="VARCHAR"/>
        <result property="province" column="Province" jdbcType="VARCHAR"/>
        <result property="city" column="City" jdbcType="VARCHAR"/>
        <result property="town" column="Town" jdbcType="VARCHAR"/>
        <result property="adr" column="Adr" jdbcType="VARCHAR"/>
        <result property="zip" column="Zip" jdbcType="VARCHAR"/>
        <result property="phone" column="Phone" jdbcType="VARCHAR"/>
        <result property="mobile" column="Mobile" jdbcType="VARCHAR"/>
        <result property="email" column="Email" jdbcType="VARCHAR"/>
        <result property="tradeId" column="TradeID" jdbcType="INTEGER"/>
        <result property="customerRemark" column="CustomerRemark" jdbcType="VARCHAR"/>
        <result property="remark" column="Remark" jdbcType="VARCHAR"/>
        <result property="postFee" column="PostFee" jdbcType="DECIMAL"/>
        <result property="goodsFee" column="GoodsFee" jdbcType="DECIMAL"/>
        <result property="taxTotal" column="TaxTotal" jdbcType="DECIMAL"/>
        <result property="totalMoney" column="TotalMoney" jdbcType="DECIMAL"/>
        <result property="favourableMoney" column="FavourableMoney" jdbcType="DECIMAL"/>
        <result property="commissionValue" column="CommissionValue" jdbcType="DECIMAL"/>
        <result property="platShareTotal" column="PlatShareTotal" jdbcType="DECIMAL"/>
        <result property="sndStyle" column="SndStyle" jdbcType="VARCHAR"/>
        <result property="chargeType" column="ChargeType" jdbcType="VARCHAR"/>
        <result property="seller" column="Seller" jdbcType="VARCHAR"/>
        <result property="qq" column="QQ" jdbcType="VARCHAR"/>
        <result property="drawBack" column="DrawBack" jdbcType="DECIMAL"/>
        <result property="payTime" column="PayTime" jdbcType="TIMESTAMP"/>
        <result property="getTime" column="GetTime" jdbcType="TIMESTAMP"/>
        <result property="dTimeStamp" column="DTimeStamp" jdbcType="FLOAT"/>
        <result property="bFx" column="BFX" jdbcType="BIT"/>
        <result property="invoiceKind" column="InvoiceKind" jdbcType="TINYINT"/>
        <result property="invoiceTitle" column="InvoiceTitle" jdbcType="VARCHAR"/>
        <result property="fromID" column="FromID" jdbcType="INTEGER"/>
        <result property="sndTime" column="SndTime" jdbcType="TIMESTAMP"/>
        <result property="codServiceFee" column="CODServiceFee" jdbcType="DECIMAL"/>
        <result property="synCause" column="SynCause" jdbcType="VARCHAR"/>
        <result property="bDelayForRemark" column="BDelayForRemark" jdbcType="TINYINT"/>
        <result property="bDelayForOrder" column="BDelayForOrder" jdbcType="TINYINT"/>
        <result property="summary" column="Summary" jdbcType="VARCHAR"/>
        <result property="tradeType" column="TradeType" jdbcType="VARCHAR"/>
        <result property="apiType" column="APIType" jdbcType="INTEGER"/>
        <result property="fxTId" column="FXTid" jdbcType="VARCHAR"/>
        <result property="remind" column="Remind" jdbcType="TINYINT"/>
        <result property="bwlb" column="Bwlb" jdbcType="TINYINT"/>
        <result property="bbrandSale" column="Bbrand_sale" jdbcType="TINYINT"/>
        <result property="bSoldOver" column="BSoldOver" jdbcType="INTEGER"/>
        <result property="bTBJHS" column="BTBJHS" jdbcType="TINYINT"/>
        <result property="apiSendTime" column="APISendTime" jdbcType="TIMESTAMP"/>
        <result property="tbFlag" column="TBFLAG" jdbcType="VARCHAR"/>
        <result property="couponPrice" column="Coupon_price" jdbcType="DECIMAL"/>
        <result property="flagID" column="FlagID" jdbcType="INTEGER"/>
        <result property="bSplit" column="BSplit" jdbcType="INTEGER"/>
        <result property="postCode" column="PostCode" jdbcType="VARCHAR"/>
        <result property="currencyType" column="Currency_type" jdbcType="VARCHAR"/>
        <result property="cardType" column="CardType" jdbcType="INTEGER"/>
        <result property="cardName" column="CardName" jdbcType="VARCHAR"/>
        <result property="idCard" column="IDCard" jdbcType="VARCHAR"/>
        <result property="sndDeadLine" column="SndDeadLine" jdbcType="TIMESTAMP"/>
        <result property="payMethod" column="PayMethod" jdbcType="VARCHAR"/>
        <result property="encryptType" column="EncryptType" jdbcType="INTEGER"/>
        <result property="waitPostTime" column="WaitPostTime" jdbcType="TIMESTAMP"/>
        <result property="receiverMaskId" column="ReceiverMaskID" jdbcType="VARCHAR"/>
        <result property="nickUId" column="NickUID" jdbcType="VARCHAR"/>
    </resultMap>

    <!--字段-->
    <sql id="BaseColumnList">
        billid,TradeNO,ShopID,curStatus,TradeStatus,TradeTime,CustomerID,CustomerName,PayID,PayAccount,SynStatus,Country,Province,City,Town,street,Adr,Zip,Phone,Mobile,email,TradeID,CustomerRemark,Remark,PostFee,GoodsFee,TaxTotal,TotalMoney,favourableMoney,CommissionValue,PlatShareTotal,SndStyle,ChargeType,Seller,QQ,DrawBack,PayTime,GetTime,dTimeStamp,bFX,InvoiceKind,InvoiceTitle,FromID,SndTime,CODServiceFee,SynCause,bDelayForRemark,bDelayForOrder,Summary,TradeType,APIType,fxtid,Remind,bwlb,bbrand_sale,bSoldOver,bTBJHS,APISendTime,TBFLAG,coupon_price,FlagID,bSplit,PostCode,currency_type,CardType,CardName,IDCard,SndDeadLine,PayMethod,WaitPostTime,bDeleteGoods,EncryptType,AppendRemark,ReceiverMaskID,bHasSplitSync,oaidSourceCode,NickUID,GUID
    </sql>

    <!-- 根据原始单号批量查询原始单 -->
    <select id="selectByTradeNos" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from g_api_tradeList
        where ShopID = #{shopId} AND TradeNo in
        <foreach collection="tradeNos" item="tradeNo" open="(" close=")" separator=",">
            #{tradeNo}
        </foreach>
    </select>

    <!-- 根据原始单号批量查询完成原始单 -->
    <select id="selectArcByTradeNos" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from g_api_tradeList_arc
        where ShopID = #{shopId} AND TradeNo in
        <foreach collection="tradeNos" item="tradeNo" open="(" close=")" separator=",">
            #{tradeNo}
        </foreach>
    </select>

    <!-- 根据原始单号批量查询归档原始单 -->
    <select id="selectHisByTradeNos" resultMap="BaseHisResultMap">
        select *
        from g_api_tradeList_his
        where ShopID = #{shopId} AND TradeNo in
        <foreach collection="tradeNos" item="tradeNo" open="(" close=")" separator=",">
            #{tradeNo}
        </foreach>
    </select>

</mapper>