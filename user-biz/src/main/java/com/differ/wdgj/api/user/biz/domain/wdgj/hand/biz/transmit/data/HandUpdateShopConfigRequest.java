package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data;

/**
 * 更新店铺配置
 *
 * <AUTHOR>
 * @date 2025/2/21 下午3:23
 */
public class HandUpdateShopConfigRequest {
    /**
     * 会员名
     */
    private String vipUser;

    /**
     * API店铺ID
     */
    private int apiShopId;

    /**
     * 管家店铺ID
     */
    private int shopId;

    /**
     * 店铺配置类型
     */
    private int bizType;

    /**
     * 配置信息（JSON格式）
     */
    private String configInfo;

    //region get/set
    public String getVipUser() {
        return vipUser;
    }

    public void setVipUser(String vipUser) {
        this.vipUser = vipUser;
    }

    public int getApiShopId() {
        return apiShopId;
    }

    public void setApiShopId(int apiShopId) {
        this.apiShopId = apiShopId;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public int getBizType() {
        return bizType;
    }

    public void setBizType(int bizType) {
        this.bizType = bizType;
    }

    public String getConfigInfo() {
        return configInfo;
    }

    public void setConfigInfo(String configInfo) {
        this.configInfo = configInfo;
    }
    //endregion
}
