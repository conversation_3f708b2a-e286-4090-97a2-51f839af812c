package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.strategy.sharding;



import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.SimpleDistributeJobData;

import java.util.List;

/**
 * 分片策略
 *
 * <AUTHOR> wangz
 * @date 2021/11/12 12:02
 */
public interface IJobShardingStrategy {


    /**
     * 获取当前分片任务数据集合
     *
     * @param allInstances    所有实例
     * @param currentInstance 当前实例
     * @param allData         所有任务数据
     * @param <T>             任务数据类型
     * @return 当前分片任务数据集合
     */
    <T extends SimpleDistributeJobData> List<T> getShardingData(List<String> allInstances, String currentInstance, List<T> allData);
}
