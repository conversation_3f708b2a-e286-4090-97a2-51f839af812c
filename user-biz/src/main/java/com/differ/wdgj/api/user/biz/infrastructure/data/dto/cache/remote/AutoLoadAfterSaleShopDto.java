package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueWriteSerializer;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

/**
 * 自动下载售后单店铺
 *
 * <AUTHOR>
 * @date 2024/9/18 下午2:12
 */
public class AutoLoadAfterSaleShopDto {
    /**
     * 店铺Id
     */
    private int shopId;
    /**
     * 店铺平台类型[OpenAPIPlatEnum]
     */
    @JSONField(deserializeUsing = EnumCodeValueDeserializer.class, serializeUsing = EnumCodeValueWriteSerializer.class)
    private PolyPlatEnum plat;

    /**
     * 间隔时间(当定时下载模式为“间隔”时，单位：秒)
     */
    private int interval;

    //region get/set
    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public int getInterval() {
        return interval;
    }

    public void setInterval(int interval) {
        this.interval = interval;
    }
    //endregion
}
