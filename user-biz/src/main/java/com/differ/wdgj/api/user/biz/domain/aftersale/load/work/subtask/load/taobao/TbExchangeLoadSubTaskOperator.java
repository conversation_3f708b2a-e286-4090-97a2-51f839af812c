package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.load.taobao;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.RdsExchangeRefundDomain;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.RdsTbRefundOutRequest;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbAfterSaleRdsPageQueryResponse;

/**
 * 淘宝换货单下载操作类
 *
 * <AUTHOR>
 * @date 2025/4/10 下午9:30
 */
public class TbExchangeLoadSubTaskOperator extends BaseTbLoadSubTaskOperator{
    //region 构造
    public TbExchangeLoadSubTaskOperator(AfterSaleLoadTaskContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取售后单总数
     *
     * @param request 请求参数
     * @return 售后单总数
     */
    @Override
    protected int getAfterSaleCount(RdsTbRefundOutRequest request) {
        RdsExchangeRefundDomain rdsExchangeRefundDomain = new RdsExchangeRefundDomain(context.getMemberName());
        return rdsExchangeRefundDomain.queryPolyRefundOrderCount(request);
    }

    /**
     * 获取售后单列表
     *
     * @param request 请求参数
     * @return 售后单列表
     */
    @Override
    protected TbAfterSaleRdsPageQueryResponse getAfterSaleList(RdsTbRefundOutRequest request) {
        RdsExchangeRefundDomain rdsExchangeRefundDomain = new RdsExchangeRefundDomain(context.getMemberName());
        return rdsExchangeRefundDomain.queryPolyRefundOrder(request);
    }
}
