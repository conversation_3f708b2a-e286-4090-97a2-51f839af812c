package com.differ.wdgj.api.user.biz.domain.stock.trigger.operation;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.StockGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 商品匹配相关操作
 *
 * <AUTHOR>
 * @date 2024-03-18 15:47
 */
@Ignore
public class StockGoodsMatchOperationTest extends AbstractSpringTest {

    /**
     * 批量插入库存同步日志
     */
    @Test
    public void batchInsertStockSyncLogTest(){
        String str = "{\"bSuccess\":1,\"cause\":\"上次同步成功(cxd手动10，库存量：975.0，订购量：50.0，待发货量：5.0，批次库存：920.0)[583256685390138368P257M0226E11]\",\"id\":4459,\"modifiedDate\":null,\"num\":10,\"numiid\":\"340\",\"recId\":0,\"skuId\":\"340001\"}";
        ApiPlatSysHisDO result = JsonUtils.deJson(str, ApiPlatSysHisDO.class);
        StockGoodsMatchOperation.singleton().batchInsertStockSyncLog("api2017", Collections.singletonList(result));
    }

    /**
     *
     */
    @Test
    public void batchSaveSyncStockResultTest(){
        String str = "{\"apiSysMatchId\":4459,\"isSys\":3,\"synFlag\":0,\"sysCount\":0,\"sysGoodsType\":\"仓库中\",\"sysLog\":\"上次同步成功\"}";
        StockSyncApiSysMatchResult result = JsonUtils.deJson(str, StockSyncApiSysMatchResult.class);
        Set<StockSyncApiSysMatchResult> set = new HashSet<>();
        set.add(result);
        StockGoodsMatchOperation.singleton().batchSaveSyncStockResult("api2017", set);
    }
}
