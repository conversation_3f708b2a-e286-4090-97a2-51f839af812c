package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund;

import java.math.BigDecimal;

/**
 * 菠萝派下载退货退款单-退货退款商品级
 * <AUTHOR>
 * @date 2024-06-06 10:33
 */
public class BusinessGetRefundResponseRefundGoodInfo {
    /**
     * 平台商品ID
     */
    private String platProductId;

    /**
     * skuId
     */
    private String sku;

    /**
     * 外部商家编码
     */
    private String outerId;

    /**
     * 外部SKU编码
     */
    private String outSkuId;

    /**
     * 商品条码
     */
    private String barCode;

    /**
     * sku名称
     */
    private String skuSpec;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 商品价格(单位: 元)
     */
    private BigDecimal price;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 商品数量
     */
    private int productNum;

    /**
     * 商品退货数量
     */
    private int refundProductNum;

    /**
     * 唯品会JIT的PO号
     */
    private String poNo;

    /**
     * 商品类别
     */
    private String productType;

    /**
     * 商品状态
     */
    private String status;

    /**
     * 子订单号
     */
    private String subTradeNo;

    /**
     * 商品退款状态:其他=JH_98,买家未收到货=JH_01,买家已收到货=JH_02,买家已退货=JH_03
     */
    private String refundStatus;

    /**
     * 是否赠品
     */
    private Boolean isGift;

    //region 推送数据扩展字段

    /**
     * 商品拓展字段
     */
    private String goodsOrderAttr;

    //endregion

    /**
     * 仓库编码
     */
    private String whsecode;

    // region get/set
    public String getPlatProductId() {
        return platProductId;
    }

    public void setPlatProductId(String platProductId) {
        this.platProductId = platProductId;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getOutSkuId() {
        return outSkuId;
    }

    public void setOutSkuId(String outSkuId) {
        this.outSkuId = outSkuId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSkuSpec() {
        return skuSpec;
    }

    public void setSkuSpec(String skuSpec) {
        this.skuSpec = skuSpec;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public int getProductNum() {
        return productNum;
    }

    public void setProductNum(int productNum) {
        this.productNum = productNum;
    }

    public int getRefundProductNum() {
        return refundProductNum;
    }

    public void setRefundProductNum(int refundProductNum) {
        this.refundProductNum = refundProductNum;
    }

    public String getPoNo() {
        return poNo;
    }

    public void setPoNo(String poNo) {
        this.poNo = poNo;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSubTradeNo() {
        return subTradeNo;
    }

    public void setSubTradeNo(String subTradeNo) {
        this.subTradeNo = subTradeNo;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getGoodsOrderAttr() {
        return goodsOrderAttr;
    }

    public void setGoodsOrderAttr(String goodsOrderAttr) {
        this.goodsOrderAttr = goodsOrderAttr;
    }

    public String getWhsecode() {
        return whsecode;
    }

    public void setWhsecode(String whsecode) {
        this.whsecode = whsecode;
    }

    public Boolean getIsGift() {
        return isGift;
    }

    public void setIsGift(Boolean isGift) {
        this.isGift = isGift;
    }
    // endregion
}
