package com.differ.wdgj.api.user.biz.infrastructure.work.data;

import com.differ.wdgj.api.component.util.enums.ValueEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作任务状态
 *
 * <AUTHOR>
 * @date 2024/6/26 14:50
 */
public enum WorkStatus implements ValueEnum {

    /**
     * 未开始
     */
    WAIT_RUN(0, "待执行"),

    /**
     * 进行中
     */
    RUNNING(1, "进行中"),

    /**
     * 任务中断
     */
    BREAK(2, "任务中断"),


    /**
     * 已失败
     */
    FAILED(3, "已失败"),

    /**
     * 已完成
     */
    COMPLETED(10, "已完成"),

    ;


    private Integer value;

    private String description;

    WorkStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public Byte getByteValue() {
        return value.byteValue();
    }

    public String getDescription() {
        return this.description;
    }

    /**
     * 获取执行中任务
     * @return 客户角度执行中任务
     */
    public static List<WorkStatus> getInProgressTasks() {
        return Arrays.asList(WorkStatus.RUNNING, WorkStatus.BREAK);
    }

    /**
     * 是否进行中的任务
     *
     * @param taskStatusValue 任务状态值
     * @return true:进行中
     */
    public static boolean isInProgressTask(Byte taskStatusValue) {
        List<WorkStatus> inProgressTasks = getInProgressTasks();

        if (null == taskStatusValue || CollectionUtils.isEmpty(inProgressTasks)) {
            return false;
        }

        List<Byte> inProgressTaskValueList = inProgressTasks.stream().map(WorkStatus::getByteValue).collect(Collectors.toList());
        if (inProgressTaskValueList.contains(taskStatusValue)) {
            return true;
        }

        return false;
    }
}
