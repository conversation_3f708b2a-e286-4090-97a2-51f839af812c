-- 分布式读写锁 - 添加读锁
-- User: wangz
-- Date: 2024/01/23 16:57

-- 锁定键，HASH 类型，例如：myRwLock
local lock_key = KEYS[1];

-- 读锁超时键，STRING 类型，格式：{锁定键}:实例ID:线程ID:read:timeout，例如：{myRwLock}:eee1da1c9c87a1c1:123:read:timeout
local read_timeout_key = KEYS[2];

-- 锁过期时间，单位：毫秒
local lock_expire_time = ARGV[1];

-- 锁名称，格式：实例ID:线程ID，例如：eee1da1c9c87a1c1:123
local lock_field = ARGV[2];

-- 读锁名称，格式：实例ID:线程ID:read，例如：eee1da1c9c87a1c1:123:read
local read_lock_field = lock_field .. ':read';

-- 写锁名称，格式：实例ID:线程ID:write，例如：eee1da1c9c87a1c1:123:write
local write_lock_field = lock_field .. ':write';

-- 获取锁模式
local mode = redis.call('hget', lock_key, 'mode');

-- 锁模式为空，即当前尚未有线程获取锁
if (mode == false) then

    -- 设置锁模式为读锁
    redis.call('hset', lock_key, 'mode', 'read');

    -- 添加读锁，加锁次数为 1
    redis.call('hset', lock_key, read_lock_field, 1);

    -- 添加读锁超时键（加锁次数 1）
    redis.call('set', read_timeout_key .. ':1', 1);

    -- 设置读锁超时键过期时间（加锁次数 1）
    redis.call('pexpire', read_timeout_key .. ':1', lock_expire_time);

    -- 设置锁定键过期时间
    redis.call('pexpire', lock_key, lock_expire_time);

    -- 返回空，表示加锁成功
    return nil;
end

-- 锁模式为读锁，或者锁模式为写锁并且获取写锁为当前线程
if (mode == 'read') or (mode == 'write' and redis.call('hexists', lock_key, write_lock_field) == 1) then

    -- 增加读锁加锁次数，并返回当前值
    local ind = redis.call('hincrby', lock_key, read_lock_field, 1);

    -- 添加锁超时键（加锁次数 ind）
    redis.call('set', read_timeout_key .. ':' .. ind, 1);

    -- 设置读锁超时键过期时间（加锁次数 ind）
    redis.call('pexpire', read_timeout_key .. ':' .. ind, lock_expire_time);

    -- 获取锁定键过期时间
    local remain_time = redis.call('pttl', lock_key);

    -- 锁定键过期时间和入参的过期时间取最大值，设置为锁定键的过期时间
    redis.call('pexpire', lock_key, math.max(remain_time, lock_expire_time));

    -- 返回空，表示加锁成功
    return nil;
end

-- 返回锁定键过期时间，表示加锁失败
return redis.call('pttl', lock_key);