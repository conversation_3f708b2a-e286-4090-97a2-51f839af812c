package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * LoadAfterSaleWorkMutex 单元测试 - 第二批：超时任务检测测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午3:00
 */
public class LoadAfterSaleWorkMutexTimeoutTest extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 测试超时任务检测场景
     * 当配置了超时时间且存在超时未运行任务时，应该创建新任务
     */
    @Test
    public void testCreate_TimeoutTaskExists_ShouldCreateNewTask() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        CreateResult expectedResult = CreateResult.successResult("timeout-task-id");
        
        // 模拟配置值为30分钟超时
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(30);
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 模拟存在超时未运行任务
            Mockito.when(mockDataOperate.existsUnRunTimeout(workData, 30)).thenReturn(true);
            Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNotNull("结果不应为空", result);
            Assert.assertTrue("应该创建成功", result.success());
            Assert.assertEquals("任务ID应该匹配", "timeout-task-id", result.getTaskId());
            
            // 验证交互
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, 30);
            Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
            
            // 验证配置方法被调用
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
        }
    }

    /**
     * 测试超时时间为0的场景
     * 当配置的超时时间为0时，不应该检查超时任务
     */
    @Test
    public void testCreate_TimeoutPeriodZero_ShouldNotCheckTimeout() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置值为0（不超时）
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(0);
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证没有检查超时任务
            Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
            
            // 验证配置方法被调用
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
        }
    }

    /**
     * 测试超时任务不存在的场景
     * 当配置了超时时间但不存在超时未运行任务时，应该返回null
     */
    @Test
    public void testCreate_NoTimeoutTask_ShouldReturnNull() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置值为30分钟超时
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(30);
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 模拟不存在超时未运行任务
            Mockito.when(mockDataOperate.existsUnRunTimeout(workData, 30)).thenReturn(false);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证交互
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, 30);
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
            
            // 验证配置方法被调用
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
        }
    }

    /**
     * 测试不同超时时间值的场景
     * 验证不同的超时时间配置都能正确传递给existsUnRunTimeout方法
     */
    @Test
    public void testCreate_DifferentTimeoutValues() {
        // 测试不同的超时时间值
        int[] timeoutValues = {5, 15, 30, 60, 120};
        
        for (int timeoutValue : timeoutValues) {
            // 重置mock
            Mockito.reset(mockDataOperate);
            
            // 准备测试数据
            WorkData<AfterSaleLoadArgs> workData = createWorkData(
                TriggerTypeEnum.AUTO, 
                AfterSaleLoadTypeEnum.TIME_RANGE
            );
            CreateResult expectedResult = CreateResult.successResult("timeout-task-" + timeoutValue);
            
            // 模拟配置值
            try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
                configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                         .thenReturn(timeoutValue);
                
                // 模拟父类方法返回null（存在未完成任务）
                Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
                
                // 模拟存在超时未运行任务
                Mockito.when(mockDataOperate.existsUnRunTimeout(workData, timeoutValue)).thenReturn(true);
                Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                
                // 执行测试
                CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                
                // 验证结果
                Assert.assertNotNull("超时时间" + timeoutValue + "分钟时结果不应为空", result);
                Assert.assertTrue("应该创建成功", result.success());
                Assert.assertEquals("任务ID应该匹配", "timeout-task-" + timeoutValue, result.getTaskId());
                
                // 验证交互 - 重点验证传递的超时时间参数
                Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, timeoutValue);
                Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                
                // 验证配置方法被调用
                configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
            }
        }
    }

    /**
     * 测试配置方法异常的场景
     * 当配置方法抛出异常时，应该使用默认值0
     */
    @Test
    public void testCreate_ConfigMethodThrowsException_ShouldUseDefaultValue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置方法抛出异常
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenThrow(new RuntimeException("配置获取失败"));
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 执行测试 - 应该抛出异常或者使用默认值
            try {
                CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                // 如果没有抛出异常，验证结果应该为null（使用默认值0）
                Assert.assertNull("当配置方法异常时，应该返回null", result);
            } catch (RuntimeException e) {
                // 如果抛出异常，验证异常信息
                Assert.assertEquals("异常信息应该匹配", "配置获取失败", e.getMessage());
            }
            
            // 验证配置方法被调用
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
        }
    }

    /**
     * 创建测试用的WorkData对象
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            triggerType, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);
        
        return WorkData.of(workContext, args);
    }
}
