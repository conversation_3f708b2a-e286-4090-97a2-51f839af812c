package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2024/7/25 下午12:00
 */
@Ignore
public class BaseSaveRefundOrderProcessorTest extends BaseAfterSaleOrderTest {

    //region 变量
    /**
     * 基础测试数据地址
     */
    private final String basePath = "D:\\esapi-java\\管家java测试数据\\AfterSaleDataOperationTest\\refund\\%s";
    //endregion

    /**
     * 保存售后单完成流程测试
     */
    @Test
    public void saveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "BusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1446);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 得物保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/fMA3J8q 得物-售后菠萝派报文分析
     */
    @Test
    public void dewuSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "DewuBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1446);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 抖音超市保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/MNUXnsl 抖音超市-售后菠萝派报文分析
     */
    @Test
    public void douDianSupermarketSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "DouDianSupermarketBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1360);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 天猫国际直营保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/c4k8RXK 天猫国际直营-售后菠萝派报文分析
     */
    @Test
    public void tmallGJZYSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "TmallGJZYBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1212);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 诚信通保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/0WiqEQC 诚信通-售后菠萝派报文分析
     */
    @Test
    @Ignore
    public void cxtSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "CXTBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 96914);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 阿里健康大药房保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/iUSVr9H 阿里健康大药房-售后菠萝派报文分析
     */
    @Test
    public void aliJianKangDaYaoFangSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "AliJianKangDaYaoFangBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1280);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 视频号小店保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/rXFBTAc 视频号小店-退货退款单菠萝派报文分析！
     */
    @Test
    public void shiPingHaoXiaoDianSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "ShiPingHaoXiaoDianBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1382);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 1688数字小店保存售后单完成流程测试
     */
    @Test
    public void shuZiXiaoDian1688SaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "ShuZiXiaoDian1688BusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1471);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 菠萝派商城保存售后单完成流程测试
     */
    @Test
    public void polyMallSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "PolyMallBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 96933);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 有赞保存售后单完成流程测试
     */
    @Test
    public void youZanSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "YouZanBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1121);
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }
}
