package com.differ.wdgj.api.user.biz.infrastructure.data;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 系统环境枚举
 *
 * <AUTHOR>
 * @since 2020-01-16  16:35
 */
public enum SystemEnvTypeEnum implements ValueEnum {
    /**
     * 开发环境
     */
    DEV(1, "开发环境"),
    /**
     * 开发环境
     */
    MODULE_TEST(2, "集成测试环境"),
    /**
     * 开发环境
     */
    SYSTEM_TEST(3, "系统测试环境"),
    /**
     * 压测环境
     */
    PERF_TEST(4, "压测环境"),
    /**
     * 开发环境
     */
    PROD(9, "正式生产环境"),
    ;

    SystemEnvTypeEnum(int value, String caption) {
        this.value = value;
        this.caption = caption;
    }

    /**
     * 值
     */
    private int value;

    /**
     * 描述
     */
    private String caption;

    /**
     * 获取值。
     *
     * @return 值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.getValue().toString();
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static SystemEnvTypeEnum convert(String value) {
        return EnumConvertCacheUtil.convert(SystemEnvTypeEnum.class, value, EnumConvertType.VALUE);
    }
}


