package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation;

import com.differ.wdgj.api.user.biz.domain.stock.data.*;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.mq.DotNetSyncStockResult;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * dotNet库存同步结果操作类
 *
 * <AUTHOR>
 * @date 2024-03-04 13:38
 */
public class DotNetSyncStockResultOperation {

    /**
     * 转换库存同步菠萝派结果集合
     * @param apiSysMatchList 商品匹配列表
     * @param syncStockResult dotNet库存同步结果
     * @return 结果
     */
    public Map<MatchIdEnhance, StockSyncResultComposite> convertToResponseComposite(
            List<ApiSysMatchDO> apiSysMatchList,
            DotNetSyncStockResult syncStockResult){
        Map<MatchIdEnhance, StockSyncResultComposite> responseComposites = new HashMap<>();
        for (DotNetSyncStockResult.SyncResult syncResult :syncStockResult.getSyncResultData()){
            // 匹配数据
            ApiSysMatchDO goodsMatch = apiSysMatchList.stream().filter(x -> x.getId() == syncResult.getApiSysMatchId()).findFirst().orElse(null);
            if(goodsMatch == null){
                continue;
            }

            // 匹配数据扩展
            String multiSign = getMultiSign(syncResult);
            GoodsMatchEnhance goodsMatchEnhance = GoodsMatchEnhance.create(goodsMatch, multiSign);

            // 构建菠萝派数据
            StockSyncResultComposite responseComposite = new StockSyncResultComposite(goodsMatchEnhance);
            buildPolyGoodsSyncStock(syncResult, responseComposite);

            responseComposites.put(MatchIdEnhance.convert(goodsMatchEnhance), responseComposite);
        }

        return responseComposites;
    }

    /**
     * 构建店铺级上下文
     * @param vipUser 会员名
     * @param syncStockResult dotNet库存同步结果
     * @return 结果
     */
    public StockContentResult<StockSyncContext> convertToContext(String vipUser, DotNetSyncStockResult syncStockResult){

        StockSyncContext context = new StockSyncContext();

        // 获取jsonBack
        DotNetSyncStockResult.SyncResult syncResult = syncStockResult.getSyncResultData().stream().findFirst().orElse(null);
        if(syncResult == null || StringUtils.isEmpty(syncResult.getJsonBackData())){
            return StockContentResult.failed("构建店铺级上下文-dotNet库存同步结果为空");
        }

        // 解析jsonBack
        SyncStockJsonBackData jsonBack = syncResult.getJsonBack();
        if(jsonBack == null){
            return StockContentResult.failed("构建店铺级上下文-解析jsonBack失败");
        }

        // 构建context
        context.setVipUser(vipUser);
        context.setPlat(PolyPlatEnum.create(jsonBack.getBusinessPlat()));
        context.setShopId(jsonBack.getShopId());
        context.setTriggerType(Boolean.TRUE.equals(jsonBack.getHand()) ? StockSyncTriggerTypeEnum.MANUAL_SYNC : StockSyncTriggerTypeEnum.AUTO_SYNC);
        context.setOperatorName(Boolean.TRUE.equals(jsonBack.getHand()) ? jsonBack.getOperatorUser() : "新API");
        context.setShopBase(ShopInfoUtils.singleByOutShopId(vipUser, jsonBack.getShopId()));

        return StockContentResult.success(context);
    }


    //region 私有方法

    /**
     * 获取多仓标识
     * @param syncResult dotNet库存同步结果
     * @return 多仓标识
     */
    private String getMultiSign(DotNetSyncStockResult.SyncResult syncResult){
        // 仓库编码
        if (StringUtils.isNotEmpty(syncResult.getWhseCode())){
            return syncResult.getWhseCode();
        }

        // 门店id
        if (StringUtils.isNotEmpty(syncResult.getPlatStoreId())){
            return syncResult.getPlatStoreId();
        }

        // 常态合作编码
        if (StringUtils.isNotEmpty(syncResult.getVipCooperationNo())){
            return syncResult.getVipCooperationNo();
        }

        return "";
    }

    /**
     * 构建 库存同步菠萝派结果集合
     * @param syncResult dotNet库存同步结果
     */
    private void buildPolyGoodsSyncStock(
            DotNetSyncStockResult.SyncResult syncResult,
            StockSyncResultComposite responseComposite){

        // 请求数据转换
        responseComposite.setPlatRequest(buildPolyGoodsSyncStockRequest(syncResult));

        // 返回数据转换
        responseComposite.setPlatResponse(buildPolyGoodsSyncStockResponse(syncResult));

        // 库存量详情
        if(syncResult.getJsonBack() != null){
            responseComposite.setDetailCount(syncResult.getJsonBack().getDetailCount());
        }
    }

    /**
     * 菠萝派库存同步商品级请求对象
     * @param syncResult dotNet库存同步结果
     * @return 结果
     */
    private BusinessBatchSyncStockRequestGoodInfo buildPolyGoodsSyncStockRequest(DotNetSyncStockResult.SyncResult syncResult){
        BusinessBatchSyncStockRequestGoodInfo polyGoodsSyncStockRequest = new BusinessBatchSyncStockRequestGoodInfo();

        SyncStockJsonBackData jsonBack = syncResult.getJsonBack();
        if(jsonBack != null){

            // 多门店
            polyGoodsSyncStockRequest.setPlatStoreId(jsonBack.getPlatStoreId());
            polyGoodsSyncStockRequest.setStoreType(jsonBack.getStoreType());
            polyGoodsSyncStockRequest.setStoreId(jsonBack.getStoreId());

            // 上下架状态
            polyGoodsSyncStockRequest.setStatus(jsonBack.getStatus());

            // 增量状态
            String syncStockType = Boolean.TRUE.equals(syncResult.getIncreFlag())
                    ? SyncStockModeEnum.Increment.getCode()
                    : SyncStockModeEnum.Full.getCode();
            polyGoodsSyncStockRequest.setSyncStockType(syncStockType);
        }

        return polyGoodsSyncStockRequest;
    }

    /**
     * 生成 菠萝派库存同步商品级返回对象
     * @param syncResult dotNet库存同步结果
     * @return 结果
     */
    private BusinessBatchSyncStockResponseGoodSyncStockResultItem buildPolyGoodsSyncStockResponse(DotNetSyncStockResult.SyncResult syncResult){
        BusinessBatchSyncStockResponseGoodSyncStockResultItem polyGoodsSyncStockResponse = new BusinessBatchSyncStockResponseGoodSyncStockResultItem();
        polyGoodsSyncStockResponse.setPolyApiRequestId(syncResult.getPolyApiRequestId());
        polyGoodsSyncStockResponse.setSuccess(syncResult.getSuccess());
        polyGoodsSyncStockResponse.setSubCode(syncResult.getSubCode());
        polyGoodsSyncStockResponse.setCode(syncResult.getSubCode());
        polyGoodsSyncStockResponse.setMessage(syncResult.getSyncMsg());
        polyGoodsSyncStockResponse.setPlatProductId(syncResult.getPlatProductId());
        polyGoodsSyncStockResponse.setSkuId(syncResult.getPlatSkuId());
        polyGoodsSyncStockResponse.setQuantity(syncResult.getSyncCount());
        polyGoodsSyncStockResponse.setPlatStoreId(syncResult.getPlatStoreId());
        polyGoodsSyncStockResponse.setWhseCode(syncResult.getWhseCode());
        polyGoodsSyncStockResponse.setVipCooperationNo(syncResult.getVipCooperationNo());
        polyGoodsSyncStockResponse.setActivityEndTime(syncResult.getActivityEndTime());

        return polyGoodsSyncStockResponse;
    }

    //endregion
}
