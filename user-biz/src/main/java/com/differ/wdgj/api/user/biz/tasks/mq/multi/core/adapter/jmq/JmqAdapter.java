package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq;

import com.differ.jackyun.framework.component.jmq.core.AbstractJMQSender;
import com.differ.jackyun.framework.component.jmq.core.consumer.JMQMessage;
import com.differ.jackyun.framework.component.jmq.core.consumer.JMQSimpleReceive;
import com.differ.jackyun.framework.component.jmq.core.enums.JMQResult;
import com.differ.wdgj.api.component.util.functional.two.Function;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueResult;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * jmq适配类
 *
 * <AUTHOR>
 * @date 2024/3/14 14:30
 */
public class JmqAdapter extends AbstractJMQSender<String> implements JMQSimpleReceive<String> {

    /**
     * 消息消费的监听方法
     */
    private Function<QueueResult, String, QueueHeader> funListener;

    /**
     * 日志标题
     */
    protected final static String LOG_CAP = "JMQ发送消息";

    /**
     * 信号量
     */
    protected final Semaphore semaphore = new Semaphore(400, true);

    /**
     * 初始化监听器
     *
     * @param funListener
     */
    public void initListener(Function<QueueResult, String, QueueHeader> funListener) {
        this.funListener = funListener;
    }

    /**
     * JMQ消费方法
     *
     * @param message
     * @return
     */
    @Override
    public JMQResult consume(JMQMessage<String> message) {
        if (funListener == null) {
            return JMQResult.RETRY;
        }
        if (message.getBody() == null) {
            // 数据为空，返回ACK
            return JMQResult.ACK;
        }

        QueueHeader header = JmqConvertUtil.convertHeader(message.head());
        QueueResult queueResult = funListener.exec(message.getBody(), header);
        return queueResult.getJmqResult();
    }

    /**
     * 发送消息到JMQ,并受信号量限制
     *
     * @param message
     * @param header
     */
    public void send(String message, QueueHeader header) {
        // 是否申请成功
        boolean acquireSuccess = false;
        try {
            // 申请信号量（上游业务层未做持久化处理，为防止消息丢失，申请失败仍然使其通过，仅记录日志）
            acquireSuccess = this.semaphore.tryAcquire(1000, TimeUnit.MILLISECONDS);
            // 发送消息
            long delay = header.getDelaySeconds();
            if (delay > 0) {
                // 注意，当使用延迟时，JMQ的配置必须设置为延迟队列，否则发送不支持
                this.send(message, delay * 1000, header.toBizMap());
            } else {
                this.send(message, header.toBizMap());
            }
        } catch (Exception e) {
            LogFactory.get(LOG_CAP).error(String.format("队列%s发送消息失败，消息：%s", header.getHandlerCode(), message), e);
        } finally {

            // 释放信号量
            if (acquireSuccess) {
                this.semaphore.release();
            } else {
                LogFactory.get(LOG_CAP).error(String.format("队列%s发送消息时获取信号量失败，消息：%s", header.getHandlerCode(), message));
            }
        }
    }
}
