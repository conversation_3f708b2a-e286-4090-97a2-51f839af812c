package com.differ.wdgj.api.user.biz.domain.aftersale.load;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Collections;

/**
 * 下载售后单 - 外部调用
 *
 * <AUTHOR>
 * @date 2024/10/8 下午6:17
 */
@Ignore
public class LoadAfterSaleFacadeTest extends AbstractSpringTest {
    //region 基础业务测试
    /**
     * 普通自动下载订单
     */
    @Test
    public void normalAutoLoadTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1292);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("新API单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }

    /**
     * 手动按时间
     */
    @Test
    public void timeHandLoadTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1292);
        param.setTriggerType(TriggerTypeEnum.HAND);
        param.setCreator("cxd单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.TIME_RANGE);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.MANUAL);
        afterSaleLoadArgs.setStartTime(LocalDateTime.now().plusHours(-20));
        afterSaleLoadArgs.setEndTime(LocalDateTime.now().plusHours(-10));
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }

    /**
     * 按单号
     */
    @Test
    public void afterSaleNoLoadTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1292);
        param.setTriggerType(TriggerTypeEnum.HAND);
        param.setCreator("cxd单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.AFTER_SALE_NO);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.MANUAL);
        afterSaleLoadArgs.setAfterSaleNos(Collections.singletonList("RE101006567362987776"));
        afterSaleLoadArgs.setRefundType(ApiAfterSaleTypeEnum.REFUND);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }
    //endregion

    //region 平台级测试
    /**
     * 速卖通
     */
    @Test
    public void  AliExpressTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1410);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("cxd单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }
    /**
     * 视频号小店
     */
    @Test
    public void ShiPingHaoXiaoDianTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1382);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("cxd单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }

    /**
     * 京东
     */
    @Test
    public void JDTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1058);
        param.setTriggerType(TriggerTypeEnum.HAND);
        param.setCreator("hyb单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }

    /**
     * 唯品会MP
     */
    @Test
    public void WPHMPTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1032);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("xiongpw单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.isSuccess());
    }

    /**
     * 拼多多
     */
    @Test
    public void PDDTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1300);
        param.setTriggerType(TriggerTypeEnum.HAND);
        param.setCreator("hyb单元测试");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.getMessage(), result.isSuccess());
    }

    /**
     * 放心购、鲁班
     */
    @Test
    public void ByteDanceTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1033);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("xiongpw单元测试 - 放心购");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.isSuccess());
    }

    /**
     * 聚宝赞
     */
    @Test
    public void JvBaoZanTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1118);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("xiongpw单元测试 - 聚宝赞");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.isSuccess());
    }

    /**
     * 快马批发
     */
    @Test
    public void KuaiMaPiFaTest(){
        LoadAfterSaleParam param = new LoadAfterSaleParam();
        param.setMember("api2017");
        param.setShopId(1118);
        param.setTriggerType(TriggerTypeEnum.AUTO);
        param.setCreator("xiongpw单元测试 - 快马批发");
        AfterSaleLoadArgs afterSaleLoadArgs = AfterSaleLoadArgs.buildAutoTimeRangeArgs();
        afterSaleLoadArgs.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        afterSaleLoadArgs.setTriggerType(OrderTriggerTypeEnum.AUTO);
        param.setLoadArgs(afterSaleLoadArgs);

        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult result = facade.createTaskAndExec(param);
        Assert.assertTrue(result.isSuccess());
    }

    //endregion
}
