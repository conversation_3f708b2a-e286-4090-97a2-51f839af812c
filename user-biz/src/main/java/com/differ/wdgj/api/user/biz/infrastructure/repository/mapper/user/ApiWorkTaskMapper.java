package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * API业务工作任务仓储
 *
 * <AUTHOR>
 * @date 2024/9/6 下午3:20
 */
public interface ApiWorkTaskMapper extends BasicOperateMapper<ApiWorkTaskDO> {
    /**
     * 添加队列消息（执行时间取数据库时间）
     *
     * @param dto API业务工作任务
     * @return 影响的行数
     */
    int addExec(@Param("dto") ApiWorkTaskDO dto);

    /**
     * 添加队列消息(无执行时间)
     *
     * @param dto API业务工作任务
     * @return 影响的行数
     */
    int addWait(@Param("dto") ApiWorkTaskDO dto);

    /**
     * 更新任务状态为执行中
     *
     * @param taskId     任务id
     * @param taskStatus 任务状态
     * @return 影响的行数
     */
    int updateToExec(@Param("taskId") String taskId, @Param("taskStatus") Byte taskStatus);

    /**
     * 更新任务状态为完成
     *
     * @param taskId     任务id
     * @param taskStatus 任务状态
     * @param jsonResult 扩展数据
     * @return 影响的行数
     */
    int updateToFinish(@Param("taskId") String taskId, @Param("taskStatus") Byte taskStatus, @Param("jsonResult") String jsonResult);

    /**
     * 是否存在未完成的工作任务
     *
     * @return 影响的行数
     */
    Integer existsUnComplete(@Param("taskType") Integer taskType, @Param("shopId") Integer shopId);

    /**
     * 是否存在未完成的工作任务
     *
     * @return 影响的行数
     */
    Integer existsUnRunTimeout(@Param("taskType") Integer taskType, @Param("shopId") Integer shopId, @Param("timeoutPeriod") int timeoutPeriod);

    /**
     * 查询待检测的未完成任务，状态：执行中和中断
     *
     * @return 未完成任务Id列表
     */
    List<String> listToCheckWork(@Param("taskType") Integer taskType);

    /**
     * 查询店铺级最后一条任务列表
     *
     * @return 店铺级任务列表
     */
    List<ApiWorkTaskDO> queryAutoShopLastFinishTask(@Param("taskType") Integer taskType, @Param("triggerType") Byte triggerType);

    /**
     * 查询店铺级最后一条任务列表
     *
     * @return 店铺级任务列表
     */
    ApiWorkTaskDO singleAutoShopLastFinishTask(@Param("shopId") Integer shopId, @Param("taskType") Integer taskType, @Param("triggerType") Byte triggerType);

    /**
     * 根据taskId查询任务
     *
     * @param taskId 任务id
     * @return 任务
     */
    ApiWorkTaskDO singleByTaskId(@Param("taskId") String taskId);

    // region 删除

    /**
     * 删除 某任务类型X天前的数据，单次上限1000条
     *
     * @param taskType   任务类型
     * @param expiredDay 过期天数(X天前)
     */
    Integer DeleteExpiredData(@Param("taskType") Integer taskType, @Param("expiredDay") Integer expiredDay);

    // endregion

}
