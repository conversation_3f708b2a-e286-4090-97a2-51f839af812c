package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.*;

/**
 * 订单时间类别枚举
 *
 * <AUTHOR>
 * @date 2024-06-24 14:47
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum LoadOrderTimeTypeEnum implements ValueEnum, CodeEnum {
    /**
     * 订单修改时间
     */
    ORDER_MODIFICATION_TIME("订单修改时间", 0, "JH_01"),

    /**
     * 订单创建时间
     */
    ORDER_CREATION_TIME("订单创建时间", 1, "JH_02"),

    /**
     * 订单付款时间
     */
    ORDER_PAYMENT_TIME("订单付款时间", 2, "JH_03"),

    /**
     * 订单发货日期
     */
    ORDER_SHIPMENT_DATE("订单发货日期", 3, "JH_04"),

    /**
     * 订单收货日期
     */
    ORDER_RECEIPT_DATE("订单收货日期", 4, "JH_05"),

    /**
     * 订单待发货时间
     */
    ORDER_PENDING_SHIPMENT_TIME("订单待发货时间", 5, "JH_06"),

    /**
     * 订单取消待处理时间
     */
    ORDER_CANCELLATION_PENDING_TIME("订单取消待处理时间", 6, "JH_07"),

    /**
     * 订单RDS修改时间
     */
    ORDER_RDS_MODIFICATION_TIME("订单RDS修改时间", 7, "JH_08"),

    /**
     * 订单RDS创建时间
     */
    ORDER_RDS_CREATION_TIME("订单RDS创建时间", 8, "JH_09");

    /**
     * 枚举项的名称
     */
    private final String name;

    /**
     * 枚举项的值
     */
    private final Integer value;

    /**
     * 枚举项的ployValue
     */
    private final String ployValue;

    /**
     * 构造方法
     *
     * @param name  枚举项的名称
     * @param value 枚举项的值
     * @param ployValue 枚举项的ployValue
     */
    LoadOrderTimeTypeEnum(String name, Integer value, String ployValue) {
        this.name = name;
        this.value = value;
        this.ployValue = ployValue;
    }

    /**
     * 获取枚举项的名称
     *
     * @return 枚举项的名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取枚举项的值
     *
     * @return 枚举项的值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取菠萝派值
     *
     * @return 菠萝派值
     */
    @Override
    public String getCode() {
        return ployValue;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static ManualLoadSupportTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(ManualLoadSupportTypeEnum.class, value, EnumConvertType.VALUE, EnumConvertType.CODE);
    }
}
