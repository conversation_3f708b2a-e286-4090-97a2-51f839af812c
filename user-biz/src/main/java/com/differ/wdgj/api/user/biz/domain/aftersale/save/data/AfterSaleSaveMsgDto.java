package com.differ.wdgj.api.user.biz.domain.aftersale.save.data;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;

import java.util.List;

/**
 * API售后单保存消息对象
 *
 * <AUTHOR>
 * @since 2024-07-01 下午 2:19
 */
public class AfterSaleSaveMsgDto {

    /**
     * 会员名
     */
    private String outAccount;

    /**
     * 平台值
     */
    private int platType;

    /**
     * 外部店铺id
     */
    private int outShopId;

    /**
     * 订单来源类型
     */
    private int orderTriggerType;

    /**
     * 聚合请求id
     */
    private String polyApiRequestId;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 退款单集合
     */
    private List<BusinessGetRefundOrderResponseOrderItem> refunds;

    // region getter & setter

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public int getPlatType() {
        return platType;
    }

    public void setPlatType(int platType) {
        this.platType = platType;
    }

    public int getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(int outShopId) {
        this.outShopId = outShopId;
    }

    public int getOrderTriggerType() {
        return orderTriggerType;
    }

    public void setOrderTriggerType(int orderTriggerType) {
        this.orderTriggerType = orderTriggerType;
    }

    public String getPolyApiRequestId() {
        return polyApiRequestId;
    }

    public void setPolyApiRequestId(String polyApiRequestId) {
        this.polyApiRequestId = polyApiRequestId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public List<BusinessGetRefundOrderResponseOrderItem> getRefunds() {
        return refunds;
    }

    public void setRefunds(List<BusinessGetRefundOrderResponseOrderItem> refunds) {
        this.refunds = refunds;
    }

    // endregion

}
