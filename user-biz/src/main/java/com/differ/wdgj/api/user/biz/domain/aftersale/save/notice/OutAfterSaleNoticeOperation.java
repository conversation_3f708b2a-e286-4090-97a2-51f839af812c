package com.differ.wdgj.api.user.biz.domain.aftersale.save.notice;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.AbstractAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.BaseAfterSaleOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderDataResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetOutNoticeItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle.IAfterSaleNotice;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberRunnable;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后单-外部消息通知实现
 *
 * <AUTHOR>
 * @date 2024-06-06 14:47
 */
public class OutAfterSaleNoticeOperation implements IOutAfterSaleNoticeOperation {
    //region 常量

    /**
     * 日志
     */
    private final Logger log = LoggerFactory.getLogger(OutAfterSaleNoticeOperation.class);

    /**
     * 上下文
     */
    private final AfterSaleSaveContext context;

    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public OutAfterSaleNoticeOperation(final AfterSaleSaveContext context) {
        // 初始化上下文
        this.context = context;
    }
    //endregion

    /**
     * 执行通知
     *
     * @param targetOutNoticeItem 外部消息通知订单列表
     */
    @Override
    public void notice(TargetOutNoticeItem targetOutNoticeItem) {
        ArrayList<String> afterSaleNos = new ArrayList<>();
        try {
            List<IAfterSaleNotice> notices = targetOutNoticeItem.getErpNoticeTypes();
            List<SaveOrderDataResult> saveDbResults = targetOutNoticeItem.getSaveDbResults();

            // 需要处理的消息事件集合
            List<IAfterSaleNotice> needDealNotices = notices;
            // 根据保存订单结果过滤无需处理的消息事件
            if (CollectionUtils.isNotEmpty(saveDbResults)) {
                // 保存成功地售后单号
                List<String> saveSuccessTradeNoList = saveDbResults.stream().filter(AbstractAfterSaleResult::isSuccess).map(BaseAfterSaleOrderResult::getAfterSaleNo).collect(Collectors.toList());
                needDealNotices = notices.stream().filter(t -> saveSuccessTradeNoList.contains(t.getAfterSaleNo()) || t.noticeOnProcessOrderFail()).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(needDealNotices)) {
                return;
            }

            // 记录需要发起的售后单号
            List<String> needDealAfterSaleNos = needDealNotices.stream().map(IAfterSaleNotice::getAfterSaleNo).collect(Collectors.toList());
            afterSaleNos.addAll(needDealAfterSaleNos);

            // 执行事件
            needDealNotices.forEach(notice ->
                    // 执行带吉客号的任务
                    TaskEnum.API_AFTER_SALE_NOTIFY.executeJack(new MemberRunnable(context.getMemberName()) {
                        @Override
                        public void run() {
                            notice.processNotice();
                        }
                    }));
        } catch (Exception e) {
            // 错误日志
            String logContent = String.format("【%s】【%s】售后单外部消息通知发起失败，售后单号：%s", context.getMemberName(), context.getShopId(), afterSaleNos);
            log.error(logContent, e);
        }
    }
}
