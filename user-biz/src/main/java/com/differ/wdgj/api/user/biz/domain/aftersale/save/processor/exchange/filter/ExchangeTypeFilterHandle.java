package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPreFiltrationOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 换货单类别过滤器
 *
 * <AUTHOR>
 * @date 2024/8/8 上午9:48
 */
public class ExchangeTypeFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangeTypeFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, TargetCovertOrderItem targetOrder) {
        // 基础数据
        AfterSaleSaveBizType bizType = orderItem.getBizType();
        String shopTypeCode = bizType.getShopType().getCode();
        ApiAfterSaleTypeEnum apiAfterSaleOrderType = bizType.getApiAfterSaleOrderType();

        // 推送平台不校验店铺业务配置
        if(context.getAfterSalesConfigPlatFeature().isMessageNotificationNoBizCheck()){
            return AfterSaleHandleResult.success();
        }

        // 店铺配置获取
        AfterSalesShopConfig shopAfterSalesConfig = context.getAfterSalesShopConfig();
        if (shopAfterSalesConfig == null || CollectionUtils.isEmpty(shopAfterSalesConfig.getSaveTypes())) {
            return AfterSaleHandleResult.failed("售后店铺配置未勾选售后类型");
        }
        // 获取当前订单业务类型的店铺配置
        List<AfterSalesShopConfig.TypeItem> bizShopOrderTypes = shopAfterSalesConfig.getSaveTypes().stream().filter(x -> x.getBizType().equals(shopTypeCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bizShopOrderTypes)) {
            return AfterSaleHandleResult.failed(String.format("【%s】售后店铺配置未勾选售后类型", shopTypeCode));
        }
        // 店铺配置校验
        if (!AfterSalesShopConfigUtils.checkShopTypeConfig(apiAfterSaleOrderType, bizShopOrderTypes)) {
            return AfterSaleHandleResult.failed(String.format("【%s】售后店铺配置未勾选售后类型，当前售后单类型：%s", shopTypeCode, apiAfterSaleOrderType.getDescription()));
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "换货单类别过滤器";
    }
    //endregion
}
