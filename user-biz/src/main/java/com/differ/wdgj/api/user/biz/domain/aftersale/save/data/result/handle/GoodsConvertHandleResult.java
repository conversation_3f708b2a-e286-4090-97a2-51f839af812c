package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle;

import org.apache.commons.lang3.StringUtils;

/**
 * 售后单转换-商品级转换结果
 *
 * <AUTHOR>
 * @date 2024/7/12 上午10:44
 */
public class GoodsConvertHandleResult extends AfterSaleHandleResult {
    /**
     * 是否保存商品
     */
    private boolean isSaveGoods;

    //region 构造
    /**
     * 构造
     */
    protected GoodsConvertHandleResult(boolean success, String message) {
        super(success, message);
    }
    //endregion

    //region 公共方法
    /**
     * 失败的结果
     *
     * @param message 失败信息
     * @return 结果
     */
    public static GoodsConvertHandleResult failed(String message) {
        return new GoodsConvertHandleResult(false, message);
    }

    /**
     * 不保存的结果
     *
     * @param message 失败信息
     * @return 结果
     */
    public static GoodsConvertHandleResult noSave(String message) {
        GoodsConvertHandleResult result = new GoodsConvertHandleResult(true, message);
        result.setSaveGoods(false);
        return result;
    }

    /**
     * 成功的结果
     *
     * @return 结果
     */
    public static GoodsConvertHandleResult success() {
        GoodsConvertHandleResult result = new GoodsConvertHandleResult(true, StringUtils.EMPTY);
        result.setSaveGoods(true);
        return result;
    }
    //endregion

    //region get/set
    public boolean isSaveGoods() {
        return isSaveGoods;
    }

    public void setSaveGoods(boolean saveGoods) {
        isSaveGoods = saveGoods;
    }
    //endregion
}
