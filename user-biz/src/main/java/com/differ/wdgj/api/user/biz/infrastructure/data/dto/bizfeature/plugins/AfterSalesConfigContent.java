package com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins;

import java.util.List;

/**
 * 平台业务特性 - 约束内容 - 售后单配置
 *
 * <AUTHOR>
 * @date 2024/7/16 上午10:19
 */
public class AfterSalesConfigContent {

    /**
     * 业务明细列表
     */
    private List<PlatShopBizEntity> bizList;

    /**
     * 推送平台是否校验店铺类型配置
     */
    private boolean isMessageNotificationNoBizCheck;

    /**
     * 是否区分已发货仅退款和未发货仅退款
     */
    private boolean bDistinguishShippedAndUnshipped;

    /**
     * 是否将菠萝派仅退款单（JH_01/JH_02）视为已发货仅退款
     */
    private boolean bPolyRefundPayRegardedRefundPaySend;

    //region get/set

    public List<PlatShopBizEntity> getBizList() {
        return bizList;
    }

    public void setBizList(List<PlatShopBizEntity> bizList) {
        this.bizList = bizList;
    }

    public boolean isMessageNotificationNoBizCheck() {
        return isMessageNotificationNoBizCheck;
    }

    public void setMessageNotificationNoBizCheck(boolean messageNotificationNoBizCheck) {
        isMessageNotificationNoBizCheck = messageNotificationNoBizCheck;
    }

    public boolean getBDistinguishShippedAndUnshipped() {
        return bDistinguishShippedAndUnshipped;
    }

    public void setBDistinguishShippedAndUnshipped(boolean bDistinguishShippedAndUnshipped) {
        this.bDistinguishShippedAndUnshipped = bDistinguishShippedAndUnshipped;
    }

    public boolean getBPolyRefundPayRegardedRefundPaySend() {
        return bPolyRefundPayRegardedRefundPaySend;
    }

    public void setBPolyRefundPayRegardedRefundPaySend(boolean bPolyRefundPayRegardedRefundPaySend) {
        this.bPolyRefundPayRegardedRefundPaySend = bPolyRefundPayRegardedRefundPaySend;
    }


    //endregion
}
