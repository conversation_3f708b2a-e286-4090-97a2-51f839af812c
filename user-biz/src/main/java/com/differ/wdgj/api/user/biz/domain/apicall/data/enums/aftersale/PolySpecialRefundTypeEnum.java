package com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;

/**
 * 菠萝派售后单类别
 *
 * <AUTHOR>
 * @date 2025/5/20 下午15:57
 */
public enum PolySpecialRefundTypeEnum implements CodeEnum {
    /**
     * 保价。
     */
    JH_PRICE_PROTECT("保价", "JH_PRICE_PROTECT"),

    /**
     * 退差返现。
     */
    JH_CASH_BACK("退差返现", "JH_CASH_BACK"),

    /**
     * 小额打款。
     */
    JH_SMALL_REFUND("小额打款", "JH_SMALL_REFUND"),

    /**
     * 补发商品。
     */
    JH_REISSUE("补发商品", "JH_REISSUE"),

    /**
     * 其他。
     */
    JH_NONE("其他", "JH_NONE");

    //region 属性
    private final String description;
    private final String code;
    //endregion

    //region 构造
    /**
     * 构造函数初始化枚举常量。
     *
     * @param description 枚举的描述。
     * @param code        枚举的代码。
     */
    PolySpecialRefundTypeEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }
    //endregion

    //region 公共方法
    /**
     * 获取枚举的描述。
     *
     * @return 描述。
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取枚举的代码。
     *
     * @return 代码。
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static PolySpecialRefundTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, PolySpecialRefundTypeEnum.class, EnumConvertType.CODE);
    }
    //endregion
}
