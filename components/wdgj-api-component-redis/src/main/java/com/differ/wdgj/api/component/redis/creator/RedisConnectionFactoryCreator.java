package com.differ.wdgj.api.component.redis.creator;

import com.differ.wdgj.api.component.redis.config.MultiRedisProperties;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisShardInfo;

import java.util.Arrays;
import java.util.List;

/**
 * Redis连接工厂的创建器，支持不同步类型的缓存创建独立的连接工厂
 * <AUTHOR>
 * @date 2020/12/14 15:02
 */
public class RedisConnectionFactoryCreator {

    private Logger log = LoggerFactory.getLogger(RedisConnectionFactoryCreator.class);

    private MultiRedisPropertiesCreator propertiesCreator;

    public RedisConnectionFactoryCreator(MultiRedisPropertiesCreator propertiesCreator) {
        this.propertiesCreator = propertiesCreator;
    }

    /**
     * 根据缓存类型创建Redis连接工厂
     *
     * @param cacheType 缓存类型
     * @return Redis连接工厂
     */
    public RedisConnectionFactory create(String cacheType) {
        log.info("开始创建Redis连接工厂，类型:{}", cacheType);
        MultiRedisProperties redisProperties = propertiesCreator.createProperties(cacheType);
        return createFactory(redisProperties,cacheType);
    }

    /**
     * 创建Redis连接工厂
     *
     * @param redisProperties redis配置属性
     * @return Redis连接工厂
     */
    private RedisConnectionFactory createFactory(MultiRedisProperties redisProperties, String cacheType) {
        // 集群工厂
        if (redisProperties.redisCluster()) {
            return createClusterFactory(redisProperties, cacheType);
        }
        // 单点工厂
        return createSingleFactory(redisProperties, cacheType);
    }

    /**
     * 创建单点Redis连接工厂
     *
     * @param redisProperties redis配置属性
     * @return Redis连接工厂
     */
    private RedisConnectionFactory createSingleFactory(MultiRedisProperties redisProperties, String cacheType) {
        // 设置连接信息，Url示例：redis://mypassword@127.0.0.1:6379/1 redis://127.0.0.1:6379/1
        String url = String.format("redis://%s:%d/%d", redisProperties.getHost(), redisProperties.getPort(), redisProperties.getDatabase());
        JedisShardInfo jedisShardInfo = new JedisShardInfo(url);
        if (StringUtils.isNotBlank(redisProperties.getPassword())) {
            jedisShardInfo.setPassword(redisProperties.getPassword());
        }
        // 单点工厂
        JedisConnectionFactory factory = new JedisConnectionFactory(jedisShardInfo);
        factory.setPoolConfig(createJedisPoolConfig(redisProperties));
        factory.setTimeout(redisProperties.getTimeout());
        factory.setUseSsl(redisProperties.getSsl());
        log.info("完成单点工厂Redis初始化，缓存类型:{},url:{}", cacheType, url);
        return factory;
    }

    /**
     * 创建集群Redis连接工厂
     *
     * @param redisProperties redis配置属性
     * @return Redis连接工厂
     */
    private RedisConnectionFactory createClusterFactory(MultiRedisProperties redisProperties, String cacheType) {
        // 集群地址处理
        List<String> nodeList = Arrays.asList(redisProperties.getNodes().split(","));
        RedisClusterConfiguration configuration = new RedisClusterConfiguration(nodeList);
        configuration.setMaxRedirects(redisProperties.getMaxRedirects());
        // 集群工厂
        JedisConnectionFactory factory = new JedisConnectionFactory(configuration);
        if (StringUtils.isNotBlank(redisProperties.getPassword())) {
            factory.setPassword(redisProperties.getPassword());
        }
        factory.setPoolConfig(createJedisPoolConfig(redisProperties));
        factory.setTimeout(redisProperties.getTimeout());
        factory.setUseSsl(redisProperties.getSsl());
        factory.afterPropertiesSet();
        log.info("完成集群工厂Redis初始化，缓存类型:{},节点地址:{}", cacheType, redisProperties.getNodes());
        return factory;
    }

    private JedisPoolConfig createJedisPoolConfig(MultiRedisProperties redisProperties) {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(redisProperties.getMaxIdle());
        poolConfig.setMinIdle(redisProperties.getMinIdle());
        poolConfig.setMaxTotal(redisProperties.getMaxTotal());
        return poolConfig;
    }
}
