package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 队列头信息上下文
 *
 * <AUTHOR>
 * @date 2024/3/14 10:59
 */
public class QueueHeader {

    /**
     * 头信息map
     */
    private Map<String, String> header;

    public QueueHeader() {
        this.header = new HashMap<>();
    }

    public QueueHeader(QueueHeader srcHeader) {
        if (srcHeader != null && srcHeader.getHeader() != null) {
            this.header = srcHeader.getHeader();
        } else {
            this.header = new HashMap<>();
        }
    }

    public QueueHeader(Map<String, String> header) {
        this.header = header;
        if (this.header == null) {
            this.header = new HashMap<>();
        }
    }

    /**
     * 获取头信息集合
     *
     * @return
     */
    public Map<String, String> getHeader() {
        return header;
    }

    /**
     * 头信息是否包含key
     *
     * @param headerKey
     * @return
     */
    public boolean containsKey(String headerKey) {
        if (StringUtils.isBlank(headerKey)) {
            return false;
        }
        return header.containsKey(headerKey);
    }

    /**
     * 获取头信息
     *
     * @param headerKey
     * @return
     */
    public String getHeaderValue(String headerKey) {
        if (StringUtils.isBlank(headerKey)) {
            return null;
        }
        return header.get(headerKey);
    }

    /**
     * 添加头信息
     *
     * @param headerKey
     * @param value
     */
    public void setHeader(String headerKey, String value) {
        header.put(headerKey, value);
    }

    /**
     * 添加头信息
     *
     * @param headerKey
     * @param value
     */
    public void setHeader(String headerKey, long value) {
        header.put(headerKey, String.valueOf(value));
    }

    /**
     * 添加头信息
     *
     * @param headerKey
     * @param value
     */
    public void setHeader(String headerKey, Object value) {
        header.put(headerKey, String.valueOf(value));
    }

    /**
     * 获取失败次数
     *
     * @return
     */
    public int getRetryCount() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.RETRY_COUNT.getKey());
        if (headerValue == null || StringUtils.isBlank(headerValue.toString())) {
            return 0;
        }
        return Integer.parseInt(headerValue.toString());
    }

    /**
     * 设置失败次数
     *
     * @return
     */
    public void setRetryCount(int failCount) {
        this.setHeader(HeaderKeyEnum.RETRY_COUNT.getKey(), String.valueOf(failCount));
    }

    /**
     * 累增重试次数
     *
     * @return 更新后的重试次数
     */
    public int incrementRetryCount() {
        int retryCount = this.getRetryCount() + 1;
        this.setRetryCount(retryCount);
        return retryCount;
    }

    /**
     * 获取延迟时间
     *
     * @return
     */
    public int getDelaySeconds() {
        return getDelayGrade().getDelaySeconds();
    }

    /**
     * 获取延迟级别
     *
     * @return
     */
    public DelayGradeEnum getDelayGrade() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.DELAY_GRADE.getKey());
        if (headerValue == null || StringUtils.isBlank(headerValue.toString())) {
            return DelayGradeEnum.getMinGrade();
        }

        return DelayGradeEnum.getByGrade(NumberUtils.toInt(headerValue.toString(), 0));
    }

    /**
     * 获取延迟时间
     *
     * @return
     */
    public void setDelayGrade(DelayGradeEnum delaySeconds) {
        setHeader(HeaderKeyEnum.DELAY_GRADE.getKey(), String.valueOf(delaySeconds.getGrade()));
    }

    /**
     * 获取处理代码
     *
     * @return
     */
    public String getHandlerCode() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.HANDLER_CODE.getKey());
        if (headerValue == null) {
            return "";
        }
        return headerValue.toString();
    }

    /**
     * 获取吉客号
     *
     * @return
     */
    public String getJackNo() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.JACK_NO.getKey());
        if (headerValue == null) {
            return "";
        }
        return headerValue.toString();
    }

    /**
     * 获取日志ID
     *
     * @return
     */
    public long getLoggerSn() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.LOGGER_SN.getKey());
        if (headerValue == null || StringUtils.isBlank(headerValue.toString())) {
            return 0;
        }
        return Long.parseLong(headerValue.toString());
    }

    /**
     * 获取业务数据平台
     *
     * @return
     */
    public int getDataPlat() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.DATA_PLAT.getKey());
        if (headerValue == null || StringUtils.isBlank(headerValue.toString())) {
            return 0;
        }
        return Integer.parseInt(headerValue.toString());
    }

    /**
     * 设置业务数据平台
     *
     * @return
     */
    public void setDataPlat(int platValue) {
        this.setHeader(HeaderKeyEnum.DATA_PLAT.getKey(), String.valueOf(platValue));
    }

    /**
     * 获取业务数据类型
     *
     * @return
     */
    public String getDataType() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.DATA_TYPE.getKey());
        if (headerValue == null) {
            return null;
        }
        return headerValue.toString();
    }

    /**
     * 设置业务数据类型
     *
     * @return
     */
    public void setDataType(String dataType) {
        this.setHeader(HeaderKeyEnum.DATA_TYPE.getKey(), dataType);
    }


    /**
     * 消息数据库主键ID，只有当数据库子队列时才有赋值
     *
     * @return
     */
    public long getDbId() {
        Object headerValue = getHeaderValue(HeaderKeyEnum.DB_ID.getKey());
        if (headerValue == null || StringUtils.isBlank(headerValue.toString())) {
            return 0;
        }
        return Long.parseLong(headerValue.toString());
    }

    /**
     * 消息数据库主键ID，只有当数据库子队列时才有赋值
     *
     * @return
     */
    public void setDbId(long dbId) {
        this.setHeader(HeaderKeyEnum.DB_ID.getKey(), String.valueOf(dbId));
    }

    /**
     * 设置消息处理类型
     *
     * @param handlerCode 一般设置multiCode
     */
    public void setHandlerCode(String handlerCode) {
        this.setHeader(HeaderKeyEnum.HANDLER_CODE.getKey(), handlerCode);
    }

    /**
     * 获取头信息集合
     *
     * @return
     */
    public Map<String, String> toBizMap() {
        Map<String, String> bizHead = new HashMap<>();
        List<String> keys = HeaderKeyEnum.getKeys();
        for (String key : keys) {
            if (header.containsKey(key)) {
                bizHead.put(key, header.get(key));
            }
        }
        return bizHead;
    }

    public String toJson() {
        return JsonUtils.toJson(this.header);
    }

    public static QueueHeader deJson(String mapJson) {
        Map<String, String> header = JsonUtils.deJson(mapJson, new TypeReference<Map<String, String>>() {
        });
        return new QueueHeader(header);
    }
}
