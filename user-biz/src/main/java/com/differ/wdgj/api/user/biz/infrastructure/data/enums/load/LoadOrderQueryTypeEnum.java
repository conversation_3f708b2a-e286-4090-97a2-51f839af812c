package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 下载订单请求来源枚举
 *
 * <AUTHOR>
 * @date 2024/9/20 下午3:45
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum LoadOrderQueryTypeEnum implements ValueEnum {
    //region 枚举项

    NORMAL("接口", (byte)0),

    PUSH("平台推送", (byte)1),

    /**
     * 推送库不存在时再请求接口
     */
    RDS("推送库", (byte) 2),

    ONLY_RDS("仅推送库", (byte) 3);

    //endregion

    //region 构造函数

    /**
     * 构造函数
     *
     * @param caption 标题
     * @param value   值
     */
    LoadOrderQueryTypeEnum(String caption, byte value) {
        this.caption = caption;
        this.value = value;
    }

    //endregion

    //region 变量
    /**
     * 标题
     */
    private String caption;
    /**
     * 值
     */
    private Byte value;

    //endregion

    //region 属性

    @Override
    public Integer getValue() {
        return this.value.intValue();
    }

    public String getCaption() {
        return caption;
    }

    //endregion

    //region 方法

    /**
     * 枚举值字符串
     *
     * @return str
     */
    @Override
    public String toString() {
        return this.getValue().toString();
    }

    /**
     * 枚举转换
     *
     * @param code code值
     * @return 返回结果
     */
    public static LoadOrderQueryTypeEnum convert(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        return EnumConvertCacheUtil.convert(code, LoadOrderQueryTypeEnum.class, EnumConvertType.CODE);
    }


    //endregion
}
