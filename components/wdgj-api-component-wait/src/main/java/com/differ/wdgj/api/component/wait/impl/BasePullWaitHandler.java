package com.differ.wdgj.api.component.wait.impl;

import com.differ.wdgj.api.component.wait.AbstractWaitEntity;
import com.differ.wdgj.api.component.wait.PullWaitLine;
import com.differ.wdgj.api.component.wait.PullWaitStrategy;
import com.differ.wdgj.api.component.wait.WaitStrategy;
import com.differ.wdgj.api.component.wait.data.CommonWaitEntity;
import com.differ.wdgj.api.component.wait.data.PullWaitContext;
import com.differ.wdgj.api.component.wait.data.WaitContext;
import com.differ.wdgj.api.component.wait.data.WaitStatusEnum;
import com.differ.wdgj.api.component.wait.strategy.PullSimpleStrategy;

/**
 * @Description 拉取模式的排队处理器
 * <AUTHOR>
 * @Date 2023/12/6 11:23
 */
public class BasePullWaitHandler extends BaseWaitHandler implements PullWaitLine {

    /**
     * 初始化，任务执行方法
     *
     * @param context
     * @param strategy
     */
    @Override
    public void init(WaitContext context, WaitStrategy strategy) {
        if (!PullWaitContext.class.isAssignableFrom(context.getClass())) {
            throw new IllegalArgumentException("拉取模式排队上下文类型错误");
        }
        if (strategy != null && !PullWaitStrategy.class.isAssignableFrom(strategy.getClass())) {
            throw new IllegalArgumentException("拉取模式排队策略类型错误");
        }
        this.initPullMode((PullWaitContext) context, (PullWaitStrategy) strategy);
    }

    /**
     * 拉取模式初始化
     *
     * @param context
     * @param strategy
     */
    @Override
    public void initPullMode(PullWaitContext context, PullWaitStrategy strategy) {
        if (strategy == null) {
            strategy = new PullSimpleStrategy();
        }
        this.waitStrategy = strategy;
        this.waitStrategy.init(context);
    }

    /**
     * 检查并修复数据容错（拉取模式不支持）
     *
     * @return 修复的记录数
     */
    @Override
    public int checkAndRepair() {
        return 0;
    }

    /**
     * 拉取下一个排队任务数据,并且处理
     *
     * @return true:取到任务并且处理，false:没有取到任务
     */
    @Override
    public boolean fetchNextAndHandle() {
        return this.receiveMessageHandleNext();
    }

    /**
     * 添加任务到排队队列
     *
     * @param entity
     * @return true:放入排队（新增排队）, false:已存在（排队中或执行中）
     */
    @Override
    public boolean putWait(AbstractWaitEntity entity) {
        WaitStatusEnum waitStatus = this.putAndGetStatus(entity);
        if (waitStatus == WaitStatusEnum.EXEC_ADD) {
            // 当出现此错误表示系统代码（Lua脚本等）存在问题,正常上线后不会出现
            throw new IllegalStateException("排队拉取模式新增错误");
        }
        return waitStatus == WaitStatusEnum.WAIT_ADD;
    }

    /**
     * 拉取下一个排队数据
     *
     * @return 返回排队的key和数据
     */
    @Override
    public CommonWaitEntity pullNext() {
        return ((PullWaitStrategy) this.waitStrategy).fetchNextWait();
    }

    /**
     * 完成任务
     *
     * @param waitUniqueId 排队的任务key
     */
    @Override
    public void complete(String waitUniqueId) {
        ((PullWaitStrategy) this.waitStrategy).complete(waitUniqueId);
    }
}
