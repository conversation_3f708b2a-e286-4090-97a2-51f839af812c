package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;

/**
 * 售后单业务类型
 *
 * <AUTHOR>
 * @date 2025/4/3 上午10:50
 */
public class AfterSaleSaveBizType {
    /**
     * 店铺类型
     */
    private ShopTypeEnum shopType;

    /**
     * api售后单类型
     */
    private ApiAfterSaleTypeEnum apiAfterSaleOrderType;

    //region 构造
    protected AfterSaleSaveBizType() {
    }
    //endregion

    /**
     * 获取默认售后单业务类型
     *
     * @return 默认售后单业务类型
     */
    public static AfterSaleSaveBizType getDefaultBizType() {
        AfterSaleSaveBizType bizType = new AfterSaleSaveBizType();
        bizType.setShopType(ShopTypeEnum.DEFAULT);
        bizType.setApiAfterSaleOrderType(null);
        return bizType;
    }

    /**
     * 构建 售后单业务类型
     *
     * @param shopType              店铺类型
     * @param apiAfterSaleOrderType api售后单类型
     * @return 售后单业务类型
     */
    public static AfterSaleSaveBizType build(ShopTypeEnum shopType, ApiAfterSaleTypeEnum apiAfterSaleOrderType) {
        AfterSaleSaveBizType bizType = new AfterSaleSaveBizType();
        bizType.setShopType(shopType);
        bizType.setApiAfterSaleOrderType(apiAfterSaleOrderType);
        return bizType;
    }

    //region get/set

    protected void setShopType(ShopTypeEnum shopType) {
        this.shopType = shopType;
    }

    public ShopTypeEnum getShopType() {
        return shopType;
    }

    public ApiAfterSaleTypeEnum getApiAfterSaleOrderType() {
        return apiAfterSaleOrderType;
    }

    protected void setApiAfterSaleOrderType(ApiAfterSaleTypeEnum apiAfterSaleOrderType) {
        this.apiAfterSaleOrderType = apiAfterSaleOrderType;
    }
    //endregion
}
