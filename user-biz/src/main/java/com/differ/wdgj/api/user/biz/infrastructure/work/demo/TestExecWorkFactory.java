package com.differ.wdgj.api.user.biz.infrastructure.work.demo;

import com.differ.wdgj.api.user.biz.infrastructure.work.business.page.PageWorkBusinessProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.demo.data.TestChildTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.demo.data.TestWorkArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.demo.data.TestWorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.AbstractPageWorkFactory;

/**
 * 测试工作任务工厂
 *
 * <AUTHOR>
 * @date 2024/9/11 上午11:38
 */
public class TestExecWorkFactory extends AbstractPageWorkFactory<WorkData<TestWorkArgs>, TestChildTask, TestWorkResult> {
    public TestExecWorkFactory() {
        super(WorkEnum.DEMO_TEST);
    }

    @Override
    public PageWorkBusinessProcessor<WorkData<TestWorkArgs>, TestChildTask, TestWorkResult> createBusinessProcessor(WorkData<TestWorkArgs> workData) {
        return new TestPageWorkBusinessProcessor();
    }

    /**
     * 获取工作任务的参数类型
     * com.differ.jackyun.omsapi.user.biz.infrastructure.work.data.WorkData<T extends WorkArgs>
     *
     * @return T的类型
     */
    @Override
    public Class<?> getWorkDataArgClass() {
        return TestWorkArgs.class;
    }
}
