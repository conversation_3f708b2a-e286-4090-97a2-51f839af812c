package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.TbTradeJdpResponseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbTradeRdsDo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbTradeRdsMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 淘宝Rds - 订单相关操作（jdp_tb_trade）
 *
 * <AUTHOR>
 * @date 2025/6/26 09:45
 */
public class RdsTbTradeDomain {
    //region 常量
    /**
     * 会员名
     */
    private final String memberName;
    //endregion

    //region 构造
    public RdsTbTradeDomain(String memberName) {
        this.memberName = memberName;
    }
    //endregion

    /**
     * 获取订单发货信息列表
     *
     * @param tIds 普通订单单号列表
     * @return 订单发货信息列表
     */
    public List<RdsTbTradeSendInfo> getOrderSendInfos(List<Integer> tIds) {
        if (CollectionUtils.isEmpty(tIds)) {
            return Collections.emptyList();
        }

        // 查询推送库订单列表
        TbTradeRdsMapper tbTradeRdsMapper = BeanContextUtil.getBean(TbTradeRdsMapper.class);
        List<TbTradeRdsDo> tbTradeRdsDos = DBSwitchUtil.doDBWithRds(memberName, () -> tbTradeRdsMapper.getTradeListByTIds(tIds));

        // 发货信息数据转换
        List<RdsTbTradeSendInfo> rdsTbTradeSendInfos = new ArrayList<>();
        for (TbTradeRdsDo tbTradeRdsDo : tbTradeRdsDos) {
            if (StringUtils.isNotEmpty(tbTradeRdsDo.getJdpResponse())) {
                TbTradeJdpResponseDto tbTradeJdpResponseDto = JsonUtils.deJson(tbTradeRdsDo.getJdpResponse(), TbTradeJdpResponseDto.class);
                if (tbTradeJdpResponseDto == null || tbTradeJdpResponseDto.getTradeFullInfoGetResponse() == null ||
                        tbTradeJdpResponseDto.getTradeFullInfoGetResponse().getTrade() == null||
                        tbTradeJdpResponseDto.getTradeFullInfoGetResponse().getTrade().getOrders() == null) {
                    continue;
                }
                // Rds订单数据
                TbTradeJdpResponseDto.Trade trade = tbTradeJdpResponseDto.getTradeFullInfoGetResponse().getTrade();

                // 构建商品级结果
                Map<Long, Boolean> subOrderSendMap = new HashMap<>();
                trade.getOrders().getOrder().forEach(order ->{
                    subOrderSendMap.put(order.getOid(), order.getConsignTime() != null);
                });
                // 构建订单级发货结果
                RdsTbTradeSendInfo rdsTbTradeSendInfo = new RdsTbTradeSendInfo();
                rdsTbTradeSendInfo.setTId(trade.getTid());
                rdsTbTradeSendInfo.setSubOrderSendMap(subOrderSendMap);
                rdsTbTradeSendInfos.add(rdsTbTradeSendInfo);
            }
        }

        return rdsTbTradeSendInfos;
    }
}
