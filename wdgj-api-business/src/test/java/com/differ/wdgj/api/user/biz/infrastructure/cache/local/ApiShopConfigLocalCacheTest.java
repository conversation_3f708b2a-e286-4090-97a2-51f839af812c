package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopConfigLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopConfigLocalDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 会员店铺配置内存缓存
 *
 * <AUTHOR>
 * @date 2024-06-26 11:12
 */
@Ignore
public class ApiShopConfigLocalCacheTest extends AbstractSpringTest {

    /**
     * 获取店铺配置
     */
    @Test
    public void getConfigTest() {
        ApiShopConfigLocalDto config = ApiShopConfigLocalCache.singleton().getConfig("api2017", ApiShopConfigBizTypes.AFTER_SALES, 4195);
    }
}
