package com.differ.wdgj.api.component.task.single.core;

import org.quartz.listeners.JobListenerSupport;
import org.quartz.listeners.TriggerListenerSupport;
import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description 单体任务作业注解，确保jobName唯一
 * <AUTHOR>
 * @Date 2020/11/19 11:00
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface SingleJobParameter {
    /**
     * 任务名
     * @return
     */
    String jobName() default "";

    /**
     * 任务所属组，group和name组成唯一解，group可用于后期的批量操作
     * @return
     */
    String jobGroup() default "default";

    /**
     * cron表达式，工具：https://cron.qqe2.com/
     * @return
     */
    String cron() default "0/3 * * * * ?";

    /**
     * 任务监听器
     * @return
     */
    Class<? extends JobListenerSupport>[] jobListner() default {};


    /**
     * 任务触发器监听器
     * @returnt
     */
    Class<? extends TriggerListenerSupport>[] triggerListner() default {};

    /**
     * 任务运行所包含的站点
     * @return
     */
    String[] sitesToRun() default {};

}
