package com.differ.wdgj.api.user.biz.utils;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 配置键工具类
 *
 * <AUTHOR>
 * @date 2024-03-12 16:07
 */
@Ignore
public class ConfigKeyUtilsTest
         extends AbstractSpringTest
{
    /**
     * 获取API配置键值
     */
    @Test
    public void getApiConfigValueTest(){
        String configValue = ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.API_PolyAPI_AppSecret);
        Assert.assertNotNull(configValue);
    }

    //region 管家
    /**
     * 获取管家配置键值
     */
    @Test
    public void getWdgjConfigValueTest(){
        String configValue = ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess);
        Assert.assertNotNull(configValue);
    }

    /**
     * 管家配置键 - 多维度配置键匹配
     */
    @Test
    public void isActionWdgjMultipleMatchValueTest(){
        boolean resultTrue = ConfigKeyUtils.isActionWdgjMultipleMatchValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, new String[]{"@","$"},"114", "api2017", "FAILNEEDRETRY");
        Assert.assertTrue(resultTrue);
        boolean resultFalse = ConfigKeyUtils.isActionWdgjMultipleMatchValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, new String[]{"@","$"},"178", "hunheyuntest", "FAILNEEDRETRY");
        Assert.assertFalse(resultFalse);
        boolean resultFalse1 = ConfigKeyUtils.isActionWdgjMultipleMatchValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, new String[]{"@","$"},"178", "hunheyuntest", "1111");
        Assert.assertFalse(resultFalse1);
        boolean resultFalse2 = ConfigKeyUtils.isActionWdgjMultipleMatchValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, new String[]{"@","$"},"188", "hunheyuntest", "FAILNEEDRETRY");
        Assert.assertFalse(resultFalse2);
    }

    /**
     * 管家配置键 - 单维度配置键匹配
     */
    @Test
    public void isActionTest(){
        boolean resultTrue = ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.IsAction_SyncStockFrequecnyControl_UpdateFenXiaoGoods, "api2017");
        Assert.assertFalse(resultTrue);
        boolean resultFalse = ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.IsAction_SyncStockFrequecnyControl_UpdateFenXiaoGoods, "api2018");
        Assert.assertFalse(resultFalse);
    }

    /**
     * 获取管家配置键值
     */
    @Test
    public void isActionWdgjTest(){
        String configValue = ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess);
        Assert.assertNotNull(configValue);
    }

    /**
     * 获取管家配置键值
     */
    @Test
    public void isActionWdgjAsyncTest(){
        boolean resultTrue = ConfigKeyUtils.isActionWdgjAsync(ConfigKeyEnum.IsAction_SyncStockFrequecnyControl_UpdateFenXiaoGoods, "api2017");
    }
    //endregion


    /**
     * 验证两个值是否匹配
     */
    @Test
    public void isMatchTest(){
        /*boolean resultTrue = ConfigKeyUtils.isMatch("13|97|74|", "13", "|");
        Assert.assertTrue(resultTrue);
        boolean resultTrue2 = ConfigKeyUtils.isMatch("ALL", "13", "|");
        Assert.assertTrue(resultTrue2);
        boolean resultFalse = ConfigKeyUtils.isMatch("13|97|74|", "12", "|");
        Assert.assertFalse(resultFalse);*/
    }

    /**
     * 多维度配置键匹配
     */
    @Test
    public void isActionOnConfigKeysTest(){
        /*String configValue = "57#1008#1138@39$ALL|1149@1#2$ALL|161@39$cora|2@46$ALL|1155@39$dengguangyufu|135@39$hunheyuntest#fzyg#jmmy|";
        String[] splitChars = new String[]{"@","$"};
        boolean resultTrue = ConfigKeyUtils.isActionMultipleMatchValue(configValue, splitChars, "57", "39", "api2017");
        Assert.assertTrue(resultTrue);
        String configValue2 = "135@39$hunheyuntest#fzyg#jmmy";
        boolean resultTrue2 = ConfigKeyUtils.isActionMultipleMatchValue(configValue2, splitChars, "135", "39", "hunheyuntest");
        Assert.assertTrue(resultTrue2);
        boolean resultFalse = ConfigKeyUtils.isActionMultipleMatchValue(configValue, splitChars, "2", "55", "api2017");
        Assert.assertFalse(resultFalse);*/
    }

    /**
     * 字符串分割
     */
    @Test
    public void stringSplit(){
        String configValue = "135@39$hunheyuntest#fzyg#jmmy";
        String[] a = configValue.split("[@$]");
        String[] a1 = StringUtils.split(configValue, "[@$]");
    }


}
