package com.differ.wdgj.api.component.wait.data;

/**
 * @Description 排队上下文配置参数，可动态变化
 * <AUTHOR>
 * @Date 2021/9/10 15:01
 */
public class WaitConfigArgs {
    /**
     * 执行中的任务上限
     */
    private int maxExec = 3;

    /**
     * 执行超时时间,默认10分钟,实际时间为executingExpire ~ executingExpire *2
     */
    private int executingExpire = 600;

    /**
     * 排队任务数据缓存键过期时间，每次加排队任务会重设过期时间，默认86400 = 24小时
     */
    private int waitDataExpire = 86400;

    /**
     * 是否使用保守模式,每次排队时会触发一次兜底检查
     */
    private boolean insureMode = true;

    public int getMaxExec() {
        return maxExec;
    }

    public void setMaxExec(int maxExec) {
        this.maxExec = maxExec;
    }

    public int getWaitDataExpire() {
        return waitDataExpire;
    }

    public void setWaitDataExpire(int waitDataExpire) {
        this.waitDataExpire = waitDataExpire;
    }

    public int getExecutingExpire() {
        return executingExpire;
    }

    public void setExecutingExpire(int executingExpire) {
        this.executingExpire = executingExpire;
    }

    public boolean isInsureMode() {
        return insureMode;
    }

    public void setInsureMode(boolean insureMode) {
        this.insureMode = insureMode;
    }
}
