package com.differ.wdgj.api.component.wait.strategy;

import com.differ.wdgj.api.component.wait.data.BaseWaitMessage;
import com.differ.wdgj.api.component.wait.data.HandlerMode;

/**
 * @Description 带key的策略，一般指用户或集群 {@link SimpleStrategy}
 * <AUTHOR>
 * @Date 2021/12/29 11:49
 */
public class KeySimpleStrategy extends SimpleStrategy {

    /**
     * 用户，一般指吉客号
     */
    private String key;

    public KeySimpleStrategy(String key) {
        this.key = key;
        mode = HandlerMode.USER;
    }

    public KeySimpleStrategy(HandlerMode mode, String key) {
        this.mode = mode;
        this.key = key;
    }

    @Override
    protected String getWaitGroup() {
        return String.format("%s_%s", this.waitContext.getWaitType(), key);
    }

    @Override
    protected boolean publishPullNext() {
        try {
            this.waitContext.getWaitMessageSubscriber().publishNext(new BaseWaitMessage(this.mode, this.waitContext.getWaitType(), this.key));
            return true;
        } catch (Exception ex) {
            log.error(String.format("%s排队触发：拉取下一个失败", this.waitContext.getWaitType()), ex);
        }
        return false;
    }
}
