package com.differ.wdgj.api.user.biz.infrastructure.work.template;

import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.DataOperateContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.FailWorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.WorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.heart.WorkHeart;
import org.slf4j.Logger;

/**
 * 工作任务执行模板抽象
 * 模板流程：
 * 1、开启并同步一次工作心跳
 * 2、初始化任务，更新任务状态、初始化进度
 * 3、执行任务
 * 4、更新任务状态为完成，包含进度的最后兜底完成
 * 5、关闭工作心跳
 *
 * <AUTHOR>
 * @date 2024/6/24 13:42
 */
public abstract class AbstractWorkRunTemplate implements WorkExecTemplate<WorkData<?>> {

    //region 常量
    protected static final Logger LOG = LogFactory.get(AbstractWorkRunTemplate.class);

    /**
     * 工作任务类型
     */
    protected WorkEnum workEnum;

    /**
     * 工作工厂
     */
    protected WorkFactory factory;
    //endregion

    //region 构造
    protected AbstractWorkRunTemplate(WorkEnum workEnum) {
        this.workEnum = workEnum;
        this.factory = workEnum.getWorkFactory();
    }
    //endregion

    //region 实现接口方法

    /**
     * 模板执行工作任务,只带存在的任务ID
     *
     * @param member 会员名
     * @param taskId 任务ID
     * @return 执行结果
     */
    @Override
    public WorkResult run(String member, String taskId) {
        WorkData<?> workData = this.getWork(member, taskId);
        if (workData == null) {
            LOG.error("工作任务不存在,会员：{},类型：{},任务ID：{}", member, this.workEnum, taskId);
            return null;
        }
        return run(workData, taskId);
    }

    /**
     * 模板执行工作任务
     *
     * @param workData 任务数据
     * @param taskId   任务id
     * @return 执行结果
     */
    @Override
    public WorkResult run(WorkData<?> workData, String taskId) {
        WorkResult workResult = null;
        try {
            if (workData.getWorkType() != this.workEnum) {
                // 检查工作任务类型不匹配
                String errorMessage = String.format("工作任务类型不匹配:%s->%s,会员：%s,任务ID:%s", this.workEnum, workData.getWorkType(), workData.getMemberName(), taskId);
                LOG.error(errorMessage);
                throw new RuntimeException(errorMessage);
            }

            // 开始执行工作任务
            DataOperateContext dataOperateContext = DataOperateContext.of(workData.getMemberName(), workData.getWorkType(), taskId);
            WorkHeart workHeart = this.factory.createWorkHeart(dataOperateContext);

            try {
                // 1、开启并同步一次工作心跳，必须在任务执行前开启，以保证执行状态的任务不会出现无心跳
                workHeart.openBeat();
                // 2、初始化任务，更新任务状态、初始化进度
                this.initToExecute(workData, taskId);
                // 3、执行任务
                workResult = execWork(workData, taskId);
                // 4、更新任务状态为完成，包含进度的最后兜底完成
                this.complete(workData, taskId, workResult);
            } finally {
                // 5、关闭工作心跳
                if (workHeart != null) {
                    workHeart.closeBeat();
                }
            }
        } catch (Exception e) {
            // 更新任务状态为失败
            if (null == workResult) {
                workResult = new FailWorkResult(e);
            }
            LOG.error("工作任务执行异常,会员：{},类型：{},任务ID：{}；异常信息：", workData.getMemberName(), this.workEnum, taskId, e);
            this.complete(workData, taskId, workResult);
        }
        return workResult;
    }
    //endregion

    //region 抽象方法

    /**
     * 查询工作任务
     *
     * @param member 会员名
     * @param taskId 任务ID
     * @return 任务数据
     */
    protected abstract WorkData<?> getWork(String member, String taskId);

    /**
     * 初始化工作任务为执行状态
     *
     * @param workData 任务数据
     * @param taskId   任务ID
     */
    protected abstract void initToExecute(WorkData<?> workData, String taskId);

    /**
     * 执行所任务，默认同步执行
     *
     * @param workData 任务数据
     * @param taskId   任务ID
     * @return 执行结果
     */
    protected abstract WorkResult execWork(WorkData<?> workData, String taskId);

    /**
     * 完成工作任务
     *
     * @param workData   任务数据
     * @param taskId     任务ID
     * @param workResult 执行结果
     */
    protected abstract void complete(WorkData<?> workData, String taskId, WorkResult workResult);
    //endregion
}
