package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.batch;

import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IAfterSaleEncryptOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl.AfterSaleEncryptOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPerBatchProcessOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiEncryptTradeDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 前置批量处理 - 查询密文信息
 *
 * <AUTHOR>
 * @date 2024/12/10 下午2:13
 */
public class BatchQueryEncryptHandle extends AbstractPerBatchProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 常量
    /**
     * api商品匹配仓储(g_api_sysMatch)
     */
    private final IAfterSaleEncryptOperationService afterSaleEncryptOperation = new AfterSaleEncryptOperationService();
    //endregion

    //region 构造
    /**
     * 构造
     *
     * @param context 上下文
     */
    public BatchQueryEncryptHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult perBatchQueryProcess(List<SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem>> orderItems) {
        // 空校验
        if(CollectionUtils.isEmpty(orderItems)){
            return AfterSaleHandleResult.success();
        }

        // 批量查询密文信息
        List<String> afterSaleNos = orderItems.stream().map(SourceAfterSaleOrderItem::getAfterSaleNo).collect(Collectors.toList());
        List<ApiEncryptTradeDO> apiEncryptTrades = afterSaleEncryptOperation.queryEncrypt(context.getMemberName(), context.getShopId(), afterSaleNos);

        // 匹配密文信息
        if(CollectionUtils.isNotEmpty(apiEncryptTrades)){
            // 构建订单级匹配信息
            orderItems.forEach(sourceOrder -> {
                String afterSaleNo = sourceOrder.getAfterSaleNo();
                ApiEncryptTradeDO apiEncryptTrade = apiEncryptTrades.stream().filter(x -> afterSaleNo.equals(x.getTradeNo())).findFirst().orElse(null);
                if(apiEncryptTrade != null){
                    sourceOrder.getDbOrderExt().setApiEncryptTrade(apiEncryptTrade);
                }
            });
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "批量查询g_api_encryptTradeList";
    }
}
