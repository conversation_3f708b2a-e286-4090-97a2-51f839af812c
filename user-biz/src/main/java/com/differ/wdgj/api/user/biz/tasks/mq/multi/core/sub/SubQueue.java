package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub;


import com.differ.wdgj.api.component.util.functional.one.FunctionOne;
import com.differ.wdgj.api.component.util.functional.two.Function;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.DelayGradeEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueResult;

/**
 * 子队列
 *
 * <AUTHOR>
 * @date 2024/3/14 10:56
 */
public interface SubQueue<T> {

    /**
     * 队列当前环境是否可用
     *
     * @return
     */
    boolean currentEnable();

    /**
     * 队列是否触发限流
     *
     * @return
     */
    boolean limited();

    /**
     * 发送数据到当前队列
     *
     * @param message
     * @param header
     * @return 是否发送成功
     */
    boolean sendToCurrentQueue(T message, QueueHeader header);

    /**
     * 注册消息处理器
     *
     * @param dataClazz
     * @param fun
     */
    void initMessageListener(Class<T> dataClazz, Function<QueueResult, T, QueueHeader> fun);

    /**
     * 注册延迟时间函数
     *
     * @param funGetDelay
     */
    void initDelayFun(FunctionOne<DelayGradeEnum, QueueHeader> funGetDelay);

    /**
     * 获取当前队列上下文
     *
     * @return
     */
    SubQueueContext getContext();
}
