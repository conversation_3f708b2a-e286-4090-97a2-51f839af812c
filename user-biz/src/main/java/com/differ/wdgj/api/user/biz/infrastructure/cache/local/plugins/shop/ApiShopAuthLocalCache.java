package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevShopDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopAuthDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopLocalKey;
import org.apache.commons.lang3.RandomUtils;

import java.util.concurrent.TimeUnit;

/**
 * 店铺授权信息内存缓存
 *
 * <AUTHOR>
 * @date 2024/8/19 下午7:09
 */
public class ApiShopAuthLocalCache extends AbstractLocalCache<ApiShopLocalKey, ApiShopAuthDto> {
    //region 构造
    private ApiShopAuthLocalCache() {
        expire = RandomUtils.nextInt(300, 600);
        timeUnit = TimeUnit.SECONDS;
        // 最大数据个数
        cacheMaxSize = 300000;
    }
    //endregion

    //region 构造单例
    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static ApiShopAuthLocalCache singleton() {
        return ApiShopAuthLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final ApiShopAuthLocalCache instance;

        private SingletonEnum() {
            instance = new ApiShopAuthLocalCache();
        }
    }

    //endregion

    // region 公共方法

    /**
     * 获取店铺配置
     *
     * @param outAccount 外部会员名
     * @param shopId     店铺id
     * @return 店铺配置
     */
    public ApiShopAuthDto getInfo(String outAccount, int shopId) {

        // 校验入参
        if (shopId == 0 || outAccount == null) {
            return null;
        }

        // 查询缓存
        return this.getCacheThenSource(ApiShopLocalKey.create(outAccount, shopId));
    }

    // endregion

    //region 重写基类方法

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key 键
     * @return 值
     */
    @Override
    protected ApiShopAuthDto loadSource(ApiShopLocalKey key) {
        if (key != null) {
            DevShopDO shopDO = ApiShopCache.create(key.getOutAccount()).get(key.getShopId());
            if (shopDO != null) {
                // 创建内存缓存对象
                ApiShopAuthDto apiShopDto = new ApiShopAuthDto();
                apiShopDto.setShopId(shopDO.getShopId());
                apiShopDto.setOutShopId(shopDO.getOutShopId());
                apiShopDto.setOutAccount(shopDO.getOutAccount());
                apiShopDto.setToken(shopDO.getToken());
                apiShopDto.setThirdAppKey(shopDO.getThirdAppKey());
                apiShopDto.setThirdAppSecret(shopDO.getThirdAppSecret());
                apiShopDto.setSessionKey(shopDO.getSessionKey());
                apiShopDto.setSessionKeyExpireTime(shopDO.getSessionKeyExpireTime());
                apiShopDto.setSessionKeyTimeout(shopDO.getSessionKeyTimeout());
                apiShopDto.setRefreshToken(shopDO.getRefreshToken());
                apiShopDto.setRefreshTokenExpireTime(shopDO.getRefreshTokenExpireTime());
                apiShopDto.setAuthorizationDuration(shopDO.getAuthorizationDuration());
                apiShopDto.setAuthorizationTime(shopDO.getAuthorizationTime());
                apiShopDto.setSubscriptionExpireTime(shopDO.getSubscriptionExpireTime());
                apiShopDto.setCustomParms(shopDO.getCustomParms());
                apiShopDto.setOtherParms(shopDO.getOtherParms());
                apiShopDto.setPlatShopId(shopDO.getPlatShopId());
                apiShopDto.setPlatShopName(shopDO.getPlatShopName());
                return apiShopDto;
            }
        }
        return null;
    }

    //endregion
}
