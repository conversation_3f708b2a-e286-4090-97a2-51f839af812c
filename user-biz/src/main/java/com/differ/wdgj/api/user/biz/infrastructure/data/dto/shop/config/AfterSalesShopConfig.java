package com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;

import java.util.List;

/**
 * 售后店铺配置实体
 *
 * <AUTHOR>
 * @date 2024-06-26 11:50
 */
public class AfterSalesShopConfig extends BaseShopConfig {
    //region 通用配置
    /**
     * 启用售后单下载
     */
    private boolean isEnableDownload;

    /**
     * 需要下载的售后单类型
     */
    private List<TypeItem> saveTypes;
    //endregion

    //region 业务配置
    /**
     * 速卖通Miravia子店铺Id列表
     */
    private List<String> miraviaShopIds;
    //endregion

    //region get/set
    public boolean isEnableDownload() {
        return isEnableDownload;
    }

    public void setIsEnableDownload(boolean isEnableDownload) {
        this.isEnableDownload = isEnableDownload;
    }

    public List<TypeItem> getSaveTypes() {
        return saveTypes;
    }

    public void setSaveTypes(List<TypeItem> saveTypes) {
        this.saveTypes = saveTypes;
    }

    public List<String> getMiraviaShopIds() {
        return miraviaShopIds;
    }

    public void setMiraviaShopIds(List<String> miraviaShopIds) {
        this.miraviaShopIds = miraviaShopIds;
    }

    //endregion

    //region 内部类

    /**
     * 店铺类型 + 售后类型
     */
    public static class TypeItem {
        /**
         * 业务类型(店铺类型)
         * {@link ShopTypeEnum}
         */
        private String bizType;

        /**
         * 售后单类型
         * {@link ApiAfterSaleTypeEnum}
         */
        private String bizValue;

        //region get/set
        public String getBizType() {
            return bizType;
        }

        public void setBizType(String bizType) {
            this.bizType = bizType;
        }

        public String getBizValue() {
            return bizValue;
        }
        //endregion

        public void setBizValue(String bizValue) {
            this.bizValue = bizValue;
        }
    }
    //endregion
}
