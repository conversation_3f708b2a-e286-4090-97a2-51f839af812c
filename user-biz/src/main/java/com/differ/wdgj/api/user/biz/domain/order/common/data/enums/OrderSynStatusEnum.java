package com.differ.wdgj.api.user.biz.domain.order.common.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

import java.util.Arrays;
import java.util.List;

/**
 * 订单发货同步状态
 *
 * <AUTHOR>
 * @date 2025/6/19 11:46
 */
public enum OrderSynStatusEnum implements ValueEnum {
    WAIT_SYNC(0, "待同步"),
    READY(1, "就绪"),
    HAND_FAIL(2, "手动同步失败"),
    WAIT_CHECK(3, "待校验"),
    SUCCESS(4, "成功"),
    CANCEL(5, "取消"),
    AUTO_FAIL(7, "自动同步失败"),
    ONLINE_WAIT_OK(8, "在线下单等确认"),
    SPLIT_UN_OUT(9, "拆单未出库"),
    SPLIT_OUT(10, "拆单出库"),
    SPLIT_SEND_FAIL(11, "拆单发货失败"),
    SYNCING(200, "发货同步中"),
    WAIT_CHECK_LOGISTIC(101, "待上传确认"),
    LOGISTIC_SUCCESS(102, "上传物流完成"),
    BIC_MERGE_WAITING(103, "合单待上传");;

    //region 常量
    /**
     * 值
     */
    private final int value;

    /**
     * 描述
     */
    private final String description;

    //endregion

    //region 构造
    OrderSynStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }
    //endregion

    //region 公共方法
    @Override
    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 获取发货成功状态列表
     *
     * @return 发货成功状态列表
     */
    public List<OrderSynStatusEnum> getSuccessSyncStatusList() {
        return Arrays.asList(SUCCESS, LOGISTIC_SUCCESS);
    }

    /**
     * 获取发货失败状态列表
     *
     * @return 发货失败状态列表
     */
    public List<OrderSynStatusEnum> getFailedSyncStatusList() {
        return Arrays.asList(HAND_FAIL, AUTO_FAIL, SPLIT_SEND_FAIL);
    }
    //endregion
}
