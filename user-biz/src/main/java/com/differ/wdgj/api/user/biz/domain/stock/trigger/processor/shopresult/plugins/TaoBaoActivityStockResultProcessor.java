package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.stock.data.task.ActivityGoodsTimeValueObject;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.data.task.OrderUpdateBusinessTypesEnum;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeBusinessUpdateDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiTradeBusinessUpdateMapper;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 库存同步结果 - 淘宝活动商品
 * <AUTHOR>
 * @date 2024-03-20 18:49
 */
public class TaoBaoActivityStockResultProcessor extends AbstractStockResultProcessor {
    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "淘宝活动商品";
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 构建业务触发数据
        Set<ApiTradeBusinessUpdateDO> apiTradeBusinessUpdateDos = buildApiTradeBusinessUpdateDO(context.getShopId(), resultPackage);

        // 批量插入业务触发表 - 批量更新匹配信息活动结束时间（后续移入定时任务）
        if(CollectionUtils.isNotEmpty(apiTradeBusinessUpdateDos)){
            doDataOperate(context.getVipUser(), context.getShopId(), new ArrayList<>(apiTradeBusinessUpdateDos));
        }
    }

    //region 私有方法
    /**
     * 构建业务触发数据
     * @param outShopId          内部店铺Id
     * @param resultPackage    库存同步结果包
     * @return 业务触发数据
     */
    private Set<ApiTradeBusinessUpdateDO> buildApiTradeBusinessUpdateDO(Integer outShopId, StockSyncResultPackage resultPackage){

        Set<ApiTradeBusinessUpdateDO> apiTradeBusinessUpdateDos = new HashSet<>();

        // 获取活动时间偏移量
        int nextResetSyncTime = Integer.parseInt(ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.NextResetSyncTime_ActivityProduct));

        // 遍历失败结果
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (composite == null) {
                return;
            }

            // 活动中商品异常
            if(SyncStockPolyErrorCodeEnum.STOCK_LOCK.equals(composite.getResponseErrorCode())){
                // 构建业务触发数据 - 平台商品级别
                ApiTradeBusinessUpdateDO apiTradeBusinessUpdateDO = new ApiTradeBusinessUpdateDO();
                apiTradeBusinessUpdateDO.setBusinessType(OrderUpdateBusinessTypesEnum.TaobaoUpdateActivityGoodsTime.getValue());
                apiTradeBusinessUpdateDO.setShopId(outShopId);
                apiTradeBusinessUpdateDO.setBillId(idEnhance.getMatchId());
                apiTradeBusinessUpdateDO.setTradeNo(composite.getPlatResponse().getPlatProductId());
                apiTradeBusinessUpdateDO.setExtendInfo(JsonUtils.toNullNumJson(convertBusinessExtendInfo(composite.getPlatResponse().getActivityEndTime(), nextResetSyncTime)));

                // 加入列表
                apiTradeBusinessUpdateDos.add(apiTradeBusinessUpdateDO);
            }
        });

        return apiTradeBusinessUpdateDos;
    }

    /**
     * 转换业务扩展数据
     * @param activityTime 活动时间
     * @param defaultSecond 默认推进时间
     * @return 业务扩展数据
     */
    private ActivityGoodsTimeValueObject convertBusinessExtendInfo(LocalDateTime activityTime, int defaultSecond){
        ActivityGoodsTimeValueObject activityGoodsTime = new ActivityGoodsTimeValueObject();
        LocalDateTime activityEndTime = activityTime != null
                ? activityTime
                : LocalDateTime.now().plusSeconds(defaultSecond);
        activityGoodsTime.setActivityEndTime(activityEndTime);

        return activityGoodsTime;
    }

    /**
     * 批量插入业务触发表 - 批量更新匹配信息活动结束时间（后续移入定时任务）
     * @param vipUser 会员名
     * @param apiTradeBusinessUpdateDos 业务触发数据
     */
    private void doDataOperate(String vipUser, Integer shopId, List<ApiTradeBusinessUpdateDO> apiTradeBusinessUpdateDos){
        ApiTradeBusinessUpdateMapper apiTradeBusinessUpdateMapper = BeanContextUtil.getBean(ApiTradeBusinessUpdateMapper.class);
        DBSwitchUtil.doDBWithUser(vipUser, () -> {
            // 验证数据库是否存在
            Integer businessType = OrderUpdateBusinessTypesEnum.TaobaoUpdateActivityGoodsTime.getValue();
            List<String> needCheckTradeNos = apiTradeBusinessUpdateDos.stream().map(ApiTradeBusinessUpdateDO::getTradeNo).collect(Collectors.toList());
            List<String> existTradeNos = apiTradeBusinessUpdateMapper.existTradeNos(businessType, shopId, needCheckTradeNos);

            // 过滤重复数据量
            List<ApiTradeBusinessUpdateDO> needInsertDos = apiTradeBusinessUpdateDos;
            if(CollectionUtils.isNotEmpty(existTradeNos)){
                needInsertDos = apiTradeBusinessUpdateDos.stream().filter(x -> !existTradeNos.contains(x.getTradeNo())).collect(Collectors.toList());
            }

            // 批量插入业务触发表
            if(CollectionUtils.isNotEmpty(needInsertDos)){
                apiTradeBusinessUpdateMapper.batchAdd(needInsertDos);
            }

        });
    }

    //endregion
}
