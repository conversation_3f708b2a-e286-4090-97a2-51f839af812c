package com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.PageDynamicStatus;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubPageTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.TaskSplitStrategyContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * 动态拆分方式
 *
 * <AUTHOR>
 * @date 2024/7/12 13:53
 */
public class DynamicTaskSplitStrategy implements TaskSplitStrategy<SubPageTask> {

    // region 构造器

    public DynamicTaskSplitStrategy(TaskSplitStrategyContext context) {
        this.context = context;
    }

    // endregion

    // region 变量

    /**
     * 任务拆分策略上下文
     */
    private final TaskSplitStrategyContext context;

    // endregion

    /**
     * 拆分下一个任务
     *
     * @param lastSubTask 上次的子任务
     * @param first       首次拆
     * @return 返回拆分子任务
     */
    @Override
    public SubPageTask splitNext(SubPageTask lastSubTask, boolean first) {

        if (first || StringUtils.isBlank(lastSubTask.getSourceTimeType())) {
            // 时间类型为空 不需要进行拆分
            return new NoTaskSplitStrategy(this.context).splitNext(lastSubTask, first);
        } else {
            // 上次拆分子任务的动态拆分信息
            PageDynamicStatus lastPageDynamicStatus = lastSubTask.getDynamicStatus();

            // 如果最后的业务数据时间为空(平台没有返回业务数据时间，不支持拆分) 不需要拆分，直接返回null结束拆分任务
            if (null == lastPageDynamicStatus.getLastSuccessDataTime()) {
                return null;
            }

            // 当前下载的数据量 小于 pageSize需要停止拆分
            if (lastPageDynamicStatus.getDataSize() < lastSubTask.getPageSize()) {
                return null;
            }

            // 本次拆分子任务的动态拆分信息
            PageDynamicStatus newPageDynamicStatus = new PageDynamicStatus();
            // 重置页码、运行状态
            newPageDynamicStatus.setPageIndex(1);
            newPageDynamicStatus.setPageSize(lastSubTask.getPageSize());
            newPageDynamicStatus.setRunStatus(RunStatusEnum.RUNNING);
            newPageDynamicStatus.setTimeType(lastPageDynamicStatus.getTimeType());

            // 本次拆分子任务的请求开始时间=上次拆分子任务最后数据的业务时间-偏移量
            int offset = context == null ? 0 : context.getOffset();
            newPageDynamicStatus.setLoadStartTime(lastPageDynamicStatus.getLastSuccessDataTime().minusSeconds(offset));

            // 结束时间为空的情况
            if (null == lastPageDynamicStatus.getLoadEndTime()) {
                if (null == lastSubTask.getTaskSourceEndTime()) {
                    newPageDynamicStatus.setLoadEndTime(LocalDateTime.now());
                } else {
                    newPageDynamicStatus.setLoadEndTime(lastSubTask.getTaskSourceEndTime());
                }
            } else {
                newPageDynamicStatus.setLoadEndTime(lastPageDynamicStatus.getLoadEndTime());
            }
            lastSubTask.setDynamicStatus(newPageDynamicStatus);
        }
        return lastSubTask;
    }
}
