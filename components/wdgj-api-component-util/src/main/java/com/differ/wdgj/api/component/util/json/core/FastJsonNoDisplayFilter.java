package com.differ.wdgj.api.component.util.json.core;

import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.ValueFilter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * fastjson过滤器：无实际意义的值不显示
 *      1.null不显示
 *      2.空字符串不显示
 * <AUTHOR>
 * @date 2024/2/18 11:43
 */
public class FastJsonNoDisplayFilter implements PropertyFilter {


    /**
     * @param object the owner of the property
     * @param name   the name of the property
     * @param value  the value of the property
     * @return true if the property will be included, false if to be filtered out
     */
    @Override
    public boolean apply(Object object, String name, Object value) {
        // null不显示
        if (null == value) {
            return false;
        }

        // 空字符串不显示
        if (value instanceof String && StringUtils.isEmpty((String)value)) {
            return false;
        }

        return true;
    }
}
