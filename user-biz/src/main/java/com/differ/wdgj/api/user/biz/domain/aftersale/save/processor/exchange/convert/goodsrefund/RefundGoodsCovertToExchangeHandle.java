package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsrefund;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractRefundGoodsConvertHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 换货单-退货商品基础数据转换插件
 *
 * <AUTHOR>
 * @date 2024/8/8 下午2:44
 */
public class RefundGoodsCovertToExchangeHandle extends AbstractRefundGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseRefundGoodInfo> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public RefundGoodsCovertToExchangeHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 重写基类方法

    /**
     * 转换商品级信息
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    public GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, SourceRefundGoodsItem<BusinessGetExchangeResponseRefundGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        ApiTradeGoodsDO apiTradeGoods = goodsItem.getApiTradeGoods();
        BusinessGetExchangeOrderResponseOrderItem ployOrder = orderItem.getPloyOrder();
        BusinessGetExchangeResponseRefundGoodInfo polyRefundGoods = goodsItem.getPloyRefundGoods();

        // 商品基础信息赋值
        refundGoods.setOuterId(StringUtils.defaultIfEmpty(polyRefundGoods.getOutSkuId(), polyRefundGoods.getOuterId()));
        refundGoods.setGoodsTitle(polyRefundGoods.getProductName());
        refundGoods.setSku(polyRefundGoods.getSkuSpec());
        refundGoods.setPlatGoodsId(polyRefundGoods.getPlatProductId());
        refundGoods.setPlatSkuId(polyRefundGoods.getSku());
        refundGoods.setOid(polyRefundGoods.getSubTradeNo());
        refundGoods.setGoodsCount(BigDecimal.valueOf(polyRefundGoods.getProductNum()));
        refundGoods.setPrice(BigDecimal.ZERO);
        refundGoods.setRemark(AfterSaleCovertUtils.replaceEmoji(ployOrder.getReason()));

        //原始单信息兼容
        if (apiTradeGoods != null) {
            // 商品编码
            refundGoods.setOuterId(StringUtils.defaultIfEmpty(refundGoods.getOuterId(), apiTradeGoods.getTradeGoodsNO()));
            // 商品名称
            refundGoods.setGoodsTitle(StringUtils.defaultIfEmpty(refundGoods.getGoodsTitle(), apiTradeGoods.getTradeGoodsName()));
            // 商品规格名称
            refundGoods.setSku(StringUtils.defaultIfEmpty(refundGoods.getSku(), apiTradeGoods.getTradeGoodsSpec()));
            // 平台商品id
            refundGoods.setPlatGoodsId(StringUtils.defaultIfEmpty(refundGoods.getPlatGoodsId(), apiTradeGoods.getPlatGoodsID()));
            // 平台规格id
            refundGoods.setPlatSkuId(StringUtils.defaultIfEmpty(refundGoods.getPlatSkuId(), apiTradeGoods.getPlatSkuID()));
        }

        // 历史主键
        if (CollectionUtils.isNotEmpty(orderItem.getDbOrder().getReturnGoods())) {
            ApiReturnDetailDO oldRefundGoods = orderItem.getDbOrder().getReturnGoods().stream().filter(x -> AfterSaleGoodsMatchOperation.isMatchHistoryRefundGoods(refundGoods, x)).findFirst().orElse(null);
            if (oldRefundGoods != null) {
                refundGoods.setRecId(oldRefundGoods.getRecId());
            }
        }

        return GoodsConvertHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "换货单退货商品基础数据转换";
    }
    //endregion
}
