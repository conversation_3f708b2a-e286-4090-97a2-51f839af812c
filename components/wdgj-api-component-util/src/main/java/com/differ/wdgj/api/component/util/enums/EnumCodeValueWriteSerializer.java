package com.differ.wdgj.api.component.util.enums;

import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.ObjectSerializer;
import com.alibaba.fastjson.serializer.SerializeWriter;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * @Description fastJson枚举值正向序列化器
 * 使用示例：枚举字段添加注解：@JSONField(serializeUsing = EnumCodeValueWriteSerializer.class)
 * <AUTHOR>
 * @Date 2020-03-18 17:03
 */
public class EnumCodeValueWriteSerializer implements ObjectSerializer {
    @Override
    public void write(JSONSerializer serializer, Object object, Object fieldName, Type fieldType, int features) throws IOException {
        SerializeWriter out = serializer.out;
        Class clazz = (Class) fieldType;
        if (ValueEnum.class.isAssignableFrom(clazz)) {
            ValueEnum valueEnum = (ValueEnum) object;
            out.writeString(valueEnum.getValue().toString());
            return;
        }

        if (CodeEnum.class.isAssignableFrom(clazz)) {
            CodeEnum codeEnum = (CodeEnum) object;
            out.writeString(codeEnum.getCode());
            return;
        }

        if (NameEnum.class.isAssignableFrom(clazz)) {
            NameEnum nameEnum = (NameEnum) object;
            out.writeString(nameEnum.getName());
            return;
        }
    }
}
