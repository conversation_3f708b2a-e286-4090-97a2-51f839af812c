package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 平台业务特性 - 下载订单模式
 *
 * <AUTHOR>
 * @date 2024-06-24 14:37
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum LoadOrderModeEnum implements ValueEnum {

    /**
     * 全量下载
     */
    FULL("全量下载", 0),

    /**
     * 增量下载
     */
    INCREMENT("增量下载", 1);

    /**
     * 枚举项的名称
     */
    private final String name;

    /**
     * 枚举项的值
     */
    private final int value;

    /**
     * 构造方法
     *
     * @param name  枚举项的名称
     * @param value 枚举项的值
     */
    LoadOrderModeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 获取枚举项的名称
     *
     * @return 枚举项的名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取枚举项的值
     *
     * @return 枚举项的值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static LoadOrderModeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, LoadOrderModeEnum.class, EnumConvertType.VALUE);
    }
}
