package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 淘宝交易订单详情响应数据
 * 对应表jdp_tb_trade字段jdp_response
 * <a href="https://open.taobao.com/v2/doc?spm=a219a.15212433.0.0.7d69669apbLtn4#/apiFile?docType=2&docId=54">平台文档</a>
 *
 * <AUTHOR>
 * @date 2025/6/26 09:58
 */
public class TbTradeJdpResponseDto {

    @JsonProperty("trade_fullinfo_get_response")
    private TradeFullInfoGetResponse tradeFullInfoGetResponse;

    public TradeFullInfoGetResponse getTradeFullInfoGetResponse() {
        return tradeFullInfoGetResponse;
    }

    public void setTradeFullInfoGetResponse(TradeFullInfoGetResponse tradeFullInfoGetResponse) {
        this.tradeFullInfoGetResponse = tradeFullInfoGetResponse;
    }

    public static class TradeFullInfoGetResponse {
        @JsonProperty("trade")
        private Trade trade;

        public Trade getTrade() {
            return trade;
        }

        public void setTrade(Trade trade) {
            this.trade = trade;
        }
    }

    //region 淘宝交易详情

    /**
     * 淘宝交易详情
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class Trade {
        /**
         * 买家的openUid
         */
        @JsonProperty("buyer_open_uid")
        private String buyerOpenUid;

        /**
         * 交易标题
         */
        @JsonProperty("title")
        private String title;

        /**
         * 交易类型
         */
        @JsonProperty("type")
        private String type;

        /**
         * 创建时间
         */
        @JsonProperty("created")
        private String created;

        /**
         * 卖家ID
         */
        @JsonProperty("sid")
        private String sid;

        /**
         * 交易ID
         */
        @JsonProperty("tid")
        private Long tid;

        /**
         * cookie ID
         */
        @JsonProperty("acookie_id")
        private String acookieId;

        /**
         * 卖家是否已评价
         */
        @JsonProperty("seller_rate")
        private Boolean sellerRate;

        /**
         * 买家是否已评价
         */
        @JsonProperty("buyer_rate")
        private Boolean buyerRate;

        /**
         * 交易状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 实付金额
         */
        @JsonProperty("payment")
        private String payment;

        /**
         * 折扣金额
         */
        @JsonProperty("discount_fee")
        private String discountFee;

        /**
         * 调整金额
         */
        @JsonProperty("adjust_fee")
        private String adjustFee;

        /**
         * 邮费
         */
        @JsonProperty("post_fee")
        private String postFee;

        /**
         * 总金额
         */
        @JsonProperty("total_fee")
        private String totalFee;

        /**
         * 付款时间
         */
        @JsonProperty("pay_time")
        private String payTime;

        /**
         * 交易结束时间
         */
        @JsonProperty("end_time")
        private String endTime;

        /**
         * 最后修改时间
         */
        @JsonProperty("modified")
        private String modified;

        /**
         * 卖家发货时间
         */
        @JsonProperty("consign_time")
        private String consignTime;

        /**
         * 收到的付款
         */
        @JsonProperty("received_payment")
        private String receivedPayment;

        /**
         * 佣金
         */
        @JsonProperty("commission_fee")
        private String commissionFee;

        /**
         * 买家备忘
         */
        @JsonProperty("buyer_memo")
        private String buyerMemo;

        /**
         * 卖家备忘
         */
        @JsonProperty("seller_memo")
        private String sellerMemo;

        /**
         * 买家地区
         */
        @JsonProperty("buyer_area")
        private String buyerArea;

        /**
         * 支付宝交易号
         */
        @JsonProperty("alipay_no")
        private String alipayNo;

        /**
         * 买家留言
         */
        @JsonProperty("buyer_message")
        private String buyerMessage;

        /**
         * 商品图片路径
         */
        @JsonProperty("pic_path")
        private String picPath;

        /**
         * 商品数字ID
         */
        @JsonProperty("num_iid")
        private Long numIid;

        /**
         * 商品价格
         */
        @JsonProperty("price")
        private String price;

        /**
         * 货到付款服务费
         */
        @JsonProperty("cod_fee")
        private String codFee;

        /**
         * 货到付款状态
         */
        @JsonProperty("cod_status")
        private String codStatus;

        /**
         * 买家货到付款服务费
         */
        @JsonProperty("buyer_cod_fee")
        private String buyerCodFee;

        /**
         * 卖家货到付款服务费
         */
        @JsonProperty("seller_cod_fee")
        private String sellerCodFee;

        /**
         * 快递代收款
         */
        @JsonProperty("express_agency_fee")
        private String expressAgencyFee;

        /**
         * 运送方式
         */
        @JsonProperty("shipping_type")
        private String shippingType;

        /**
         * 商品数量
         */
        @JsonProperty("num")
        private Integer num;

        /**
         * 使用的积分
         */
        @JsonProperty("point_fee")
        private Integer pointFee;

        /**
         * 实际使用积分
         */
        @JsonProperty("real_point_fee")
        private Integer realPointFee;

        /**
         * 买家获得积分
         */
        @JsonProperty("buyer_obtain_point_fee")
        private Integer buyerObtainPointFee;

        /**
         * 买家支付宝账号
         */
        @JsonProperty("buyer_alipay_no")
        private String buyerAlipayNo;

        /**
         * 收货人姓名
         */
        @JsonProperty("receiver_name")
        private String receiverName;

        /**
         * 收货人国家
         */
        @JsonProperty("receiver_country")
        private String receiverCountry;

        /**
         * 收货人省份
         */
        @JsonProperty("receiver_state")
        private String receiverState;

        /**
         * 收货人城市
         */
        @JsonProperty("receiver_city")
        private String receiverCity;

        /**
         * 收货人区县
         */
        @JsonProperty("receiver_district")
        private String receiverDistrict;

        /**
         * 收货人街道/镇
         */
        @JsonProperty("receiver_town")
        private String receiverTown;

        /**
         * 收货人详细地址
         */
        @JsonProperty("receiver_address")
        private String receiverAddress;

        /**
         * 收货人邮编
         */
        @JsonProperty("receiver_zip")
        private String receiverZip;

        /**
         * 收货人手机
         */
        @JsonProperty("receiver_mobile")
        private String receiverMobile;

        /**
         * 收货人电话
         */
        @JsonProperty("receiver_phone")
        private String receiverPhone;

        /**
         * 买家邮箱
         */
        @JsonProperty("buyer_email")
        private String buyerEmail;

        /**
         * 卖家支付宝账号
         */
        @JsonProperty("seller_alipay_no")
        private String sellerAlipayNo;

        /**
         * 子订单列表
         */
        @JsonProperty("orders")
        private Orders orders;

        /**
         * 优惠详情
         */
        @JsonProperty("promotion_details")
        private PromotionDetails promotionDetails;

        /**
         * 服务标签
         */
        @JsonProperty("service_tags")
        private ServiceTags serviceTags;

        /**
         * 服务子订单列表
         */
        @JsonProperty("service_orders")
        private ServiceOrders serviceOrders;

        /**
         * 配送计划
         */
        @JsonProperty("delivery_plan")
        private DeliveryPlan deliveryPlan;

        /**
         * 组合物流详情
         */
        @JsonProperty("combine_logistics_details")
        private CombineLogisticsDetails combineLogisticsDetails;

        /**
         * 备注操作信息
         */
        @JsonProperty("memo_operator_infos")
        private MemoOperatorInfos memoOperatorInfos;

        /**
         * 是否部分发货
         */
        @JsonProperty("is_part_consign")
        private Boolean isPartConsign;

        /**
         * 是否代销订单
         */
        @JsonProperty("is_daixiao")
        private Boolean isDaixiao;

        /**
         * 是否网厅订单
         */
        @JsonProperty("is_wt")
        private Boolean isWt;

        /**
         * 送达间隔
         */
        @JsonProperty("arrive_interval")
        private Integer arriveInterval;

        /**
         * 送达截止时间
         */
        @JsonProperty("arrive_cut_time")
        private String arriveCutTime;

        /**
         * 发货间隔
         */
        @JsonProperty("consign_interval")
        private Integer consignInterval;

        /**
         * O2O信息
         */
        @JsonProperty("o2o")
        private String o2o;

        /**
         * O2O导购ID
         */
        @JsonProperty("o2o_guide_id")
        private String o2oGuideId;

        /**
         * O2O导购名称
         */
        @JsonProperty("o2o_guide_name")
        private String o2oGuideName;

        /**
         * O2O店铺ID
         */
        @JsonProperty("o2o_shop_id")
        private String o2oShopId;

        /**
         * O2O店铺名称
         */
        @JsonProperty("o2o_shop_name")
        private String o2oShopName;

        /**
         * O2O配送方式
         */
        @JsonProperty("o2o_delivery")
        private String o2oDelivery;

        /**
         * O2O外部交易ID
         */
        @JsonProperty("o2o_out_trade_id")
        private String o2oOutTradeId;

        /**
         * 店铺编码
         */
        @JsonProperty("shop_code")
        private String shopCode;

        /**
         * 是否海外订单
         */
        @JsonProperty("is_sh_ship")
        private Boolean isShShip;

        /**
         * 是否强制配送中心
         */
        @JsonProperty("is_force_dc")
        private Boolean isForceDc;

        /**
         * 是否周期购
         */
        @JsonProperty("is_cycle_buy")
        private Boolean isCycleBuy;

        /**
         * 是否工业企业
         */
        @JsonProperty("is_inds_qyg")
        private Boolean isIndsQyg;

        /**
         * 是否MSS
         */
        @JsonProperty("is_mss")
        private Boolean isMss;

        /**
         * 是否支持找刻
         */
        @JsonProperty("is_zk_identify")
        private Boolean isZkIdentify;

        /**
         * 是否国家机关
         */
        @JsonProperty("is_gfjy")
        private Boolean isGfjy;

        /**
         * 政府序列号检查
         */
        @JsonProperty("gov_sn_check")
        private String govSnCheck;

        /**
         * 青牛配送
         */
        @JsonProperty("qn_distr")
        private String qnDistr;

        /**
         * 政府主体
         */
        @JsonProperty("gov_main_subject")
        private String govMainSubject;

        /**
         * 使用政府补贴
         */
        @JsonProperty("use_gov_subsidy")
        private String useGovSubsidy;

        /**
         * 政府补贴金额
         */
        @JsonProperty("gov_subsidy_amount")
        private String govSubsidyAmount;

        /**
         * 使用政府预测
         */
        @JsonProperty("use_gov_predict")
        private String useGovPredict;

        /**
         * 政府补贴精确金额
         */
        @JsonProperty("gov_subsidy_amount_exact")
        private String govSubsidyAmountExact;

        /**
         * 以旧换新
         */
        @JsonProperty("oldfornew")
        private String oldfornew;

        /**
         * 政府商店
         */
        @JsonProperty("gov_store")
        private String govStore;

        /**
         * 使用新政府补贴
         */
        @JsonProperty("use_gov_subsidy_new")
        private String useGovSubsidyNew;

        /**
         * 新政府补贴金额
         */
        @JsonProperty("gov_subsidy_amount_new")
        private String govSubsidyAmountNew;

        /**
         * 政府支付类型
         */
        @JsonProperty("gov_pay_type")
        private String govPayType;

        /**
         * 政府补贴类型
         */
        @JsonProperty("gov_subsidy_type")
        private String govSubsidyType;

        /**
         * 政府补贴类型额外信息
         */
        @JsonProperty("gov_subsidy_type_extra")
        private String govSubsidyTypeExtra;

        // Getters and Setters
        public String getBuyerOpenUid() {
            return buyerOpenUid;
        }

        public void setBuyerOpenUid(String buyerOpenUid) {
            this.buyerOpenUid = buyerOpenUid;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getCreated() {
            return created;
        }

        public void setCreated(String created) {
            this.created = created;
        }

        public String getSid() {
            return sid;
        }

        public void setSid(String sid) {
            this.sid = sid;
        }

        public Long getTid() {
            return tid;
        }

        public void setTid(Long tid) {
            this.tid = tid;
        }

        public String getAcookieId() {
            return acookieId;
        }

        public void setAcookieId(String acookieId) {
            this.acookieId = acookieId;
        }

        public Boolean getSellerRate() {
            return sellerRate;
        }

        public void setSellerRate(Boolean sellerRate) {
            this.sellerRate = sellerRate;
        }

        public Boolean getBuyerRate() {
            return buyerRate;
        }

        public void setBuyerRate(Boolean buyerRate) {
            this.buyerRate = buyerRate;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getPayment() {
            return payment;
        }

        public void setPayment(String payment) {
            this.payment = payment;
        }

        public String getDiscountFee() {
            return discountFee;
        }

        public void setDiscountFee(String discountFee) {
            this.discountFee = discountFee;
        }

        public String getAdjustFee() {
            return adjustFee;
        }

        public void setAdjustFee(String adjustFee) {
            this.adjustFee = adjustFee;
        }

        public String getPostFee() {
            return postFee;
        }

        public void setPostFee(String postFee) {
            this.postFee = postFee;
        }

        public String getTotalFee() {
            return totalFee;
        }

        public void setTotalFee(String totalFee) {
            this.totalFee = totalFee;
        }

        public String getPayTime() {
            return payTime;
        }

        public void setPayTime(String payTime) {
            this.payTime = payTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public String getConsignTime() {
            return consignTime;
        }

        public void setConsignTime(String consignTime) {
            this.consignTime = consignTime;
        }

        public String getReceivedPayment() {
            return receivedPayment;
        }

        public void setReceivedPayment(String receivedPayment) {
            this.receivedPayment = receivedPayment;
        }

        public String getCommissionFee() {
            return commissionFee;
        }

        public void setCommissionFee(String commissionFee) {
            this.commissionFee = commissionFee;
        }

        public String getBuyerMemo() {
            return buyerMemo;
        }

        public void setBuyerMemo(String buyerMemo) {
            this.buyerMemo = buyerMemo;
        }

        public String getSellerMemo() {
            return sellerMemo;
        }

        public void setSellerMemo(String sellerMemo) {
            this.sellerMemo = sellerMemo;
        }

        public String getBuyerArea() {
            return buyerArea;
        }

        public void setBuyerArea(String buyerArea) {
            this.buyerArea = buyerArea;
        }

        public String getAlipayNo() {
            return alipayNo;
        }

        public void setAlipayNo(String alipayNo) {
            this.alipayNo = alipayNo;
        }

        public String getBuyerMessage() {
            return buyerMessage;
        }

        public void setBuyerMessage(String buyerMessage) {
            this.buyerMessage = buyerMessage;
        }

        public String getPicPath() {
            return picPath;
        }

        public void setPicPath(String picPath) {
            this.picPath = picPath;
        }

        public Long getNumIid() {
            return numIid;
        }

        public void setNumIid(Long numIid) {
            this.numIid = numIid;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getCodFee() {
            return codFee;
        }

        public void setCodFee(String codFee) {
            this.codFee = codFee;
        }

        public String getCodStatus() {
            return codStatus;
        }

        public void setCodStatus(String codStatus) {
            this.codStatus = codStatus;
        }

        public String getBuyerCodFee() {
            return buyerCodFee;
        }

        public void setBuyerCodFee(String buyerCodFee) {
            this.buyerCodFee = buyerCodFee;
        }

        public String getSellerCodFee() {
            return sellerCodFee;
        }

        public void setSellerCodFee(String sellerCodFee) {
            this.sellerCodFee = sellerCodFee;
        }

        public String getExpressAgencyFee() {
            return expressAgencyFee;
        }

        public void setExpressAgencyFee(String expressAgencyFee) {
            this.expressAgencyFee = expressAgencyFee;
        }

        public String getShippingType() {
            return shippingType;
        }

        public void setShippingType(String shippingType) {
            this.shippingType = shippingType;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public Integer getPointFee() {
            return pointFee;
        }

        public void setPointFee(Integer pointFee) {
            this.pointFee = pointFee;
        }

        public Integer getRealPointFee() {
            return realPointFee;
        }

        public void setRealPointFee(Integer realPointFee) {
            this.realPointFee = realPointFee;
        }

        public Integer getBuyerObtainPointFee() {
            return buyerObtainPointFee;
        }

        public void setBuyerObtainPointFee(Integer buyerObtainPointFee) {
            this.buyerObtainPointFee = buyerObtainPointFee;
        }

        public String getBuyerAlipayNo() {
            return buyerAlipayNo;
        }

        public void setBuyerAlipayNo(String buyerAlipayNo) {
            this.buyerAlipayNo = buyerAlipayNo;
        }

        public String getReceiverName() {
            return receiverName;
        }

        public void setReceiverName(String receiverName) {
            this.receiverName = receiverName;
        }

        public String getReceiverCountry() {
            return receiverCountry;
        }

        public void setReceiverCountry(String receiverCountry) {
            this.receiverCountry = receiverCountry;
        }

        public String getReceiverState() {
            return receiverState;
        }

        public void setReceiverState(String receiverState) {
            this.receiverState = receiverState;
        }

        public String getReceiverCity() {
            return receiverCity;
        }

        public void setReceiverCity(String receiverCity) {
            this.receiverCity = receiverCity;
        }

        public String getReceiverDistrict() {
            return receiverDistrict;
        }

        public void setReceiverDistrict(String receiverDistrict) {
            this.receiverDistrict = receiverDistrict;
        }

        public String getReceiverTown() {
            return receiverTown;
        }

        public void setReceiverTown(String receiverTown) {
            this.receiverTown = receiverTown;
        }

        public String getReceiverAddress() {
            return receiverAddress;
        }

        public void setReceiverAddress(String receiverAddress) {
            this.receiverAddress = receiverAddress;
        }

        public String getReceiverZip() {
            return receiverZip;
        }

        public void setReceiverZip(String receiverZip) {
            this.receiverZip = receiverZip;
        }

        public String getReceiverMobile() {
            return receiverMobile;
        }

        public void setReceiverMobile(String receiverMobile) {
            this.receiverMobile = receiverMobile;
        }

        public String getReceiverPhone() {
            return receiverPhone;
        }

        public void setReceiverPhone(String receiverPhone) {
            this.receiverPhone = receiverPhone;
        }

        public String getBuyerEmail() {
            return buyerEmail;
        }

        public void setBuyerEmail(String buyerEmail) {
            this.buyerEmail = buyerEmail;
        }

        public String getSellerAlipayNo() {
            return sellerAlipayNo;
        }

        public void setSellerAlipayNo(String sellerAlipayNo) {
            this.sellerAlipayNo = sellerAlipayNo;
        }

        public Orders getOrders() {
            return orders;
        }

        public void setOrders(Orders orders) {
            this.orders = orders;
        }

        public PromotionDetails getPromotionDetails() {
            return promotionDetails;
        }

        public void setPromotionDetails(PromotionDetails promotionDetails) {
            this.promotionDetails = promotionDetails;
        }

        public ServiceTags getServiceTags() {
            return serviceTags;
        }

        public void setServiceTags(ServiceTags serviceTags) {
            this.serviceTags = serviceTags;
        }

        public ServiceOrders getServiceOrders() {
            return serviceOrders;
        }

        public void setServiceOrders(ServiceOrders serviceOrders) {
            this.serviceOrders = serviceOrders;
        }

        public DeliveryPlan getDeliveryPlan() {
            return deliveryPlan;
        }

        public void setDeliveryPlan(DeliveryPlan deliveryPlan) {
            this.deliveryPlan = deliveryPlan;
        }

        public CombineLogisticsDetails getCombineLogisticsDetails() {
            return combineLogisticsDetails;
        }

        public void setCombineLogisticsDetails(CombineLogisticsDetails combineLogisticsDetails) {
            this.combineLogisticsDetails = combineLogisticsDetails;
        }

        public MemoOperatorInfos getMemoOperatorInfos() {
            return memoOperatorInfos;
        }

        public void setMemoOperatorInfos(MemoOperatorInfos memoOperatorInfos) {
            this.memoOperatorInfos = memoOperatorInfos;
        }

        public Boolean getPartConsign() {
            return isPartConsign;
        }

        public void setPartConsign(Boolean partConsign) {
            isPartConsign = partConsign;
        }

        public Boolean getDaixiao() {
            return isDaixiao;
        }

        public void setDaixiao(Boolean daixiao) {
            isDaixiao = daixiao;
        }

        public Boolean getWt() {
            return isWt;
        }

        public void setWt(Boolean wt) {
            isWt = wt;
        }

        public Integer getArriveInterval() {
            return arriveInterval;
        }

        public void setArriveInterval(Integer arriveInterval) {
            this.arriveInterval = arriveInterval;
        }

        public String getArriveCutTime() {
            return arriveCutTime;
        }

        public void setArriveCutTime(String arriveCutTime) {
            this.arriveCutTime = arriveCutTime;
        }

        public Integer getConsignInterval() {
            return consignInterval;
        }

        public void setConsignInterval(Integer consignInterval) {
            this.consignInterval = consignInterval;
        }

        public String getO2o() {
            return o2o;
        }

        public void setO2o(String o2o) {
            this.o2o = o2o;
        }

        public String getO2oGuideId() {
            return o2oGuideId;
        }

        public void setO2oGuideId(String o2oGuideId) {
            this.o2oGuideId = o2oGuideId;
        }

        public String getO2oGuideName() {
            return o2oGuideName;
        }

        public void setO2oGuideName(String o2oGuideName) {
            this.o2oGuideName = o2oGuideName;
        }

        public String getO2oShopId() {
            return o2oShopId;
        }

        public void setO2oShopId(String o2oShopId) {
            this.o2oShopId = o2oShopId;
        }

        public String getO2oShopName() {
            return o2oShopName;
        }

        public void setO2oShopName(String o2oShopName) {
            this.o2oShopName = o2oShopName;
        }

        public String getO2oDelivery() {
            return o2oDelivery;
        }

        public void setO2oDelivery(String o2oDelivery) {
            this.o2oDelivery = o2oDelivery;
        }

        public String getO2oOutTradeId() {
            return o2oOutTradeId;
        }

        public void setO2oOutTradeId(String o2oOutTradeId) {
            this.o2oOutTradeId = o2oOutTradeId;
        }

        public String getShopCode() {
            return shopCode;
        }

        public void setShopCode(String shopCode) {
            this.shopCode = shopCode;
        }

        public Boolean getShShip() {
            return isShShip;
        }

        public void setShShip(Boolean shShip) {
            isShShip = shShip;
        }

        public Boolean getForceDc() {
            return isForceDc;
        }

        public void setForceDc(Boolean forceDc) {
            isForceDc = forceDc;
        }

        public Boolean getCycleBuy() {
            return isCycleBuy;
        }

        public void setCycleBuy(Boolean cycleBuy) {
            isCycleBuy = cycleBuy;
        }

        public Boolean getIndsQyg() {
            return isIndsQyg;
        }

        public void setIndsQyg(Boolean indsQyg) {
            isIndsQyg = indsQyg;
        }

        public Boolean getMss() {
            return isMss;
        }

        public void setMss(Boolean mss) {
            isMss = mss;
        }

        public Boolean getZkIdentify() {
            return isZkIdentify;
        }

        public void setZkIdentify(Boolean zkIdentify) {
            isZkIdentify = zkIdentify;
        }

        public Boolean getGfjy() {
            return isGfjy;
        }

        public void setGfjy(Boolean gfjy) {
            isGfjy = gfjy;
        }

        public String getGovSnCheck() {
            return govSnCheck;
        }

        public void setGovSnCheck(String govSnCheck) {
            this.govSnCheck = govSnCheck;
        }

        public String getQnDistr() {
            return qnDistr;
        }

        public void setQnDistr(String qnDistr) {
            this.qnDistr = qnDistr;
        }

        public String getGovMainSubject() {
            return govMainSubject;
        }

        public void setGovMainSubject(String govMainSubject) {
            this.govMainSubject = govMainSubject;
        }

        public String getUseGovSubsidy() {
            return useGovSubsidy;
        }

        public void setUseGovSubsidy(String useGovSubsidy) {
            this.useGovSubsidy = useGovSubsidy;
        }

        public String getGovSubsidyAmount() {
            return govSubsidyAmount;
        }

        public void setGovSubsidyAmount(String govSubsidyAmount) {
            this.govSubsidyAmount = govSubsidyAmount;
        }

        public String getUseGovPredict() {
            return useGovPredict;
        }

        public void setUseGovPredict(String useGovPredict) {
            this.useGovPredict = useGovPredict;
        }

        public String getGovSubsidyAmountExact() {
            return govSubsidyAmountExact;
        }

        public void setGovSubsidyAmountExact(String govSubsidyAmountExact) {
            this.govSubsidyAmountExact = govSubsidyAmountExact;
        }

        public String getOldfornew() {
            return oldfornew;
        }

        public void setOldfornew(String oldfornew) {
            this.oldfornew = oldfornew;
        }

        public String getGovStore() {
            return govStore;
        }

        public void setGovStore(String govStore) {
            this.govStore = govStore;
        }

        public String getUseGovSubsidyNew() {
            return useGovSubsidyNew;
        }

        public void setUseGovSubsidyNew(String useGovSubsidyNew) {
            this.useGovSubsidyNew = useGovSubsidyNew;
        }

        public String getGovSubsidyAmountNew() {
            return govSubsidyAmountNew;
        }

        public void setGovSubsidyAmountNew(String govSubsidyAmountNew) {
            this.govSubsidyAmountNew = govSubsidyAmountNew;
        }

        public String getGovPayType() {
            return govPayType;
        }

        public void setGovPayType(String govPayType) {
            this.govPayType = govPayType;
        }

        public String getGovSubsidyType() {
            return govSubsidyType;
        }

        public void setGovSubsidyType(String govSubsidyType) {
            this.govSubsidyType = govSubsidyType;
        }

        public String getGovSubsidyTypeExtra() {
            return govSubsidyTypeExtra;
        }

        public void setGovSubsidyTypeExtra(String govSubsidyTypeExtra) {
            this.govSubsidyTypeExtra = govSubsidyTypeExtra;
        }
    }
    //endregion

    //region 淘宝交易子订单列表

    /**
     * 淘宝交易子订单列表
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class Orders {
        /**
         * 子订单列表
         */
        @JsonProperty("order")
        private List<Order> order;

        public List<Order> getOrder() {
            return order;
        }

        public void setOrder(List<Order> order) {
            this.order = order;
        }
    }

    /**
     * 淘宝交易子订单
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class Order {
        /**
         * 商品标题
         */
        @JsonProperty("title")
        private String title;

        /**
         * 商品图片路径
         */
        @JsonProperty("pic_path")
        private String picPath;

        /**
         * 商品价格
         */
        @JsonProperty("price")
        private String price;

        /**
         * 商品数字ID
         */
        @JsonProperty("num_iid")
        private Long numIid;

        /**
         * SKU ID
         */
        @JsonProperty("sku_id")
        private String skuId;

        /**
         * 商品外部编码
         */
        @JsonProperty("outer_iid")
        private String outerIid;

        /**
         * SKU外部编码
         */
        @JsonProperty("outer_sku_id")
        private String outerSkuId;

        /**
         * 退款状态
         */
        @JsonProperty("refund_status")
        private String refundStatus;

        /**
         * 子订单状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 子订单ID
         */
        @JsonProperty("oid")
        private Long oid;

        /**
         * 应付金额
         */
        @JsonProperty("total_fee")
        private String totalFee;

        /**
         * 实付金额
         */
        @JsonProperty("payment")
        private String payment;

        /**
         * 折扣金额
         */
        @JsonProperty("discount_fee")
        private String discountFee;

        /**
         * 调整金额
         */
        @JsonProperty("adjust_fee")
        private String adjustFee;

        /**
         * 商品数量
         */
        @JsonProperty("num")
        private Integer num;

        /**
         * 定制信息
         */
        @JsonProperty("customization")
        private String customization;

        /**
         * 发票类型
         */
        @JsonProperty("inv_type")
        private String invType;

        /**
         * 是否海外订单
         */
        @JsonProperty("is_sh_ship")
        private Boolean isShShip;

        /**
         * 发货人
         */
        @JsonProperty("shipper")
        private String shipper;

        /**
         * 分单类型
         */
        @JsonProperty("f_type")
        private String fType;

        /**
         * 分单状态
         */
        @JsonProperty("f_status")
        private String fStatus;

        /**
         * 分单条款
         */
        @JsonProperty("f_term")
        private String fTerm;

        /**
         * 组合ID
         */
        @JsonProperty("combo_id")
        private String comboId;

        /**
         * 订单属性
         */
        @JsonProperty("order_attr")
        private String orderAttr;

        /**
         * 组装关系
         */
        @JsonProperty("assembly_rela")
        private String assemblyRela;

        /**
         * 组装价格
         */
        @JsonProperty("assembly_price")
        private String assemblyPrice;

        /**
         * 组装商品
         */
        @JsonProperty("assembly_item")
        private String assemblyItem;

        /**
         * 子订单税费促销金额
         */
        @JsonProperty("sub_order_tax_promotion_fee")
        private String subOrderTaxPromotionFee;

        /**
         * 首付款
         */
        @JsonProperty("down_payment")
        private String downPayment;

        /**
         * 首付款比例
         */
        @JsonProperty("down_payment_ratio")
        private String downPaymentRatio;

        /**
         * 月付款
         */
        @JsonProperty("month_payment")
        private String monthPayment;

        /**
         * 尾款
         */
        @JsonProperty("tail_payment")
        private String tailPayment;

        /**
         * 分期数
         */
        @JsonProperty("installment_num")
        private String installmentNum;

        /**
         * 违约金
         */
        @JsonProperty("penalty")
        private String penalty;

        /**
         * 配送时间
         */
        @JsonProperty("delivery_time")
        private String deliveryTime;

        /**
         * 收货时间
         */
        @JsonProperty("collect_time")
        private String collectTime;

        /**
         * 卖家发货时间
         */
        @JsonProperty("consign_time")
        private String consignTime;

        /**
         * 派送时间
         */
        @JsonProperty("dispatch_time")
        private String dispatchTime;

        /**
         * 签收时间
         */
        @JsonProperty("sign_time")
        private String signTime;

        /**
         * 承诺结束时间
         */
        @JsonProperty("promise_end_time")
        private String promiseEndTime;

        /**
         * 扩展信息
         */
        @JsonProperty("extend_info")
        private String extendInfo;

        /**
         * 是否需要退回
         */
        @JsonProperty("need_return")
        private Boolean needReturn;

        /**
         * 物流信息
         */
        @JsonProperty("ship_info")
        private ShipInfo shipInfo;

        /**
         * 组合商品信息
         */
        @JsonProperty("combine_item_info")
        private CombineItemInfo combineItemInfo;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getPicPath() {
            return picPath;
        }

        public void setPicPath(String picPath) {
            this.picPath = picPath;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public Long getNumIid() {
            return numIid;
        }

        public void setNumIid(Long numIid) {
            this.numIid = numIid;
        }

        public String getSkuId() {
            return skuId;
        }

        public void setSkuId(String skuId) {
            this.skuId = skuId;
        }

        public String getOuterIid() {
            return outerIid;
        }

        public void setOuterIid(String outerIid) {
            this.outerIid = outerIid;
        }

        public String getOuterSkuId() {
            return outerSkuId;
        }

        public void setOuterSkuId(String outerSkuId) {
            this.outerSkuId = outerSkuId;
        }

        public String getRefundStatus() {
            return refundStatus;
        }

        public void setRefundStatus(String refundStatus) {
            this.refundStatus = refundStatus;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Long getOid() {
            return oid;
        }

        public void setOid(Long oid) {
            this.oid = oid;
        }

        public String getTotalFee() {
            return totalFee;
        }

        public void setTotalFee(String totalFee) {
            this.totalFee = totalFee;
        }

        public String getPayment() {
            return payment;
        }

        public void setPayment(String payment) {
            this.payment = payment;
        }

        public String getDiscountFee() {
            return discountFee;
        }

        public void setDiscountFee(String discountFee) {
            this.discountFee = discountFee;
        }

        public String getAdjustFee() {
            return adjustFee;
        }

        public void setAdjustFee(String adjustFee) {
            this.adjustFee = adjustFee;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public String getCustomization() {
            return customization;
        }

        public void setCustomization(String customization) {
            this.customization = customization;
        }

        public String getInvType() {
            return invType;
        }

        public void setInvType(String invType) {
            this.invType = invType;
        }

        public Boolean getShShip() {
            return isShShip;
        }

        public void setShShip(Boolean shShip) {
            isShShip = shShip;
        }

        public String getShipper() {
            return shipper;
        }

        public void setShipper(String shipper) {
            this.shipper = shipper;
        }

        public String getfType() {
            return fType;
        }

        public void setfType(String fType) {
            this.fType = fType;
        }

        public String getfStatus() {
            return fStatus;
        }

        public void setfStatus(String fStatus) {
            this.fStatus = fStatus;
        }

        public String getfTerm() {
            return fTerm;
        }

        public void setfTerm(String fTerm) {
            this.fTerm = fTerm;
        }

        public String getComboId() {
            return comboId;
        }

        public void setComboId(String comboId) {
            this.comboId = comboId;
        }

        public String getOrderAttr() {
            return orderAttr;
        }

        public void setOrderAttr(String orderAttr) {
            this.orderAttr = orderAttr;
        }

        public String getAssemblyRela() {
            return assemblyRela;
        }

        public void setAssemblyRela(String assemblyRela) {
            this.assemblyRela = assemblyRela;
        }

        public String getAssemblyPrice() {
            return assemblyPrice;
        }

        public void setAssemblyPrice(String assemblyPrice) {
            this.assemblyPrice = assemblyPrice;
        }

        public String getAssemblyItem() {
            return assemblyItem;
        }

        public void setAssemblyItem(String assemblyItem) {
            this.assemblyItem = assemblyItem;
        }

        public String getSubOrderTaxPromotionFee() {
            return subOrderTaxPromotionFee;
        }

        public void setSubOrderTaxPromotionFee(String subOrderTaxPromotionFee) {
            this.subOrderTaxPromotionFee = subOrderTaxPromotionFee;
        }

        public String getDownPayment() {
            return downPayment;
        }

        public void setDownPayment(String downPayment) {
            this.downPayment = downPayment;
        }

        public String getDownPaymentRatio() {
            return downPaymentRatio;
        }

        public void setDownPaymentRatio(String downPaymentRatio) {
            this.downPaymentRatio = downPaymentRatio;
        }

        public String getMonthPayment() {
            return monthPayment;
        }

        public void setMonthPayment(String monthPayment) {
            this.monthPayment = monthPayment;
        }

        public String getTailPayment() {
            return tailPayment;
        }

        public void setTailPayment(String tailPayment) {
            this.tailPayment = tailPayment;
        }

        public String getInstallmentNum() {
            return installmentNum;
        }

        public void setInstallmentNum(String installmentNum) {
            this.installmentNum = installmentNum;
        }

        public String getPenalty() {
            return penalty;
        }

        public void setPenalty(String penalty) {
            this.penalty = penalty;
        }

        public String getDeliveryTime() {
            return deliveryTime;
        }

        public void setDeliveryTime(String deliveryTime) {
            this.deliveryTime = deliveryTime;
        }

        public String getCollectTime() {
            return collectTime;
        }

        public void setCollectTime(String collectTime) {
            this.collectTime = collectTime;
        }

        public String getDispatchTime() {
            return dispatchTime;
        }

        public void setDispatchTime(String dispatchTime) {
            this.dispatchTime = dispatchTime;
        }

        public String getSignTime() {
            return signTime;
        }

        public void setSignTime(String signTime) {
            this.signTime = signTime;
        }

        public String getPromiseEndTime() {
            return promiseEndTime;
        }

        public void setPromiseEndTime(String promiseEndTime) {
            this.promiseEndTime = promiseEndTime;
        }

        public String getExtendInfo() {
            return extendInfo;
        }

        public void setExtendInfo(String extendInfo) {
            this.extendInfo = extendInfo;
        }

        public Boolean getNeedReturn() {
            return needReturn;
        }

        public void setNeedReturn(Boolean needReturn) {
            this.needReturn = needReturn;
        }

        public ShipInfo getShipInfo() {
            return shipInfo;
        }

        public void setShipInfo(ShipInfo shipInfo) {
            this.shipInfo = shipInfo;
        }

        public CombineItemInfo getCombineItemInfo() {
            return combineItemInfo;
        }

        public void setCombineItemInfo(CombineItemInfo combineItemInfo) {
            this.combineItemInfo = combineItemInfo;
        }

        public String getConsignTime() {
            return consignTime;
        }

        public void setConsignTime(String consignTime) {
            this.consignTime = consignTime;
        }
    }
    //endregion

    //region 淘宝交易优惠详情

    /**
     * 淘宝交易优惠详情
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class PromotionDetails {
        /**
         * 优惠详情列表
         */
        @JsonProperty("promotion_detail")
        private List<PromotionDetail> promotionDetail;

        public List<PromotionDetail> getPromotionDetail() {
            return promotionDetail;
        }

        public void setPromotionDetail(List<PromotionDetail> promotionDetail) {
            this.promotionDetail = promotionDetail;
        }
    }

    /**
     * 淘宝交易优惠详情项
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class PromotionDetail {
        /**
         * 优惠ID
         */
        @JsonProperty("id")
        private Long id;

        /**
         * 优惠名称
         */
        @JsonProperty("promotion_name")
        private String promotionName;

        /**
         * 优惠金额
         */
        @JsonProperty("discount_fee")
        private String discountFee;

        /**
         * 赠品名称
         */
        @JsonProperty("gift_item_name")
        private String giftItemName;

        /**
         * 赠品ID
         */
        @JsonProperty("gift_item_id")
        private String giftItemId;

        /**
         * 赠品数量
         */
        @JsonProperty("gift_item_num")
        private String giftItemNum;

        /**
         * 优惠描述
         */
        @JsonProperty("promotion_desc")
        private String promotionDesc;

        /**
         * 优惠ID
         */
        @JsonProperty("promotion_id")
        private String promotionId;

        /**
         * 跨店优惠金额
         */
        @JsonProperty("kd_discount_fee")
        private String kdDiscountFee;

        /**
         * 跨店子优惠金额
         */
        @JsonProperty("kd_child_discount_fee")
        private String kdChildDiscountFee;

        // Getters and Setters
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getPromotionName() {
            return promotionName;
        }

        public void setPromotionName(String promotionName) {
            this.promotionName = promotionName;
        }

        public String getDiscountFee() {
            return discountFee;
        }

        public void setDiscountFee(String discountFee) {
            this.discountFee = discountFee;
        }

        public String getGiftItemName() {
            return giftItemName;
        }

        public void setGiftItemName(String giftItemName) {
            this.giftItemName = giftItemName;
        }

        public String getGiftItemId() {
            return giftItemId;
        }

        public void setGiftItemId(String giftItemId) {
            this.giftItemId = giftItemId;
        }

        public String getGiftItemNum() {
            return giftItemNum;
        }

        public void setGiftItemNum(String giftItemNum) {
            this.giftItemNum = giftItemNum;
        }

        public String getPromotionDesc() {
            return promotionDesc;
        }

        public void setPromotionDesc(String promotionDesc) {
            this.promotionDesc = promotionDesc;
        }

        public String getPromotionId() {
            return promotionId;
        }

        public void setPromotionId(String promotionId) {
            this.promotionId = promotionId;
        }

        public String getKdDiscountFee() {
            return kdDiscountFee;
        }

        public void setKdDiscountFee(String kdDiscountFee) {
            this.kdDiscountFee = kdDiscountFee;
        }

        public String getKdChildDiscountFee() {
            return kdChildDiscountFee;
        }

        public void setKdChildDiscountFee(String kdChildDiscountFee) {
            this.kdChildDiscountFee = kdChildDiscountFee;
        }
    }
    //endregion

    //region 淘宝交易服务标签

    /**
     * 淘宝交易服务标签
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class ServiceTags {
        /**
         * 物流标签列表
         */
        @JsonProperty("logistics_tag")
        private List<LogisticsTag> logisticsTag;

        public List<LogisticsTag> getLogisticsTag() {
            return logisticsTag;
        }

        public void setLogisticsTag(List<LogisticsTag> logisticsTag) {
            this.logisticsTag = logisticsTag;
        }
    }

    /**
     * 淘宝交易物流标签
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class LogisticsTag {
        /**
         * 标签ID
         */
        @JsonProperty("tag_id")
        private String tagId;

        /**
         * 标签名称
         */
        @JsonProperty("tag_name")
        private String tagName;

        /**
         * 标签值
         */
        @JsonProperty("tag_value")
        private String tagValue;

        // Getters and Setters
        public String getTagId() {
            return tagId;
        }

        public void setTagId(String tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public String getTagValue() {
            return tagValue;
        }

        public void setTagValue(String tagValue) {
            this.tagValue = tagValue;
        }
    }

    //endregion

    //region 淘宝交易服务子订单列表

    /**
     * 淘宝交易服务子订单列表
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class ServiceOrders {
        /**
         * 服务子订单列表
         */
        @JsonProperty("service_order")
        private List<ServiceOrder> serviceOrder;

        public List<ServiceOrder> getServiceOrder() {
            return serviceOrder;
        }

        public void setServiceOrder(List<ServiceOrder> serviceOrder) {
            this.serviceOrder = serviceOrder;
        }
    }

    /**
     * 淘宝交易服务子订单
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class ServiceOrder {
        /**
         * 服务订单ID
         */
        @JsonProperty("oid")
        private Long oid;

        /**
         * 商品订单ID
         */
        @JsonProperty("item_oid")
        private Long itemOid;

        /**
         * 服务ID
         */
        @JsonProperty("service_id")
        private Long serviceId;

        /**
         * 服务详情URL
         */
        @JsonProperty("service_detail_url")
        private String serviceDetailUrl;

        /**
         * 数量
         */
        @JsonProperty("num")
        private Integer num;

        /**
         * 价格
         */
        @JsonProperty("price")
        private String price;

        /**
         * 实付金额
         */
        @JsonProperty("payment")
        private String payment;

        /**
         * 标题
         */
        @JsonProperty("title")
        private String title;

        /**
         * 总金额
         */
        @JsonProperty("total_fee")
        private String totalFee;

        /**
         * 买家昵称
         */
        @JsonProperty("buyer_nick")
        private String buyerNick;

        /**
         * 退款ID
         */
        @JsonProperty("refund_id")
        private String refundId;

        /**
         * 卖家昵称
         */
        @JsonProperty("seller_nick")
        private String sellerNick;

        /**
         * 图片路径
         */
        @JsonProperty("pic_path")
        private String picPath;

        /**
         * 天猫服务SPU编码
         */
        @JsonProperty("tmser_spu_code")
        private String tmserSpuCode;

        /**
         * 预约服务时间
         */
        @JsonProperty("et_ser_time")
        private String etSerTime;

        /**
         * 预约门店名称
         */
        @JsonProperty("et_shop_name")
        private String etShopName;

        /**
         * 核销门店名称
         */
        @JsonProperty("et_verified_shop_name")
        private String etVerifiedShopName;

        /**
         * 车牌号
         */
        @JsonProperty("et_plate_number")
        private String etPlateNumber;

        /**
         * 订单ID字符串
         */
        @JsonProperty("oid_str")
        private String oidStr;

        /**
         * Apple Care邮箱
         */
        @JsonProperty("apple_care_email")
        private String appleCareEmail;

        /**
         * Apple Care MPN
         */
        @JsonProperty("apple_care_mpn")
        private String appleCareMpn;

        /**
         * 服务外部ID
         */
        @JsonProperty("service_outer_id")
        private String serviceOuterId;

        /**
         * 服务订单类型
         */
        @JsonProperty("service_order_type")
        private String serviceOrderType;

        /**
         * 扩展服务业务ID
         */
        @JsonProperty("ext_service_biz_id")
        private String extServiceBizId;

        /**
         * 组合子商品ID
         */
        @JsonProperty("combine_sub_item_id")
        private Long combineSubItemId;

        /**
         * 组合子商品SKU ID
         */
        @JsonProperty("combine_sub_item_sku_id")
        private Long combineSubItemSkuId;

        /**
         * 佣金金额单位
         */
        @JsonProperty("comm_amount_unit")
        private String commAmountUnit;

        // Getters and Setters
        public Long getOid() {
            return oid;
        }

        public void setOid(Long oid) {
            this.oid = oid;
        }

        public Long getItemOid() {
            return itemOid;
        }

        public void setItemOid(Long itemOid) {
            this.itemOid = itemOid;
        }

        public Long getServiceId() {
            return serviceId;
        }

        public void setServiceId(Long serviceId) {
            this.serviceId = serviceId;
        }

        public String getServiceDetailUrl() {
            return serviceDetailUrl;
        }

        public void setServiceDetailUrl(String serviceDetailUrl) {
            this.serviceDetailUrl = serviceDetailUrl;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer num) {
            this.num = num;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getPayment() {
            return payment;
        }

        public void setPayment(String payment) {
            this.payment = payment;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getTotalFee() {
            return totalFee;
        }

        public void setTotalFee(String totalFee) {
            this.totalFee = totalFee;
        }

        public String getBuyerNick() {
            return buyerNick;
        }

        public void setBuyerNick(String buyerNick) {
            this.buyerNick = buyerNick;
        }

        public String getRefundId() {
            return refundId;
        }

        public void setRefundId(String refundId) {
            this.refundId = refundId;
        }

        public String getSellerNick() {
            return sellerNick;
        }

        public void setSellerNick(String sellerNick) {
            this.sellerNick = sellerNick;
        }

        public String getPicPath() {
            return picPath;
        }

        public void setPicPath(String picPath) {
            this.picPath = picPath;
        }

        public String getTmserSpuCode() {
            return tmserSpuCode;
        }

        public void setTmserSpuCode(String tmserSpuCode) {
            this.tmserSpuCode = tmserSpuCode;
        }

        public String getEtSerTime() {
            return etSerTime;
        }

        public void setEtSerTime(String etSerTime) {
            this.etSerTime = etSerTime;
        }

        public String getEtShopName() {
            return etShopName;
        }

        public void setEtShopName(String etShopName) {
            this.etShopName = etShopName;
        }

        public String getEtVerifiedShopName() {
            return etVerifiedShopName;
        }

        public void setEtVerifiedShopName(String etVerifiedShopName) {
            this.etVerifiedShopName = etVerifiedShopName;
        }

        public String getEtPlateNumber() {
            return etPlateNumber;
        }

        public void setEtPlateNumber(String etPlateNumber) {
            this.etPlateNumber = etPlateNumber;
        }

        public String getOidStr() {
            return oidStr;
        }

        public void setOidStr(String oidStr) {
            this.oidStr = oidStr;
        }

        public String getAppleCareEmail() {
            return appleCareEmail;
        }

        public void setAppleCareEmail(String appleCareEmail) {
            this.appleCareEmail = appleCareEmail;
        }

        public String getAppleCareMpn() {
            return appleCareMpn;
        }

        public void setAppleCareMpn(String appleCareMpn) {
            this.appleCareMpn = appleCareMpn;
        }

        public String getServiceOuterId() {
            return serviceOuterId;
        }

        public void setServiceOuterId(String serviceOuterId) {
            this.serviceOuterId = serviceOuterId;
        }

        public String getServiceOrderType() {
            return serviceOrderType;
        }

        public void setServiceOrderType(String serviceOrderType) {
            this.serviceOrderType = serviceOrderType;
        }

        public String getExtServiceBizId() {
            return extServiceBizId;
        }

        public void setExtServiceBizId(String extServiceBizId) {
            this.extServiceBizId = extServiceBizId;
        }

        public Long getCombineSubItemId() {
            return combineSubItemId;
        }

        public void setCombineSubItemId(Long combineSubItemId) {
            this.combineSubItemId = combineSubItemId;
        }

        public Long getCombineSubItemSkuId() {
            return combineSubItemSkuId;
        }

        public void setCombineSubItemSkuId(Long combineSubItemSkuId) {
            this.combineSubItemSkuId = combineSubItemSkuId;
        }

        public String getCommAmountUnit() {
            return commAmountUnit;
        }

        public void setCommAmountUnit(String commAmountUnit) {
            this.commAmountUnit = commAmountUnit;
        }
    }
    //endregion

    //region 淘宝交易物流信息

    /**
     * 淘宝交易物流信息
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class ShipInfo {
        /**
         * 物流信息列表
         */
        @JsonProperty("ship_info")
        private List<ShipInfoItem> shipInfo;

        public List<ShipInfoItem> getShipInfo() {
            return shipInfo;
        }

        public void setShipInfo(List<ShipInfoItem> shipInfo) {
            this.shipInfo = shipInfo;
        }

    }

    /**
     * 物流信息项
     */
    public static class ShipInfoItem {
        /**
         * 运输类型
         */
        @JsonProperty("transport_type")
        private String transportType;

        public String getTransportType() {
            return transportType;
        }

        public void setTransportType(String transportType) {
            this.transportType = transportType;
        }
    }
    //endregion

    //region 淘宝交易组合商品信息

    /**
     * 淘宝交易组合商品信息
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class CombineItemInfo {
        /**
         * 组合子商品列表
         */
        @JsonProperty("combine_sub_item_d_o")
        private List<CombineSubItemDO> combineSubItemDO;

        public List<CombineSubItemDO> getCombineSubItemDO() {
            return combineSubItemDO;
        }

        public void setCombineSubItemDO(List<CombineSubItemDO> combineSubItemDO) {
            this.combineSubItemDO = combineSubItemDO;
        }
    }

    /**
     * 组合子商品
     */
    public static class CombineSubItemDO {
        /**
         * 商品ID
         */
        @JsonProperty("item_id")
        private Long itemId;

        /**
         * SKU ID
         */
        @JsonProperty("sku_id")
        private Long skuId;

        /**
         * 数量
         */
        @JsonProperty("quantity")
        private Integer quantity;

        /**
         * 商品名称
         */
        @JsonProperty("item_name")
        private String itemName;

        /**
         * 外部商品ID
         */
        @JsonProperty("outer_iid")
        private String outerIid;

        /**
         * SKU标题
         */
        @JsonProperty("sku_title")
        private String skuTitle;

        /**
         * 原始费用
         */
        @JsonProperty("origin_fee")
        private Long originFee;

        /**
         * 组合子商品费用
         */
        @JsonProperty("combine_sub_item_fee")
        private Long combineSubItemFee;

        /**
         * 是否主商品
         */
        @JsonProperty("ismain")
        private Boolean ismain;

        /**
         * 预计发货时间
         */
        @JsonProperty("estcon_time")
        private String estconTime;

        /**
         * 外部SKU ID
         */
        @JsonProperty("outer_sku_id")
        private String outerSkuId;

        /**
         * 教育原始费用
         */
        @JsonProperty("edu_original_fee")
        private Long eduOriginalFee;

        /**
         * Apple CC信息
         */
        @JsonProperty("apple_cc")
        private String appleCc;

        // Getters and Setters
        public Long getItemId() {
            return itemId;
        }

        public void setItemId(Long itemId) {
            this.itemId = itemId;
        }

        // 其他getter和setter方法...
    }
    //endregion

    //region 淘宝交易配送计划

    /**
     * 淘宝交易配送计划
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class DeliveryPlan {
        /**
         * 配送计划列表
         */
        @JsonProperty("delivery_plan")
        private List<DeliveryPlanItem> deliveryPlan;

        public List<DeliveryPlanItem> getDeliveryPlan() {
            return deliveryPlan;
        }

        public void setDeliveryPlan(List<DeliveryPlanItem> deliveryPlan) {
            this.deliveryPlan = deliveryPlan;
        }

    }

    /**
     * 配送计划项
     */
    public static class DeliveryPlanItem {
        /**
         * 计划ID
         */
        @JsonProperty("plan_id")
        private Long planId;

        /**
         * 订单ID
         */
        @JsonProperty("order_id")
        private Long orderId;

        /**
         * 当前阶段
         */
        @JsonProperty("curr_phase")
        private Long currPhase;

        /**
         * 外部物流ID
         */
        @JsonProperty("out_logistics_id")
        private String outLogisticsId;

        /**
         * 准备时间开始
         */
        @JsonProperty("prepare_time_begin")
        private String prepareTimeBegin;

        /**
         * 发货时间开始
         */
        @JsonProperty("ship_time_begin")
        private String shipTimeBegin;

        /**
         * 实际发货时间
         */
        @JsonProperty("actual_ship_time")
        private String actualShipTime;

        /**
         * 商品数量
         */
        @JsonProperty("goods_num")
        private Long goodsNum;

        /**
         * 计划状态
         */
        @JsonProperty("plan_status")
        private Long planStatus;

        /**
         * 计划退款状态
         */
        @JsonProperty("plan_refund_status")
        private Long planRefundStatus;

        /**
         * 收货人姓名
         */
        @JsonProperty("receiver_name")
        private String receiverName;

        /**
         * 收货人手机
         */
        @JsonProperty("receiver_mobile")
        private String receiverMobile;

        /**
         * 收货人电话
         */
        @JsonProperty("receiver_phone")
        private String receiverPhone;

        /**
         * 收货人地址
         */
        @JsonProperty("receiver_address")
        private String receiverAddress;

        /**
         * 隐私号
         */
        @JsonProperty("oaid")
        private String oaid;

        /**
         * 隐私号
         */
        @JsonProperty("privacy_num")
        private String privacyNum;

        /**
         * 隐私号过期时间
         */
        @JsonProperty("privacy_expire_time")
        private String privacyExpireTime;

        /**
         * 收货人街道/镇
         */
        @JsonProperty("receiver_town")
        private String receiverTown;

        /**
         * 收货人区县
         */
        @JsonProperty("receiver_district")
        private String receiverDistrict;

        /**
         * 收货人城市
         */
        @JsonProperty("receiver_city")
        private String receiverCity;

        /**
         * 收货人省份
         */
        @JsonProperty("receiver_state")
        private String receiverState;

        /**
         * 收货人国家
         */
        @JsonProperty("receiver_country")
        private String receiverCountry;

        // Getters and Setters
        public Long getPlanId() {
            return planId;
        }

        public void setPlanId(Long planId) {
            this.planId = planId;
        }

        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public Long getCurrPhase() {
            return currPhase;
        }

        public void setCurrPhase(Long currPhase) {
            this.currPhase = currPhase;
        }

        public String getOutLogisticsId() {
            return outLogisticsId;
        }

        public void setOutLogisticsId(String outLogisticsId) {
            this.outLogisticsId = outLogisticsId;
        }

        public String getPrepareTimeBegin() {
            return prepareTimeBegin;
        }

        public void setPrepareTimeBegin(String prepareTimeBegin) {
            this.prepareTimeBegin = prepareTimeBegin;
        }

        public String getShipTimeBegin() {
            return shipTimeBegin;
        }

        public void setShipTimeBegin(String shipTimeBegin) {
            this.shipTimeBegin = shipTimeBegin;
        }

        public String getActualShipTime() {
            return actualShipTime;
        }

        public void setActualShipTime(String actualShipTime) {
            this.actualShipTime = actualShipTime;
        }

        public Long getGoodsNum() {
            return goodsNum;
        }

        public void setGoodsNum(Long goodsNum) {
            this.goodsNum = goodsNum;
        }

        public Long getPlanStatus() {
            return planStatus;
        }

        public void setPlanStatus(Long planStatus) {
            this.planStatus = planStatus;
        }

        public Long getPlanRefundStatus() {
            return planRefundStatus;
        }

        public void setPlanRefundStatus(Long planRefundStatus) {
            this.planRefundStatus = planRefundStatus;
        }

        public String getReceiverName() {
            return receiverName;
        }

        public void setReceiverName(String receiverName) {
            this.receiverName = receiverName;
        }

        public String getReceiverMobile() {
            return receiverMobile;
        }

        public void setReceiverMobile(String receiverMobile) {
            this.receiverMobile = receiverMobile;
        }

        public String getReceiverPhone() {
            return receiverPhone;
        }

        public void setReceiverPhone(String receiverPhone) {
            this.receiverPhone = receiverPhone;
        }

        public String getReceiverAddress() {
            return receiverAddress;
        }

        public void setReceiverAddress(String receiverAddress) {
            this.receiverAddress = receiverAddress;
        }

        public String getOaid() {
            return oaid;
        }

        public void setOaid(String oaid) {
            this.oaid = oaid;
        }

        public String getPrivacyNum() {
            return privacyNum;
        }

        public void setPrivacyNum(String privacyNum) {
            this.privacyNum = privacyNum;
        }

        public String getPrivacyExpireTime() {
            return privacyExpireTime;
        }

        public void setPrivacyExpireTime(String privacyExpireTime) {
            this.privacyExpireTime = privacyExpireTime;
        }

        public String getReceiverTown() {
            return receiverTown;
        }

        public void setReceiverTown(String receiverTown) {
            this.receiverTown = receiverTown;
        }

        public String getReceiverDistrict() {
            return receiverDistrict;
        }

        public void setReceiverDistrict(String receiverDistrict) {
            this.receiverDistrict = receiverDistrict;
        }

        public String getReceiverCity() {
            return receiverCity;
        }

        public void setReceiverCity(String receiverCity) {
            this.receiverCity = receiverCity;
        }

        public String getReceiverState() {
            return receiverState;
        }

        public void setReceiverState(String receiverState) {
            this.receiverState = receiverState;
        }

        public String getReceiverCountry() {
            return receiverCountry;
        }

        public void setReceiverCountry(String receiverCountry) {
            this.receiverCountry = receiverCountry;
        }
    }
    //endregion

    //region 淘宝交易组合物流详情

    /**
     * 淘宝交易组合物流详情
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class CombineLogisticsDetails {
        /**
         * 组合物流详情列表
         */
        @JsonProperty("combine_logistics_detail")
        private List<CombineLogisticsDetail> combineLogisticsDetail;

        public List<CombineLogisticsDetail> getCombineLogisticsDetail() {
            return combineLogisticsDetail;
        }

        public void setCombineLogisticsDetail(List<CombineLogisticsDetail> combineLogisticsDetail) {
            this.combineLogisticsDetail = combineLogisticsDetail;
        }
    }

    /**
     * 组合物流详情项
     */
    public static class CombineLogisticsDetail {
        /**
         * 运单号
         */
        @JsonProperty("invoice_no")
        private String invoiceNo;

        /**
         * 物流公司
         */
        @JsonProperty("logistics_company")
        private String logisticsCompany;

        /**
         * 子订单ID
         */
        @JsonProperty("sub_order_id")
        private String subOrderId;

        /**
         * 发货商品详情
         */
        @JsonProperty("send_goods_details")
        private SendGoodsDetails sendGoodsDetails;

        public String getInvoiceNo() {
            return invoiceNo;
        }

        public void setInvoiceNo(String invoiceNo) {
            this.invoiceNo = invoiceNo;
        }

        public String getLogisticsCompany() {
            return logisticsCompany;
        }

        public void setLogisticsCompany(String logisticsCompany) {
            this.logisticsCompany = logisticsCompany;
        }

        public String getSubOrderId() {
            return subOrderId;
        }

        public void setSubOrderId(String subOrderId) {
            this.subOrderId = subOrderId;
        }

        public SendGoodsDetails getSendGoodsDetails() {
            return sendGoodsDetails;
        }

        public void setSendGoodsDetails(SendGoodsDetails sendGoodsDetails) {
            this.sendGoodsDetails = sendGoodsDetails;
        }
    }

    /**
     * 发货商品详情
     */
    public static class SendGoodsDetails {
        /**
         * 发货商品详情列表
         */
        @JsonProperty("send_goods_detail")
        private List<SendGoodsDetail> sendGoodsDetail;

        public List<SendGoodsDetail> getSendGoodsDetail() {
            return sendGoodsDetail;
        }

        public void setSendGoodsDetail(List<SendGoodsDetail> sendGoodsDetail) {
            this.sendGoodsDetail = sendGoodsDetail;
        }
    }

    /**
     * 发货商品详情项
     */
    public static class SendGoodsDetail {
        /**
         * 类型
         */
        @JsonProperty("type")
        private Integer type;

        /**
         * 发货状态
         */
        @JsonProperty("consign_status")
        private Integer consignStatus;

        /**
         * 数量
         */
        @JsonProperty("amount")
        private Integer amount;

        /**
         * 商品详情
         */
        @JsonProperty("goods_details")
        private GoodsDetails goodsDetails;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Integer getConsignStatus() {
            return consignStatus;
        }

        public void setConsignStatus(Integer consignStatus) {
            this.consignStatus = consignStatus;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public GoodsDetails getGoodsDetails() {
            return goodsDetails;
        }

        public void setGoodsDetails(GoodsDetails goodsDetails) {
            this.goodsDetails = goodsDetails;
        }
    }

    /**
     * 商品详情
     */
    public static class GoodsDetails {
        /**
         * 包裹商品详情列表
         */
        @JsonProperty("package_goods_detail")
        private List<PackageGoodsDetail> packageGoodsDetail;

        public List<PackageGoodsDetail> getPackageGoodsDetail() {
            return packageGoodsDetail;
        }

        public void setPackageGoodsDetail(List<PackageGoodsDetail> packageGoodsDetail) {
            this.packageGoodsDetail = packageGoodsDetail;
        }
    }

    /**
     * 包裹商品详情
     */
    public static class PackageGoodsDetail {
        /**
         * 商品ID
         */
        @JsonProperty("item_id")
        private Long itemId;

        /**
         * SKU ID
         */
        @JsonProperty("sku_id")
        private Long skuId;

        /**
         * 数量
         */
        @JsonProperty("amount")
        private Integer amount;

        public Long getItemId() {
            return itemId;
        }

        public void setItemId(Long itemId) {
            this.itemId = itemId;
        }

        public Long getSkuId() {
            return skuId;
        }

        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }
    }
    //endregion

    //region 淘宝交易备注操作信息

    /**
     * 淘宝交易备注操作信息
     *
     * <AUTHOR>
     * @date 2025/6/26 09:58
     */
    public static class MemoOperatorInfos {
        /**
         * 备注操作信息列表
         */
        @JsonProperty("memo_operator_info")
        private List<MemoOperatorInfo> memoOperatorInfo;

        public List<MemoOperatorInfo> getMemoOperatorInfo() {
            return memoOperatorInfo;
        }

        public void setMemoOperatorInfo(List<MemoOperatorInfo> memoOperatorInfo) {
            this.memoOperatorInfo = memoOperatorInfo;
        }

    }

    /**
     * 备注操作信息项
     */
    public static class MemoOperatorInfo {
        /**
         * 操作者ID
         */
        @JsonProperty("operator_id")
        private String operatorId;

        /**
         * 操作者名称
         */
        @JsonProperty("operator_name")
        private String operatorName;

        /**
         * 操作时间
         */
        @JsonProperty("operate_time")
        private String operateTime;

        /**
         * 备注内容
         */
        @JsonProperty("memo")
        private String memo;

        /**
         * 备注标签
         */
        @JsonProperty("memo_tag")
        private String memoTag;

        /**
         * 备注类型
         */
        @JsonProperty("memo_type")
        private String memoType;

        public String getOperatorId() {
            return operatorId;
        }

        public void setOperatorId(String operatorId) {
            this.operatorId = operatorId;
        }

        public String getOperatorName() {
            return operatorName;
        }

        public void setOperatorName(String operatorName) {
            this.operatorName = operatorName;
        }

        public String getOperateTime() {
            return operateTime;
        }

        public void setOperateTime(String operateTime) {
            this.operateTime = operateTime;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }

        public String getMemoTag() {
            return memoTag;
        }

        public void setMemoTag(String memoTag) {
            this.memoTag = memoTag;
        }

        public String getMemoType() {
            return memoType;
        }

        public void setMemoType(String memoType) {
            this.memoType = memoType;
        }
    }
    //endregion
}
