package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 售后单处理插件-订单级数据转换
 *
 * <AUTHOR>
 * @date 2024/7/22 上午10:36
 */
public abstract class AbstractOrderConvertHandle<O> implements IOrderConvertHandle<O>, IInitContext {
    //region 常量
    /**
     * 日志
     */
    protected static final Logger log = LoggerFactory.getLogger(AbstractOrderConvertHandle.class);

    /**
     * 上下文
     */
    protected AfterSaleSaveContext context;

    /**
     * 覆盖方法
     */
    private IOrderConvertHandle<O> coveringMethod;

    //endregion

    //region 构造
    /**
     * 构造
     *
     * @param context 上下文
     */
    protected AbstractOrderConvertHandle(AfterSaleSaveContext context) {
        init(context);
    }
    //endregion

    //region 实现接口方法
    /**
     * 初始化
     *
     * @param context 上下文
     */
    @Override
    public void init(AfterSaleSaveContext context){
        this.context = context;
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult convert(SourceAfterSaleOrderItem<O> sourceOrder, TargetCovertOrderItem targetOrder) {
        try {
            // 插件业务/覆写逻辑执行
            return coveringMethod != null
                    ? coveringMethod.convert(sourceOrder, targetOrder)
                    : convertOrder(sourceOrder, targetOrder);
        } catch (Exception e) {
            String massage = String.format("【%s】【%s】【%s】售后单订单级数据转换处理失败-%s，原因：%s", context.getMemberName(), context.getShopId(), sourceOrder.getAfterSaleNo(), caption(), e.getMessage());
            log.error(massage, e);
            return GoodsConvertHandleResult.failed(massage);
        }
    }
    //endregion

    //region 供子类重写
    /**
     * 转换商品级信息-供子类重写
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    protected abstract AfterSaleHandleResult convertOrder(SourceAfterSaleOrderItem<O> sourceOrder, TargetCovertOrderItem targetOrder);

    /**
     * 标题
     *
     * @return 标题
     */
    protected abstract String caption();
    //endregion

    //region get/set
    public void setCoveringMethod(IOrderConvertHandle<O> coveringMethod) {
        this.coveringMethod = coveringMethod;
    }
    //endregion
}
