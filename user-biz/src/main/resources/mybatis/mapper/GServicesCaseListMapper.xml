<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.GServicesCaseListMapper" >
  <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.GServicesCaseListDO" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="CaseID" property="caseid" jdbcType="INTEGER" />
    <result column="CaseType" property="casetype" jdbcType="VARCHAR" />
    <result column="RegTime" property="regtime" jdbcType="TIMESTAMP" />
    <result column="RegOperator" property="regoperator" jdbcType="VARCHAR" />
    <result column="Summary" property="summary" jdbcType="VARCHAR" />
    <result column="CustomerID" property="customerid" jdbcType="INTEGER" />
    <result column="TradeID" property="tradeid" jdbcType="INTEGER" />
    <result column="TradeNO" property="tradeno" jdbcType="VARCHAR" />
    <result column="CustomerNick" property="customernick" jdbcType="VARCHAR" />
    <result column="Adr" property="adr" jdbcType="VARCHAR" />
    <result column="Tel" property="tel" jdbcType="VARCHAR" />
    <result column="CustomerName" property="customername" jdbcType="VARCHAR" />
    <result column="Email" property="email" jdbcType="VARCHAR" />
    <result column="QQ" property="qq" jdbcType="VARCHAR" />
    <result column="wangwang" property="wangwang" jdbcType="VARCHAR" />
    <result column="CaseInfo" property="caseinfo" jdbcType="VARCHAR" />
    <result column="DeptID" property="deptid" jdbcType="INTEGER" />
    <result column="AssignedStaff" property="assignedstaff" jdbcType="VARCHAR" />
    <result column="DeadLine" property="deadline" jdbcType="TIMESTAMP" />
    <result column="FlagID" property="flagid" jdbcType="INTEGER" />
    <result column="curStatus" property="curstatus" jdbcType="INTEGER" />
    <result column="HandleInfo" property="handleinfo" jdbcType="VARCHAR" />
    <result column="ShopID" property="shopid" jdbcType="INTEGER" />
    <result column="ConfirmTime" property="confirmtime" jdbcType="TIMESTAMP" />
    <result column="ToConfirmTime" property="toconfirmtime" jdbcType="TIMESTAMP" />
    <result column="Summary1" property="summary1" jdbcType="VARCHAR" />
    <result column="WareHouseID" property="warehouseid" jdbcType="INTEGER" />
    <result column="TradeNO2" property="tradeno2" jdbcType="VARCHAR" />
    <result column="reserved1" property="reserved1" jdbcType="VARCHAR" />
    <result column="reserved2" property="reserved2" jdbcType="VARCHAR" />
    <result column="reserved3" property="reserved3" jdbcType="VARCHAR" />
    <result column="reserved4" property="reserved4" jdbcType="VARCHAR" />
    <result column="reserved5" property="reserved5" jdbcType="VARCHAR" />
    <result column="reserved6" property="reserved6" jdbcType="VARCHAR" />
    <result column="nickUID" property="nickuid" jdbcType="VARCHAR" />
  </resultMap>
</mapper>