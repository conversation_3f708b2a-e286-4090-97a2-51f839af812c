package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.common.ActiveMembersLocalCache;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.util.List;

/**
 * 有效会员内存缓存 测试
 *
 * <AUTHOR>
 * @date 2024/10/18 下午2:16
 */
@Ignore
public class ActiveMembersLocalCacheTest extends AbstractSpringTest {
    /**
     * 获取所有有效会员
     */
    @Test
    public void getActiveMembersCurrentClusterTest(){
        List<String> members = ActiveMembersLocalCache.singleton().getActiveMembersCurrentCluster();
        Assert.assertTrue(CollectionUtils.isNotEmpty(members));
    }
}
