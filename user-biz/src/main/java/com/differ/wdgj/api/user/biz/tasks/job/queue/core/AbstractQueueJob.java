package com.differ.wdgj.api.user.biz.tasks.job.queue.core;

import com.differ.wdgj.api.component.task.single.core.AbstractSingleBeanJob;
import com.differ.wdgj.api.component.task.single.core.JobExecTimeStrategy;
import com.differ.wdgj.api.component.util.system.ThreadUtil;
import com.differ.wdgj.api.component.util.type.GenericTypeUtil;
import com.differ.wdgj.api.user.biz.infrastructure.common.CpuControl;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.SystemErrorCodes;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.ExecuteMonitorEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberAble;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberCallable;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.AddResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.JobResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.QueueJobData;
import com.differ.wdgj.api.user.biz.tasks.job.queue.monitor.DefaultQueueJobMonitor;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.init.DefaultQueueJobInitExecStrategy;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.init.IQueueJobInitExecStrategy;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue.JobTaskSourceQueue;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue.RemoteRedisWaitTaskSourceQueue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * 队列定时任务抽象基类
 * <p>
 * 组件说明：
 * 1、支持多副本负载均衡执行，同一任务在随机一个副本定时执行
 * 2、实现思路：多副本在同一队列中拉取数据，执行完成后再归还到队列中
 * 3、支持扩展数据队列，可实现本地队列，也可实现全局队列
 * 4、支持低频兜底补全任务数据，避免因某一副本未及时归还任务数据而导致整体任务链中断
 * <p>
 * 适用场景：
 * 1、需要持续多次或无限次执行的任务
 * 2、需要高频快速执行的任务
 * 3、存在可能互相影响的长耗时任务
 *
 * <AUTHOR>
 * @date 2023-12-01 16:57
 */
public abstract class AbstractQueueJob<T extends QueueJobData> extends AbstractSingleBeanJob implements InitializingBean {

    /**
     * 任务编码
     */
    private String jobCode;

    /**
     * 任务队列
     */
    private JobTaskSourceQueue<T> taskQueue;

    /**
     * 初始化执行策略
     */
    private IQueueJobInitExecStrategy initExecStrategy;

    /**
     * 执行监控的跟踪ID
     */
    private Long monitorTraceId;

    /**
     * 临时任务监控（临时，后续替换成通用）
     */
    protected DefaultQueueJobMonitor tempMonitor;

    /**
     * 泛型的业务数据类型
     */
    private Class<T> dataClazz;

    /**
     * 初始化和参数校验
     *
     * @throws Exception
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        // 任务编码
        String jobCode = this.getJobCode();
        if (StringUtils.isBlank(jobCode)) {
            throw new AppException(SystemErrorCodes.SYSTEMERROR, "任务代码不能为空");
        }

        // 临时监控（后面替换掉）
        this.tempMonitor = new DefaultQueueJobMonitor(jobCode);
    }

    /**
     * 是否支持在本站点运行
     *
     * @param sitesToRun 可运行的站点集合，由注解参数设置
     * @return 是否运行
     */
    @Override
    public boolean runOnSite(String[] sitesToRun) {
        return SystemAppConfig.get().runOnSite(sitesToRun);
    }

    /**
     * 任务执行
     */
    @Override
    protected void doWork() {

        // 任务编码
        String jobCode = this.getJobCode();
        if (StringUtils.isBlank(jobCode)) {
            return;
        }

        // 任务队列
        JobTaskSourceQueue<T> jobTaskQueue = this.getTaskQueue();
        if (jobTaskQueue == null) {
            return;
        }

        try {
            // 任务初始化执行策略
            IQueueJobInitExecStrategy jobInitExecStrategy = this.getInitExecStrategy();
            // 任务执行初始化（刷新全部任务到队列）
            if (jobInitExecStrategy != null && jobInitExecStrategy.executable(jobCode, this.getInitExecFrequency())) {

                // CPU 控制
                doCpuControl();

                // 获取全部需要自动初始化的任务
                List<T> initTasks = this.getAllAutoInitTasks();

                // 添加到队列
                if (CollectionUtils.isNotEmpty(initTasks)) {
                    jobTaskQueue.addTasks(initTasks);
                }
            }

            // CPU 控制
            doCpuControl();

            // 根据线程池的设置：异步或者同步执行任务
            TaskEnum taskEnum = taskPool();
            if (taskEnum == null) {
                // 同步线程池，使得同步异步的逻辑统一
                taskEnum = TaskEnum.API_SYNC;
            }
            // 带线程池的异步执行
            executeAsyncInner(taskEnum);

        } catch (Throwable t) {
            log.error("负载均衡总任务异常", t);
        }
    }

    /**
     * 添加任务
     *
     * @param task 任务数据
     * @return 是否成功 true:放入排队（新增排队）, false:已存在（排队中或执行中）
     */
    public AddResult addTask(T task) {
        boolean success = this.getTaskQueue().addTask(task);
        if (success) {
            return AddResult.successResult();
        }
        return AddResult.failResult("任务添加失败");
    }

    /**
     * 异步执行（内部方法）
     *
     * @param taskEnum
     */
    private void executeAsyncInner(TaskEnum taskEnum) {
        // 从任务队列获取任务数据并执行
        int fetchCount = taskEnum.getCoreThreadIdleCount();
        for (int i = 0; i < fetchCount; i++) {
            Map<String, T> next = this.getTaskQueue().pullNext();
            if (MapUtils.isEmpty(next)) {
                break;
            }

            for (Map.Entry<String, T> entry : next.entrySet()) {
                String key = entry.getKey();
                T data = entry.getValue();

                // 丢线程池执行
                Callable<JobResult> executeCallable = getExecuteCallable(key, data);
                try {
                    taskEnum.submitListenable(executeCallable, (result, t) -> callbackComplete(key, data, result, t));
                } catch (Throwable t) {
                    // 被线程池拒绝等异常时也要处理回调，这时引发同步，并记录日志
                    log.error("负载均衡任务异步增加任务异常，触发同步回调", t);
                    callbackComplete(key, data, null, t);
                }
            }

            // 再次判断，防止多加任务
            if (taskEnum.getCoreThreadIdleCount() <= 0) {
                break;
            }

            if (i < fetchCount) {
                // cpu控制
                int currentTasks = taskEnum.getPoolExecutor().getExecuteAndWait().getTotalCount();
                if (!doCpuControl(currentTasks, fetchCount)) {
                    break;
                }
            }
        }
    }

    /**
     * 执行任务的方法
     *
     * @param key  任务key
     * @param data 任务数据
     * @return
     */
    private Callable<JobResult> getExecuteCallable(String key, T data) {
        // 支持吉客号的任务
        if (data instanceof MemberAble) {
            return new MemberCallable<JobResult>(((MemberAble) data).getMember()) {
                @Override
                public JobResult call() throws Exception {
                    return executeWithMinSeconds(key, data);
                }
            };
        }

        // 不带吉客云的
        return () -> executeWithMinSeconds(key, data);
    }

    /**
     * 执行任务，支持最小执行时间
     *
     * @param key  任务key
     * @param data 任务数据
     * @return
     */
    private JobResult executeWithMinSeconds(String key, T data) {
        long currentTimeMillis = System.currentTimeMillis();
        // 执行前处理
        beforeExecute(key, data);

        // 具体执行
        JobResult jobResult = execute(data);

        // 计算执行时间，并判断是否需要延迟
        long execTime = System.currentTimeMillis() - currentTimeMillis;
        long needSleepTime = getMinExecSeconds() * 1000 - execTime;
        if (needSleepTime > 0) {
            // 未超过最小执行时间的，休眠
            ThreadUtil.sleep(needSleepTime);
        }

        return jobResult;
    }

    /**
     * 执行任务前处理
     *
     * @param key  任务key
     * @param data 任务数据
     * @return
     */
    protected void beforeExecute(String key, T data) {

    }

    /**
     * 完成任务时回调，无论成功失败，都要完成任务，失败或者异常采用重入队列的方式处理
     *
     * @param key    任务key，对应uniqueSign
     * @param data   任务数据
     * @param result 执行结果
     * @param t      执行结果异常，无异常则为null
     */
    protected void callbackComplete(String key, T data, JobResult result, Throwable t) {

        if (t != null) {
            log.error("负载均衡任务执行异常", t);
        }

        // 无论成功失败，都要完成任务，
        JobTaskSourceQueue<T> taskQueue = getTaskQueue();
        taskQueue.complete(key, data);

        if (result == null) {
            result = JobResult.RETRY;
        }

        // 失败或者异常采用重入队列的方式处理
        if (result == JobResult.RETRY) {
            if (data.getExecTimes() <= getMaxRetryTimes()) {
                data.incrementTimes();
                taskQueue.addTask(data);
            } else {
                log.error(String.format("%s负载均衡任务达到最大重试次数，任务:%s", this.jobCode, data.serialize()), t);
            }
        } else if (result == JobResult.REQUEUE) {
            taskQueue.addTask(data);
        }

        // 完成任务后的子类特殊处理
        afterCallbackComplete(key, data, result, t);
    }

    /**
     * 完成回调后的处理
     *
     * @param key    任务key，对应uniqueSign
     * @param data   任务数据
     * @param result 执行结果
     * @param t      执行结果异常，无异常则为null
     */
    protected void afterCallbackComplete(String key, T data, JobResult result, Throwable t) {

    }

    /**
     * 获取自动生成的初始化任务，强调 ：是自动的，不是外部加的，是集群任务定时初始化的，不是直接拿来用的
     *
     * @return 全部任务
     */
    protected abstract List<T> getAllAutoInitTasks();

    /**
     * 取线程池
     *
     * @return
     */
    protected abstract TaskEnum taskPool();

    /**
     * 执行任务
     *
     * @param data 任务数据
     * @return 任务执行结果：完成，重入队
     */
    protected abstract JobResult execute(T data);

    /**
     * 获取任务数据类型
     *
     * @return
     */
    protected Class<T> getJobDataType() {
        if (dataClazz == null) {
            Class clazz = AopUtils.getTargetClass(this);
            dataClazz = (Class<T>) GenericTypeUtil.getGenericDataType(clazz, 0);
        }
        return dataClazz;
    }

    /**
     * 获取初始化执行频率（单位：秒）
     *
     * @return 初始化执行频率
     */
    protected int getInitExecFrequency() {
        return Integer.parseInt(this.getConfigValue(ConfigKeyEnum.QUEUE_JOB_INIT_EXEC_FREQUENCY, "300"));
    }

    /**
     * 获取任务执行超时时间（单位：秒）
     * 特别注意：当使用redis的排队队列时，执行超时时间在ExecTimeout 与 ExecTimeout *2 之间，
     *
     * @return 任务执行超时时间
     */
    protected int getTaskExecTimeout() {
        return Integer.parseInt(this.getConfigValue(ConfigKeyEnum.QUEUE_JOB_TASK_EXEC_TIMEOUT, "300"));
    }

    /**
     * 获取任务执行的最小时间（单位：秒）,大于0才会起效限制
     * 最小执行时间，当执行小于此时间时，会sleep后再执行完成回调
     *
     * @return 拉取执行频率
     */
    protected int getMinExecSeconds() {
        return Integer.parseInt(this.getConfigValue(ConfigKeyEnum.QUEUE_JOB_MIN_EXEC_SPENT, "5"));
    }

    /**
     * 最大重试次数
     *
     * @return 最大重试次数
     */
    protected int getMaxRetryTimes() {
        return Integer.parseInt(this.getConfigValue(ConfigKeyEnum.QUEUE_JOB_TASK_EXEC_MAX_RETRY_TIMES, "10"));
    }

    /**
     * 获取拉取执行频率（单位：秒）
     *
     * @return 拉取执行频率
     */
    protected int getFetchExecFrequency() {
        return Integer.parseInt(this.getConfigValue(ConfigKeyEnum.QUEUE_JOB_FETCH_EXEC_FREQUENCY, "3"));
    }

    /**
     * 获取拉取执行策略
     *
     * @return 拉取执行策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        this.execTimeStrategy.setRunFrequency(this.getFetchExecFrequency());
        return this.execTimeStrategy;
    }

    /**
     * 获取配置值
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 结果
     */
    protected String getConfigValue(ConfigKeyEnum configKey, String defaultValue) {
        return ConfigKeyUtils.getConfigBySeparatorDefault(configKey, this.getJobCode(), defaultValue);
    }

    /**
     * 获取任务编码
     *
     * @return 任务编码
     */
    protected String getJobCode() {
        if (StringUtils.isEmpty(this.jobCode)) {
            SimpleQueueJobParameter[] jobParameters = getClass().getAnnotationsByType(SimpleQueueJobParameter.class);
            if (jobParameters.length > 0) {
                String formatCode = jobParameters[0].jobCode();
                if (!formatCode.startsWith("job.")) {
                    formatCode = String.format("job.%s", formatCode);
                }
                // 封装集群号
                formatCode = String.format("%s:%s", SystemAppConfig.getClusterNo(), formatCode);
                this.jobCode = formatCode;
            }
        }

        return this.jobCode;
    }

    /**
     * 获取任务队列
     *
     * @return 任务队列
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    protected JobTaskSourceQueue<T> getTaskQueue() {
        if (this.taskQueue == null) {
            SimpleQueueJobParameter[] jobParameters = getClass().getAnnotationsByType(SimpleQueueJobParameter.class);
            Class<? extends JobTaskSourceQueue> JobQueueClazz = RemoteRedisWaitTaskSourceQueue.class;
            if (jobParameters.length > 0) {
                JobQueueClazz = jobParameters[0].JobQueue();
            }

            try {
                // 生成实例,并初始化
                JobTaskSourceQueue<T> queue = JobQueueClazz.newInstance();
                queue.init(this.getJobCode(), this.getJobDataType(), () -> getTaskExecTimeout());
                // 最后赋值
                this.taskQueue = queue;
            } catch (Throwable t) {
                log.error(String.format("负载均衡任务队列%s创建失败", this.getJobCode()), t);
            }
        }

        return this.taskQueue;
    }

    /**
     * 执行监控器
     *
     * @return
     */
    protected ExecuteMonitorEnum getExecuteMonitor() {
        return ExecuteMonitorEnum.CHAIN_LOG;
    }

    /**
     * CPU 控制
     *
     * @param currentTasks 当前任务数
     * @param maxTasks     最大任务数
     * @return true:cpu资源可用
     */
    protected boolean doCpuControl(int currentTasks, int maxTasks) {
        int halfMaxTasks = maxTasks / 2;
        if (currentTasks < halfMaxTasks) {
            // 基本保证一半的任务能加入执行
            return CpuControl.singleton().checkLoop();
        }
        double defaultMaxCpu = Double.parseDouble(ConfigKeyUtils.getWdgjConfigValue(ConfigKeyEnum.SYSTEM_CPU_MAX_DEFAULT));
        double maxCpu = defaultMaxCpu * currentTasks / maxTasks;
        return CpuControl.singleton().checkLoop(maxCpu);
    }

    /**
     * CPU 控制
     */
    protected boolean doCpuControl() {
        return CpuControl.singleton().checkLoop();
    }

    /**
     * 获取初始化执行策略
     *
     * @return 修复初始化执行策略
     */
    private IQueueJobInitExecStrategy getInitExecStrategy() {
        if (this.initExecStrategy == null) {
            SimpleQueueJobParameter[] jobParameters = getClass().getAnnotationsByType(SimpleQueueJobParameter.class);
            Class<? extends IQueueJobInitExecStrategy> initExecStrategyClazz = DefaultQueueJobInitExecStrategy.class;
            if (jobParameters.length > 0) {
                initExecStrategyClazz = jobParameters[0].initExecStrategy();
            }

            try {
                this.initExecStrategy = initExecStrategyClazz.newInstance();
            } catch (Throwable t) {
                log.error(String.format("负载均衡任务初始化执行策略%s创建失败", this.getJobCode()), t);
            }
        }

        return this.initExecStrategy;
    }
}