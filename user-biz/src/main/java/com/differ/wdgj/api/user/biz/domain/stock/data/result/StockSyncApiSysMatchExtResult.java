package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.PlatGoodsTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockFlagEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockRestrictedModeEnum;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 库存同步匹配扩展表结果
 * <AUTHOR>
 * @date 2024-03-15 15:18
 */
public class StockSyncApiSysMatchExtResult {
    /**
     * 商品匹配扩展表Id
     */
    private int apiSysMatchId;

    /**
     * 下次重新触发时间。
     */
    private LocalDateTime nextResetSyncTime ;

    /**
     * 是否增量标记。 增量标识 {@link SyncStockFlagEnum}
     */
    private Integer increFlag ;

    /**
     * 限制/禁止模式 增量标识 {@link SyncStockRestrictedModeEnum}
     */
    private Integer restrictedMode ;

    /**
     * 平台商品类型 {@link PlatGoodsTypeEnum}
     */
    private Integer platGoodsType ;

    /**
     * 创建默认结果
     * @param apiSysMatchId 商品匹配扩展表Id
     * @return 库存同步匹配扩展表结果
     */
    public static StockSyncApiSysMatchExtResult createDefault(int apiSysMatchId){
        StockSyncApiSysMatchExtResult result = new StockSyncApiSysMatchExtResult();
        result.setApiSysMatchId(apiSysMatchId);
        result.setIncreFlag(SyncStockFlagEnum.None.getValue());
        result.setNextResetSyncTime(null);
        result.setRestrictedMode(SyncStockRestrictedModeEnum.None.getValue());
        // 空表示不更新
        result.setPlatGoodsType(null);
        return result;
    }

    //region get/set
    public int getApiSysMatchId() {
        return apiSysMatchId;
    }

    public void setApiSysMatchId(int apiSysMatchId) {
        this.apiSysMatchId = apiSysMatchId;
    }

    public LocalDateTime getNextResetSyncTime() {
        return nextResetSyncTime;
    }

    public void setNextResetSyncTime(LocalDateTime nextResetSyncTime) {
        this.nextResetSyncTime = nextResetSyncTime;
    }

    public Integer getIncreFlag() {
        return increFlag;
    }

    public void setIncreFlag(Integer increFlag) {
        this.increFlag = increFlag;
    }

    public Integer getRestrictedMode() {
        return restrictedMode;
    }

    public void setRestrictedMode(Integer restrictedMode) {
        this.restrictedMode = restrictedMode;
    }

    public Integer getPlatGoodsType() {
        return platGoodsType;
    }

    public void setPlatGoodsType(Integer platGoodsType) {
        this.platGoodsType = platGoodsType;
    }
    //endregion

    //region equal/hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StockSyncApiSysMatchExtResult that = (StockSyncApiSysMatchExtResult) o;
        return apiSysMatchId == that.apiSysMatchId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(apiSysMatchId);
    }
    //endregion
}
