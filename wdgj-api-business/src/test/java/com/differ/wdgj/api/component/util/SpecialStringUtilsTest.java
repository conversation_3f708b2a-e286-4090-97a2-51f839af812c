package com.differ.wdgj.api.component.util;

import com.differ.wdgj.api.component.util.tools.SpecialStringUtils;
import org.junit.Test;

/**
 * 特殊字符串处理
 *
 * <AUTHOR>
 * @date 2024/12/10 上午9:36
 */
public class SpecialStringUtilsTest {
    /**
     * 将emoji表情字符(四个字节 utf8mb4格式)替换为别的字符串，默认为空字符串
     */
    @Test
    public void replaceEmojiTest(){
        String source = "测试1\uD83D测试2\uDE00\uD83D\uDE03\uD83D\uDE04\uD83D测试3\uDE01\uD83D\uDE06";
        String replaceStr = "☺";
        String s = SpecialStringUtils.replaceEmoji(source, replaceStr);
    }
}
