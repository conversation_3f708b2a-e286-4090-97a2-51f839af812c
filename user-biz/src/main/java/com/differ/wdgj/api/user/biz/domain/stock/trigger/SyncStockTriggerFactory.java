package com.differ.wdgj.api.user.biz.domain.stock.trigger;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.mode.IAutoSyncStockTriggerMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.ISyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plat.DeWuSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plat.TaoBaoSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plat.YouZanSyncStockProcessor;

/**
 * 库存同步-业务处理-工厂
 *
 * <AUTHOR>
 * @date 2024-02-23 14:04
 */
public class SyncStockTriggerFactory {

    //region 构造
    private SyncStockTriggerFactory() {};
    //endregion

    /**
     * 创建库存同步触发适配器
     *
     * @param memberName    会员名
     * @param syncStockMode 库存同步模式
     * @return 实例
     */
    public static IAutoSyncStockTriggerMode createTriggerAdapter(String memberName, SyncStockModeEnum syncStockMode) {
        if (syncStockMode == null) {
            return null;
        }

        switch (syncStockMode) {
            default:
                return null;
        }
    }

    /**
     * 创建库存同步处理器
     *
     * @param context          上下文
     * //@param bizDataContainer 库存业务数据缓存容器
     * //@param stockCalculation 库存计算实例
     * @return 实例
     */
    public static ISyncStockProcessor createStockSyncProcessor(
            StockSyncContext context
    ) {
        switch (context.getPlat()) {
            case BUSINESS_Taobao:
                return new TaoBaoSyncStockProcessor(context);
            case BUSINESS_DeWu:
                return new DeWuSyncStockProcessor(context);
            case BUSINESS_Youzan2:
                return new YouZanSyncStockProcessor(context);
            default:
                return new BaseSyncStockProcessor(context);
        }
    }
}
