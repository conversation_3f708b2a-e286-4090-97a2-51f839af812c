package com.differ.wdgj.api.user.biz.domain.aftersale.save.operation;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.AfterSaleGoodsMatchResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleGoodsSysMatchItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.DownloadOrderShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.DownloadOrderShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 售后商品匹配操作测试
 *
 * <AUTHOR>
 * @date 2024/8/14 下午3:00
 */
@Ignore
public class AfterSaleGoodsMatchOperationTest extends AbstractSpringTest {

    /**
     * 上下文
     */
    private AfterSaleSaveContext context;

    /**
     * 类前置执行
     */
    @Before
    public void setUp() {
        String memberName = "api2017";
        int outShopId = 1000;
        
        // 创建上下文
        context = new AfterSaleSaveContext();
        context.setMemberName(memberName);
        context.setShopId(outShopId);
        context.setOrderTriggerType(OrderTriggerTypeEnum.MANUAL);
        context.setOperatorName("单元测试");
        
        // 设置配置信息
        AfterSalesShopConfig afterSalesConfig = AfterSalesShopConfigUtils.singleByShopId(memberName, outShopId);
        DownloadOrderShopConfig downloadOrderConfig = DownloadOrderShopConfigUtils.singleByShopId(memberName, outShopId);
        ApiShopBaseDto apiShopBase = ShopInfoUtils.singleByOutShopId(memberName, outShopId);
        
        context.setAfterSalesShopConfig(afterSalesConfig);
        context.setDownLoadOrderShopConfig(downloadOrderConfig);
        context.setPlat(apiShopBase.getPlat());
    }

    /**
     * 测试换货商品匹配 - 使用商品匹配表数据
     */
    @Test
    public void testExchangeGoodsErpMatchWithSysMatchItem() {
        // 准备测试数据
        String afterSaleNo = "EX123456789";
        
        // 创建商品匹配表数据
        AfterSaleGoodsSysMatchItem goodsSysMatchItem = new AfterSaleGoodsSysMatchItem();
        goodsSysMatchItem.setGoodsID(1001);
        goodsSysMatchItem.setSpecID(2001);
        goodsSysMatchItem.setGoodsType(1);
        
        // 创建换货商品
        ApiReturnDetailTwoDO exchangeGoods = new ApiReturnDetailTwoDO();
        exchangeGoods.setOuterId("TEST001");
        exchangeGoods.setGoodsTitle("测试商品");
        exchangeGoods.setSku("规格1");
        
        // 执行测试
        AfterSaleGoodsMatchResult result = AfterSaleGoodsMatchOperation.exchangeGoodsErpMatch(
                context, afterSaleNo, goodsSysMatchItem, exchangeGoods);
        
        // 验证结果
        Assert.assertTrue("匹配应该成功", result.isSuccess());
        Assert.assertNotNull("匹配结果不应为空", result.getGoodsMatchResult());
        Assert.assertEquals("商品ID应匹配", 1001, result.getGoodsMatchResult().getGoodsId());
        Assert.assertEquals("规格ID应匹配", 2001, result.getGoodsMatchResult().getSpecId());
        Assert.assertEquals("商品类型应匹配", 1, result.getGoodsMatchResult().getbFit());
    }
    
    /**
     * 测试换货商品匹配 - 使用存储过程匹配
     * 注意：此测试需要实际的数据库环境，会调用真实的存储过程
     */
    @Test
    public void testExchangeGoodsErpMatchWithStoredProcedure() {
        // 准备测试数据
        String afterSaleNo = "EX123456789";
        
        // 创建空的商品匹配表数据（强制使用存储过程匹配）
        AfterSaleGoodsSysMatchItem goodsSysMatchItem = null;
        
        // 创建换货商品 - 使用可能存在于数据库中的商品信息
        ApiReturnDetailTwoDO exchangeGoods = new ApiReturnDetailTwoDO();
        exchangeGoods.setOuterId("TEST001"); // 使用实际存在的商品编码
        exchangeGoods.setGoodsTitle("测试商品");
        exchangeGoods.setSku("规格1");
        
        // 执行测试
        AfterSaleGoodsMatchResult result = AfterSaleGoodsMatchOperation.exchangeGoodsErpMatch(
                context, afterSaleNo, goodsSysMatchItem, exchangeGoods);
        
        // 验证结果 - 只验证是否成功，不验证具体的匹配结果（因为依赖实际数据库数据）
        Assert.assertTrue("匹配应该成功", result.isSuccess());
    }
    
    /**
     * 测试换货商品匹配 - 使用不存在的商品信息
     * 注意：此测试需要实际的数据库环境
     */
    @Test
    public void testExchangeGoodsErpMatchWithNonExistentGoods() {
        // 准备测试数据
        String afterSaleNo = "EX123456789";
        
        // 创建空的商品匹配表数据
        AfterSaleGoodsSysMatchItem goodsSysMatchItem = null;
        
        // 创建换货商品 - 使用不太可能存在于数据库中的商品信息
        ApiReturnDetailTwoDO exchangeGoods = new ApiReturnDetailTwoDO();
        exchangeGoods.setOuterId("NONEXISTENT_CODE_12345");
        exchangeGoods.setGoodsTitle("不存在的测试商品");
        exchangeGoods.setSku("不存在的规格");
        
        // 执行测试
        AfterSaleGoodsMatchResult result = AfterSaleGoodsMatchOperation.exchangeGoodsErpMatch(
                context, afterSaleNo, goodsSysMatchItem, exchangeGoods);
        
        // 验证结果 - 匹配应该成功，但可能返回空的或默认的匹配结果
        Assert.assertTrue("匹配应该成功", result.isSuccess());
    }
}
