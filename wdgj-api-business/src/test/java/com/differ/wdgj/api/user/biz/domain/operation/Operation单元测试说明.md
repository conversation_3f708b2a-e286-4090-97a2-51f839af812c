# Operation类单元测试说明

## 概述

为 `RefundTypeCovertOperation` 和 `OrderSendStatusOperation` 两个类实现了完整的单元测试。

## 测试文件

### 1. RefundTypeCovertOperationTest.java
**位置**: `wdgj-api-business/src/test/java/com/differ/wdgj/api/user/biz/domain/aftersale/save/operation/`

**测试覆盖**:
- ✅ 退货退款单转换 (JH_04 → REFUND)
- ✅ 退款单转换 (JH_03 → REFUND_PAY)
- ✅ 换货单转换 (JH_05 → EXCHANGE)
- ✅ 补寄单转换 (JH_SUPPLEMENT_SEND → REFUND_SUPPLEMENT)
- ✅ 价保订单转换 (JH_10 → REFUND_BJ)
- ✅ 维修单转换 (JH_WEIXIU → REPAIR)
- ✅ 特殊售后单转换 (JH_SPECIALREFUND)
  - 保价 (JH_PRICE_PROTECT → REFUND_BJ)
  - 补发商品 (JH_REISSUE → REFUND_SUPPLEMENT)
  - 其他类型 → REFUND_MERCHANT
- ✅ 未知类型转换 → REFUND_ALL
- ✅ 所有枚举值覆盖测试
- ✅ 边界条件测试 (null参数处理)

**测试方法数**: 12个

### 2. OrderSendStatusOperationTest.java
**位置**: `wdgj-api-business/src/test/java/com/differ/wdgj/api/user/biz/domain/order/common/operation/`

**测试覆盖**:
- ✅ 整单发货成功场景
- ✅ 整单发货失败场景
- ✅ 整单待发货场景
- ✅ 拆单发货全部成功场景
- ✅ 拆单发货部分成功场景
- ✅ 拆单发货失败场景
- ✅ 所有发货状态枚举值覆盖
- ✅ 所有商品发货状态枚举值覆盖
- ✅ 边界条件测试 (null参数处理)

**测试方法数**: 11个

## 修正问题

### 问题1: 方法名错误
- **问题**: 测试中调用了 `convertApiAfterSaleType`，但实际方法名是 `covertApiAfterSaleType`
- **解决**: 批量替换所有方法调用名称

### 问题2: 参数顺序错误
- **问题**: 原始参数顺序为 `(ployOrder, dbOrder, shopType, context)`
- **实际**: 正确参数顺序为 `(context, shopType, ployOrder, dbOrder)`
- **解决**: 批量调整所有方法调用的参数顺序

## 运行测试

### 单独运行测试
```bash
# RefundTypeCovertOperation测试
mvn test -Dtest=RefundTypeCovertOperationTest

# OrderSendStatusOperation测试
mvn test -Dtest=OrderSendStatusOperationTest
```

### 运行所有Operation测试
```bash
mvn test -Dtest="*Operation*Test"
```

## 测试特点

### RefundTypeCovertOperation测试
1. **全面覆盖**: 测试了所有 `PolyRefundTypeEnum` 枚举值的转换
2. **特殊处理**: 针对特殊售后单 `JH_SPECIALREFUND` 的子类型进行了详细测试
3. **边界安全**: 测试了所有参数为null的边界情况
4. **业务准确**: 验证了每种售后类型到API售后类型的正确映射

### OrderSendStatusOperation测试
1. **场景完整**: 覆盖了整单发货和拆单发货的所有状态组合
2. **状态验证**: 详细验证了订单级和商品级的发货状态计算
3. **数据准确**: 验证了成功、失败、等待发货数量的正确计算
4. **枚举覆盖**: 测试了所有发货状态枚举值的处理

## 质量保证

1. **Mock使用**: 合理使用Mock对象，避免外部依赖
2. **断言完整**: 每个测试都有详细的断言验证
3. **数据构造**: 提供了完整的测试数据构造方法
4. **异常处理**: 测试了异常情况和边界条件
5. **可维护性**: 测试代码结构清晰，易于维护和扩展

## 注意事项

1. **方法签名**: 确保测试中的方法调用与实际类中的方法签名完全一致
2. **参数顺序**: 特别注意参数顺序，避免参数位置错误
3. **枚举值**: 测试中使用的枚举值应与实际业务中的枚举值保持一致
4. **边界条件**: 充分测试null值和异常情况的处理
