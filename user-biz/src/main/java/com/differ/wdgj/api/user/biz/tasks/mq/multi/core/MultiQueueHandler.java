package com.differ.wdgj.api.user.biz.tasks.mq.multi.core;

import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueResult;

/**
 * 多节点队列消息处理器（发送和接收)
 *
 * <AUTHOR>
 * @date 2024/3/14 14:30
 */
public interface MultiQueueHandler<T>  {

    /**
     * 初始化
     */
    void init();

    /**
     * 执行消息
     *
     * @param message
     * @param header
     */
    QueueResult receiveMulti(T message, QueueHeader header);

    /**
     * 发送消息
     * @param message
     */
    void sendMulti(T message);

    /**
     * 发送消息
     *
     * @param message
     * @param header 业务头信息
     */
    void sendMulti(T message, QueueHeader header);

    /**
     * 获取泛型的业务数据类型
     *
     * @return
     */
    Class<T> getDataClazz();
}
