'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

enum WorkEnum <<工作任务类型>>
class WorkData <<工作任务数据>>
interface WorkDataOperate <<数据存储操作>>
interface  WorkExecTemplate <<执行流程模板>>
interface  WorkFactory <<抽象工厂接口>>
interface SubTask <<子任务>>
interface SubRunMode <<子任务执行模式>>
interface SubTaskConverter <<子任务转换器>>
interface WorkHeart <<工作任务心跳功能>>
interface BusinessProcessor <<工作任务业务处理模块>>
class WorkFacade <<工作任务门面操作入口类>>

SubTask <|.. AbstractSubTask
SubRunMode <|.. AbstractRunMode

WorkFacade ..> WorkData
WorkFacade ..> WorkFactory
WorkFacade ..> WorkEnum
WorkEnum ..> WorkFactory

WorkFactory <|-- LoadGoodsWorkFactory  : 继承
WorkFactory <|-- MatchGoodsWorkFactory : 继承

WorkFactory ..> WorkExecTemplate : 创建
WorkFactory ..> WorkDataOperate : 创建
WorkFactory ..> SubTaskConverter : 创建
WorkFactory ..> WorkHeart : 创建
SubTaskConverter ..> SubTask
SubTask ..> SubRunMode
AbstractRunMode ..> BusinessProcessor

WorkExecTemplate <|.. BaseWorkRunTemplate



@enduml