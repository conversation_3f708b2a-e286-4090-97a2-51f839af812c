package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 保存售后订单-结果
 *
 * <AUTHOR>
 * @date 2024-06-27 18:59
 */
public class SaveAfterSaleOutResult {
    //region 属性
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 订单级结果
     */
    private Map<String, SaveAfterSaleOrderResult> orderResultMap;

    /**
     * 需要重试的售后单号
     */
    private List<String> needRetryAfterSaleNos;

    //endregion

    //region 构造

    /**
     * 构造
     */
    private SaveAfterSaleOutResult() {
    }
    //endregion

    //region 公用静态方法

    /**
     * 成功结果
     *
     * @return 结果
     */
    public static SaveAfterSaleOutResult success() {
        SaveAfterSaleOutResult result = new SaveAfterSaleOutResult();
        result.setSuccess(true);
        result.setOrderResultMap(new HashMap<>());
        result.setNeedRetryAfterSaleNos(new ArrayList<>());
        return result;
    }

    /**
     * 成功结果
     *
     * @param orderResultMap        订单级结果
     * @param needRetryAfterSaleNos 需要重试的售后单号
     * @return 结果
     */
    public static SaveAfterSaleOutResult success(Map<String, SaveAfterSaleOrderResult> orderResultMap, List<String> needRetryAfterSaleNos) {
        SaveAfterSaleOutResult result = new SaveAfterSaleOutResult();
        result.setSuccess(true);
        result.setOrderResultMap(orderResultMap);
        result.setNeedRetryAfterSaleNos(needRetryAfterSaleNos);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static SaveAfterSaleOutResult failed(String message, Map<String, SaveAfterSaleOrderResult> orderResultMap, List<String> needRetryAfterSaleNos) {
        SaveAfterSaleOutResult result = new SaveAfterSaleOutResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setOrderResultMap(orderResultMap);
        result.setNeedRetryAfterSaleNos(needRetryAfterSaleNos);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static SaveAfterSaleOutResult failed(String message) {
        return failed(message, null, null);
    }

    //endregion

    //region 公共方法

    /**
     * 是否失败
     *
     * @return 结果
     */
    public boolean isFailed() {
        return !success;
    }

    //endregion

    // region getter & setter

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, SaveAfterSaleOrderResult> getOrderResultMap() {
        return orderResultMap;
    }

    protected void setOrderResultMap(Map<String, SaveAfterSaleOrderResult> orderResultMap) {
        this.orderResultMap = orderResultMap;
    }

    public List<String> getNeedRetryAfterSaleNos() {
        return needRetryAfterSaleNos;
    }

    public void setNeedRetryAfterSaleNos(List<String> needRetryAfterSaleNos) {
        this.needRetryAfterSaleNos = needRetryAfterSaleNos;
    }

    // endregion
}
