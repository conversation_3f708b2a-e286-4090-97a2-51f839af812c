package com.differ.wdgj.api.user.biz.infrastructure.work.data;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 工作任务上下文
 *
 * <AUTHOR>
 * @date 2024/7/3 11:01
 */
public class WorkContext {
    /**
     * 会员
     */
    private String member;

    /**
     * 任务类型
     */
    private WorkEnum workType;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 触发类型
     */
    private TriggerTypeEnum triggerType;
    /**
     * 创建人
     */
    private String creator;

    //region 公共方法

    /**
     * 参数验证
     *
     * @return 验证失败的错误消息，成功返回null或空
     */
    public String validArgs() {
        if (StringUtils.isBlank(member)) {
            return "会员不能为空";
        }

        if (workType == null) {
            return "任务类型不能为空";
        }

        return null;
    }

    /**
     * 创建工作任务上下文
     *
     * @param member         会员
     * @param workType       任务类型
     * @param shopId         店铺ID
     * @param triggerType    触发类型
     * @param creator        创建人
     * @return 工作任务上下文
     */
    public static WorkContext of(String member, WorkEnum workType, Integer shopId, TriggerTypeEnum triggerType, String creator) {
        WorkContext workContext = new WorkContext();
        workContext.setMember(member);
        workContext.setWorkType(workType);
        workContext.setShopId(shopId);
        workContext.setTriggerType(triggerType);
        workContext.setCreator(creator);
        return workContext;
    }
    //endregion

    //region get/set
    public WorkEnum getWorkType() {
        return workType;
    }

    public void setWorkType(WorkEnum workType) {
        this.workType = workType;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public TriggerTypeEnum getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(TriggerTypeEnum triggerType) {
        this.triggerType = triggerType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member;
    }
    //endregion
}
