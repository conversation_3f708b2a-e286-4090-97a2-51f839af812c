package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor;

import com.alibaba.fastjson.JSON;
import com.differ.wdgj.api.component.util.tools.DateTimeUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.exchage.ExchangeRefundJdpResponseDto;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * 淘宝Rds - 换货单转换器测试
 *
 * <AUTHOR>
 * @date 2025/4/10 下午8:27
 */
@Ignore
public class RdsExchangeRefundConvertorTest {

    /**
     * 测试 convertTaobaoOrderExchangeStatus 方法
     */
    @Test
    public void testConvertTaobaoOrderExchangeStatus() {
        // 测试已知状态
        Assert.assertEquals(PolyExchangeStatusEnum.JH_01,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("换货待处理"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_02,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("待买家退货"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_03,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("买家已退货，待收货"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_05,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("换货关闭"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_06,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("换货成功"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_07,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("待发出换货商品"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_08,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("待买家收货"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_09,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("请退款"));
        Assert.assertEquals(PolyExchangeStatusEnum.JH_12,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("待买家修改"));

        // 测试未知状态
        Assert.assertEquals(PolyExchangeStatusEnum.JH_99,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus("未知状态"));

        // 测试空值
        Assert.assertEquals(PolyExchangeStatusEnum.JH_99,
                RdsExchangeRefundConvertor.convertTaobaoOrderExchangeStatus(null));
    }

    /**
     * 测试 convertPolyExchangeOrder 方法 - 使用JSON字符串构建测试数据
     */
    @Test
    public void testConvertPolyExchangeOrder() {
        //region 构建测试数据JSON字符串
        String jsonStr = "{\n" +
                "  \"tmall_exchange_get_response\": {\n" +
                "    \"result\": {\n" +
                "      \"message\": \"成功\",\n" +
                "      \"msg_code\": \"0\",\n" +
                "      \"success\": true,\n" +
                "      \"exchange\": {\n" +
                "        \"buyer_logistic_no\": \"SF1234567890\",\n" +
                "        \"alipay_no\": \"2023042822001123456789\",\n" +
                "        \"desc\": \"商品有瑕疵，需要更换\",\n" +
                "        \"reason\": \"商品质量问题\",\n" +
                "        \"attributes\": \"{\\\"NewExchangeRepair\\\":\\\"true\\\"}\",\n" +
                "        \"refund_phase\": \"aftersale\",\n" +
                "        \"exchange_sku\": \"***********\",\n" +
                "        \"buyer_address\": \"广东省深圳市南山区科技园\",\n" +
                "        \"oaid\": \"buyer_open_uid_123\",\n" +
                "        \"operation_contraint\": \"exchange\",\n" +
                "        \"title\": \"测试商品\",\n" +
                "        \"created\": \"2025-04-10 10:00:00\",\n" +
                "        \"seller_nick\": \"卖家昵称\",\n" +
                "        \"advance_status\": \"NO_ADVANCE\",\n" +
                "        \"buyer_nick\": \"买家昵称\",\n" +
                "        \"buyer_logistic_name\": \"顺丰快递\",\n" +
                "        \"status\": \"换货待处理\",\n" +
                "        \"refund_version\": \"1\",\n" +
                "        \"seller_logistic_name\": \"圆通快递\",\n" +
                "        \"bought_sku\": \"***********\",\n" +
                "        \"modified\": \"2025-04-10 11:00:00\",\n" +
                "        \"dispute_id\": \"123456789\",\n" +
                "        \"num\": 2,\n" +
                "        \"seller_logistic_no\": \"YT9876543210\",\n" +
                "        \"price\": \"50.00\",\n" +
                "        \"time_out\": \"2025-04-17 10:00:00\",\n" +
                "        \"cs_status\": 0,\n" +
                "        \"address\": \"浙江省杭州市余杭区阿里巴巴西溪园区\",\n" +
                "        \"good_status\": \"BUYER_RECEIVED\",\n" +
                "        \"biz_order_id\": \"987654322\",\n" +
                "        \"buyer_phone\": \"***********\",\n" +
                "        \"buyer_name\": \"张三\"\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        //endregion

        // 将JSON字符串反序列化为ExchangeRefundJdpResponseDto对象
        ExchangeRefundJdpResponseDto entity = JSON.parseObject(jsonStr, ExchangeRefundJdpResponseDto.class);

        // 调用被测试方法
        BusinessGetExchangeOrderResponseOrderItem result = RdsExchangeRefundConvertor.convertPolyExchangeOrder(entity);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals("SF1234567890", result.getBuyerLogisticNo());
        Assert.assertEquals("YT9876543210", result.getSellerLogisticNo());
        Assert.assertEquals("2023042822001123456789", result.getPlatOrderNo());
        Assert.assertEquals("987654322", result.getPlatSubOrderNo());
        Assert.assertEquals("123456789", result.getExchangeOrderNo());
        Assert.assertEquals(PolyExchangeStatusEnum.JH_01.getCode(), result.getOrderStatus());
        Assert.assertEquals("商品质量问题", result.getReason());
        Assert.assertEquals("商品有瑕疵，需要更换", result.getDesc());
        Assert.assertEquals(DateTimeUtils.stringToTime("2025-04-10 10:00:00"), result.getCreateTime());
        Assert.assertEquals(DateTimeUtils.stringToTime("2025-04-10 11:00:00"), result.getUpdateTime());
        Assert.assertEquals("买家昵称", result.getBuyerNick());
        Assert.assertEquals("卖家昵称", result.getSellerNick());
        Assert.assertEquals("true", result.getNewExchangeRepair());

        // 验证退货商品信息
        Assert.assertNotNull(result.getRefundGoods());
        Assert.assertEquals(1, result.getRefundGoods().size());
        BusinessGetExchangeResponseRefundGoodInfo refundGood = result.getRefundGoods().get(0);
        Assert.assertEquals("测试商品", refundGood.getProductName());
        Assert.assertEquals("***********", refundGood.getSku());
        Assert.assertEquals(2, refundGood.getProductNum());
        Assert.assertEquals(2, refundGood.getRefundProductNum());
        Assert.assertEquals(new BigDecimal("50.00"), refundGood.getPrice());
        Assert.assertEquals("987654322", refundGood.getSubTradeNo());
        Assert.assertEquals("BUYER_RECEIVED", refundGood.getGoodsStatusDesc());

        // 验证换出商品信息
        Assert.assertNotNull(result.getExchangeGoods());
        Assert.assertEquals(1, result.getExchangeGoods().size());
        Assert.assertEquals("测试商品", result.getExchangeGoods().get(0).getProductName());
        Assert.assertEquals("***********", result.getExchangeGoods().get(0).getSku());
        Assert.assertEquals(2, result.getExchangeGoods().get(0).getProductNum());
        Assert.assertEquals(new BigDecimal("50.00"), result.getExchangeGoods().get(0).getPrice());
        Assert.assertEquals("987654322", result.getExchangeGoods().get(0).getSubTradeNo());
    }

    /**
     * 测试 convertPolyExchangeOrder 方法 - 空值测试
     */
    @Test
    public void testConvertPolyExchangeOrderWithNull() {
        // 测试null值
        BusinessGetExchangeOrderResponseOrderItem result1 = RdsExchangeRefundConvertor.convertPolyExchangeOrder(null);
        Assert.assertNull(result1);

        // 测试空对象
        ExchangeRefundJdpResponseDto entity2 = new ExchangeRefundJdpResponseDto();
        BusinessGetExchangeOrderResponseOrderItem result2 = RdsExchangeRefundConvertor.convertPolyExchangeOrder(entity2);
        Assert.assertNull(result2);

        // 测试空ExchangeGetResponse
        ExchangeRefundJdpResponseDto entity3 = new ExchangeRefundJdpResponseDto();
        ExchangeRefundJdpResponseDto.ExchangeGetResponse exchangeGetResponse = new ExchangeRefundJdpResponseDto.ExchangeGetResponse();
        entity3.setTmallExchangeGetResponse(exchangeGetResponse);
        BusinessGetExchangeOrderResponseOrderItem result3 = RdsExchangeRefundConvertor.convertPolyExchangeOrder(entity3);
        Assert.assertNull(result3);

        // 测试空ExchangeResult
        ExchangeRefundJdpResponseDto entity4 = new ExchangeRefundJdpResponseDto();
        ExchangeRefundJdpResponseDto.ExchangeGetResponse exchangeGetResponse2 = new ExchangeRefundJdpResponseDto.ExchangeGetResponse();
        ExchangeRefundJdpResponseDto.ExchangeResult exchangeResult = new ExchangeRefundJdpResponseDto.ExchangeResult();
        exchangeGetResponse2.setResult(exchangeResult);
        entity4.setTmallExchangeGetResponse(exchangeGetResponse2);
        BusinessGetExchangeOrderResponseOrderItem result4 = RdsExchangeRefundConvertor.convertPolyExchangeOrder(entity4);
        Assert.assertNull(result4);
    }
}
