package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;

import java.util.List;

/**
 * 售后单处理插件-前置批量查询
 *
 * <AUTHOR>
 * @date 2024/7/22 下午7:43
 */
public interface IPerBatchProcessOrderHandle<O> {
    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单列表
     * @return 过滤结果
     */
    AfterSaleHandleResult perBatchProcess(List<SourceAfterSaleOrderItem<O>> orderItems);
}
