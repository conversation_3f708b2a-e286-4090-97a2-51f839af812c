package com.differ.wdgj.api.component.multidb.aop;

import com.differ.wdgj.api.component.multidb.DataSourceContextHolder;
import com.differ.wdgj.api.component.multidb.annotation.DataSourceAutoSwitcher;
import com.differ.wdgj.api.component.multidb.core.DataSourceContext;
import com.differ.wdgj.api.component.multidb.core.IDataSourceContextContainer;
import com.differ.wdgj.api.component.multidb.exception.MultidbException;
import com.differ.wdgj.api.component.util.aop.AopUtil;
import com.differ.wdgj.api.component.util.functional.zero.Action;
import com.differ.wdgj.api.component.util.functional.zero.Function;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 数据库自动切换标记的切面，注解： {@link DataSourceAutoSwitcher}
 *
 * <AUTHOR>
 * @date 2020-07-10 10:38
 */
@Aspect
@Component
@Order(1)
public class DataSourceAutoSwitcherAop {

    /**
     * 切面点：带注解 {@link DataSourceAutoSwitcher} 的类或方法。
     */
    @Pointcut("@within(com.differ.wdgj.api.component.multidb.annotation.DataSourceAutoSwitcher)||@annotation(com.differ.wdgj.api.component.multidb.annotation.DataSourceAutoSwitcher)")
    public void pointcut() {
        // 切面点
    }

    /**
     * 注解切点之前执行方法
     *
     * @param joinPoint 反射出的方法参数
     */
    @Before("pointcut()")
    public void doBefore(JoinPoint joinPoint) {
        // 数据库上下文
        Object target = joinPoint.getTarget();
        // 优先取继承接口的参数
        DataSourceContext context = null;
        IDataSourceContextContainer parameter = AopUtil.getParameter(joinPoint, IDataSourceContextContainer.class, true);
        if (parameter != null) {
            context = parameter.getDataSourceContext();
        }
        // 再取参数中的上下文,
        if (context == null) {
            context = AopUtil.getParameter(joinPoint, DataSourceContext.class,true);
        }
        if (context == null && IDataSourceContextContainer.class.isAssignableFrom(target.getClass())) {
            Scope scope = AopUtil.getAnnotation(joinPoint, Scope.class);
            if (scope == null || !ConfigurableBeanFactory.SCOPE_PROTOTYPE.equals(scope.value())) {
                // 上下文不支持非多例模式
                throw new MultidbException("数据库上下文参数不正确");
            }
            // 多例模式时支取上下文
            context = ((IDataSourceContextContainer) target).getDataSourceContext();
        }

        if (context == null) {
            throw new MultidbException("数据库上下文参数不能为空");
        }
        DataSourceContextHolder.set(context);
    }

    /**
     * 注解切点之后执行方法
     *
     * @param joinPoint 反射出的方法参数
     */
    @After("pointcut()")
    public void after(JoinPoint joinPoint) {
        //清除上下文资源
        DataSourceContextHolder.remove();
    }

    /**
     * 执行数据库操作
     *
     * @param context
     * @param fun
     */
    public static void doDBExec(DataSourceContext context, Action fun) {
        try {
            DataSourceContextHolder.set(context);
            fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }

    /**
     * 执行数据库操作
     *
     * @param context
     * @param fun
     */
    public static <R> R doDBExec(DataSourceContext context, Function<R> fun) {
        try {
            DataSourceContextHolder.set(context);
            return fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }
}
