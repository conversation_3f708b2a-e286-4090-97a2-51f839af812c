package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.config;

import com.differ.wdgj.api.component.redis.config.MultiRedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 云端会员数据缓存配置(由InfrastructureBeansConfig配置bean)
 *
 * <AUTHOR>
 * @date 2024/2/5 16:59
 */
@ConfigurationProperties(prefix = "wdgj.api.redis.member", ignoreInvalidFields = true)
public class MemberMultiRedisProperties extends MultiRedisProperties {
}
