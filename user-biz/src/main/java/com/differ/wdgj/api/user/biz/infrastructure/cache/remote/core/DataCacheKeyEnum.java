/*
 * Copyright(C) 2017 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */

package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.*;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import org.springframework.util.StringUtils;

/**
 * 数据缓存键。
 * 注意：
 * 1. 基础缓存请以DT_打头；自定义缓存以CUSTOMER_打头
 * 2. 不支持缓存刷新的，构造器最后三项固定为null, false, null
 *
 * <AUTHOR>
 * @since 2019-12-24  15:01
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class, serializer = EnumCodeValueWriteSerializer.class)
public enum DataCacheKeyEnum implements CodeEnum {
    // region java基础缓存 - 中心库

    /**
     * 数据表：operation.dbconfig
     */
    DT_GLO_DBCONFIG("Glo.DBConfig", null, DataCacheTypeEnum.NORMAL_BASE, null, true, "DbConfigCache"),

    /**
     * 数据表：AccountMapping
     */
    DT_MEMBER_ACCOUNTMAPPING("Member.AccountMapping", null, DataCacheTypeEnum.NORMAL_BASE, new String[]{}, true, "AccountMappingCache"),

    /**
     * 数据表：Glo_ServerRdsMapping
     */
    DT_GLO_SERVERRDSMAPPING("Glo.ServerRdsMapping", null, DataCacheTypeEnum.NORMAL_BASE, new String[]{}, true, "ServerRdsMappingCache"),

    /**
     * 数据表：dev_plat_bizFeature
     */
    DT_DEV_PLATBIZFEATURE("Dev.PlatBizFeatureDto", null, DataCacheTypeEnum.NORMAL_BASE, new String[]{}, true, "PlatBizFeatureCache"),

    // endregion

    // region java基础缓存 - 会员库

    /**
     * 数据表：g_cfg_shopListExt<p/>
     */
    USER_API_SHOP_CONFIG("user.api.shopConfig_", "%s", DataCacheTypeEnum.NORMAL_BASE, new String[]{"OutAccount"}, true, "ApiShopConfigCache"),

    /**
     * 会员所有店铺<p/>
     */
    USER_API_ALL_SHOP("user.api.allShop", "", DataCacheTypeEnum.NORMAL_BASE, new String[]{}, true, "ApiAllShopCache"),

    /**
     * 自动下载售后单店铺
     */
    USER_API_AUTO_LOAD_AFTER_SALE("user.api.auto.load.aftersale", "", DataCacheTypeEnum.NORMAL_BASE, new String[]{}, true, "AutoLoadAfterShopCache"),

    // endregion

    // region dotNet维护缓存 - 只读

    /**
     * 数据表：dev_shop
     */
    DT_OPERATION_SHOP("Dev.Shop_", "%s_%s", DataCacheTypeEnum.DOT_NET_NORMAL_BASE, new String[]{"OutPlatTypes转整型", "OutAccount"}, false, null),

    /**
     * 数据表：glo_splitService
     */
    DT_GLO_SPLITSERVICE("Glo.Splitservice", "", DataCacheTypeEnum.DOT_NET_NORMAL_BASE, new String[]{}, false, null),

    /**
     * 数据表：member_user
     */
    DT_MEMBER_USER("Member.User", "", DataCacheTypeEnum.DOT_NET_NORMAL_BASE, new String[]{}, false, null),

    // endregion

    //region 自定义缓存
    /**
     * 发送短信策略缓存
     */
    CUSTOMER_SMSSENDRECORD("operation.smssendrecord.", "%s", DataCacheTypeEnum.NORMAL_BASE, new String[]{"hash键(通过手机号和短信内容生成缓存健CacheKey)"}, false, "SmsSendCache"),
    /**
     * 通用缓存代理
     */
    CUSTOMER_COMMON("common", null, DataCacheTypeEnum.NORMAL_BASE, null, false, "CommonDataCacheProxy"),
    /**
     * 缓存代理：当数据源发生变化。
     */
    CUSTOMER_DATASOURCECHANGED("rm.refurmemorydata", null, DataCacheTypeEnum.REALTIME_DATA, null, false, "DataSourceChangedCache"),

    //endregion

    //region 网店管家云端缓存

    /**
     * 数据表：operation.dbconfig
     */
    WDGJYUN_BASICCFG_DATASOURCE("c001-wdgjyun-basiccfg-datasource", null, DataCacheTypeEnum.WDGJYUN_MEMBER, null, false, "WdgjYunDataSourceCache"),


    /**
     * 数据表：operation.dbconfig
     */
    WDGJYUN_BASICCFG_MEMBER_DATASOURCE("c001-wdgjyun-basiccfg-member-datasource", null, DataCacheTypeEnum.WDGJYUN_MEMBER, null, false, "WdgjYunMemberDataSourceCache"),

    //endregion

    ;

    //region 构造器

    /**
     * 构造器**
     *
     * @param preFix                前缀
     * @param format                格式化字符串
     * @param cacheKeyArgs          缓存键参数集(中文提示)
     * @param isSupportFlashCache   是否支持刷新缓存
     * @param dataProxyTypeBeanName 对应的缓存代理类类型bean
     */
    private DataCacheKeyEnum(String preFix, String format, DataCacheTypeEnum cacheType, String[] cacheKeyArgs, boolean isSupportFlashCache, String dataProxyTypeBeanName) {
        this.preFix = preFix.toLowerCase();
        this.code = preFix.toLowerCase();
        if (!StringUtils.isEmpty(format)) {
            this.code += format.toLowerCase();
        }
        this.cacheType = cacheType;
        this.cacheKeyArgs = cacheKeyArgs;
        this.isSupportFlashCache = isSupportFlashCache;
        this.dataProxyTypeBeanName = dataProxyTypeBeanName;
    }

    //endregion

    //region 变量

    /**
     * 标题
     */
    private String code;
    /**
     * 缓存键参数集(中文提示)。
     */
    private String[] cacheKeyArgs;
    /**
     * 是否支持刷新缓存。
     */
    private boolean isSupportFlashCache;
    /**
     * 对应的缓存代理类类型。
     */
    private Class<? extends IHashDataCache> dataProxyType;

    /**
     * 对应的缓存代理类类型bean名。
     */
    private String dataProxyTypeBeanName;

    /**
     * 缓存类型
     */
    private DataCacheTypeEnum cacheType;

    /**
     * 前缀。String类型的缓存要求以“:”结尾
     */
    private String preFix;

    //endregion

    //region 方法

    /**
     * 获取标题
     *
     * @return 标题
     */
    @Override
    public String getCode() {
        return String.format("{wdgj.api.%s}", this.code);
    }

    /**
     * 获取原始cacheKey
     * 注意：只有使用外部源redis的时候才需要使用
     *
     * @return 原始cacheKey
     */
    public String getOriginalCode() {
        return code;
    }

    /**
     * 枚举值字符串
     *
     * @return S
     */
    @Override
    public String toString() {
        return this.getCode();
    }

    /**
     * 获取 缓存键参数集(中文提示)
     *
     * @return
     */
    public String[] getCacheKeyArgs() {
        return this.cacheKeyArgs;
    }

    /**
     * 获取 是否支持刷新缓存
     *
     * @return
     */
    public boolean getIsSupportFlashCache() {
        return this.isSupportFlashCache;
    }

    /**
     * 获取 对应的缓存代理类类型
     *
     * @return
     */
    public IHashDataCache getDataProxyType() {
        if (StringUtils.isEmpty(this.dataProxyTypeBeanName)) {
            return null;
        }
        return BeanContextUtil.getBean(this.dataProxyTypeBeanName, IHashDataCache.class);
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static DataCacheKeyEnum convert(String value) {
        return EnumConvertCacheUtil.convert(DataCacheKeyEnum.class, value, EnumConvertType.CODE);
    }

    /**
     * 获取缓存类别
     *
     * @return
     */
    public DataCacheTypeEnum getCacheType() {
        return cacheType;
    }

    /**
     * 获取前缀
     *
     * @return
     */
    public String getPreFix() {
        return preFix;
    }

    //endregion
}
