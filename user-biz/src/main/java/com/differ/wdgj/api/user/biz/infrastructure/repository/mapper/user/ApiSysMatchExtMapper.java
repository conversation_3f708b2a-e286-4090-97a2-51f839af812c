package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * api商品匹配扩展仓储
 *
 * <AUTHOR>
 * @date 2024-03-05 14:05
 */
public interface ApiSysMatchExtMapper extends BasicOperateMapper<ApiSysMatchExtDO> {
    // region 查询

    /**
     * 批量获取商品匹配扩展
     *
     * @param ids 商品匹配表id - 列表
     * @return 商品匹配列表
     */
    List<ApiSysMatchExtDO> selectByIds(@Param("ids") List<Integer> ids);

    // endregion

    // region 修改

    /**
     * 批量获取商品匹配扩展
     *
     * @param jsonParamsMap key:匹配表Id，value:扩展数据jsonParams
     * @return 商品匹配列表
     */
    int batchUpdateJsonParams(@Param("jsonParamsMap") Map<Integer, String> jsonParamsMap);

    // endregion

    // region 新增

    /**
     * 批量新增或更新api商品匹配扩展
     *
     * @param goodsMatchExtraList 匹配扩展集合
     * @return 影响行数
     */
    int addOrUpdateGoodsMatchExtra(@Param("goodsMatchExtraList") List<ApiSysMatchExtDO> goodsMatchExtraList);

    // endregion

    // region 删除

    /**
     * 根据主键删除商品匹配
     *
     * @param ids 商品匹配表id - 列表
     */
    void deleteById(@Param("ids") List<Integer> ids);

    // endregion
}
