package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj.WdgjYunMemberDataSourceCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WdgjYunMemberDataSourceDto;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 网店管家云端 - 会员数据库链接缓存
 * <AUTHOR>
 * @date 2024-03-26 15:33
 */
@Ignore
public class WdgjYunMemberDataSourceCacheTest extends AbstractSpringTest {

    /**
     * 获取 会员数据库链接缓存
     */
    @Test
    public void getMemberDataSourceTest(){
        WdgjYunMemberDataSourceDto api2017 = WdgjYunMemberDataSourceCache.singleton().getMemberDataSource("api2017");
        Assert.assertNotNull(api2017);
    }
}
