package com.differ.wdgj.api.user.biz.domain.stock.data;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.GoodsPolyEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;

/**
 * 匹配数据扩展类
 *
 * <AUTHOR>
 * @date 2024-03-01 19:30
 */
public class GoodsMatchEnhance {

    /**
     * 匹配数据
     */
    private ApiSysMatchDO sysMatch;

    /**
     * 平台仓标识
     */
    private String multiSign;

    /**
     * 平台值
     * {@link PolyPlatEnum}
     */
    private int platValue;

    /**
     * 创建实例
     *
     * @param sysMatch 匹配数据
     * @return 实例
     */
    public static GoodsMatchEnhance create(ApiSysMatchDO sysMatch) {
        GoodsMatchEnhance matchEnhance = new GoodsMatchEnhance();
        matchEnhance.setSysMatch(sysMatch);
        matchEnhance.setMultiSign("");
        matchEnhance.setPlatValue(GoodsPolyEnum.create(sysMatch.getbTBGoods()).getPolyPlat().getValue());
        return matchEnhance;
    }

    /**
     * 创建实例
     *
     * @param sysMatch  匹配数据
     * @param multiSign 多仓标识
     * @return 实例
     */
    public static GoodsMatchEnhance create(ApiSysMatchDO sysMatch, String multiSign) {
        GoodsMatchEnhance matchEnhance = new GoodsMatchEnhance();
        matchEnhance.setSysMatch(sysMatch);
        matchEnhance.setMultiSign(multiSign);
        matchEnhance.setPlatValue(GoodsPolyEnum.create(sysMatch.getbTBGoods()).getPolyPlat().getValue());
        return matchEnhance;
    }

    //region get/set

    public String getMultiSign() {
        return multiSign;
    }

    public void setMultiSign(String multiSign) {
        this.multiSign = multiSign;
    }

    public ApiSysMatchDO getSysMatch() {
        return sysMatch;
    }

    public void setSysMatch(ApiSysMatchDO sysMatch) {
        this.sysMatch = sysMatch;
    }

    public int getPlatValue() {
        return platValue;
    }

    public void setPlatValue(int platValue) {
        this.platValue = platValue;
    }
    //endregion
}
