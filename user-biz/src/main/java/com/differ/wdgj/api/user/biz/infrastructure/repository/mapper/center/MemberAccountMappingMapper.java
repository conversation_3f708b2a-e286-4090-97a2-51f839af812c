package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberAccountMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;

/**
 * 外部账号映射
 *
 * <AUTHOR>
 * @date 2024/3/1 14:07
 */
public interface MemberAccountMappingMapper extends BasicOperateMapper<MemberAccountMappingDO> {

    MemberAccountMappingDO selectByUserName(String username);
}
