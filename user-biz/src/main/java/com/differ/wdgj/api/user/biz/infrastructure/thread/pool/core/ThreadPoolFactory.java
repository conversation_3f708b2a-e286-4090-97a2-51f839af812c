package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject.TryRequeueRunsPolicy;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @Description 线程池工厂
 * <AUTHOR>
 * @Date 2021/11/23 16:23
 */
public class ThreadPoolFactory {

    /**
     * 业务线程池
     */
    private ConcurrentHashMap<TaskEnum, BaseThreadPoolExecutor> threadMap = new ConcurrentHashMap<>();

    //region 构造和枚举单例

    private ThreadPoolFactory() {
        // 私有，为了单例
    }

    /**
     * 枚举单例
     *
     * @return
     */
    public static ThreadPoolFactory singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        SINGLETON;

        private ThreadPoolFactory instance;

        private SingletonEnum() {
            instance = new ThreadPoolFactory();
        }
    }

    //endregion

    public Map<String, Object> getPoolInfo(TaskEnum taskEnum) {
        if (!threadMap.containsKey(taskEnum)) {
            return null;
        }
        BaseThreadPoolExecutor threadPoolExecutor = threadMap.get(taskEnum);
        BaseThreadPoolExecutor.ExecuteAndWaitCount executeAndWait = threadPoolExecutor.getExecuteAndWait();
        Map<String, Object> map = new HashMap<>();
        map.put(" ", taskEnum.getNamePreFix());
        map.put("ActiveCount", executeAndWait.getExecuteCount());
        map.put("CorePoolSize", threadPoolExecutor.getCorePoolSize());
        map.put("PoolSize", threadPoolExecutor.getPoolSize());
        map.put("LargestPoolSize", threadPoolExecutor.getLargestPoolSize());
        map.put("MaximumPoolSize", threadPoolExecutor.getMaximumPoolSize());
        map.put("CompletedTaskCount", threadPoolExecutor.getCompletedTaskCount());
        map.put("KeepAliveTime", threadPoolExecutor.getKeepAliveTime(TimeUnit.SECONDS));
        map.put("BlockingQueueSize", threadPoolExecutor.getQueue().size());
        map.put("TaskCount", executeAndWait.getExecuteCount() + executeAndWait.getWaitCount());
        map.put("RejectedExecutionHandler", threadPoolExecutor.getRejectedExecutionHandler().getClass());
        map.put("BlockingQueueAlarmLimit", threadPoolExecutor.getBlockingQueueAlarmLimit());
        return map;
    }

    /**
     * 获取线程池，没有时创建线程池
     *
     * @param taskEnum
     * @return
     */
    public BaseThreadPoolExecutor getOrCreateIfAbsent(TaskEnum taskEnum) {
        BaseThreadPoolExecutor threadPoolExecutor = threadMap.computeIfAbsent(taskEnum, t -> create(t));

        /*
        线程池参数修改后，重新设值
         */
        int corePoolSize = taskEnum.getCorePoolSize();
        if (threadPoolExecutor.getCorePoolSize() != corePoolSize) {
            threadPoolExecutor.setCorePoolSize(corePoolSize);
        }
        int maximumPoolSize = taskEnum.getMaximumPoolSize();
        if (maximumPoolSize < corePoolSize){
            // 最大线程数必须大于等于核心线程数
            maximumPoolSize = corePoolSize;
        }
        if (threadPoolExecutor.getMaximumPoolSize() != maximumPoolSize) {
            threadPoolExecutor.setMaximumPoolSize(maximumPoolSize);
        }

        return threadPoolExecutor;
    }

    /**
     * 创建线程池
     *
     * @param taskEnum
     * @return
     */
    private BaseThreadPoolExecutor create(TaskEnum taskEnum) {
        // 设值线程池参数
        int corePoolSize = taskEnum.getCorePoolSize();
        if (corePoolSize < 1) {
            // 防止线程池初始化参数异常：核心线程数必须大于0
            corePoolSize = 1;
        }
        int maximumPoolSize = taskEnum.getMaximumPoolSize();
        if (maximumPoolSize < corePoolSize) {
            // 防止线程池初始化参数异常：最大线程数必须大于等于核心线程数
            maximumPoolSize = corePoolSize;
        }

        long keepAliveTime = taskEnum.getKeepAliveTime();
        if (keepAliveTime < 0) {
            // 防止线程池初始化参数异常
            keepAliveTime = 0;
        }

        TimeUnit unit = taskEnum.getUnit();
        int queueCapacity = taskEnum.getQueueCapacity();

        // 阻塞队列
        BlockingQueue<Runnable> blockingQueue;
        if (queueCapacity > 0) {
            blockingQueue = new LinkedBlockingQueue<Runnable>(queueCapacity);
        } else {
            blockingQueue = new SynchronousQueue<Runnable>();
        }

        try {
            // 拒绝策略
            RejectedExecutionHandler rejectedHandler = null;
            if (StringUtils.isNotEmpty(taskEnum.getRejectedHandlerBean())) {
                // 优先取bean的拒绝策略
                rejectedHandler = BeanContextUtil.getBean(taskEnum.getRejectedHandlerBean(), RejectedExecutionHandler.class);
            } else if (taskEnum.getRejectedHandlerClass() != null) {
                // 再取class的拒绝策略
                rejectedHandler = taskEnum.getRejectedHandlerClass().newInstance();
            } else {
                // 默认拒绝策略
                rejectedHandler = new TryRequeueRunsPolicy();
            }
            // 创建线程池
            if (taskEnum.equals(TaskEnum.API_SYNC)) {
                return new SyncPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, unit, blockingQueue, taskEnum, rejectedHandler);
            }
            return new BaseThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, unit, blockingQueue, taskEnum, rejectedHandler);
        } catch (Exception e) {
            LogFactory.get("线程池工厂").error(String.format("线程池初始异常:%s", taskEnum.getNamePreFix()), e);
            throw new RuntimeException(String.format("线程池初始失败:%s", taskEnum.getNamePreFix()), e);
        }
    }

}
