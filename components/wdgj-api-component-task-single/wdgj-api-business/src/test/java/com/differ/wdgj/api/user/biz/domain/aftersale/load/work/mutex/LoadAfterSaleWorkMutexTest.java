package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * LoadAfterSaleWorkMutex 单元测试 - 第一批：基础功能测试
 * 不使用MockedStatic，专注于基础业务逻辑验证
 *
 * <AUTHOR>
 * @date 2024/12/19 下午2:30
 */
public class LoadAfterSaleWorkMutexTest extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 测试消息推送不互斥场景
     * 当触发类型为MESSAGE_NOTIFICATION且加载类型为AFTER_SALE_NO时，直接创建任务
     */
    @Test
    public void testCreate_MessageNotificationWithAfterSaleNo_ShouldCreateDirectly() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION,
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        CreateResult expectedResult = CreateResult.successResult("test-task-id");

        // 模拟依赖行为
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "test-task-id", result.getTaskId());

        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
        // 验证没有调用父类的existsUnComplete方法（因为直接返回了）
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnComplete(workData);
    }

    /**
     * 测试消息推送但加载类型不是AFTER_SALE_NO的场景
     * 应该继续执行后续逻辑而不是直接创建
     */
    @Test
    public void testCreate_MessageNotificationWithOtherLoadType_ShouldContinue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION,
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );

        // 模拟父类逻辑：不存在未完成任务，创建新任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果 - 应该通过父类逻辑创建任务
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试非消息推送触发类型的场景
     * 应该继续执行后续逻辑
     */
    @Test
    public void testCreate_NonMessageNotification_ShouldContinue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND,
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );

        // 模拟父类逻辑：不存在未完成任务，创建新任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果 - 应该通过父类逻辑创建任务
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试args为null的边界情况
     * 当AfterSaleLoadArgs为null时，应该跳过消息推送检查，继续执行后续逻辑
     */
    @Test
    public void testCreate_ArgsNull_ShouldSkipMessageCheck() {
        // 准备测试数据
        WorkContext workContext = WorkContext.of(
            "test-member",
            WorkEnum.LOAD_AFTER_SALE,
            1001,
            TriggerTypeEnum.MESSAGE_NOTIFICATION,
            "test-creator"
        );
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, null);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");

        // 模拟父类逻辑：不存在未完成任务，创建新任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);

        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);

        // 验证结果 - 应该通过父类逻辑创建任务
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());

        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 创建测试用的WorkData对象
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "test-member",
            WorkEnum.LOAD_AFTER_SALE,
            1001,
            triggerType,
            "test-creator"
        );

        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);

        return WorkData.of(workContext, args);
    }
}
