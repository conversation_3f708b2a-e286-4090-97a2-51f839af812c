<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiEncryptTradeMapper">
    <!-- 定义包含jdbcType的BaseResultMap -->
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiEncryptTradeDO">
        <!-- 主键映射 -->
        <id column="id" property="id" jdbcType="INTEGER" />

        <!-- 普通字段映射 -->
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="tradeno" property="tradeNo" jdbcType="VARCHAR" />
        <result column="shopid" property="shopId" jdbcType="INTEGER" />
        <result column="customeraccount" property="customerAccount" jdbcType="VARCHAR" />
        <result column="receivername" property="receiverName" jdbcType="VARCHAR" />
        <result column="mobile" property="mobile" jdbcType="VARCHAR" />
        <result column="phone" property="phone" jdbcType="VARCHAR" />
        <result column="province" property="province" jdbcType="VARCHAR" />
        <result column="city" property="city" jdbcType="VARCHAR" />
        <result column="area" property="area" jdbcType="VARCHAR" />
        <result column="town" property="town" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="address2" property="addressTwo" jdbcType="VARCHAR" />
        <result column="invoicetitle" property="invoiceTitle" jdbcType="VARCHAR" />
        <result column="invoicecode" property="invoiceCode" jdbcType="VARCHAR" />
        <result column="invoicephone" property="invoicePhone" jdbcType="VARCHAR" />
        <result column="invoiceregphone" property="invoiceregPhone" jdbcType="VARCHAR" />
        <result column="invoicebankaccount" property="invoiceBankAccount" jdbcType="VARCHAR" />
        <result column="invoiceuseraddress" property="invoiceUserAddress" jdbcType="VARCHAR" />
        <result column="invoiceuserphone" property="invoiceUserPhone" jdbcType="VARCHAR" />
        <result column="payno" property="payNo" jdbcType="VARCHAR" />
        <result column="idcardname" property="idCardName" jdbcType="VARCHAR" />
        <result column="idcardno" property="idCardNo" jdbcType="VARCHAR" />
        <result column="innertransactionid" property="innerTransactionId" jdbcType="VARCHAR" />
        <result column="gmtcreate" property="gmtCreate" jdbcType="TIMESTAMP" />
        <result column="gmtmodified" property="gmtModified" jdbcType="TIMESTAMP" />
        <result column="polyapitoken" property="polyApiToken" jdbcType="VARCHAR" />
        <result column="selffetch_info" property="selffetchInfo" jdbcType="VARCHAR" />
        <result column="platshopid" property="platShopId" jdbcType="VARCHAR" />
        <result column="ReceiverMaskID" property="receiverMaskId" jdbcType="VARCHAR" />
        <result column="StoreConsigneeCID" property="storeConsigneeCID" jdbcType="VARCHAR" />
        <result column="InvoiceReceiverCID" property="invoiceReceiverCID" jdbcType="VARCHAR" />
        <result column="email" property="email" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 定义基础列列表 -->
    <sql id="BaseColumnList">
        id, type, tradeno, shopid, customeraccount, receivername, mobile, phone,
        province, city, area, town, address, address2, invoicetitle, invoicecode,
        invoicephone, invoiceregphone, invoicebankaccount, invoiceuseraddress, invoiceuserphone,
        payno, idcardname, idcardno, innertransactionid, gmtcreate, gmtmodified, polyapitoken,
        selffetch_info, platshopid, ReceiverMaskID, StoreConsigneeCID, InvoiceReceiverCID,
        email, type
    </sql>

    <!-- 根据billIds批量查询ReturnInfo -->
    <select id="selectByTradeNos" resultMap="BaseResultMap">
        SELECT <include refid="BaseColumnList"/>
        FROM g_api_encrypttradelist
        WHERE type = #{type} AND shopId = #{shopId} AND tradeNo IN
        <foreach item="tradeNo" collection="tradeNos" open="(" separator="," close=")">
            #{tradeNo}
        </foreach>
    </select>

    <!-- 批量新增/更新 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO g_api_encrypttradelist
        (tradeno, shopid, customeraccount, receivername, mobile, phone, province, city, area, town, address, address2, invoicetitle, invoicecode, invoicephone, invoiceregphone, invoicebankaccount, invoiceuseraddress, invoiceuserphone, payno, idcardname, idcardno, innertransactionid, gmtcreate, gmtmodified, polyApiToken, selffetch_info, platshopid, ReceiverMaskID, StoreConsigneeCID, InvoiceReceiverCID, email, type)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.tradeNo}, #{item.shopId}, #{item.customerAccount}, #{item.receiverName}, #{item.mobile}, #{item.phone}, #{item.province}, #{item.city}, #{item.area}, #{item.town}, #{item.address}, #{item.addressTwo}, #{item.invoiceTitle}, #{item.invoiceCode}, #{item.invoicePhone}, #{item.invoiceregPhone}, #{item.invoiceBankAccount}, #{item.invoiceUserAddress}, #{item.invoiceUserPhone}, #{item.payNo}, #{item.idCardName}, #{item.idCardNo}, #{item.innerTransactionId}, #{item.gmtCreate}, #{item.gmtModified}, #{item.polyApiToken}, #{item.selffetchInfo}, #{item.platShopId}, #{item.receiverMaskId}, #{item.storeConsigneeCID}, #{item.invoiceReceiverCID}, #{item.email}, #{item.type})
        </foreach>
        ON DUPLICATE KEY UPDATE
        tradeno = VALUES(tradeno),
        shopid = VALUES(shopid),
        customeraccount = VALUES(customeraccount),
        receivername = VALUES(receivername),
        mobile = VALUES(mobile),
        phone = VALUES(phone),
        province = VALUES(province),
        city = VALUES(city),
        area = VALUES(area),
        town = VALUES(town),
        address = VALUES(address),
        address2 = VALUES(address2),
        invoicetitle = VALUES(invoicetitle),
        invoicecode = VALUES(invoicecode),
        invoicephone = VALUES(invoicephone),
        invoiceregphone = VALUES(invoiceregphone),
        invoicebankaccount = VALUES(invoicebankaccount),
        invoiceuseraddress = VALUES(invoiceuseraddress),
        invoiceuserphone = VALUES(invoiceuserphone),
        payno = VALUES(payno),
        idcardname = VALUES(idcardname),
        idcardno = VALUES(idcardno),
        innertransactionid = VALUES(innertransactionid),
        gmtcreate = VALUES(gmtcreate),
        gmtmodified = VALUES(gmtmodified),
        polyApiToken = VALUES(polyApiToken),
        selffetch_info = VALUES(selffetch_info),
        platshopid = VALUES(platshopid),
        ReceiverMaskID = VALUES(ReceiverMaskID),
        StoreConsigneeCID = VALUES(StoreConsigneeCID),
        InvoiceReceiverCID = VALUES(InvoiceReceiverCID),
        email = VALUES(email),
        type = VALUES(type);
    </insert>

    <!-- 批量新增 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO g_api_encrypttradelist
        (tradeno, shopid, customeraccount, receivername, mobile, phone, province, city, area, town, address, address2, invoicetitle, invoicecode, invoicephone, invoiceregphone, invoicebankaccount, invoiceuseraddress, invoiceuserphone, payno, idcardname, idcardno, innertransactionid, gmtcreate, gmtmodified, polyApiToken, selffetch_info, platshopid, ReceiverMaskID, StoreConsigneeCID, InvoiceReceiverCID, email, type)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tradeNo}, #{item.shopId}, #{item.customerAccount}, #{item.receiverName}, #{item.mobile}, #{item.phone}, #{item.province}, #{item.city}, #{item.area}, #{item.town}, #{item.address}, #{item.addressTwo}, #{item.invoiceTitle}, #{item.invoiceCode}, #{item.invoicePhone}, #{item.invoiceregPhone}, #{item.invoiceBankAccount}, #{item.invoiceUserAddress}, #{item.invoiceUserPhone}, #{item.payNo}, #{item.idCardName}, #{item.idCardNo}, #{item.innerTransactionId}, #{item.gmtCreate}, #{item.gmtModified}, #{item.polyApiToken}, #{item.selffetchInfo}, #{item.platShopId}, #{item.receiverMaskId}, #{item.storeConsigneeCID}, #{item.invoiceReceiverCID}, #{item.email}, #{item.type})
        </foreach>
    </insert>

    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE g_api_encrypttradelist
            SET
            customeraccount = #{item.customerAccount},
            receivername = #{item.receiverName},
            mobile = #{item.mobile},
            phone = #{item.phone},
            province = #{item.province},
            city = #{item.city},
            area = #{item.area},
            town = #{item.town},
            address = #{item.address},
            address2 = #{item.addressTwo},
            invoicetitle = #{item.invoiceTitle},
            invoicecode = #{item.invoiceCode},
            invoicephone = #{item.invoicePhone},
            invoiceregphone = #{item.invoiceregPhone},
            invoicebankaccount = #{item.invoiceBankAccount},
            invoiceuseraddress = #{item.invoiceUserAddress},
            invoiceuserphone = #{item.invoiceUserPhone},
            payno = #{item.payNo},
            idcardname = #{item.idCardName},
            idcardno = #{item.idCardNo},
            innertransactionid = #{item.innerTransactionId},
            gmtcreate = #{item.gmtCreate},
            gmtmodified = #{item.gmtModified},
            polyApiToken = #{item.polyApiToken},
            selffetch_info = #{item.selffetchInfo},
            platshopid = #{item.platShopId},
            ReceiverMaskID = #{item.receiverMaskId},
            StoreConsigneeCID = #{item.storeConsigneeCID},
            InvoiceReceiverCID = #{item.invoiceReceiverCID},
            email = #{item.email}
            WHERE id = #{item.id}
        </foreach>
    </update>
</mapper>