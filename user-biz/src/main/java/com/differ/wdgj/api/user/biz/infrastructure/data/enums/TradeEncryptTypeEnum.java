package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 订单密文类型枚举
 *
 * <AUTHOR>
 * @date 2024-07-01 10:51
 */
public enum TradeEncryptTypeEnum implements ValueEnum {
    /**
     * 订单密文
     */
    ORDER_ENCRYPT(0),

    /**
     * 换货单密文
     */
    EXCHANGE_ENCRYPT(1);

    /**
     * 枚举值
     */
    private final Integer value;

    TradeEncryptTypeEnum(Integer value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
