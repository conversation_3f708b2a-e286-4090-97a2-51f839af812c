package com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.demo;

import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.component.util.hash.HashUtil;
import com.differ.wdgj.api.user.biz.infrastructure.condition.ConditionalOnEnvType;
import com.differ.wdgj.api.user.biz.infrastructure.data.SystemEnvTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.AbstractQueueJob;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.SimpleQueueJobParameter;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.JobResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.UserQueueJobData;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue.UserClusterJobTaskSourceQueue;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.List;

/**
 * 示例：用户和集群两级排队的队列定时任务
 *
 * <AUTHOR>
 * @date 2024/5/29 11:59
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "job.queue.test.demo",
        cron = "0/3 * * * * ?"
)
@SimpleQueueJobParameter(jobCode = "job.queue.t.demo", JobQueue = UserClusterJobTaskSourceQueue.class)
@ConditionalOnEnvType({SystemEnvTypeEnum.DEV})
public class DemoUserClusterQueueJob extends AbstractQueueJob<DemoUserClusterQueueJob.DemoData> {

    /**
     * 获取自动生成的初始化任务，强调 ：是自动的，不是外部加的，是集群任务定时初始化的，不是直接拿来用的
     *
     * @return 全部任务
     */
    @Override
    protected List<DemoData> getAllAutoInitTasks() {
        List<DemoData> lst = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            DemoData demoData = new DemoData();
            demoData.setMemberName("api2017");
            demoData.setData("data_" + i + ",create:" + DateTime.now());
            lst.add(demoData);
        }

        return lst;
    }

    /**
     * 取线程池
     *
     * @return
     */
    @Override
    protected TaskEnum taskPool() {
        return TaskEnum.API_SYNC;
    }

    /**
     * 获取任务执行的最小时间（单位：秒）,大于0才会起效限制
     * 最小执行时间，当执行小于此时间时，会sleep后再执行完成回调
     *
     * @return 拉取执行频率
     */
    @Override
    protected int getMinExecSeconds() {
        return 0;
    }

    /**
     * 执行任务
     *
     * @param data 任务数据
     * @return 任务执行结果：完成，重入队
     */
    @Override
    protected JobResult execute(DemoData data) {
        System.err.println("执行任务：" + data.getData());
        return JobResult.FINISH;
    }

    public static class DemoData extends UserQueueJobData {
        private String data;

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }

        /**
         * 任务唯一键ID
         *
         * @return
         */
        @Override
        public String taskUniqueId() {
            return String.valueOf(HashUtil.murMurHash(this.serialize()));
        }

    }
}
