package com.differ.wdgj.api.component.wait;

/**
 * @Description 排队任务执行器
 * <AUTHOR>
 * @Date 2021/9/10 14:26
 */
public interface WaitExecutor {

    /**
     * 执行任务
     * @param waitUniqueId
     * @param data
     * @param fromWait
     */
    default void exec(String waitUniqueId, String data, boolean fromWait){
        this.exec(data,fromWait);
    }

    /**
     * 执行任务
     *
     * @param data
     * @param fromWait 是否来自排队
     */
    @Deprecated
    void exec(String data, boolean fromWait);
}
