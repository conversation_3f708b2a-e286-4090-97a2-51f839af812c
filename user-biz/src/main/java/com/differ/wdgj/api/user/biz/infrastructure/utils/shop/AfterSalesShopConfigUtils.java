package com.differ.wdgj.api.user.biz.infrastructure.utils.shop;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * 售后店铺配置工具类</p>
 * https://s.jkyun.biz/bSISLko 【管家java】开发设计 - 售后店铺配置
 *
 * <AUTHOR>
 * @date 2024-06-26 14:41
 */
public class AfterSalesShopConfigUtils {
    //region 常量
    /**
     * 业务类型
     */
    private static final ApiShopConfigBizTypes TYPE = ApiShopConfigBizTypes.AFTER_SALES;

    /**
     * 允许保存售后类型map
     */
    private static final Map<ApiAfterSaleTypeEnum, List<ApiAfterSaleTypeEnum>> allowSaveAfterSaleTypeMap = new EnumMap<>(ApiAfterSaleTypeEnum.class);

    //endregion

    //region 静态初始化块
    static {
        //region 初始化-允许保存售后类型map
        // 退货退款单
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND));
        // 换货单
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.EXCHANGE, Collections.singletonList(ApiAfterSaleTypeEnum.EXCHANGE));
        // 退货单
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_GOODS, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_GOODS));
        // 价保售后单
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_BJ, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_BJ));
        // 补寄单
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_SUPPLEMENT, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_SUPPLEMENT));
        // 仅退款
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_PAY, Arrays.asList(ApiAfterSaleTypeEnum.REFUND_PAY, ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND, ApiAfterSaleTypeEnum.REFUND_PAY_SEND, ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT, ApiAfterSaleTypeEnum.REFUND_PAY_RECEIVE));
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND));
        // 已发货仅退款
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_PAY_SEND, Arrays.asList(ApiAfterSaleTypeEnum.REFUND_PAY_SEND, ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT, ApiAfterSaleTypeEnum.REFUND_PAY_RECEIVE));
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT));
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_PAY_RECEIVE, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_PAY_RECEIVE));
        //维修单
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REPAIR, Collections.singletonList(ApiAfterSaleTypeEnum.REPAIR));
        //商家主动退款
        allowSaveAfterSaleTypeMap.put(ApiAfterSaleTypeEnum.REFUND_MERCHANT, Collections.singletonList(ApiAfterSaleTypeEnum.REFUND_MERCHANT));
        //endregion
    }
    //endregion

    //region 构造
    private AfterSalesShopConfigUtils() {
    }
    //endregion

    //region 获取配置

    /**
     * 根据店铺id获取售后店铺配置
     *
     * @param outAccount 外部会员名
     * @param apiShopId  店铺id
     * @return 店铺配置
     */
    public static AfterSalesShopConfig singleByShopId(String outAccount, int apiShopId) {
        return (AfterSalesShopConfig) ShopConfigUtils.getBizConfig(outAccount, TYPE, apiShopId);
    }
    //endregion

    //region 业务判断

    /**
     * 校验订单售后类型是否在店铺配置中
     *
     * @param orderType         售后单类型
     * @param bizShopOrderTypes 当前业务店铺配置
     * @return 结果
     */
    public static boolean checkShopTypeConfig(ApiAfterSaleTypeEnum orderType, List<AfterSalesShopConfig.TypeItem> bizShopOrderTypes) {
        if (orderType == ApiAfterSaleTypeEnum.REFUND_ALL) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(bizShopOrderTypes)) {
            for (AfterSalesShopConfig.TypeItem shopOrderType : bizShopOrderTypes) {
                ApiAfterSaleTypeEnum shopAfterSaleType = ApiAfterSaleTypeEnum.create(shopOrderType.getBizValue());
                if (shopAfterSaleType != null) {
                    // 所有售后单（默认值）为true
                    if (shopAfterSaleType == ApiAfterSaleTypeEnum.REFUND_ALL) {
                        return true;
                    }
                    // 验证店铺配置是否存在当前售后单类型
                    List<ApiAfterSaleTypeEnum> allowSaveAfterSaleTypes = allowSaveAfterSaleTypeMap.getOrDefault(shopAfterSaleType, null);
                    if (CollectionUtils.isNotEmpty(allowSaveAfterSaleTypes) && allowSaveAfterSaleTypes.stream().anyMatch(x -> x == orderType)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
    //endregion
}
