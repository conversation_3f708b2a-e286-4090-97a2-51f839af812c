package com.differ.wdgj.api.user.biz.domain.aftersale.save.savedata;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.CheckAfterSaleHashCodeResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.QueryDbOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderDataResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetSaveOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.DownloadOrderShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.DownloadOrderShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 保存售后单到DB操作类
 *
 * <AUTHOR>
 * @date 2024/7/24 下午5:23
 */
@Ignore
public class AfterSaleDataOperationTest extends AbstractSpringTest {

    /**
     * 上下文
     */
    private static AfterSaleDataOperation afterSaleDataOperation;

    /**
     * 基础测试数据地址
     */
    private final String basePath = "D:\\esapi-java\\管家java测试数据\\AfterSaleDataOperationTest\\refund\\%s";

    /**
     * 类前置执行
     */
    @Before
    public void setBeforeClass(){
        String memberName = "api2017";
        int outShopId = 1446;
        AfterSalesShopConfig afterSalesConfig = AfterSalesShopConfigUtils.singleByShopId(memberName, outShopId);
        DownloadOrderShopConfig downloadOrderConfig = DownloadOrderShopConfigUtils.singleByShopId(memberName, outShopId);
        ApiShopBaseDto apiShopBase = ShopInfoUtils.singleByOutShopId(memberName, outShopId);

        AfterSaleSaveContext context = new AfterSaleSaveContext();
        context.setMemberName(memberName);
        context.setShopId(outShopId);
        context.setPlat(apiShopBase.getPlat());
        context.setOrderTriggerType(OrderTriggerTypeEnum.MANUAL);
        context.setOperatorName(StringUtils.EMPTY);
        context.setWriteBusinessLog(false);
        context.setPolyApiRequestId("678209738626042881P3114M0232V13E216");
        context.setLoadTime(LocalDateTime.now());
        context.setApiShopBaseInfo(apiShopBase);
        context.setAfterSalesShopConfig(afterSalesConfig);
        context.setDownLoadOrderShopConfig(downloadOrderConfig);

        afterSaleDataOperation = new AfterSaleDataOperation(context);
    }

    /**
     * 校验hash
     */
    @Test
    public void checkPolyOrderHashCodeTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "BusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {});
        List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> sourceOrderList = polyOrderList.stream().map(x -> new SourceAfterSaleOrderItem<>(x.getRefundNo(), x.getPlatOrderNo(), AfterSaleSaveBizType.getDefaultBizType(), x)).collect(Collectors.toList());
        SaveAfterSaleResult<List<CheckAfterSaleHashCodeResult>> listSaveAfterSaleResult = afterSaleDataOperation.checkPolyOrderHashCode(sourceOrderList);
    }

    /**
     * 获取历史售后单
     */
    @Test
    public void getOldDbOrderItemsTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "BusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {});
        List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> sourceOrderList = polyOrderList.stream().map(x -> new SourceAfterSaleOrderItem<>(x.getRefundNo(), x.getPlatOrderNo(), AfterSaleSaveBizType.getDefaultBizType(), x)).collect(Collectors.toList());
        SaveAfterSaleResult<List<QueryDbOrderResult>> oldDbOrderItems = afterSaleDataOperation.getOldDbOrderItems(sourceOrderList);
    }

    /**
     * 保存数据
     */
    @Test
    public void saveDataTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format("D:\\esapi-java\\管家java测试数据\\AfterSaleDataOperationTest\\%s", "saveDataTest.json"))));
        List<TargetSaveOrderItem> targetSaveOrders = JsonUtils.deJson(str, new TypeReference<List<TargetSaveOrderItem>>() {});
        SaveAfterSaleResult<List<SaveOrderDataResult>> listSaveAfterSaleResult = afterSaleDataOperation.saveData(targetSaveOrders);
    }
}

