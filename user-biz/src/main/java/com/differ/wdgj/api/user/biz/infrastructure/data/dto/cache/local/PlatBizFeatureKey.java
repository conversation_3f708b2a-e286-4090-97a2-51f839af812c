package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature.PlatBizFeatureTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;

import java.util.Objects;

/**
 * 平台业务特性唯一键
 *
 * <AUTHOR>
 * @date 2024-06-20 18:19
 */
public class PlatBizFeatureKey {


    /**
     * 平台
     */
    private PolyPlatEnum plat;

    /**
     * 业务类型
     */
    private PlatBizFeatureTypeEnum bizType;

    /**
     * 创建实例
     *
     * @param plat    平台
     * @param bizType 业务类型
     * @return 结果
     */
    public static PlatBizFeatureKey create(PolyPlatEnum plat, PlatBizFeatureTypeEnum bizType) {
        PlatBizFeatureKey key = new PlatBizFeatureKey();
        key.setPlat(plat);
        key.setBizType(bizType);
        return key;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        PlatBizFeatureKey that = (PlatBizFeatureKey) o;
        return plat == that.plat && bizType == that.bizType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(plat, bizType);
    }

    // region getter & setter

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public PlatBizFeatureTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(PlatBizFeatureTypeEnum bizType) {
        this.bizType = bizType;
    }

    // endregion
}
