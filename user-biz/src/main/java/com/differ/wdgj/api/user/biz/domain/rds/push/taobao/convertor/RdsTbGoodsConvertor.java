package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct.BusinessDownloadProductResponseGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct.BusinessDownloadProductResponseGoodInfoSku;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.goods.TbGoodsJdpResponseDto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 淘宝Rds - 商品转换器
 *
 * <AUTHOR>
 * @date 2024-03-22 10:23
 */
public class RdsTbGoodsConvertor {

    //region 构造
    private RdsTbGoodsConvertor(){}
    //endregion

    /**
     * 将淘宝商品数据转换为菠萝派商品数据
     *
     * @param jdpResponses 淘宝商品数据列表
     * @return 菠萝派商品数据列表
     */
    public static List<BusinessDownloadProductResponseGoodInfo> convertJdpResponse(List<TbGoodsJdpResponseDto> jdpResponses) {
        List<BusinessDownloadProductResponseGoodInfo> ployGoodsInfoList = new ArrayList<>();
        for (TbGoodsJdpResponseDto jdpResponse : jdpResponses) {
            TbGoodsJdpResponseDto.Item tbGoods = jdpResponse.getItem_get_response().getItem();

            BusinessDownloadProductResponseGoodInfo ployGoodsInfo = new BusinessDownloadProductResponseGoodInfo();
            ployGoodsInfo.setPlatProductId(Long.toString(tbGoods.getNum_iid()));
            ployGoodsInfo.setName(tbGoods.getTitle());
            ployGoodsInfo.setOuterId(tbGoods.getOuter_id());
            ployGoodsInfo.setPrice(new BigDecimal(tbGoods.getPrice()));
            ployGoodsInfo.setNum(tbGoods.getNum());
            ployGoodsInfo.setPictureUrl(tbGoods.getPic_url());
            ployGoodsInfo.setDetailUrl(tbGoods.getDetail_url());

            List<BusinessDownloadProductResponseGoodInfoSku> ploySkus = new ArrayList<>();
            if (tbGoods.getSkus() != null) {
                for (TbGoodsJdpResponseDto.Sku sku : tbGoods.getSkus().getSku()) {
                    BusinessDownloadProductResponseGoodInfoSku ploySkuInfo = new BusinessDownloadProductResponseGoodInfoSku();
                    ploySkuInfo.setSkuId(sku.getSku_id());
                    ploySkuInfo.setSkuOuterId(sku.getOuter_id());
                    ploySkuInfo.setSkuPrice(new BigDecimal(sku.getPrice()));
                    ploySkuInfo.setSkuQuantity(sku.getQuantity());
                    ploySkuInfo.setSkuName(sku.getProperties_name());
                    ploySkuInfo.setSkuName2("");
                    ploySkuInfo.setSkuProperty(sku.getProperties());
                    ploySkuInfo.setSkuPictureUrl(getSkuPictureUrl(tbGoods.getProp_imgs(), sku.getProperties()));

                    ploySkus.add(ploySkuInfo);
                }

                ployGoodsInfo.setSkus(ploySkus);
                ployGoodsInfoList.add(ployGoodsInfo);
            }
        }

        return ployGoodsInfoList;
    }

    //region 私有方法
    /**
     * 获取规格级图片地址
     * @param propImgs 图片列表
     * @param skuProperties 规格唯一标识
     * @return 规格级图片地址
     */
    private static String getSkuPictureUrl(TbGoodsJdpResponseDto.Prop_Imgs propImgs, String skuProperties){
        try {
            if(propImgs != null && propImgs.getProp_img() != null && propImgs.getProp_img().length > 0){
                TbGoodsJdpResponseDto.Prop_Img propImg = Arrays.stream(propImgs.getProp_img()).filter(x -> x.getProperties().contains(skuProperties)).findFirst().orElse(null);
                if(propImg != null){
                    return propImg.getUrl();
                }
            }
        }
        catch (Exception e){
            return null;
        }

        return null;
    }
    //endregion
}
