package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor.RdsTbOrderConvertor;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.TbTradeJdpResponseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbTradeRdsDo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbTradeRdsMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 淘宝Rds - 订单相关操作（jdp_tb_trade）
 *
 * <AUTHOR>
 * @date 2025/6/26 09:45
 */
public class RdsTbTradeDomain {
    //region 常量
    /**
     * 会员名
     */
    private final String memberName;
    //endregion

    //region 构造
    public RdsTbTradeDomain(String memberName) {
        this.memberName = memberName;
    }
    //endregion

    /**
     * 获取订单发货信息列表
     *
     * @param tIds 普通订单单号列表
     * @return 订单发货信息列表
     */
    public List<RdsTbTradeSendInfo> getOrderSendInfos(List<Integer> tIds) {
        if (CollectionUtils.isEmpty(tIds)) {
            return Collections.emptyList();
        }

        // 查询推送库订单列表
        TbTradeRdsMapper tbTradeRdsMapper = BeanContextUtil.getBean(TbTradeRdsMapper.class);
        List<TbTradeRdsDo> tbTradeRdsDos = DBSwitchUtil.doDBWithRds(memberName, () -> tbTradeRdsMapper.getTradeListByTIds(tIds));

        // 发货信息数据转换
        List<RdsTbTradeSendInfo> rdsTbTradeSendInfos = new ArrayList<>();
        for (TbTradeRdsDo tbTradeRdsDo : tbTradeRdsDos) {
            if (StringUtils.isNotEmpty(tbTradeRdsDo.getJdpResponse())) {
                TbTradeJdpResponseDto tbTradeJdpResponseDto = JsonUtils.deJson(tbTradeRdsDo.getJdpResponse(), TbTradeJdpResponseDto.class);
                // 转换订单发货信息
                RdsTbTradeSendInfo rdsTbTradeSendInfo = RdsTbOrderConvertor.convertTbTradeSendInfo(tbTradeJdpResponseDto);
                if(rdsTbTradeSendInfo != null){
                    rdsTbTradeSendInfos.add(rdsTbTradeSendInfo);
                }
            }
        }

        return rdsTbTradeSendInfos;
    }
}
