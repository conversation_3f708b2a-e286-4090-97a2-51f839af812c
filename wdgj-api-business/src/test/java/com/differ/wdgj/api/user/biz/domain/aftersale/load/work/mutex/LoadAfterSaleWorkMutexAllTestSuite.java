package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * LoadAfterSaleWorkMutex 完整测试套件
 * 包含所有相关的单元测试和集成测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午5:30
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    LoadAfterSaleWorkMutexTest.class,                    // 原有：基础功能测试
    LoadAfterSaleWorkMutexIntegrationTest.class,         // 原有：集成测试
    LoadAfterSaleWorkMutexSimpleTest.class,              // 原有：简单测试
    LoadAfterSaleWorkMutexIntegrationTestNew.class       // 新增：专业集成测试
})
public class LoadAfterSaleWorkMutexAllTestSuite {
    // 测试套件类，无需实现内容
}
