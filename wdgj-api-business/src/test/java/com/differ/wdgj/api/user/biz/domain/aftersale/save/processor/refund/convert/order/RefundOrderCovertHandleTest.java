package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.order;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.Assert.*;

/**
 * RefundOrderCovertHandle 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class RefundOrderCovertHandleTest extends BaseAfterSaleOrderTest {

    //region 变量
    /**
     * 上下文
     */
    private AfterSaleSaveContext context;

    /**
     * 测试对象
     */
    private RefundOrderCovertHandle handle;
    //endregion

    //region 初始化
    /**
     * 测试前置
     */
    @Before
    public void setUp() {
        String memberName = "api2017";
        int outShopId = 1446;
        context = getContext(memberName, outShopId);
        handle = new RefundOrderCovertHandle(context);
    }
    //endregion

    //region 基础功能测试

    /**
     * 测试构造函数
     */
    @Test
    public void testConstructor() {
        assertNotNull("构造函数应该成功创建对象", handle);
        assertEquals("标题应该正确", "订单级基础数据转换", handle.caption());
    }

    /**
     * 测试正常的退货退款单转换
     */
    @Test
    public void testConvertOrderWithRefund() {
        // 准备测试数据
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        // 执行转换
        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);

        // 验证结果
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertNotNull("售后单对象不应为空", afterSaleOrder);

        // 验证基础信息
        assertEquals("店铺ID应该正确", context.getShopId().intValue(), afterSaleOrder.getShopID());
        assertEquals("获取时间应该正确", context.getLoadTime(), afterSaleOrder.getGetTime());

        // 验证原始单信息
        assertEquals("原始平台订单号应该正确", "TEST_PLAT_ORDER_NO", afterSaleOrder.getOldTid());
        assertEquals("原始子订单号应该正确", "TEST_SUB_ORDER_NO", afterSaleOrder.getOldOid());

        // 验证售后信息
        assertEquals("退款单号应该正确", "TEST_REFUND_NO", afterSaleOrder.getRefundId());
        assertEquals("退款类型应该是退货退款", WdgjRefundTypeEnum.REFUND.getValue().intValue(), afterSaleOrder.getType());
        assertEquals("退款金额应该正确", new BigDecimal("100.00"), afterSaleOrder.getRefundFee());

        // 验证买家信息
        assertEquals("买家昵称应该正确", "TEST_BUYER_NICK", afterSaleOrder.getCustomerId());
        assertEquals("买家UID应该正确", "TEST_BUYER_UID", afterSaleOrder.getNickUId());

        // 验证物流信息
        assertEquals("物流公司应该正确", "TEST_LOGISTICS", afterSaleOrder.getLogisticName());
        assertEquals("物流单号应该正确", "TEST_LOGISTICS_NO", afterSaleOrder.getLogisticNo());

        // 验证处理类型
        assertEquals("处理类型应该是新增", AfterSaleProcessTypeEnum.INSERTER, targetOrder.getProcessType());

        // 验证日志
        assertFalse("应该有日志记录", targetOrder.getAfterSaleOrderLogs().isEmpty());
    }

    /**
     * 测试仅退款单转换
     */
    @Test
    public void testConvertOrderWithRefundOnly() {
        // 准备测试数据 - 无退货
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(false);
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        // 执行转换
        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);

        // 验证结果
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertEquals("退款类型应该是仅退款", WdgjRefundTypeEnum.REFUND_PAY.getValue().intValue(), afterSaleOrder.getType());
    }

    /**
     * 测试更新已存在的售后单
     */
    @Test
    public void testConvertOrderWithExistingOrder() {
        // 准备测试数据 - 包含已存在的售后单
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrderWithExistingData();
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        // 执行转换
        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);

        // 验证结果
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertEquals("应该保留原有的BillId", 3094374, afterSaleOrder.getBillId());
        assertEquals("应该保留原有的状态", 0, afterSaleOrder.getCurStatus());
        assertEquals("应该保留原有的原始单ID", 1010424740, afterSaleOrder.getOldBillID().intValue());

        // 验证处理类型
        assertEquals("处理类型应该是更新", AfterSaleProcessTypeEnum.UPDATE, targetOrder.getProcessType());
    }

    /**
     * 测试状态转换
     */
    @Test
    public void testRefundStatusConversion() {
        // 测试各种状态的转换
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);

        // 设置不同的状态
        sourceOrder.getPloyOrder().setRefundStatus("JH_01"); // 申请中
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertEquals("状态应该正确转换", "买家已经申请退款等待卖家同意", afterSaleOrder.getReturnStatus());
    }

    /**
     * 测试未知状态处理
     */
    @Test
    public void testUnknownRefundStatus() {
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);

        // 设置未知状态
        sourceOrder.getPloyOrder().setRefundStatus("UNKNOWN_STATUS");
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertEquals("未知状态应该返回默认值", "未知状态", afterSaleOrder.getReturnStatus());
    }

    /**
     * 测试创建时间为空的情况
     */
    @Test
    public void testNullCreateTime() {
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);

        // 设置创建时间为空
        sourceOrder.getPloyOrder().setCreateTime(null);
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertEquals("空创建时间应该设置为默认值",
                LocalDateTime.of(1900, 1, 1, 0, 0, 0, 0),
                afterSaleOrder.getCreatedTime());
    }

    /**
     * 测试退款原因长度截取
     */
    @Test
    public void testReasonTruncation() {
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);

        // 设置超长的退款原因
        String longReason = StringUtils.repeat("测试原因", 20); // 超过50个字符
        sourceOrder.getPloyOrder().setReason(longReason);
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        assertTrue("退款原因应该被截取到50个字符以内",
                afterSaleOrder.getReturnReason().length() <= 50);
    }

    /**
     * 测试表情符号替换
     */
    @Test
    public void testEmojiReplacement() {
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);

        // 设置包含表情符号的描述
        sourceOrder.getPloyOrder().setDesc("测试描述😀😁😂");
        sourceOrder.getPloyOrder().setReason("测试退款原因😀😁😂");
        TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();

        AfterSaleHandleResult result = handle.convertOrder(sourceOrder, targetOrder);
        assertTrue("转换应该成功", result.isSuccess());

        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        // 验证表情符号被处理（具体处理逻辑取决于AfterSaleCovertUtils.replaceEmoji的实现）
        assertNotNull("备注不应为空", afterSaleOrder.getRemark());
    }
    //endregion

    //region 辅助方法

    /**
     * 创建测试用的源订单数据
     *
     * @param hasGoodsReturn 是否有退货
     * @return 源订单数据
     */
    private SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> createSourceOrder(boolean hasGoodsReturn) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = new BusinessGetRefundOrderResponseOrderItem();
        ployOrder.setPlatOrderNo("TEST_PLAT_ORDER_NO");
        ployOrder.setSubPlatOrderNo("TEST_SUB_ORDER_NO");
        ployOrder.setRefundNo("TEST_REFUND_NO");
        ployOrder.setHasGoodsReturn(hasGoodsReturn);
        ployOrder.setRefundStatus("JH_01"); // 申请中
        ployOrder.setReason("测试退款原因");
        ployOrder.setDesc("测试描述");
        ployOrder.setCreateTime(LocalDateTime.now());
        ployOrder.setLogisticName("TEST_LOGISTICS");
        ployOrder.setLogisticNo("TEST_LOGISTICS_NO");
        ployOrder.setRefundAmount(new BigDecimal("100.00"));
        ployOrder.setBuyerNick("TEST_BUYER_NICK");
        ployOrder.setBuyerOpenUid("TEST_BUYER_UID");

        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder =
            new SourceAfterSaleOrderItem<>("TEST_REFUND_NO", "TEST_PLAT_ORDER_NO", AfterSaleSaveBizType.getDefaultBizType(), ployOrder);

        DbAfterSaleOrderItem dbOrder = new DbAfterSaleOrderItem();
        // 设置为新订单（无历史数据）
        dbOrder.setAfterSaleOrder(null);
        dbOrder.setApiTrade(null);
        sourceOrder.setDbOrder(dbOrder);

        return sourceOrder;
    }

    /**
     * 创建包含已存在数据的测试源订单
     *
     * @return 源订单数据
     */
    private SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> createSourceOrderWithExistingData() {
        SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder = createSourceOrder(true);

        // 设置已存在的售后单数据
        ApiReturnListDO existingAfterSaleOrder = new ApiReturnListDO();
        existingAfterSaleOrder.setBillId(3094374);
        existingAfterSaleOrder.setCurStatus(0);

        // 设置已存在的原始订单数据
        ApiTradeDO existingApiTrade = new ApiTradeDO();
        existingApiTrade.setBillId(1010424740);

        DbAfterSaleOrderItem dbOrder = new DbAfterSaleOrderItem();
        dbOrder.setAfterSaleOrder(existingAfterSaleOrder);
        dbOrder.setApiTrade(existingApiTrade);
        sourceOrder.setDbOrder(dbOrder);

        return sourceOrder;
    }
    //endregion
}
