package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractExchangeGoodsConvertHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;

/**
 * 换货单-换货商品平台级特殊处理
 *
 * <AUTHOR>
 * @date 2024/8/9 下午2:39
 */
public class ExchangeGoodsPlatSpecialHandle  extends AbstractExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ExchangeGoodsPlatSpecialHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类逻辑

    /**
     * 转换商品级信息
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后退货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        return GoodsConvertHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return String.format("平台【%s】换货单换货商品转换-平台级特殊处理", context.getPlat().getName());
    }
    //endregion
}
