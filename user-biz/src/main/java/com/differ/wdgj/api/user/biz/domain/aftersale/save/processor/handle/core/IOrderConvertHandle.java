package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;

/**
 * 售后单处理插件-订单级数据转换
 *
 * <AUTHOR>
 * @date 2024-06-07 14:10
 */
public interface IOrderConvertHandle<T> {
    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    AfterSaleHandleResult convert(SourceAfterSaleOrderItem<T> sourceOrder, TargetCovertOrderItem targetOrder);
}
