package com.differ.wdgj.api.user.biz.infrastructure.initializer.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.core.spi.FilterReply;
import com.differ.wdgj.api.component.util.loop.LoopUtil;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.slf4j.Marker;
import org.slf4j.helpers.MessageFormatter;

import java.time.LocalDateTime;

/**
 * 日志自定义过滤
 *
 * <AUTHOR>
 * @date 2022/10/11 14:52
 */
public class LogCustomFilterImpl implements LogCustomFilter {
    //region 常量
    private static final String IS_DENY_INFO = "isDenyInfo";
    private static final String IS_AGREE_WARN = "isAgreeWarn";
    private static final String IS_AGREE_ERROR = "isAGREEError";
    //endregion

    @Override
    public FilterReply filter(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {

        if (logger == null) {
            return FilterReply.NEUTRAL;
        }

        // 自定义过滤
        FilterReply reply = doFilterCustom(marker, logger, level, format, params, t);

        // 跟踪日志
        if (reply == FilterReply.DENY) {
            System.out.println(new StringBuilder().append(LocalDateTime.now()).append(" println-").append(reply).append(":").append(MessageFormatter.arrayFormat(format, params).getMessage()));
        }

        return reply;
    }

    //region 私有方法
    private FilterReply doFilterCustom(Marker marker, Logger logger, Level level, String format, Object[] params, Throwable t) {
        // 根据配置键过滤指定日志标题
        String logName = logger.getName();

        // 指定INFO日志写入
        if (level == Level.INFO && isDenyInfo(logName)) {
            return FilterReply.DENY;
        }

        // 指定Warn日志排除
        if (level == Level.WARN && isAgreeWarn(logName)) {
            return FilterReply.DENY;
        }

        // 指定ERROR日志排除
        if (level == Level.ERROR && isAgreeError(logName)) {
            return FilterReply.DENY;
        }

        return FilterReply.NEUTRAL;
    }

    /**
     * 是否拒绝info日志
     *
     * @param logName 日志标题
     * @return 是否开启
     */
    private boolean isDenyInfo(String logName) {
        return LoopUtil.avoidGlobalRecursion(
                () -> !ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.IsAction_JavaElkLog_Info, logName),
                Boolean.TRUE, this, IS_DENY_INFO);
    }

    /**
     * 是否同意warn日志
     *
     * @param logName 日志标题
     * @return 是否关闭
     */
    private boolean isAgreeWarn(String logName) {
        return LoopUtil.avoidGlobalRecursion(
                () -> ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.IsExclude_JavaElkLog_Warn, logName),
                Boolean.FALSE, this, IS_AGREE_WARN);
    }

    /**
     * 是否关闭写错误日志
     *
     * @param logName 日志标题
     * @return 是否关闭
     */
    private boolean isAgreeError(String logName) {
        return LoopUtil.avoidGlobalRecursion(
                () -> ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.IsExclude_JavaElkLog_Error, logName),
                Boolean.FALSE, this, IS_AGREE_ERROR);
    }
    //endregion
}
