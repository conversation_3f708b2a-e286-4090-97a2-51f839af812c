package com.differ.wdgj.api.component.util.tools;

/**
 * 其他扩展方法
 *
 * <AUTHOR>
 * @date 2024/5/16 14:59
 */
public class ExtUtils {
    private ExtUtils() {
    }

    /**
     * StringBuilder拼接字符串。
     *
     * @param strs 字符串数组
     * @return 拼接后的StringBuilder对象
     */
    public static StringBuilder stringBuilderAppend(String... strs) {
        StringBuilder sb = new StringBuilder();

        //拼接内容项。
        for (String str : strs) {
            sb.append(str);
        }

        return sb;
    }
}
