package com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IAfterSaleLogOperationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnLogMapper;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * 售后单日志操作
 *
 * <AUTHOR>
 * @date 2024/12/10 上午11:39
 */
public class AfterSaleLogOperationService implements IAfterSaleLogOperationService {
    //region 常量
    /**
     * 密文数据仓储
     */
    private final ApiReturnLogMapper apiReturnLogMapper = BeanContextUtil.getBean(ApiReturnLogMapper.class);
    //endregion

    /**
     * 批量保新增售后单日志
     *
     * @param memberName         会员名
     * @param afterSaleOrderLogs 售后单日志列表
     */
    @Override
    public void batchInsertLog(String memberName, List<ApiReturnLogDO> afterSaleOrderLogs) {
        // 拆分
        List<List<ApiReturnLogDO>> subInsertLists = Lists.partition(afterSaleOrderLogs, 200);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<ApiReturnLogDO> subInsertList : subInsertLists) {
                apiReturnLogMapper.batchInsert(subInsertList);
            }
        });
    }
}
