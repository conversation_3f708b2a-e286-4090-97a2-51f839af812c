package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 售后单下载工作任务结果
 *
 * <AUTHOR>
 * @date 2024/9/11 下午2:24
 */
public class LoadAfterSaleWorkResult extends WorkResult {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 子任务结果整合
     */
    private List<LoadAfterSaleDetail> details;

    //region 公共方法

    /**
     * 成功
     *
     * @return 返回结果对象
     */
    public static LoadAfterSaleWorkResult onSuccess() {
        LoadAfterSaleWorkResult result = new LoadAfterSaleWorkResult();
        result.setSuccess(Boolean.TRUE);
        return result;
    }

    /**
     * 成功
     *
     * @param subTask    子任务
     * @param loadResult 下载结果
     * @return 返回结果对象
     */
    public static LoadAfterSaleWorkResult onSuccess(AfterSaleLoadSubTask subTask, LoadAfterSaleSubTaskResult loadResult) {
        LoadAfterSaleWorkResult result = new LoadAfterSaleWorkResult();
        result.setSuccess(Boolean.TRUE);
        // 追加任务详情
        result.appendResultDetail(subTask, loadResult, Boolean.TRUE, result.getMessage());
        return result;
    }

    /**
     * 失败
     *
     * @param subTask    子任务
     * @param msg        错误消息
     * @return 返回结果对象
     */
    public static LoadAfterSaleWorkResult onFail(AfterSaleLoadSubTask subTask, String msg) {
        LoadAfterSaleWorkResult result = onFail(msg);
        // 追加任务详情
        result.appendResultDetail(subTask, null, Boolean.FALSE, result.getMessage());
        return result;
    }

    /**
     * 失败
     *
     * @param msg 错误消息
     * @return 返回结果对象
     */
    public static LoadAfterSaleWorkResult onFail(String msg) {
        LoadAfterSaleWorkResult result = new LoadAfterSaleWorkResult();
        result.setSuccess(Boolean.FALSE);
        result.setMessage(msg);
        return result;
    }
    //endregion

    //region 私有方法

    /**
     * 追加任务详情
     *
     * @param subTask    子任务
     * @param loadResult 下载结果
     * @param success    是否成功
     * @param msg        消息
     */
    private void appendResultDetail(AfterSaleLoadSubTask subTask, LoadAfterSaleSubTaskResult loadResult, boolean success, String msg) {
        if (null == this.details) {
            this.details = new ArrayList<>();
        }

        // 详情查找
        LoadAfterSaleDetail resultDetail = this.details.stream().filter(t -> StringUtils.equals(t.getSubTypeKey(), subTask.getSubTypeKey())).findFirst().orElse(null);
        if (null != resultDetail) {
            // 如果已存在是 失败不能替换
            if (!resultDetail.isSuccess()) {
                return;
            }
            resultDetail.setSuccess(success);
            resultDetail.setMessage(message);
            resultDetail.of(subTask, loadResult);
        } else {
            resultDetail = new LoadAfterSaleDetail(success, msg);
            this.details.add(resultDetail.of(subTask, loadResult));
        }
    }

    /**
     * 合并任务详情
     *
     * @param details 任务详情
     */
    public void mergeResultDetails(LoadAfterSaleDetail... details) {
        if (ArrayUtils.isEmpty(details)) {
            return;
        }
        if (null == this.details) {
            this.details = new ArrayList<>();
        }
        for (LoadAfterSaleDetail detail : details) {
            if (null == detail) {
                continue;
            }
            LoadAfterSaleDetail resultDetail = this.details.stream().filter(t -> StringUtils.equals(t.getSubTypeKey(), detail.getSubTypeKey())).findFirst().orElse(null);
            if (null != resultDetail) {
                resultDetail.margeDetail(detail);
            } else {
                this.details.add(detail);
            }
        }
    }
    //endregion

    //region 实现基类方法
    @Override
    public boolean success() {
        return success;
    }

    public boolean failed() {
        return !success;
    }

    @Override
    public String formatFrontShow() {
        return "";
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<LoadAfterSaleDetail> getDetails() {
        return details;
    }

    public void setDetails(List<LoadAfterSaleDetail> details) {
        this.details = details;
    }
    //endregion
}
