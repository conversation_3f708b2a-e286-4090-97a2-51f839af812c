package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor;

import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncResultDetail;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;

import java.util.Map;

/**
 * 库存同步-业务处理接口
 *
 * <AUTHOR>
 * @date 2024-02-23 15:10
 */
public interface ISyncStockProcessor {

    /**
     * 构建库存同步请求
     */
    void buildSyncStockRequests();

    /**
     * 发起菠萝派库存同步
     */
    void doRequest();

    /**
     * 保存库存同步结果
     */
    Map<MatchIdEnhance, StockContentResult<StockSyncResultDetail>> saveSyncStockResult(Map<MatchIdEnhance, StockSyncResultComposite> composites);
}
