package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloServerRdsMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberAccountMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/3/5 11:50
 */
@Ignore
public class GloServerRdsMappingMapperTest extends AbstractSpringTest {

    @Autowired(required = false)
    private GloServerRdsMappingMapper mapper;

    @Test
    public void selectByUserNameAuto() {
        GloServerRdsMappingDO accountMappingDO =  mapper.selectByServerIpTest(SwitchDbContext.buildEsApi(), "localhost");
        Assert.assertNotNull(accountMappingDO);
    }

    @Test
    public void selectByUserNameAutoFor() {
        GloServerRdsMappingDO accountMappingDO = null;
        GloServerRdsMappingDO insert = new GloServerRdsMappingDO();
        insert.setServerIp("localhost2");
        insert.setRdsid("setRdsid");
        insert.setCreatetime(LocalDateTime.now());
        insert.setRdspwd("setRdspwd");
        insert.setRdsserverip("setRdsserverip");
        DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(),()->mapper.insert(insert));

        for (int i = 0; i < 10; i++) {
            accountMappingDO =  mapper.selectByServerIpTest(SwitchDbContext.buildEsApi(), "localhost");
        }
        Assert.assertNotNull(accountMappingDO);
    }
}
