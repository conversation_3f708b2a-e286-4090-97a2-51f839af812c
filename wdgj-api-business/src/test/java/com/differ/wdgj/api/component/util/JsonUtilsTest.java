package com.differ.wdgj.api.component.util;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleDetail;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleWorkResult;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * json工具
 *
 * <AUTHOR>
 * @date 2024/10/10 下午6:44
 */
@Ignore
public class JsonUtilsTest {

    @Test
    public void deJsonTest() {
        LoadAfterSaleWorkResult lastWorkResult = new LoadAfterSaleWorkResult();
        List<LoadAfterSaleDetail> a = new ArrayList<>();
        LoadAfterSaleDetail detail = new LoadAfterSaleDetail();
        detail.setSubTypeKey("11111");
        a.add(detail);
        lastWorkResult.setDetails(a);

        String jsonStr = JsonUtils.toJson(lastWorkResult);
        lastWorkResult = JsonUtils.deJson(jsonStr, LoadAfterSaleWorkResult.class);

        String jsonStr1 = "{\"details\":[{\"dataSize\":1,\"lastSuccessDataTime\":\"2024-10-09 14:10:59\",\"message\":\"111\",\"subTypeCaption\":\"仅退款单-全部状态\",\"subTypeKey\":\"DEFAULT-REFUND_PAY-REFUND_PAY\",\"success\":true}],\"errorCode\":\"\",\"message\":\"11\",\"success\":true}";
        lastWorkResult = JsonUtils.deJson(jsonStr1, LoadAfterSaleWorkResult.class);
    }
}
