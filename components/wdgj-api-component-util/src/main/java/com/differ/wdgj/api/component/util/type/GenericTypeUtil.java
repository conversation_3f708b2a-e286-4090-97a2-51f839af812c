package com.differ.wdgj.api.component.util.type;

import org.springframework.aop.support.AopUtils;
import org.springframework.core.ResolvableType;

/**
 * <AUTHOR>
 * @date 2024/3/28 17:57
 */
public class GenericTypeUtil {

    /**
     * 递归解析直到获取到泛型对象的数据类型
     * @param objectGeneric
     * @param index
     * @return 未获取到泛型数据类型时，返回 Object.class
     */
    public static  Class<?> getGenericObjectDataType(Object objectGeneric, int index) {
        Class clazz = AopUtils.getTargetClass(objectGeneric);
        return getGenericDataType(clazz, 0);
    }


    /**
     * 递归解析直到获取到泛型类的数据类型
     * @param clazzGeneric
     * @param index
     * @return 未获取到泛型数据类型时，返回 Object.class
     */
    public static Class<?> getGenericDataType(Class<?> clazzGeneric, int index) {
        ResolvableType resolvableType = ResolvableType.forClass(clazzGeneric);
        ResolvableType resolvableTypeGeneric = getSuperGenericClass(resolvableType);
        if (resolvableTypeGeneric != null) {
            return resolvableTypeGeneric.getGeneric(index).resolve();
        }
        return Object.class;
    }

    /**
     * 递归解析直到获取到泛型类
     *
     * @param src
     * @return
     */
    private static ResolvableType getSuperGenericClass(ResolvableType src) {
        if (src.hasGenerics()) {
            return src;
        }
        if (Object.class == src.resolve()) {
            return null;
        }
        return getSuperGenericClass(src.getSuperType());
    }

    /**
     * 递归解析直到获取指定的类
     *
     * @param src
     * @param superTargetClass 目标基类类型，包含本类
     * @return
     */
    private static ResolvableType getSuperClass(ResolvableType src, Class<?> superTargetClass) {
        if (superTargetClass == src.resolve()) {
            return src;
        }
        if (Object.class == src.resolve()) {
            return null;
        }
        return getSuperClass(src.getSuperType(), superTargetClass);
    }
}
