package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.post;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPostProcessOrderHandle;

/**
 * 退货退款单-平台级后置处理
 *
 * <AUTHOR>
 * @date 2024/7/28 下午5:38
 */
public class RefundPlatSpecialPostProcessHandle extends AbstractPostProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造
    public RefundPlatSpecialPostProcessHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 售后单后置处理
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult processOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 供平台级特殊处理
        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return String.format("平台【%s】退货退款单订单级后置转换-平台级特殊处理", context.getPlat().getName());
    }
    //endregion
}
