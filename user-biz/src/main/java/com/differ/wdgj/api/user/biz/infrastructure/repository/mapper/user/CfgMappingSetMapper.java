package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.CfgMappingSetDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 管家匹配信息仓储
 * <AUTHOR>
 * @date 2024-03-19 9:34
 */
public interface CfgMappingSetMapper extends BasicOperateMapper<CfgMappingSetDO> {
    /**
     * 根据平台店铺Id删除管家匹配信息
     * @param mapName 业务类型
     * @param platStoreIds 平台店铺Id - 列表
     */
    void deleteByPlatStoreId(@Param("mapName") String mapName, @Param("platStoreIds") List<String> platStoreIds);
}
