package com.differ.wdgj.api.user.biz.domain.goods.match.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 商品匹配停用状态
 *
 * <AUTHOR>
 * @date 2024-03-11 15:32
 */
public enum ApiSysMatchStopStateEnum implements ValueEnum {
    /**
     * 启用
     */
    Enable(0, "启用"),
    /**
     * 停用
     */
    Disable(1, "停用"),
    ;

    //region 构造

    ApiSysMatchStopStateEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    //endregion

    //region 变量
    /**
     * 值
     */
    private final Integer value;

    /**
     * 描述
     */

    private final String description;
    //endregion

    //region 公共方法
    /**
     * 获取枚举值
     *
     * @return
     */
    @Override
    public Integer getValue() {
        return value;
    }
    //endregion
}
