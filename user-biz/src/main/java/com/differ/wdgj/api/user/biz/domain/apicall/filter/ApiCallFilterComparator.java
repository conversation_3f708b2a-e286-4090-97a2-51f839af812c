package com.differ.wdgj.api.user.biz.domain.apicall.filter;

import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;

import java.util.Comparator;

/**
 * API调用过滤器比较器
 *
 * <AUTHOR>
 * @date 2022-03-14 19:46
 */
public class ApiCallFilterComparator<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData>
        implements Comparator<ApiCallFilterWrapper<RequestBizData, ResponseBizData>> {

    /**
     * 比较
     *
     * @param o1 o1
     * @param o2 o2
     * @return 结果
     */
    @Override
    public int compare(ApiCallFilterWrapper o1, ApiCallFilterWrapper o2) {
        return Integer.compare(o1.getOrder(), o2.getOrder());
    }
}
