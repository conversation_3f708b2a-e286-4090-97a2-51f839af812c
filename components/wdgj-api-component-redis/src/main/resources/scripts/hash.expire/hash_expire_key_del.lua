-- 支持过期的hash批量删除
-- User: chuff
-- Date: 2021/11/29 14:25
--

local cacheKey = KEYS[1]

-- 缓存键过期时间
local cacheKeyExpire = tonumber(ARGV[1])
-- 当前时间
local currentTime = tonumber(ARGV[2])

-- 缓存键的key后缀
local cacheKeyTail = math.floor(currentTime / cacheKeyExpire)

local cacheKeyWithTail = cacheKey .. cacheKeyTail
local cacheKeyWithBefore = cacheKey .. (cacheKeyTail - 1)


redis.call("del", cacheKeyWithTail)
redis.call("del", cacheKeyWithBefore)

return 1
