package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;

import java.util.List;

/**
 * 保存售后订单处理器接口
 *
 * <AUTHOR>
 * @date 2024-06-05 15:44
 */
public interface ISaveAfterSaleOrder<T> {

    /**
     * 初始化
     *
     * @param context 上下文
     */
    void init(AfterSaleSaveContext context);

    /**
     * 保存订单
     *
     * @param ployOrders 订单数据
     * @return 订单保存结果
     */
    SaveAfterSaleResult<List<SaveOrderResultComposite>> saveOrder(List<T> ployOrders);
}
