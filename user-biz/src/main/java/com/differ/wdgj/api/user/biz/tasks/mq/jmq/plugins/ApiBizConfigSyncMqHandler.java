package com.differ.wdgj.api.user.biz.tasks.mq.jmq.plugins;

import com.differ.jackyun.framework.component.jmq.core.consumer.JMQMessage;
import com.differ.jackyun.framework.component.jmq.core.consumer.JMQSimpleReceive;
import com.differ.jackyun.framework.component.jmq.core.enums.JMQResult;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.BizConfigSyncProcessorFactory;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.IBizConfigSyncProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.data.BizConfigSyncMsgDto;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.data.BizConfigSyncResult;
import com.differ.wdgj.api.user.biz.infrastructure.condition.ConditionalOnEnvType;
import com.differ.wdgj.api.user.biz.infrastructure.data.SystemEnvTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ApiBizConfigSyncTypeEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.jmq.core.ApiJMQ;
import com.differ.wdgj.api.user.biz.tasks.mq.jmq.core.ApiJMQSender;
import org.apache.commons.lang3.StringUtils;

/**
 * API业务配置同步
 *
 * <AUTHOR>
 * @since 2024-07-26 上午 9:40
 */
@ApiJMQ(
        code = "wdgj-api.bizconfig.sync",
        sitesToReceive = SiteTypeCodeConst.WDGJ_API_BUSINESS,
        sitesToSend = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
@ConditionalOnEnvType(exceptEnv = {SystemEnvTypeEnum.DEV})
public class ApiBizConfigSyncMqHandler extends ApiJMQSender implements JMQSimpleReceive<String> {
    /**
     * 消息消费
     *
     * @param jmqMessage 消息内容
     * @return 消费结果
     */
    @Override
    public JMQResult consume(JMQMessage<String> jmqMessage) {

        try {

            if (StringUtils.isAllBlank(jmqMessage.getBody())) {
                log.warn("[API业务配置同步]消息体为空");
                return JMQResult.ACK;
            }

            BizConfigSyncMsgDto mqDto = JsonUtils.deJson(jmqMessage.getBody(), BizConfigSyncMsgDto.class);
            if (mqDto == null) {
                log.warn("[API业务配置同步]消息体解析失败");
                return JMQResult.ACK;
            }

            if (StringUtils.isAllBlank(mqDto.getBizConfigInfo())) {
                log.warn("[API业务配置同步]业务配置信息为空");
                return JMQResult.ACK;
            }

            ApiBizConfigSyncTypeEnum bizType = ApiBizConfigSyncTypeEnum.getEnumByValue(mqDto.getBizType());
            if (bizType == null) {
                log.warn("[API业务配置同步]无法识别的业务类型: {}", mqDto.getBizType());
                return JMQResult.ACK;
            }

            IBizConfigSyncProcessor processor = BizConfigSyncProcessorFactory.create(bizType);
            BizConfigSyncResult result = processor.syncConfig(mqDto.getBizConfigInfo());
            if (result == null) {
                log.warn("[API业务配置同步]业务配置同步失败");
                return JMQResult.ACK;
            }
            if (!result.isSuccess()) {
                log.warn("[API业务配置同步]业务配置同步失败: {}", result.getErrorMsg());
                return JMQResult.ACK;
            }

        } catch (Exception e) {
            log.error("[API业务配置同步]异常", e);
        }
        return JMQResult.ACK;
    }

    /**
     * 发送Mq消息
     * @param bizType 消息类别
     * @param bizConfigInfo 消息信息
     */
    public void sendMessage(ApiBizConfigSyncTypeEnum bizType, String bizConfigInfo){
        BizConfigSyncMsgDto bizConfigSyncMsgDto = new BizConfigSyncMsgDto();
        bizConfigSyncMsgDto.setBizType(bizType.getValue());
        bizConfigSyncMsgDto.setBizConfigInfo(bizConfigInfo);
        sendMessage(JsonUtils.toJson(bizConfigSyncMsgDto));
    }
}
