package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject;

import org.apache.commons.lang3.RandomUtils;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description 线程池拒绝策略:先阻塞（最多半小时），超时后启用调用者同步执行
 * <AUTHOR>
 * @Date 2023/12/4 11:20
 */
public class BlockAndCallerRunsRejectedPolicy extends AbstractThreadPoolRejectedPolicy {

    /**
     * 最大等待时间（单位秒）,默认20分钟
     */
    private static final int DEFAULT_MAX_WAIT_DOWN = 1200;
    /**
     * 随机上浮数（单位秒）,默认10分钟
     */
    private static final int DEFAULT_RANDOM_RANGE = 600;

    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        try {
            BlockingQueue<Runnable> queue = e.getQueue();
            if (!queue.offer(r, getRandomMaxWait(), TimeUnit.SECONDS)) {
                // 阻塞超时后，调用者同步执行
                LOG.error("阻塞调用拒绝策略,阻塞超时后，同步执行:{}", r.toString());
                r.run();
            } else {
                LOG.warn("阻塞调用拒绝策略,阻塞未超时:{}", r.toString());
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new RejectedExecutionException("线程被中断", ex);
        }

    }

    /**
     * 最大阻塞等待时间（秒）
     *
     * @return
     */
    private long getRandomMaxWait() {
        int maxCurrentWaitDown = getMaxWaitDown();
        int maxCurrentWaitUp = maxCurrentWaitDown + getMaxWaitRandomRange();
        return RandomUtils.nextInt(maxCurrentWaitDown, maxCurrentWaitUp);
    }

    /**
     * 最大阻塞等待的下限值（秒）
     *
     * @return
     */
    protected int getMaxWaitDown() {
        return DEFAULT_MAX_WAIT_DOWN;
    }

    /**
     * 阻塞等待的随机浮动值范围（秒）
     *
     * @return
     */
    protected int getMaxWaitRandomRange() {
        return DEFAULT_RANDOM_RANGE;
    }
}
