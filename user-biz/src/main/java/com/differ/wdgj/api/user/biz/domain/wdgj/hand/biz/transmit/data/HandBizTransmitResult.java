package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data;

/**
 * 手动业务操作转发结果
 *
 * <AUTHOR>
 * @date 2024/10/28 下午6:03
 */
public class HandBizTransmitResult
{
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String message;

    //region 公共方法
    /**
     * 成功结果
     *
     * @return 结果
     */
    public static HandBizTransmitResult successResult() {
        HandBizTransmitResult result = new HandBizTransmitResult();
        result.setSuccess(true);
        return result;
    }
    /**
     * 成功结果
     *
     * @return 结果
     */
    public static HandBizTransmitResult successResult(String message) {
        HandBizTransmitResult result = new HandBizTransmitResult();
        result.setSuccess(true);
        result.setMessage(message);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static HandBizTransmitResult failedResult(String message) {
        HandBizTransmitResult result = new HandBizTransmitResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    //endregion
}
