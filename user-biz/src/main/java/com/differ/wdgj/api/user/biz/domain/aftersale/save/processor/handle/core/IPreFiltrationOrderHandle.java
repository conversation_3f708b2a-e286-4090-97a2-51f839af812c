package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;

/**
 * 售后单处理插件-前置过滤订单
 *
 * <AUTHOR>
 * @date 2024-06-07 13:43
 */
public interface IPreFiltrationOrderHandle<O> {
    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    AfterSaleHandleResult preFiltration(SourceAfterSaleOrderItem<O> orderItem, TargetCovertOrderItem targetOrder);
}
