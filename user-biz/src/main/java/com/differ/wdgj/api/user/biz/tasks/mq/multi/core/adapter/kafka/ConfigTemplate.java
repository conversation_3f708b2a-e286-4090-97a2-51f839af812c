package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.kafka;


import com.differ.wdgj.api.user.biz.tasks.mq.kafka.core.KafkaUtil;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * kafka配置模板
 *
 * <AUTHOR>
 * @date 2024/4/10 10:20
 */
@Configuration
@EnableKafka
public class ConfigTemplate {

    /**
     * api的通用kafka配置模板
     */
    public static final String BEAN_API_COMMON_KAFKAPROPERTIES_TEMPLATE = "wdgj.api.common.KafkaProperties.template";

    @Primary
    @Bean
    @ConfigurationProperties(prefix = "spring.kafka", ignoreInvalidFields = true)
    public KafkaProperties getDefaultProperties() {
        return KafkaUtil.defaultKafkaConfigProperties(null, null);
    }

    @Bean(BEAN_API_COMMON_KAFKAPROPERTIES_TEMPLATE)
    @Scope("prototype")
    @ConfigurationProperties(prefix = "omsapi.common.kafka", ignoreInvalidFields = true)
    public KafkaProperties getApiCommonKafkaConfigProperties() {
        return KafkaUtil.defaultKafkaConfigProperties(null, null);
    }
}
