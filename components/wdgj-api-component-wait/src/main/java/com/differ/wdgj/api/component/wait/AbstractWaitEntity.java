package com.differ.wdgj.api.component.wait;

/**
 * @Description 排队任务实体
 * <AUTHOR>
 * @Date 2021/9/10 14:35
 */
public abstract class AbstractWaitEntity {

    public AbstractWaitEntity(String waitUniqueId, Object data) {
        this.waitUniqueId = waitUniqueId;
        this.data = data;
    }

    /**
     * 排队任务ID
     */
    protected String waitUniqueId;

    /**
     * 业务数据
     */
    protected Object data;

    public String getWaitUniqueId() {
        return waitUniqueId;
    }

    /**
     * 获取任务数据字符串
     * @return
     */
    public abstract String getDataString();
}
