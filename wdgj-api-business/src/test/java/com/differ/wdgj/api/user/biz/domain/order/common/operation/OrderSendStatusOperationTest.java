package com.differ.wdgj.api.user.biz.domain.order.common.operation;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderGoodsSendInfoDto;
import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderSendInfoDto;
import com.differ.wdgj.api.user.biz.domain.order.common.data.enums.OrderGoodsSendStatusEnum;
import com.differ.wdgj.api.user.biz.domain.order.common.data.enums.OrderSendStatusEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * OrderSendStatusOperation 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午6:30
 */
public class OrderSendStatusOperationTest extends AbstractSpringTest {

    /**
     * 测试整单发货成功场景
     * 当订单为整单发货且同步状态为成功时，应该返回全部发货成功的结果
     */
    @Test
    public void testGetOrderSendStatus_WholeSendSuccess_ShouldReturnAllSent() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(false, OrderSendStatusEnum.SUCCESS.getValue());
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001, 1002),
            Arrays.asList(new BigDecimal("2"), new BigDecimal("3")),
            Arrays.asList(OrderGoodsSendStatusEnum.SUCCESS.getValue(), OrderGoodsSendStatusEnum.SUCCESS.getValue())
        );
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertEquals("订单ID应该匹配", 1001, result.getBillId());
        Assert.assertEquals("订单号应该匹配", "test-trade-no-001", result.getTradeNo());
        Assert.assertEquals("店铺ID应该匹配", Integer.valueOf(2001), result.getShopId());
        Assert.assertTrue("整单发货成功时应该全部发货", result.isbAllSend());
        
        // 验证商品级结果
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品数量应该匹配", 2, result.getOrderGoodsSendStatusList().size());
        
        for (OrderGoodsSendInfoDto goodsSendInfo : result.getOrderGoodsSendStatusList()) {
            Assert.assertTrue("每个商品都应该全部发货", goodsSendInfo.isbAllSend());
            Assert.assertTrue("成功发货数量应该大于0", goodsSendInfo.getSuccessSendCount().compareTo(BigDecimal.ZERO) > 0);
            Assert.assertEquals("等待发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getWaitSendCount());
            Assert.assertEquals("失败发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getFailSendCount());
        }
    }

    /**
     * 测试整单发货失败场景
     * 当订单为整单发货且同步状态为失败时，应该返回全部发货失败的结果
     */
    @Test
    public void testGetOrderSendStatus_WholeSendFailed_ShouldReturnAllFailed() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(false, OrderSendStatusEnum.HAND_FAIL.getValue());
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001, 1002),
            Arrays.asList(new BigDecimal("2"), new BigDecimal("3")),
            Arrays.asList(OrderGoodsSendStatusEnum.Failed.getValue(), OrderGoodsSendStatusEnum.Failed.getValue())
        );
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertFalse("整单发货失败时不应该全部发货", result.isbAllSend());
        
        // 验证商品级结果
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品数量应该匹配", 2, result.getOrderGoodsSendStatusList().size());
        
        for (OrderGoodsSendInfoDto goodsSendInfo : result.getOrderGoodsSendStatusList()) {
            Assert.assertFalse("每个商品都不应该全部发货", goodsSendInfo.isbAllSend());
            Assert.assertEquals("成功发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getSuccessSendCount());
            Assert.assertEquals("等待发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getWaitSendCount());
            Assert.assertTrue("失败发货数量应该大于0", goodsSendInfo.getFailSendCount().compareTo(BigDecimal.ZERO) > 0);
        }
    }

    /**
     * 测试整单待发货场景
     * 当订单为整单发货且同步状态为待同步时，应该返回全部待发货的结果
     */
    @Test
    public void testGetOrderSendStatus_WholeSendWaiting_ShouldReturnAllWaiting() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(false, OrderSendStatusEnum.WAIT_SYNC.getValue());
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001, 1002),
            Arrays.asList(new BigDecimal("2"), new BigDecimal("3")),
            Arrays.asList(OrderGoodsSendStatusEnum.WAIT_SYNC.getValue(), OrderGoodsSendStatusEnum.WAIT_SYNC.getValue())
        );
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertFalse("整单待发货时不应该全部发货", result.isbAllSend());
        
        // 验证商品级结果
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品数量应该匹配", 2, result.getOrderGoodsSendStatusList().size());
        
        for (OrderGoodsSendInfoDto goodsSendInfo : result.getOrderGoodsSendStatusList()) {
            Assert.assertFalse("每个商品都不应该全部发货", goodsSendInfo.isbAllSend());
            Assert.assertEquals("成功发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getSuccessSendCount());
            Assert.assertTrue("等待发货数量应该大于0", goodsSendInfo.getWaitSendCount().compareTo(BigDecimal.ZERO) > 0);
            Assert.assertEquals("失败发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getFailSendCount());
        }
    }

    /**
     * 测试拆单发货全部成功场景
     * 当订单为拆单发货且所有商品都发货成功时，应该返回全部发货成功的结果
     */
    @Test
    public void testGetOrderSendStatus_SplitSendAllSuccess_ShouldReturnAllSent() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(true, OrderSendStatusEnum.SPLIT_OUT.getValue());
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001, 1002),
            Arrays.asList(new BigDecimal("2"), new BigDecimal("3")),
            Arrays.asList(OrderGoodsSendStatusEnum.SUCCESS.getValue(), OrderGoodsSendStatusEnum.SUCCESS.getValue())
        );
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("拆单发货全部成功时应该全部发货", result.isbAllSend());
        
        // 验证商品级结果
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品数量应该匹配", 2, result.getOrderGoodsSendStatusList().size());
        
        for (OrderGoodsSendInfoDto goodsSendInfo : result.getOrderGoodsSendStatusList()) {
            Assert.assertTrue("每个商品都应该全部发货", goodsSendInfo.isbAllSend());
            Assert.assertTrue("成功发货数量应该大于0", goodsSendInfo.getSuccessSendCount().compareTo(BigDecimal.ZERO) > 0);
            Assert.assertEquals("等待发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getWaitSendCount());
            Assert.assertEquals("失败发货数量应该为0", BigDecimal.ZERO, goodsSendInfo.getFailSendCount());
        }
    }

    /**
     * 测试拆单发货部分成功场景
     * 当订单为拆单发货且部分商品发货成功时，应该返回部分发货的结果
     */
    @Test
    public void testGetOrderSendStatus_SplitSendPartialSuccess_ShouldReturnPartialSent() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(true, OrderSendStatusEnum.SPLIT_OUT.getValue());
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001, 1002),
            Arrays.asList(new BigDecimal("2"), new BigDecimal("3")),
            Arrays.asList(OrderGoodsSendStatusEnum.SUCCESS.getValue(), OrderGoodsSendStatusEnum.WAIT_SYNC.getValue())
        );
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertFalse("拆单发货部分成功时不应该全部发货", result.isbAllSend());
        
        // 验证商品级结果
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品数量应该匹配", 2, result.getOrderGoodsSendStatusList().size());
        
        // 验证第一个商品（成功）
        OrderGoodsSendInfoDto firstGoods = result.getOrderGoodsSendStatusList().get(0);
        Assert.assertTrue("第一个商品应该全部发货", firstGoods.isbAllSend());
        Assert.assertTrue("第一个商品成功发货数量应该大于0", firstGoods.getSuccessSendCount().compareTo(BigDecimal.ZERO) > 0);
        
        // 验证第二个商品（待发货）
        OrderGoodsSendInfoDto secondGoods = result.getOrderGoodsSendStatusList().get(1);
        Assert.assertFalse("第二个商品不应该全部发货", secondGoods.isbAllSend());
        Assert.assertTrue("第二个商品等待发货数量应该大于0", secondGoods.getWaitSendCount().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * 测试拆单发货失败场景
     * 当订单为拆单发货且商品发货失败时，应该返回发货失败的结果
     */
    @Test
    public void testGetOrderSendStatus_SplitSendFailed_ShouldReturnFailed() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(true, OrderSendStatusEnum.SPLIT_SEND_FAIL.getValue());
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001, 1002),
            Arrays.asList(new BigDecimal("2"), new BigDecimal("3")),
            Arrays.asList(OrderGoodsSendStatusEnum.Failed.getValue(), OrderGoodsSendStatusEnum.PARTIAL_SUCCESS.getValue())
        );
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertFalse("拆单发货失败时不应该全部发货", result.isbAllSend());
        
        // 验证商品级结果
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品数量应该匹配", 2, result.getOrderGoodsSendStatusList().size());
        
        // 验证第一个商品（失败）
        OrderGoodsSendInfoDto firstGoods = result.getOrderGoodsSendStatusList().get(0);
        Assert.assertFalse("第一个商品不应该全部发货", firstGoods.isbAllSend());
        Assert.assertTrue("第一个商品失败发货数量应该大于0", firstGoods.getFailSendCount().compareTo(BigDecimal.ZERO) > 0);
        
        // 验证第二个商品（部分成功）
        OrderGoodsSendInfoDto secondGoods = result.getOrderGoodsSendStatusList().get(1);
        Assert.assertFalse("第二个商品不应该全部发货", secondGoods.isbAllSend());
        Assert.assertTrue("第二个商品等待发货数量应该大于0", secondGoods.getWaitSendCount().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * 测试所有发货状态枚举值的覆盖
     * 确保所有OrderSendStatusEnum枚举值都能正常处理
     */
    @Test
    public void testGetOrderSendStatus_AllSendStatusEnums() {
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001),
            Arrays.asList(new BigDecimal("1")),
            Arrays.asList(OrderGoodsSendStatusEnum.SUCCESS.getValue())
        );
        
        OrderSendStatusEnum[] allStatuses = OrderSendStatusEnum.values();
        
        for (OrderSendStatusEnum status : allStatuses) {
            ApiTradeDO apiTrade = createApiTrade(false, status.getValue());
            
            // 执行测试
            OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
            
            // 验证结果不为空
            Assert.assertNotNull(String.format("状态%s应该能正常处理", status), result);
            Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        }
    }

    /**
     * 测试所有商品发货状态枚举值的覆盖
     * 确保所有OrderGoodsSendStatusEnum枚举值都能正常处理
     */
    @Test
    public void testGetOrderSendStatus_AllGoodsSendStatusEnums() {
        ApiTradeDO apiTrade = createApiTrade(true, OrderSendStatusEnum.SPLIT_OUT.getValue());
        OrderGoodsSendStatusEnum[] allStatuses = OrderGoodsSendStatusEnum.values();
        
        for (OrderGoodsSendStatusEnum status : allStatuses) {
            List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
                Arrays.asList(1001),
                Arrays.asList(new BigDecimal("1")),
                Arrays.asList(status.getValue())
            );
            
            // 执行测试
            OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, apiTradeGoodsList);
            
            // 验证结果不为空
            Assert.assertNotNull(String.format("商品状态%s应该能正常处理", status), result);
            Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
            Assert.assertEquals("商品数量应该为1", 1, result.getOrderGoodsSendStatusList().size());
        }
    }

    /**
     * 测试边界条件 - apiTrade为null
     */
    @Test(expected = NullPointerException.class)
    public void testGetOrderSendStatus_ApiTradeNull_ShouldThrowException() {
        // 准备测试数据
        List<ApiTradeGoodsDO> apiTradeGoodsList = createApiTradeGoodsList(
            Arrays.asList(1001),
            Arrays.asList(new BigDecimal("1")),
            Arrays.asList(OrderGoodsSendStatusEnum.SUCCESS.getValue())
        );
        
        // 执行测试 - 应该抛出异常
        OrderSendStatusOperation.getOrderSendStatus(null, apiTradeGoodsList);
    }

    /**
     * 测试边界条件 - apiTradeGoodsList为null
     */
    @Test
    public void testGetOrderSendStatus_ApiTradeGoodsListNull_ShouldReturnBasicInfo() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(false, OrderSendStatusEnum.SUCCESS.getValue());
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, null);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertEquals("订单ID应该匹配", 1001, result.getBillId());
        Assert.assertEquals("订单号应该匹配", "test-trade-no-001", result.getTradeNo());
        Assert.assertEquals("店铺ID应该匹配", Integer.valueOf(2001), result.getShopId());
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品发货状态列表应该为空", 0, result.getOrderGoodsSendStatusList().size());
    }

    /**
     * 测试边界条件 - apiTradeGoodsList为空列表
     */
    @Test
    public void testGetOrderSendStatus_ApiTradeGoodsListEmpty_ShouldReturnBasicInfo() {
        // 准备测试数据
        ApiTradeDO apiTrade = createApiTrade(false, OrderSendStatusEnum.SUCCESS.getValue());
        List<ApiTradeGoodsDO> emptyList = new ArrayList<>();
        
        // 执行测试
        OrderSendInfoDto result = OrderSendStatusOperation.getOrderSendStatus(apiTrade, emptyList);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertEquals("订单ID应该匹配", 1001, result.getBillId());
        Assert.assertEquals("订单号应该匹配", "test-trade-no-001", result.getTradeNo());
        Assert.assertEquals("店铺ID应该匹配", Integer.valueOf(2001), result.getShopId());
        Assert.assertNotNull("商品发货状态列表不应为空", result.getOrderGoodsSendStatusList());
        Assert.assertEquals("商品发货状态列表应该为空", 0, result.getOrderGoodsSendStatusList().size());
    }

    /**
     * 创建测试用的ApiTrade对象
     *
     * @param bSplit    是否拆单
     * @param synStatus 同步状态
     * @return ApiTrade对象
     */
    private ApiTradeDO createApiTrade(boolean bSplit, int synStatus) {
        ApiTradeDO apiTrade = new ApiTradeDO();
        apiTrade.setBillId(1001);
        apiTrade.setTradeNo("test-trade-no-001");
        apiTrade.setShopId(2001);
        apiTrade.setbSplit(bSplit);
        apiTrade.setSynStatus(synStatus);
        return apiTrade;
    }

    /**
     * 创建测试用的ApiTradeGoods列表
     *
     * @param recIds      记录ID列表
     * @param goodsCounts 商品数量列表
     * @param bSendValues 发货状态列表
     * @return ApiTradeGoods列表
     */
    private List<ApiTradeGoodsDO> createApiTradeGoodsList(List<Integer> recIds, List<BigDecimal> goodsCounts, List<Integer> bSendValues) {
        List<ApiTradeGoodsDO> apiTradeGoodsList = new ArrayList<>();
        
        for (int i = 0; i < recIds.size(); i++) {
            ApiTradeGoodsDO apiTradeGoods = new ApiTradeGoodsDO();
            apiTradeGoods.setRecId(recIds.get(i));
            apiTradeGoods.setGoodsId("test-goods-id-" + String.format("%03d", i + 1));
            apiTradeGoods.setGoodsName("测试商品" + (i + 1));
            apiTradeGoods.setGoodsCount(goodsCounts.get(i));
            apiTradeGoods.setbSend(bSendValues.get(i));
            apiTradeGoodsList.add(apiTradeGoods);
        }
        
        return apiTradeGoodsList;
    }
}
