package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 淘宝 - 快递拦截出资方
 *
 * <AUTHOR>
 * @date 2025/4/3 下午3:46
 */
public enum LogisticInterceptInvestorEnum implements ValueEnum {

    /**
     * 淘宝出资方
     */
    TAO_BAO("淘宝", 0),

    /**
     * 商家出资方
     */
    SELLER("商家", 1);

    /**
     * 描述
     */
    private final String caption;

    /**
     * 值
     */
    private final Integer value;


    //region 构造
    private LogisticInterceptInvestorEnum(String caption, Integer value) {
        this.caption = caption;
        this.value = value;
    }

    //endregion

    //region 公共方法
    @Override
    public Integer getValue() {
        return this.value;
    }

    public String getCaption() {
        return caption;
    }

    /**
     * 根据平台值获取对应的枚举。
     *
     * @param value 平台值
     * @return 对应的枚举
     */
    public static LogisticInterceptInvestorEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, LogisticInterceptInvestorEnum.class, EnumConvertType.VALUE);
    }
    //endregion
}


