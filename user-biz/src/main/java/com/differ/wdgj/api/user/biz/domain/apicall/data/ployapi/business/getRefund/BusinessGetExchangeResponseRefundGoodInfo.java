package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund;

import java.math.BigDecimal;

/**
 * 菠萝派下载换货单-换货商品（Differ.JH.Business.GetExchange 节点 refundGoods）
 *
 * <AUTHOR>
 * @date 2024/8/7 上午11:31
 */
public class BusinessGetExchangeResponseRefundGoodInfo {
    /**
     * 平台商品ID
     */
    private String platProductId;

    /**
     * skuId
     */
    private String sku;

    /**
     * 外部商家编码
     */
    private String outerId;

    /**
     * 外部SKU编码
     */
    private String outSkuId;

    /**
     * sku名称
     */
    private String skuSpec;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 商品价格(单位: 元)
     */
    private BigDecimal price;

    /**
     * 商品数量
     */
    private int productNum;

    /**
     * 商品退货数量
     */
    private int refundProductNum;

    /**
     * 商品状态
     */
    private String goodsStatus;

    /**
     * 商品状态描述
     */
    private String goodsStatusDesc;

    /**
     * 子订单号
     */
    private String subTradeNo;

    //region get/set
    public String getPlatProductId() {
        return platProductId;
    }

    public void setPlatProductId(String platProductId) {
        this.platProductId = platProductId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getOutSkuId() {
        return outSkuId;
    }

    public void setOutSkuId(String outSkuId) {
        this.outSkuId = outSkuId;
    }

    public String getSkuSpec() {
        return skuSpec;
    }

    public void setSkuSpec(String skuSpec) {
        this.skuSpec = skuSpec;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public int getProductNum() {
        return productNum;
    }

    public void setProductNum(int productNum) {
        this.productNum = productNum;
    }

    public int getRefundProductNum() {
        return refundProductNum;
    }

    public void setRefundProductNum(int refundProductNum) {
        this.refundProductNum = refundProductNum;
    }

    public String getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(String goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public String getGoodsStatusDesc() {
        return goodsStatusDesc;
    }

    public void setGoodsStatusDesc(String goodsStatusDesc) {
        this.goodsStatusDesc = goodsStatusDesc;
    }

    public String getSubTradeNo() {
        return subTradeNo;
    }

    public void setSubTradeNo(String subTradeNo) {
        this.subTradeNo = subTradeNo;
    }
    //endregion
}
