package com.differ.wdgj.api.user.biz.infrastructure.external.qywx.message;

import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.external.qywx.QyWeixinRobotMessage;
import com.differ.wdgj.api.user.biz.infrastructure.external.qywx.data.enums.MessageTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 上午10:15
 */
public class TextQyWeixinRobotMessage extends QyWeixinRobotMessage {
    /**
     * 文本内容，最长不超过2048个字节，必须是utf8编码
     */
    private String content;

    /**
     * 手机号列表，提醒手机号对应的群成员(@某个成员)，@all表示提醒所有人
     */
    @JSONField(name = "mentioned_mobile_list")
    private List<String> mentionedMobileList;

    //region 构造
    public TextQyWeixinRobotMessage(String content, List<String> mobiles) {
        this.content = content;
        this.mentionedMobileList = mobiles;
    }
    //endregion

    //region 重写基类方法
    @Override
    public String getMsgType() {
        return MessageTypeEnum.TEXT.getCode();
    }

    //endregion

    //region 私有方法
    /**
     * 转换手机号
     *
     * @param mobile 手机号列表字符串
     * @return 手机号列表
     */
    private ArrayList<String> parseMobileList(String mobile) {
        ArrayList<String> mobileList = new ArrayList<>();
        if (mobile.startsWith("[") && mobile.endsWith("]")) {
            return JsonUtils.deJson(mobile, new TypeReference<ArrayList<String>>(){});
        }
        String[] mobiles = mobile.split(",");
        for (String m : mobiles) {
            if (StringUtils.isNotBlank(m)) {
                mobileList.add(m.trim());
            }
        }
        return mobileList;
    }
    //endregion

    //region get/set
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<String> getMentionedMobileList() {
        return mentionedMobileList;
    }

    public void setMentionedMobileList(List<String> mentionedMobileList) {
        this.mentionedMobileList = mentionedMobileList;
    }
    //endregion
}
