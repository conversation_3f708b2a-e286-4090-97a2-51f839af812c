package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.post;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPostProcessOrderHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后商品删除处理
 *
 * <AUTHOR>
 * @date 2024/7/18 下午7:14
 */
public class RefundGoodsDeleteProcessHandle extends AbstractPostProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造
    public RefundGoodsDeleteProcessHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 售后单后置处理
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult processOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        if (dbOrder != null) {
            // 需要删除的退货商品Id
            List<Integer> deleteReturnGoodsIds = new ArrayList<>();
            // 退货/退款商品存在校验
            List<ApiReturnDetailDO> oldReturnGoodsList = dbOrder.getReturnGoods();
            if (CollectionUtils.isNotEmpty(oldReturnGoodsList)) {
                for (ApiReturnDetailDO oldReturnGoods : oldReturnGoodsList) {
                    if (targetOrder.getRefundGoods().stream().noneMatch(x -> AfterSaleGoodsMatchOperation.isMatchHistoryRefundGoods(x, oldReturnGoods))) {
                        deleteReturnGoodsIds.add(oldReturnGoods.getRecId());
                    }
                }
            }
            targetOrder.getDeleteReturnGoodsIds().addAll(deleteReturnGoodsIds);

            // 换货商品存在校验
            List<ApiReturnDetailTwoDO> exchangeGoodsList = dbOrder.getExchangeGoods();
            // 存在换货商品，且不为换货单，删除换货商品
            if (CollectionUtils.isNotEmpty(exchangeGoodsList) && afterSaleOrder.getType() != WdgjRefundTypeEnum.EXCHANGE.getValue()) {
                List<Integer> exchangeGoodRecIds = exchangeGoodsList.stream().map(ApiReturnDetailTwoDO::getRecId).collect(Collectors.toList());
                targetOrder.getDeleteExchangeGoodsIds().addAll(exchangeGoodRecIds);
            }
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "售后商品删除处理";
    }
    //endregion
}
