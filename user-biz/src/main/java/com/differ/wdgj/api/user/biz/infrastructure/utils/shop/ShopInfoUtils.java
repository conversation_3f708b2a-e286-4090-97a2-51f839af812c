package com.differ.wdgj.api.user.biz.infrastructure.utils.shop;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopAuthLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopBaseLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopOutIdLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopAuthDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.OutShopInfoDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.auth.ShopAuthCustomParam;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 店铺信息工具类
 *
 * <AUTHOR>
 * @date 2024-06-26 17:39
 */
public class ShopInfoUtils {
    //region 构造
    private ShopInfoUtils() {
    }
    //endregion

    //region 获取店铺基础信息

    /**
     * 通过店铺Id获取店铺基础信息
     *
     * @param outAccount 外部会员名
     * @param shopId     店铺Id
     * @return 店铺基础信息
     */
    public static ApiShopBaseDto singleByShopId(String outAccount, int shopId) {
        return ApiShopBaseLocalCache.singleton().getInfo(outAccount, shopId);
    }

    /**
     * 通过外部店铺Id获取店铺基础信息
     *
     * @param outAccount 外部会员名
     * @param outShopId  外部店铺Id
     * @return 店铺基础信息
     */
    public static ApiShopBaseDto singleByOutShopId(String outAccount, int outShopId) {
        OutShopInfoDto outShopInfo = ApiShopOutIdLocalCache.singleton().findShopId(outAccount, outShopId);
        if (outShopInfo != null) {
            return ApiShopBaseLocalCache.singleton().getInfo(outAccount, outShopInfo.getApiShopId());
        }
        return null;
    }
    //endregion

    //region 获取店铺授权信息

    /**
     * 通过店铺Id获取店铺基础信息
     *
     * @param outAccount 外部会员名
     * @param shopId     店铺Id
     * @return 店铺基础信息
     */
    public static ApiShopAuthDto singleAuthByShopId(String outAccount, int shopId) {
        return ApiShopAuthLocalCache.singleton().getInfo(outAccount, shopId);
    }

    /**
     * 通过外部店铺Id获取店铺基础信息
     *
     * @param outAccount 外部会员名
     * @param outShopId  外部店铺Id
     * @return 店铺基础信息
     */
    public static ApiShopAuthDto singleAuthByOutShopId(String outAccount, int outShopId) {
        OutShopInfoDto outShopInfo = ApiShopOutIdLocalCache.singleton().findShopId(outAccount, outShopId);
        if (outShopInfo != null) {
            return ApiShopAuthLocalCache.singleton().getInfo(outAccount, outShopInfo.getApiShopId());
        }
        return null;
    }

    /**
     * 解析授权自定义参数
     *
     * @param customParams 自定义参数
     * @return 解析后自定义参数列表
     */
    public static List<ShopAuthCustomParam> DeserializationAuthCustomParams(String customParams) {
        if(StringUtils.isNotEmpty(customParams)){
            String[] customParamStrArray = customParams.split("\\*#\\$\\^\\$#\\*");
            List<ShopAuthCustomParam> list = new ArrayList<>();
            for (String paramsStr : customParamStrArray) {
                String[] params = paramsStr.split("#@\\^@#");
                ShopAuthCustomParam shopAuthCustomParam = new ShopAuthCustomParam();
                shopAuthCustomParam.seteKey(params[0]);
                shopAuthCustomParam.setcKey(params[1]);
                String value = params.length > 2 ? params[2] : StringUtils.EMPTY;
                shopAuthCustomParam.setValue(value);
                list.add(shopAuthCustomParam);
            }
            return list;
        }

        return new ArrayList<>();
    }

    //endregion
}
