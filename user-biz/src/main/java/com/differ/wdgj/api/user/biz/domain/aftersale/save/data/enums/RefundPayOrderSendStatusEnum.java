package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums;

/**
 * 仅退款订单发货状态
 *
 * <AUTHOR>
 * @date 2025/6/19 15:47
 */
public enum RefundPayOrderSendStatusEnum {
    /**
     * 未知
     */
    UNKNOWN(0, "未知"),
    /**
     * 未发货
     */
    NOT_SEND(1, "未发货"),
    /**
     * 已发货
     */
    SEND(2, "已发货"),
    ;

    //region 常量
    /**
     * 值
     */
    private final int value;

    /**
     * 描述
     */
    private final String description;

    //endregion

    //region 构造
    RefundPayOrderSendStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }
    //endregion

    //region 公共方法
    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
    //endregion
}
