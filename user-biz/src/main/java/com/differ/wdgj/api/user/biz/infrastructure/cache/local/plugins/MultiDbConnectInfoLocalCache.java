package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins;

import com.differ.wdgj.api.component.multidb.core.DataSourceContext;
import com.differ.wdgj.api.component.multidb.core.DbConnectionInfo;
import com.differ.wdgj.api.component.multidb.core.IMultiDbConnectInfoLocalCache;
import com.differ.wdgj.api.component.multidb.exception.MultidbException;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.AccountMappingCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.ServerRdsMappingCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj.WdgjYunDataSourceCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj.WdgjYunMemberDataSourceCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloServerRdsMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberAccountMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WdgjYunDataSourceDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WdgjYunMemberDataSourceDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.SystemErrorCodes;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SingleDataSourceConfig;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitcherDBTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.cryptography.APICryptography;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 多数据库连接的业务组件实现
 *
 * <AUTHOR>
 * @date 2021/1/18 13:21
 */
public class MultiDbConnectInfoLocalCache extends AbstractLocalCache<SwitchDbContext, DbConnectionInfo> implements IMultiDbConnectInfoLocalCache {

    /**
     * 初始化并检查数据源信息
     */
    @Override
    public void initAndCheck() {

    }

    /**
     * 根据上下文获取数据源信息
     *
     * @param context
     * @return
     */
    @Override
    public DbConnectionInfo getCache(DataSourceContext context) {
        if (context instanceof SwitchDbContext) {
            return this.getCacheThenSource((SwitchDbContext) context);
        }

        throw new AppException(SystemErrorCodes.SYSTEMERROR, "数据源上下文类型错误");
    }

    @Override
    public DataSource getSingleDataSource(DataSourceContext context) {
        if (context instanceof SwitchDbContext) {
            SwitchDbContext key = (SwitchDbContext) context;
            switch (key.getDataSourceSwitcherType()) {
                case API:
                    return BeanContextUtil.getBean(SingleDataSourceConfig.CENTER_DB_DATASOURCE,DataSource.class);
                default:
                    break;
            }
        }
        return null;
    }

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key
     * @return
     */
    @Override
    protected DbConnectionInfo loadSource(SwitchDbContext key) {
        // 本地开发环境时，可指定数据源
//        if (SystemEnvTypeEnum.DEV.equals(LocalConfig.get().getSystemEnvType())) {
//            return LocalTestDataSource.getInstance().get(key);
//        }

        // 非本地开发环境，从缓存取
        switch (key.getDataSourceSwitcherType()) {
            case WDGJ:
            case RDS_PUSH:
                return getWdgjDbConnectionInfo(key);
                // 注释原因：单体数据库直接走spring配置
//            case API:
//                return getAPIDbConnectionInfo(key);
            default:
                break;
        }
        throw new AppException(SystemErrorCodes.LOGICERROR, "不支持的数据库切换类型:" + key.getDataSourceSwitcherType());
    }

    /**
     * 从上下文创建API数据库连接信息
     *
     * @param context 连接上下文
     * @return 数据库连接信息
     */
    private DbConnectionInfo getAPIDbConnectionInfo(SwitchDbContext context) {
        SingleDataSourceConfig connection = BeanContextUtil.getBean(SingleDataSourceConfig.class);
        if(connection == null) {
            throw new AppException(SystemErrorCodes.LOGICERROR, "未找到中心库连接信息");
        }
        return connection;
    }

    /**
     * 从上下文创建会员数据库连接信息
     *
     * @param context 连接上下文
     * @return 数据库连接信息
     */
    private DbConnectionInfo getWdgjDbConnectionInfo(SwitchDbContext context) {
        if (StringUtils.isEmpty(context.getWdgjUser())) {
            throw new MultidbException("操作业务的会员参数不能为空");
        }

        DbConnectionInfo dbConnectionInfo = getWdgjDbConnectionInfoFromWdgjYun(context);
        if(dbConnectionInfo != null){
            return dbConnectionInfo;
        }

        log.error("网店管家云端会员数据库链接缓存映射失败：{}", context.getWdgjUser());

        return getWdgjDbConnectionInfoFromApi(context);
    }



    /**
     * 通过API缓存信息创建会员数据库连接信息
     * @param context 连接上下文
     * @return 数据库连接信息
     */
    private DbConnectionInfo getWdgjDbConnectionInfoFromWdgjYun(SwitchDbContext context){
        try {

            WdgjYunMemberDataSourceDto memberDataSource = WdgjYunMemberDataSourceCache.singleton().getMemberDataSource(context.getWdgjUser());
            if(memberDataSource == null || StringUtils.isEmpty(memberDataSource.getRdsId())){
                return null;
            }

            WdgjYunDataSourceDto dataSource = WdgjYunDataSourceCache.singleton().getDataSource(memberDataSource.getRdsId());
            if(dataSource == null){
                return null;
            }

            return toDbConnectionInfo(context, dataSource.getAddress(), dataSource.getPort(), dataSource.getUserName(), dataSource.getPassword());
        }
        catch (Exception e){
            log.error("获取网店管家云端会员数据库链接缓存映射异常：" + context.getWdgjUser(), e);
            return null;
        }

    }

    /**
     * 通过API缓存信息创建会员数据库连接信息
     * @param context 连接上下文
     * @return 数据库连接信息
     */
    private DbConnectionInfo getWdgjDbConnectionInfoFromApi(SwitchDbContext context){
        //取IP
        String apiUserName = OuterApiEnum.WDGJ.getApiUserName(context.getWdgjUser());
        MemberAccountMappingDO accountMapping = AccountMappingCache.singleton().getAndSyncIfAbsent(apiUserName);
        if (accountMapping == null) {
            log.error("未找到会员映射：{}", apiUserName);
            throw new AppException(SystemErrorCodes.LOGICERROR, "未找到会员映射：" + apiUserName);
        }
        String serverIp = accountMapping.getServeripport();
        String[] arrIp = accountMapping.getServeripport().split(":");
        if (arrIp.length > 1) {
            serverIp = arrIp[0];
        }
        //获取RDS信息
        GloServerRdsMappingDO rds = ServerRdsMappingCache.singleton().getAndSyncIfAbsent(serverIp);
        if (rds == null) {
            throw new AppException(SystemErrorCodes.LOGICERROR, "未找到RDS信息：" + serverIp);
        }
        int port = Integer.parseInt(rds.getRdsserverport());
        log.debug("getRdsUserId解密前：{}", rds.getRdsuserid());
        String rdsUser = APICryptography.singleton().decrypt(rds.getRdsuserid());
        log.debug("getRdsUserId解密后：{}", rdsUser);

        log.debug("getRdsPwd：{}", rds.getRdspwd());
        String rdsPwd = APICryptography.singleton().decrypt(rds.getRdspwd());
        log.debug("getRdsPwd：{}", rdsPwd);

        return toDbConnectionInfo(context, rds.getRdsserverip(), port, rdsUser, rdsPwd);
    }


    /**
     *  添加单个数据库信息，用于开发环境初始数据库信息
     * @param context 连接上下文
     * @param ip 数据库地址
     * @param port 数据库接口
     * @param dbUserName 数据库用户名
     * @param dbPassword 数据库密码
     * @return 数据库连接信息
     */
    protected DbConnectionInfo toDbConnectionInfo(SwitchDbContext context, String ip, int port, String dbUserName, String dbPassword) {
        DbConnectionInfo connectionInfo = new DbConnectionInfo();
        connectionInfo.setIp(ip);
        connectionInfo.setPort(port);
        connectionInfo.setUserName(dbUserName);
        connectionInfo.setPassword(dbPassword);
        return connectionInfo;
    }

    private static class LocalTestDataSource {

        /**
         * 数据库连接信息
         */
        private Map<DataSourceContext, DbConnectionInfo> mapDbConncetions;

        //region 饿汉式单例

        private final static LocalTestDataSource instance;

        static {
            instance = new LocalTestDataSource();
        }

        public static LocalTestDataSource getInstance() {
            return instance;
        }

        public LocalTestDataSource() {
            mapDbConncetions = new HashMap<>();
            this.addDatabase(SwitcherDBTypeEnum.WDGJ, "api2017", "**************", 3306, "honggw", "honggw3307");
            this.addDatabase(SwitcherDBTypeEnum.WDGJ, "esapi2017", "***********", 3307, "wdgjdev", "devwdgj");
            this.addDatabase(SwitcherDBTypeEnum.RDS_PUSH, "api2017", "**************", 3306, "honggw", "honggw3307");
            this.addDatabase(SwitcherDBTypeEnum.RDS_PUSH, "esapi2017", "**************", 3306, "honggw", "honggw3307");
            this.addDatabase(SwitcherDBTypeEnum.API, null, "**************", 3306, "honggw", "honggw3307");
        }

        //endregion


        /**
         * 添加单个数据库信息，用于开发环境初始数据库信息
         *
         * @param switcherType
         * @param wdgjUser
         * @param ip
         * @param port
         * @param dbUserName
         * @param dbPassword
         */
        private void addDatabase(SwitcherDBTypeEnum switcherType, String wdgjUser, String ip, int port, String dbUserName, String dbPassword) {
            DataSourceContext context = new SwitchDbContext(switcherType, wdgjUser);
            DbConnectionInfo connectionInfo = new DbConnectionInfo();
            connectionInfo.setIp(ip);
            connectionInfo.setPort(port);
            connectionInfo.setUserName(dbUserName);
            connectionInfo.setPassword(dbPassword);
            mapDbConncetions.put(context, connectionInfo);
        }

        public DbConnectionInfo get(DataSourceContext context) {
            return this.mapDbConncetions.get(context);
        }
    }
}
