package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data;

import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen.JmqBatchListenAnnotation;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.callback.fail.DefaultMultiConsumerFailCallback;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.callback.fail.MultiConsumerFailCallback;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.enable.DefaultApiMultiMqEnabled;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.enable.MultiQueueEnabled;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.intercept.DefaultMultiReceiveInterceptorImpl;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.intercept.DefaultMultiSendInterceptorImpl;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.intercept.MultiReceiveInterceptor;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.intercept.MultiSendInterceptor;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueueEnum;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * API多队列注解
 *
 * <AUTHOR>
 * @date 2024-03-19 13:52
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
@Inherited
public @interface ApiMultiMQ {

    /**
     * 队列标识
     *
     * @return 队列标识
     */
    String code() default "";

    /**
     * 子队列的的列表，列表顺序表示发送优先顺序
     *
     * @return
     */
    SubQueueEnum[] subQueues() default {};

    /**
     * 超过最大重试次数时的数据保存队列
     *
     * @return
     */
    SubQueueEnum maxRetryFailQueue() default SubQueueEnum.DB_CENTER;

    /**
     * 是否开启限流
     *
     * @return
     */
    boolean limitEnable() default true;

    /**
     * 发送站点
     *
     * @return 站点集合
     */
    String[] sitesToSend() default {};

    /**
     * 接收站点
     *
     * @return 站点集合
     */
    String[] sitesToReceive() default {};

    /**
     * 消费者/生产者是否会被创建
     *
     * @return enable
     */
    Class<? extends MultiQueueEnabled> queueEnabled() default DefaultApiMultiMqEnabled.class;

    /**
     * 消息发送拦截器
     *
     * @return ConsumerFailCallback
     */
    Class<? extends MultiSendInterceptor> multiSendInterceptor() default DefaultMultiSendInterceptorImpl.class;

    /**
     * 消息消费拦截器
     *
     * @return ConsumerFailCallback
     */
    Class<? extends MultiReceiveInterceptor> multiReceiveInterceptor() default DefaultMultiReceiveInterceptorImpl.class;


    /**
     * 消息消费重试达到最大次数回调处理
     *
     * @return ConsumerFailCallback
     */
    Class<? extends MultiConsumerFailCallback> consumerFailCallback() default DefaultMultiConsumerFailCallback.class;

    /**
     * 消费延时级别
     *
     * @return 消费延时级别
     */
    DelayGradeEnum delayTime() default DelayGradeEnum.GRADE_0;

    /**
     * JMQ批量监听参数
     * @return
     */
    JmqBatchListenAnnotation jmqBatch() default @JmqBatchListenAnnotation();

}
