package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen;

import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.JmqAdapter;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.JmqDynamicBeanFactory;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.ApiMultiMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.ApplicationContext;

/**
 * JMQ适配对象实例创建器
 *
 * <AUTHOR>
 * @date 2024/6/7 14:32
 */
public class JmqDynamicCreator {

    private static Logger log = LoggerFactory.getLogger(JmqDynamicCreator.class);

    /**
     * 创建注册bean的JMQ适配实例
     *
     * @param jmqCode
     * @param multiMQ
     * @param nameGenerator
     * @param registry
     * @param applicationContext
     * @return
     */
    public static JmqAdapter createRegister(String jmqCode, ApiMultiMQ multiMQ, BeanNameGenerator nameGenerator, BeanDefinitionRegistry registry, ApplicationContext applicationContext) {
        // 开始动态新增Bean
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(JmqAdapter.class);
        GenericBeanDefinition definition = (GenericBeanDefinition) builder.getRawBeanDefinition();
        // 构造参数与FactoryBean保持一致
        definition.getConstructorArgumentValues().addGenericArgumentValue(JmqAdapter.class);
        definition.getConstructorArgumentValues().addGenericArgumentValue(jmqCode);
        definition.getConstructorArgumentValues().addGenericArgumentValue(multiMQ);
        //设置FactoryBean，这里的BeanClass是生成Bean实例的工厂，不是Bean本身。
        definition.setBeanClass(JmqDynamicBeanFactory.class);
        //这里采用的是byType方式注入，类似的还有byName等
        definition.setAutowireMode(GenericBeanDefinition.AUTOWIRE_BY_TYPE);

        String beanName = nameGenerator.generateBeanName(definition, registry);
        registry.registerBeanDefinition(beanName, definition);

        JmqAdapter instance = (JmqAdapter) applicationContext.getBean(beanName);
        return instance;
    }

    /**
     * 创建未注册bean的JMQ适配实例
     *
     * @param jmqCode
     * @param multiMQ
     * @return
     */
    public static JmqAdapter createUnRegister(String jmqCode, ApiMultiMQ multiMQ) {
        JmqDynamicBeanFactory jmqDynamicBeanFactory = new JmqDynamicBeanFactory(JmqAdapter.class, jmqCode, multiMQ);
        try {
            return (JmqAdapter) jmqDynamicBeanFactory.getObject();
        } catch (Exception e) {
            log.error("创建JMQ适配对象失败!", e);
            return null;
        }
    }

}
