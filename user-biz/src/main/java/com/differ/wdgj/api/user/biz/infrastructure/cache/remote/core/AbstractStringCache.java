package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description 按需缓存，数据会过期，同时能防止缓存雪崩和穿透
 * <AUTHOR>
 * @Date 2020/12/17 17:18
 */
public abstract class AbstractStringCache extends AbstractExpireCache {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());

    /**
     * 缓存过期时间最大值(秒)
     */
    protected long maxTimeout = 60;

    /**
     * 缓存过期时间最小值
     */
    protected long minTimeout = 10;

    private static final String DEFAULT_SOURCE_NULL = "$DEFAULT_VALUE$";

    /**
     * 从缓存获取数据，如果不存在时，则同步数据源到缓存
     *
     * @param cacheKey
     * @return
     */
    public String getAndSyncIfAbsent(String cacheKey) {

        // 标记：当redis不可用时,是否继续访问数据库
        boolean accessDb = accessDbOnRedisError();

        // 1.先从缓存取数据，取到则返回
        String cacheValue = null;
        try {
            cacheValue = cacher.get(cacheKey);
            if (cacheValue != null) {
                return getRealValue(cacheValue);
            }
        } catch (Throwable e) {
            if (!accessDb) {
                // 当redis错误时，不继续查库
                throw e;
            }
        }

        // 2.取不到时，从数据源取数据，同时同步到缓存
        cacheValue = getSourceData(cacheKey);
        if (cacheValue == null) {
            // 2.1.预防穿透：若数据源没有数据，把空的数据也缓存起来，比如空字符串，空对象，空数组或list
            cacheValue = this.getSourceNull();
        }
        // 2.2.预防雪崩：同步数据源到缓存，同时设置待范围的过期时间，比如，过期时间范围为5~10分钟内的随机值
        long timeout = (long) (Math.random() * (maxTimeout - minTimeout) + minTimeout);
        try {
            cacher.set(cacheKey, cacheValue, timeout);
        } catch (Throwable e) {
            LOG.error("同步redis-string失败", e);
        }

        // 3.返回数据源取到的结果
        return getRealValue(cacheValue);
    }

    /**
     * 获取数据源
     *
     * @param cacheKey
     * @return
     */
    protected abstract String getSourceData(String cacheKey);

    /**
     * 当redis不可用时，是否直接访问数据库,建议存在二级缓存或者低频访问操作时才可设置true
     *
     * @return
     */
    protected boolean accessDbOnRedisError() {
        return false;
    }

    /**
     * 若数据源没有数据时，应缓存的空值
     *
     * @return
     */
    protected String getSourceNull() {
        return DEFAULT_SOURCE_NULL;
    }

    /**
     * 取实际值，转换空值标识后的值
     * @param cacheValue
     * @return
     */
    private String getRealValue(String cacheValue) {
        if (getSourceNull().equals(cacheValue)) {
            return null;
        }
        return cacheValue;
    }
}

