package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit;

import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitMqDto;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitResult;

/**
 * 手动业务转发处理器接口
 *
 * <AUTHOR>
 * @date 2024/10/28 下午5:56
 */
public interface IHandBizTransmitProcessor {
    /**
     * 执行业务
     *
     * @param dto 业务参数
     * @return 同步结果
     */
    HandBizTransmitResult execute(HandBizTransmitMqDto dto);
}
