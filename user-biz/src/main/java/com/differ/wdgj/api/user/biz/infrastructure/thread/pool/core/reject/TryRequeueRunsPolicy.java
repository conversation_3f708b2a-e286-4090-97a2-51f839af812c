package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject;


import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description 尝试重新入队的拒绝策略，尝试一分钟
 * <AUTHOR>
 * @Date 2021/11/24 16:25
 */
public class TryRequeueRunsPolicy extends AbstractThreadPoolRejectedPolicy {

    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        try {
            if (e.getQueue().offer(r, 60, TimeUnit.SECONDS)) {
                return;
            }
        } catch (InterruptedException ex) {
            throw new RejectedExecutionException(String.format("[线程池拒绝策略]尝试重新入队被中断,当前队列数：%d", e.getQueue().size()), ex);
        }
        throw new RejectedExecutionException(String.format("[线程池拒绝策略]尝试重新入队失败,当前队列数：%d", e.getQueue().size()));
    }
}
