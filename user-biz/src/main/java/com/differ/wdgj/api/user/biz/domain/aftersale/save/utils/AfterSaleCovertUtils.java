package com.differ.wdgj.api.user.biz.domain.aftersale.save.utils;

import com.differ.wdgj.api.component.util.tools.Md5Utils;
import com.differ.wdgj.api.component.util.tools.SpecialStringUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order.PolyOrderStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiEncryptTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.TradeEncryptTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * 售后单转换工具类
 *
 * <AUTHOR>
 * @date 2024/7/22 下午3:38
 */
public class AfterSaleCovertUtils {
    //region 变量
    /**
     * 日志
     */
    private static final Logger log = LoggerFactory.getLogger(AfterSaleCovertUtils.class);
    //endregion

    //region 构造
    private AfterSaleCovertUtils() {
    }
    //endregion

    //region 转换字段兼容逻辑

    /**
     * 转换api售后业务类型
     *
     * @param context   上下文
     * @param ployOrder 菠萝派售后单
     * @return api售后业务类型
     */
    @Deprecated
    public static ApiAfterSaleTypeEnum covertApiAfterSaleType(AfterSaleSaveContext context, BusinessGetRefundOrderResponseOrderItem ployOrder) {
        // 获取菠萝派返回售后类型
        PolyRefundTypeEnum polyRefundType = PolyRefundTypeEnum.create(ployOrder.getRefundType());
        if (polyRefundType == null) {
            return ployOrder.getHasGoodsReturn()
                    ? ApiAfterSaleTypeEnum.REFUND
                    : ApiAfterSaleTypeEnum.REFUND_PAY;
        }
        // 商品状态以及订单状态转换
        PolyRefundGoodsStatusEnum polyGoodsStatus = PolyRefundGoodsStatusEnum.create(ployOrder.getGoodsStatus());
        PolyOrderStatusEnum polyOrderStatus = PolyOrderStatusEnum.create(ployOrder.getOrderStatus());

        // 退货单
        if (polyRefundType == PolyRefundTypeEnum.JH_02) {
            return ApiAfterSaleTypeEnum.REFUND_GOODS;
        }
        // 退货退款单
        if (polyRefundType == PolyRefundTypeEnum.JH_04) {
            return ApiAfterSaleTypeEnum.REFUND;
        }
        // 仅退款
        if (polyRefundType == PolyRefundTypeEnum.JH_01 || polyRefundType == PolyRefundTypeEnum.JH_03) {
            if (polyOrderStatus != PolyOrderStatusEnum.OTHER) {
                // todo 是否发货判断，需要菠萝派评估
                // 1、原始单判断；2、菠萝派返回判断

                // 是否收货判断
                if (polyGoodsStatus != PolyRefundGoodsStatusEnum.OTHER) {
                    if (polyGoodsStatus == PolyRefundGoodsStatusEnum.BUYER_NOT_RECEIVED) {
                        // 在途仅退款
                        return ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT;
                    } else if (polyGoodsStatus == PolyRefundGoodsStatusEnum.BUYER_RECEIVED ||
                               polyGoodsStatus == PolyRefundGoodsStatusEnum.BUYER_RETURNED) {
                        // 已收货仅退款
                        return ApiAfterSaleTypeEnum.REFUND_PAY_RECEIVE;
                    }
                }
            }
            return ApiAfterSaleTypeEnum.REFUND_PAY;
        }
        // 价保售后单
        if (polyRefundType == PolyRefundTypeEnum.JH_10) {
            return ApiAfterSaleTypeEnum.REFUND_BJ;
        }
        // 补寄售后单
        if (polyRefundType == PolyRefundTypeEnum.JH_SUPPLEMENT_SEND) {
            return ApiAfterSaleTypeEnum.REFUND_SUPPLEMENT;
        }

        return null;
    }

    /**
     * 兼容转换退货/退款商品数量
     *
     * @param apiTradeGoods 原始的那货品
     * @param refundOrder   售后单
     * @param refundGoods   售后单货品
     * @return 结果
     */
    public static BigDecimal covertRefundGoodsCount(ApiTradeGoodsDO apiTradeGoods, BusinessGetRefundOrderResponseOrderItem refundOrder, BusinessGetRefundResponseRefundGoodInfo refundGoods) {
        BigDecimal refundGoodsCount = BigDecimal.ZERO;
        // 商品级退货数量计算
        if (refundGoods != null) {
            // 优先RefundProductNum，其次ProductNum
            int refundGoodsCountInt = refundGoods.getRefundProductNum() > 0 ? refundGoods.getRefundProductNum() : refundGoods.getProductNum();
            refundGoodsCount = BigDecimal.valueOf(refundGoodsCountInt);
        }
        // 若商品级为0，订单级退货数量计算
        if (refundGoodsCount.equals(BigDecimal.ZERO) && refundOrder != null) {
            int refundGoodsCountInt = refundOrder.getProductNum();
            refundGoodsCount = BigDecimal.valueOf(refundGoodsCountInt);
        }
        // 若订单级为0，使用原始单商品数量
        if (refundGoodsCount.equals(BigDecimal.ZERO) && apiTradeGoods != null) {
            refundGoodsCount = apiTradeGoods.getGoodsCount();
        }
        if (refundGoodsCount == null) {
            return BigDecimal.ZERO;
        }

        return refundGoodsCount;
    }

    /**
     * 兼容转换退货/退款原因
     *
     * @param refundOrder 售后单
     * @param refundGoods 售后单货品
     * @return 结果
     */
    public static String covertRefundReason(BusinessGetRefundOrderResponseOrderItem refundOrder, BusinessGetRefundResponseRefundGoodInfo refundGoods) {
        // 商品级退货/退款原因
        if (refundGoods != null) {
            // 优先商品级，其次订单级
            return StringUtils.defaultIfEmpty(refundGoods.getReason(), refundOrder.getReason());
        }
        // 订单级退货/退款原因
        if (refundOrder != null) {
            return refundOrder.getReason();
        }
        return StringUtils.EMPTY;
    }
    //endregion

    //region 脱敏

    /**
     * 格式化脱敏信息
     *
     * @param origin 脱敏信息
     * @param flag   标识
     * @return 格式化脱敏信息
     */
    public static String formatDesensitizationInfo(String origin, String flag) {
        if (StringUtils.isNotEmpty(origin)) {
            return String.format("~%s~/%s/~1~~", origin, flag);
        }
        return origin;
    }

    /**
     * 转换密文对象
     *
     * @param context            上下文
     * @param ployOrder          菠萝派售后单信息
     * @param targetEncryptTrade 密文对象
     */
    public static ApiEncryptTradeDO covertEncryptTrade(AfterSaleSaveContext context, BusinessGetExchangeOrderResponseOrderItem ployOrder, ApiEncryptTradeDO sourceEncryptTrade, ApiEncryptTradeDO targetEncryptTrade) {
        targetEncryptTrade = ObjectUtils.defaultIfNull(targetEncryptTrade, new ApiEncryptTradeDO());
        targetEncryptTrade.setShopId(context.getShopId());
        targetEncryptTrade.setTradeNo(ployOrder.getExchangeOrderNo());
        targetEncryptTrade.setMobile(ployOrder.getBuyerPhone());
        targetEncryptTrade.setReceiverName(ployOrder.getBuyerName());
        targetEncryptTrade.setProvince(ployOrder.getProvince());
        targetEncryptTrade.setCity(ployOrder.getCity());
        targetEncryptTrade.setArea(ployOrder.getArea());
        targetEncryptTrade.setTown(ployOrder.getTown());
        targetEncryptTrade.setPolyApiToken(context.getApiShopBaseInfo().getToken());
        targetEncryptTrade.setAddress(StringUtils.defaultIfEmpty(ployOrder.getBuyerAddress(), ployOrder.getAddress()));
        targetEncryptTrade.setType(TradeEncryptTypeEnum.EXCHANGE_ENCRYPT.getValue());
        // 历史数据主键
        if(sourceEncryptTrade != null){
            targetEncryptTrade.setId(sourceEncryptTrade.getId());
        }
        return targetEncryptTrade;
    }

    /**
     * 生成hashcode网名
     *
     * @param keys 原字符串列表
     * @return hashcode网名
     */
    public static String generateHashcodeNick(String... keys) {
        try {
            if (keys == null || keys.length == 0 || Arrays.stream(keys).allMatch(StringUtils::isEmpty)) {
                return StringUtils.EMPTY;
            }

            return Md5Utils.encryptHash(StringUtils.join(keys, "")).toString();
        } catch (Exception e) {
            log.error("生成hashcode网名失败", e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 获取密文数据的索引密文
     *
     * @param ciphertextStr  密文串
     * @param sep            分隔符
     * @param location       索引密文位置
     * @return 索引密文
     */
    public static String getCiphertextIndex(String ciphertextStr, String sep, int location) {
        // 如果密文为空，直接返回空
        if (StringUtils.isEmpty(ciphertextStr)) {
            return ciphertextStr;
        }

        // 是否存在分隔符，不存在直接返回原字符串
        if (!ciphertextStr.contains(sep)) {
            return ciphertextStr;
        }

        // 索引密文位置如果小于0，直接返回原密文字符串
        if (location < 0) {
            return ciphertextStr;
        }

        // 按照分隔符进行数据分割
        String[] arr = StringUtils.split(ciphertextStr, sep);

        // 如果分割的数组小于传入的索引位置，直接返回原密文串
        if (arr.length <= location) {
            return ciphertextStr;
        }

        return arr[location];
    }

    //endregion

    //region emoji表情字符
    /**
     * 将emoji表情字符替换为笑脸
     *
     * @param source     原始数据字符串
     * @return 替换后的字符串
     */
    public static String replaceEmoji(String source) {
        String replaceStr = "☺";
        return SpecialStringUtils.replaceEmoji(source, replaceStr);
    }
    //endregion
}
