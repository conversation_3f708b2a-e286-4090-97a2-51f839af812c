package com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubTask;

/**
 * 任务拆分策略
 *
 * <AUTHOR>
 * @date 2024/7/4 13:32
 */
public interface TaskSplitStrategy<S extends SubTask> {

    /**
     * 拆分下一个任务
     *
     * @param lastSubTask 上次的子任务
     * @param first 首次拆
     * @return
     */
    S splitNext(S lastSubTask,boolean first);
}
