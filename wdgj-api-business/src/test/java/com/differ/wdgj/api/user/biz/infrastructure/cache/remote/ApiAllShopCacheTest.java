package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiAllShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiAllShopDto;
import org.junit.Test;

/**
 * 获取会员级所有店铺-Redis缓存
 *
 * <AUTHOR>
 * @date 2024/12/5 下午3:17
 */
public class ApiAllShopCacheTest extends AbstractSpringTest {
    /**
     * 查询
     */
    @Test
    public void getDataTest(){
        ApiAllShopDto andSyncIfAbsent = ApiAllShopCache.singleton().getAndSyncIfAbsent("api2017");
    }

    /**
     * 清除
     */
    @Test
    public void clearTest(){
        ApiAllShopCache.singleton().clearCache("api2017");
    }
}
