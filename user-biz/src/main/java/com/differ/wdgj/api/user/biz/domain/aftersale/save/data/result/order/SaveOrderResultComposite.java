package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单级结果-保存售后单订单级结果组合
 *
 * <AUTHOR>
 * @date 2024-07-03 13:39
 */
public class SaveOrderResultComposite extends BaseAfterSaleOrderResult {
    //region 构造

    /**
     * 构造
     *
     * @param afterSaleNo 售后单号
     */
    public SaveOrderResultComposite(String afterSaleNo) {
        super(afterSaleNo, true, StringUtils.EMPTY);
    }
    //endregion

    //region 属性

    /**
     * hash校验结果
     */
    private CheckAfterSaleHashCodeResult checkAfterSaleHashCodeResult;

    /**
     * 查询历史订单数据结果
     */
    private QueryDbOrderResult getOldDbOrderResult;

    /**
     * 过滤/转换售后订单结果
     */
    private FilterAndConvertOrderResult convertOrderResult;

    /**
     * 保存售后单到db结果
     */
    private SaveOrderDataResult saveOrderDataResult;

    //endregion

    //region 公共方法

    /**
     * 聚合结果
     *
     * @return 保存结果描述
     */
    public SaveAfterSaleOrderResult convergedResults() {
        String afterSaleNo = getAfterSaleNo();
        boolean isNeedRetry = false;
        String message = StringUtils.EMPTY;
        AfterSaleProcessTypeEnum saveOrderToDbType = null;
        boolean success = true;
        WdgjRefundTypeEnum wdgjRefundType = null;

        // 1、hash校验结果
        if(checkAfterSaleHashCodeResult != null){
            message = checkAfterSaleHashCodeResult.getMessage();
            success = checkAfterSaleHashCodeResult.isSuccess();
        }

        // 2、历史数据查询结果
        if(getOldDbOrderResult != null){
            message = getOldDbOrderResult.getMessage();
            success = success && getOldDbOrderResult.isSuccess();
        }

        // 3、转换结果
        if (convertOrderResult != null) {
            message = convertOrderResult.getMessage();
            success = success && convertOrderResult.isSuccess();

            isNeedRetry = convertOrderResult.isNeedBizRetry();
            saveOrderToDbType = convertOrderResult.getProcessType();

            if(convertOrderResult.getTargetOrder() != null && convertOrderResult.getTargetOrder().getAfterSaleOrder() != null){
                ApiReturnListDO afterSaleOrder = convertOrderResult.getTargetOrder().getAfterSaleOrder();
                wdgjRefundType = WdgjRefundTypeEnum.create(String.valueOf(afterSaleOrder.getType()));
            }
        }

        // 4、保存结果
        if(saveOrderDataResult != null){
            message = saveOrderDataResult.getMessage();
            success = success && saveOrderDataResult.isSuccess();
        }

        // 5、配置键校验，是否需要重试
        if(!success && !isNeedRetry && StringUtils.isNotEmpty(message)){
            isNeedRetry = AfterSaleConfigKeyUtils.isNeedRetryMessage(message);
        }

        SaveAfterSaleOrderResult saveAfterSaleOrderResult = new SaveAfterSaleOrderResult();
        saveAfterSaleOrderResult.setAfterSaleNo(afterSaleNo);
        saveAfterSaleOrderResult.setSuccess(success);
        saveAfterSaleOrderResult.setMessage(message);
        saveAfterSaleOrderResult.setNeedRetry(isNeedRetry);
        saveAfterSaleOrderResult.setSaveOrderToDbType(saveOrderToDbType);
        saveAfterSaleOrderResult.setWdgjRefundType(wdgjRefundType);

        return saveAfterSaleOrderResult;
    }

    //endregion

    //region get/set

    public CheckAfterSaleHashCodeResult getCheckAfterSaleHashCodeResult() {
        return checkAfterSaleHashCodeResult;
    }

    public void setCheckAfterSaleHashCodeResult(CheckAfterSaleHashCodeResult checkAfterSaleHashCodeResult) {
        this.checkAfterSaleHashCodeResult = checkAfterSaleHashCodeResult;
    }

    public QueryDbOrderResult getGetOldDbOrderResult() {
        return getOldDbOrderResult;
    }

    public void setGetOldDbOrderResult(QueryDbOrderResult getOldDbOrderResult) {
        this.getOldDbOrderResult = getOldDbOrderResult;
    }

    public FilterAndConvertOrderResult getConvertOrderResult() {
        return convertOrderResult;
    }

    public void setConvertOrderResult(FilterAndConvertOrderResult convertOrderResult) {
        this.convertOrderResult = convertOrderResult;
    }

    public SaveOrderDataResult getSaveOrderDataResult() {
        return saveOrderDataResult;
    }

    public void setSaveOrderDataResult(SaveOrderDataResult saveOrderDataResult) {
        this.saveOrderDataResult = saveOrderDataResult;
    }

    //endregion
}
