package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen.strategy;

import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen.JmqAdapterPropertiesUtil;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen.MatchListenStrategy;

import java.util.ArrayList;
import java.util.List;

/**
 * 匹配以包含表达式的队列code
 *
 * <AUTHOR>
 * @date 2024/6/4 10:18
 */
public class ContainsMatchListenStrategy implements MatchListenStrategy {

    @Override
    public String[] matchCode(String expression) {
        ArrayList<String> matchList = new ArrayList<>();
        List<String> allCode = JmqAdapterPropertiesUtil.getAllCodes();
        for (String s : allCode) {
            if (s.contains(expression)) {
                matchList.add(s);
            }
        }
        return matchList.toArray(new String[0]);
    }
}
