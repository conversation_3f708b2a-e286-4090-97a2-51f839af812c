package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;

import java.util.List;

/**
 * 售后单下载到的订单
 *
 * <AUTHOR>
 * @date 2024/9/25 下午1:40
 */
public class AfterSaleLoadOrdersDto {
    /**
     * 菠萝派日志Id。
     */
    private String polyApiRequestId;

    /**
     * 退货退款单集合
     */
    private List<BusinessGetRefundOrderResponseOrderItem> refunds;

    /**
     * 换货单集合
     */
    private List<BusinessGetExchangeOrderResponseOrderItem> exchanges;


    //region get/set

    public String getPolyApiRequestId() {
        return polyApiRequestId;
    }

    public void setPolyApiRequestId(String polyApiRequestId) {
        this.polyApiRequestId = polyApiRequestId;
    }

    public List<BusinessGetRefundOrderResponseOrderItem> getRefunds() {
        return refunds;
    }

    public void setRefunds(List<BusinessGetRefundOrderResponseOrderItem> refunds) {
        this.refunds = refunds;
    }

    public List<BusinessGetExchangeOrderResponseOrderItem> getExchanges() {
        return exchanges;
    }

    public void setExchanges(List<BusinessGetExchangeOrderResponseOrderItem> exchanges) {
        this.exchanges = exchanges;
    }
    //endregion
}
