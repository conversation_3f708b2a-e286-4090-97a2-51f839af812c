package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * LoadAfterSaleWorkMutex 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午2:30
 */
public class LoadAfterSaleWorkMutexTest extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 测试消息推送不互斥场景
     * 当触发类型为MESSAGE_NOTIFICATION且加载类型为AFTER_SALE_NO时，直接创建任务
     */
    @Test
    public void testCreate_MessageNotificationWithAfterSaleNo_ShouldCreateDirectly() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        CreateResult expectedResult = CreateResult.successResult("test-task-id");
        
        // 模拟依赖行为
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "test-task-id", result.getTaskId());
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试消息推送但加载类型不是AFTER_SALE_NO的场景
     * 应该继续执行后续逻辑而不是直接创建
     */
    @Test
    public void testCreate_MessageNotificationWithOtherLoadType_ShouldContinue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        
        // 模拟配置值为0（不超时）
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("0");
            
            // 模拟父类方法返回null
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证没有直接调用create方法
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        }
    }

    /**
     * 测试非消息推送触发类型的场景
     * 应该继续执行后续逻辑
     */
    @Test
    public void testCreate_NonMessageNotification_ShouldContinue() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        
        // 模拟配置值为0（不超时）
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("0");
            
            // 模拟父类方法返回null
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证没有直接调用create方法
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        }
    }

    /**
     * 测试基类创建逻辑返回非空结果的场景
     * 当父类create方法返回非空结果时，应该直接返回该结果
     */
    @Test
    public void testCreate_SuperCreateReturnsResult_ShouldReturnSuperResult() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        
        // 模拟父类方法返回结果（存在未完成任务时返回null，不存在时创建任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 测试超时任务检测场景
     * 当配置了超时时间且存在超时未运行任务时，应该创建新任务
     */
    @Test
    public void testCreate_TimeoutTaskExists_ShouldCreateNewTask() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        CreateResult expectedResult = CreateResult.successResult("timeout-task-id");
        
        // 模拟配置值为30分钟超时
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("30");
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 模拟存在超时未运行任务
            Mockito.when(mockDataOperate.existsUnRunTimeout(workData, 30)).thenReturn(true);
            Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNotNull("结果不应为空", result);
            Assert.assertTrue("应该创建成功", result.success());
            Assert.assertEquals("任务ID应该匹配", "timeout-task-id", result.getTaskId());
            
            // 验证交互
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, 30);
            Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
        }
    }

    /**
     * 测试超时时间为0的场景
     * 当配置的超时时间为0时，不应该检查超时任务
     */
    @Test
    public void testCreate_TimeoutPeriodZero_ShouldNotCheckTimeout() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置值为0（不超时）
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("0");
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证没有检查超时任务
            Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        }
    }

    /**
     * 测试超时任务不存在的场景
     * 当配置了超时时间但不存在超时未运行任务时，应该返回null
     */
    @Test
    public void testCreate_NoTimeoutTask_ShouldReturnNull() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置值为30分钟超时
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("30");
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 模拟不存在超时未运行任务
            Mockito.when(mockDataOperate.existsUnRunTimeout(workData, 30)).thenReturn(false);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证交互
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, 30);
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        }
    }

    /**
     * 测试args为null的边界情况
     * 当AfterSaleLoadArgs为null时，应该跳过消息推送检查，继续执行后续逻辑
     */
    @Test
    public void testCreate_ArgsNull_ShouldSkipMessageCheck() {
        // 准备测试数据
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            "test-creator"
        );
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, null);
        
        // 模拟配置值为0（不超时）
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("0");
            
            // 模拟父类方法返回null
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证没有直接调用create方法
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        }
    }

    /**
     * 测试配置值为非数字字符串的场景
     * 当配置值无法解析为整数时，应该使用默认值0
     */
    @Test
    public void testCreate_InvalidTimeoutConfig_ShouldUseDefaultZero() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置值为无效字符串
        try (MockedStatic<ConfigKeyUtils> configMock = Mockito.mockStatic(ConfigKeyUtils.class)) {
            configMock.when(() -> ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD))
                     .thenReturn("invalid");
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("结果应为空", result);
            
            // 验证没有检查超时任务（因为默认值为0）
            Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        }
    }

    /**
     * 创建测试用的WorkData对象
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            triggerType, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);
        
        return WorkData.of(workContext, args);
    }
}
