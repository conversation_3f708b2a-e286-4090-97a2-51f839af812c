package com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.data.AfterSaleBasicInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.data.enums.AfterSaleTableTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IBasicsAfterSaleOperationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnDetailMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnDetailTwoMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnListMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后单基础操作实现
 *
 * <AUTHOR>
 * @date 2024/11/26 下午3:39
 */
public class BasicsAfterSaleOperationService implements IBasicsAfterSaleOperationService {
    //region 常量
    /**
     * 售后单仓储
     */
    private final ApiReturnListMapper afterSaleOrderMapper = BeanContextUtil.getBean(ApiReturnListMapper.class);

    /**
     * 售后单退货商品仓储
     */
    private final ApiReturnDetailMapper returnGoodsMapper = BeanContextUtil.getBean(ApiReturnDetailMapper.class);

    /**
     * 售后单换货商品仓储
     */
    private final ApiReturnDetailTwoMapper exchangeGoodsMapper = BeanContextUtil.getBean(ApiReturnDetailTwoMapper.class);
    //endregion

    /**
     * 查询售后单数据（包含his）
     *
     * @param memberName   会员名
     * @param shopId       店铺Id
     * @param afterSaleNos 售后单号
     * @return 售后单列表
     */
    @Override
    public List<AfterSaleBasicInfo> queryAfterSale(String memberName, int shopId, List<String> afterSaleNos) {
        List<AfterSaleBasicInfo> afterSaleBasicInfos = new ArrayList<>();
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            // 查询基础表信息
            List<ApiReturnListDO> apiReturnListDOS = afterSaleOrderMapper.selectByRefundId(shopId, afterSaleNos);
            if (CollectionUtils.isNotEmpty(apiReturnListDOS)) {
                List<Integer> billIds = apiReturnListDOS.stream().map(ApiReturnListDO::getBillId).collect(Collectors.toList());
                List<ApiReturnDetailDO> apiReturnDetailAlls = returnGoodsMapper.selectByBillIds(billIds);
                List<ApiReturnDetailTwoDO> apiReturnDetailTwoAlls = exchangeGoodsMapper.selectByBillIds(billIds);
                apiReturnListDOS.forEach(afterSale -> {
                    List<ApiReturnDetailDO> apiReturnDetails = apiReturnDetailAlls.stream().filter(x -> x.getBillId().equals(afterSale.getBillId())).collect(Collectors.toList());
                    List<ApiReturnDetailTwoDO> apiReturnDetailTwos = apiReturnDetailTwoAlls.stream().filter(x -> x.getBillId().equals(afterSale.getBillId())).collect(Collectors.toList());
                    afterSaleBasicInfos.add(new AfterSaleBasicInfo(afterSale, apiReturnDetails, apiReturnDetailTwos, AfterSaleTableTypeEnum.NORMAL));
                });
            }

            // 查询归档表信息
            List<ApiReturnListDO> apiReturnListHisDOS = afterSaleOrderMapper.selectHisByRefundId(shopId, afterSaleNos);
            if (CollectionUtils.isNotEmpty(apiReturnListHisDOS)) {
                List<Integer> billIds = apiReturnListHisDOS.stream().map(ApiReturnListDO::getBillId).collect(Collectors.toList());
                List<ApiReturnDetailDO> apiReturnHisDetailAlls = returnGoodsMapper.selectHisByBillIds(billIds);
                List<ApiReturnDetailTwoDO> apiReturnHisDetailTwoAlls = exchangeGoodsMapper.selectHisByBillIds(billIds);
                apiReturnListHisDOS.forEach(afterSale -> {
                    List<ApiReturnDetailDO> apiReturnHisDetails = apiReturnHisDetailAlls.stream().filter(x -> x.getBillId().equals(afterSale.getBillId())).collect(Collectors.toList());
                    List<ApiReturnDetailTwoDO> apiReturnHisDetailTwos = apiReturnHisDetailTwoAlls.stream().filter(x -> x.getBillId().equals(afterSale.getBillId())).collect(Collectors.toList());
                    afterSaleBasicInfos.add(new AfterSaleBasicInfo(afterSale, apiReturnHisDetails, apiReturnHisDetailTwos, AfterSaleTableTypeEnum.HISTORY));
                });
            }
        });

        return afterSaleBasicInfos;
    }

    /**
     * 批量保存（新增/更新）售后单
     * <p>依赖主键区分新增/更新</p>
     *
     * @param memberName      会员名
     * @param afterSaleOrders 售后单列表
     */
    @Override
    public void batchSaveOrder(String memberName, List<ApiReturnListDO> afterSaleOrders){
        // 区分新增/更新
        List<ApiReturnListDO> needInsertList = new ArrayList<>();
        List<ApiReturnListDO> needUpdateList = new ArrayList<>();
        afterSaleOrders.forEach(afterSaleOrder -> {
            if (afterSaleOrder.getBillId() > 0){
                needUpdateList.add(afterSaleOrder);
            }
            else {
                needInsertList.add(afterSaleOrder);
            }
        });

        // 拆分
        List<List<ApiReturnListDO>> subInsertLists = Lists.partition(needInsertList, 100);
        List<List<ApiReturnListDO>> subUpdateLists = Lists.partition(needUpdateList, 100);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<ApiReturnListDO> subInsertList : subInsertLists) {
                afterSaleOrderMapper.batchInsert(subInsertList);
            }
            for (List<ApiReturnListDO> subUpdateList : subUpdateLists){
                afterSaleOrderMapper.batchUpdate(subUpdateList);
            }
        });
    }

    /**
     * 批量保存（新增/更新）售后单退货商品
     * <p>依赖主键区分新增/更新</p>
     *
     * @param memberName  会员名
     * @param returnGoods 售后单退货商品列表
     */
    @Override
    public void batchSaveRefundGoods(String memberName, List<ApiReturnDetailDO> returnGoods){
        // 区分新增/更新
        List<ApiReturnDetailDO> needInsertList = new ArrayList<>();
        List<ApiReturnDetailDO> needUpdateList = new ArrayList<>();
        returnGoods.forEach(returnGood -> {
            if (returnGood.getRecId() != null && returnGood.getRecId() > 0){
                needUpdateList.add(returnGood);
            }
            else {
                needInsertList.add(returnGood);
            }
        });

        // 拆分
        List<List<ApiReturnDetailDO>> subInsertLists = Lists.partition(needInsertList, 100);
        List<List<ApiReturnDetailDO>> subUpdateLists = Lists.partition(needUpdateList, 100);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<ApiReturnDetailDO> subInsertList : subInsertLists) {
                returnGoodsMapper.batchInsert(subInsertList);
            }
            for (List<ApiReturnDetailDO> subUpdateList : subUpdateLists){
                returnGoodsMapper.batchUpdate(subUpdateList);
            }
        });
    }

    /**
     * 批量删除售后单退货商品
     *
     * @param memberName           会员名
     * @param deleteReturnGoodsIds 主键列表
     */
    @Override
    public void batchDeleteRefundGoods(String memberName, List<Integer> deleteReturnGoodsIds){
        // 拆分
        List<List<Integer>> subDeleteIdLists = Lists.partition(deleteReturnGoodsIds, 200);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<Integer> subDeleteIdList : subDeleteIdLists) {
                returnGoodsMapper.deleteByRecIds(subDeleteIdList);
            }
        });
    }

    /**
     * 批量保存（新增/更新）售后单换货商品
     * <p>依赖主键区分新增/更新</p>
     *
     * @param memberName    会员名
     * @param exchangeGoods 售后单退货商品列表
     */
    @Override
    public void batchSaveExchangeGoods(String memberName, List<ApiReturnDetailTwoDO> exchangeGoods){
        // 区分新增/更新
        List<ApiReturnDetailTwoDO> needInsertList = new ArrayList<>();
        List<ApiReturnDetailTwoDO> needUpdateList = new ArrayList<>();
        exchangeGoods.forEach(exchangeGood -> {
            if (exchangeGood.getRecId() != null && exchangeGood.getRecId() > 0){
                needUpdateList.add(exchangeGood);
            }
            else {
                needInsertList.add(exchangeGood);
            }
        });

        // 拆分
        List<List<ApiReturnDetailTwoDO>> subInsertLists = Lists.partition(needInsertList, 100);
        List<List<ApiReturnDetailTwoDO>> subUpdateLists = Lists.partition(needUpdateList, 100);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<ApiReturnDetailTwoDO> subInsertList : subInsertLists) {
                exchangeGoodsMapper.batchInsert(subInsertList);
            }
            for (List<ApiReturnDetailTwoDO> subUpdateList : subUpdateLists){
                exchangeGoodsMapper.batchUpdate(subUpdateList);
            }
        });
    }

    /**
     * 批量删除售后单换货商品
     *
     * @param memberName             会员名
     * @param deleteExchangeGoodsIds 主键列表
     */
    @Override
    public void batchDeleteExchangeGoods(String memberName, List<Integer> deleteExchangeGoodsIds){
        // 拆分
        List<List<Integer>> subDeleteIdLists = Lists.partition(deleteExchangeGoodsIds, 200);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<Integer> subDeleteIdList : subDeleteIdLists) {
                exchangeGoodsMapper.deleteByRecIds(subDeleteIdList);
            }
        });
    }
}
