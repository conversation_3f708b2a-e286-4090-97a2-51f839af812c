package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation;

import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchExtResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.utils.StockResultUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;

import java.util.Map;

/**
 * 库存同步响应操作
 *
 * <AUTHOR>
 * @date 2024-03-13 15:45
 */
public class StockSyncResponseOperation {

    // region 构造方法

    /**
     * 构造方法
     *
     * @param context            上下文
     * @param stockSyncProcessor 库存同步处理器
     */
    public StockSyncResponseOperation(StockSyncContext context, BaseSyncStockProcessor stockSyncProcessor) {
        this.context = context;
        this.stockSyncProcessor = stockSyncProcessor;
    }

    // endregion

    // region 变量

    /**
     * 上下文
     */
    protected final StockSyncContext context;

    /**
     * 库存同步处理器
     */
    protected final BaseSyncStockProcessor stockSyncProcessor;

    // endregion

    /**
     * 构建库存同步结果
     * @param responseComposites 库存同步结果组合列表
     */
    public void buildStockSyncResult(Map<MatchIdEnhance, StockSyncResultComposite> responseComposites){
        // 构建禁止库存同步（生成请求时禁止）结果

        // 构建菠萝派库存同步结果
        buildPloyStockSyncResult(responseComposites);
    }

    /**
     * 构建菠萝派库存同步结果
     * @param responseComposites 库存同步结果组合列表
     */
    private void buildPloyStockSyncResult(Map<MatchIdEnhance, StockSyncResultComposite> responseComposites){
        responseComposites.forEach((idEnhance, resultComposite) ->{
            // 构建匹配级库存同步结果
            StockSyncApiSysMatchResult apiSysMatchResult = StockResultUtils.convertApiSysMatchResult(context, resultComposite);
            // 构建匹配扩展级库存同步结果
            StockSyncApiSysMatchExtResult apiSysMatchExtResult = null;
            if(StockResultUtils.isNeedClearFlagMatch(resultComposite)){
                apiSysMatchExtResult = StockSyncApiSysMatchExtResult.createDefault(idEnhance.getMatchId());
            }
            // 构建库存同步日志
            ApiPlatSysHisDO stockSyncLog = StockResultUtils.convertApiPlatSysHisDO(context, resultComposite);
            // 更新库存同步结果数据
            resultComposite.setApiSysMatchResult(apiSysMatchResult);
            resultComposite.setApiSysMatchExtResult(apiSysMatchExtResult);
            resultComposite.setStockSyncLog(stockSyncLog);

            // 平台级特殊处理
            stockSyncProcessor.specialProcessSyncResults(resultComposite);
        });
    }
}
