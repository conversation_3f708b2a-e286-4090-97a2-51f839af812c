package com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.*;

/**
 * 平台业务特性配置模式枚举
 *
 * <AUTHOR>
 * @date 2024-06-20 18:50
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum PlatBizFeatureConfigModeEnum  implements ValueEnum, NameEnum {
    /**
     * 默认
     */
    DEFAULT("默认", 0),

    /**
     * 通用
     */
    NORMAL("通用", 1),

    /**
     * 定制
     */
    CUSTOMIZATION("定制", 2);

    // region 属性

    /**
     * 分类名称
     */
    private final String name;
    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造方法
     *
     * @param name  名称
     * @param value 值
     */
    PlatBizFeatureConfigModeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 获取名称
     *
     * @return 名称
     */
    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 获取类型
     *
     * @return 类型
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static PlatBizFeatureConfigModeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, PlatBizFeatureConfigModeEnum.class, EnumConvertType.VALUE);
    }
}
