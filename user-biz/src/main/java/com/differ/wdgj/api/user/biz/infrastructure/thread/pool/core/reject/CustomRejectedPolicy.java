package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject;

import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.CustomRejectRunnable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description 自定义线程池拒绝策略
 * <AUTHOR>
 * @Date 2023/4/6 13:56
 */
public class CustomRejectedPolicy implements RejectedExecutionHandler {

    protected final Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        if (r instanceof CustomRejectRunnable) {
            CustomRejectRunnable reject = (CustomRejectRunnable) r;
            reject.getReject().rejectedExecution(r, executor);
            return;
        }
        LOG.error(String.format("未指定自定义线程池拒绝策略,任务:%s", r.toString()));
        throw new RejectedExecutionException("未指定自定义线程池拒绝策略");
    }
}
