package com.differ.wdgj.api.user.biz.domain.apicall;


import com.differ.jackyun.framework.component.utils.basic.enums.ErrorCodes;
import com.differ.wdgj.api.component.util.type.GenericTypeUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.adapter.PlatAuthorizeAdapter;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallPlatAuthorize;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.filter.ApiCallFilterChain;
import com.differ.wdgj.api.user.biz.domain.apicall.filter.ApiCallFilterRegister;
import com.differ.wdgj.api.user.biz.domain.apicall.filter.IApiCallFilter;
import com.differ.wdgj.api.user.biz.domain.apicall.processor.IApiCallProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.processor.http.HttpApiCallProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.utils.ApiCallConstantUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.utils.ApiCallLogUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;

import java.util.List;

/**
 * API调用抽象类
 *
 * <AUTHOR>
 * @date 2022-03-07 13:27
 */
public abstract class AbstractApiCall<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData>
        implements IApiCall<RequestBizData, ResponseBizData> {
    //region 常量
    /**
     * 返回结果类型 - 泛型ResponseBizData
     */
    private final Class<ResponseBizData> responseClass;
    //endregion

    //region 构造
    protected AbstractApiCall() {
        Class<?> clazz = AopUtils.getTargetClass(this);
        this.responseClass = (Class<ResponseBizData>) GenericTypeUtil.getGenericDataType(clazz, 1);
    }
    //endregion

    // region 实现接口方法

    /**
     * 店铺级请求
     *
     * @param memberName     会员名
     * @param shopId         店铺Id
     * @param requestBizData 请求业务数据
     * @return 响应
     */
    @Override
    public ApiCallResponse<ResponseBizData> apiCall(String memberName, int shopId, RequestBizData requestBizData) {
        // 封装上下文
        ApiCallContext context = this.shopFormatContext(memberName, shopId);
        // 发起请求
        return this.call(context, requestBizData);
    }

    /**
     * 请求
     *
     * @param context        上下文
     * @param requestBizData 请求业务数据
     * @return 响应
     */
    @Override
    public ApiCallResponse<ResponseBizData> apiCall(ApiCallContext context, RequestBizData requestBizData) {
        // 发起请求
        return this.call(context, requestBizData);
    }

    // endregion

    // region 公共方法

    /**
     * 执行请求
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param callExtendedInfo 请求扩展数据
     * @return 响应
     */
    public ApiCallResponse<ResponseBizData> onRequest(ApiCallContext context, RequestBizData requestBizData, ApiCallExtendedInfo callExtendedInfo) {
        // 创建实例
        IApiCallProcessor<RequestBizData, ResponseBizData> instance = this.createInstance(context);

        // 执行请求
        return instance.call(context, requestBizData, responseClass, callExtendedInfo);
    }

    // endregion

    // region 子类重写方法

    /**
     * 获取接口类型
     *
     * @return 接口类型
     */
    protected abstract PolyAPITypeEnum getApiType();

    /**
     * 请求前执行
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param callExtendedInfo 请求扩展数据
     */
    protected void onBeforeRequest(ApiCallContext context, RequestBizData requestBizData, ApiCallExtendedInfo callExtendedInfo) {

    }

    /**
     * 请求后执行
     *
     * @param context        上下文
     * @param requestBizData 请求业务数据
     * @param response       响应
     */
    protected void onAfterRequest(ApiCallContext context, RequestBizData requestBizData, ApiCallResponse<ResponseBizData> response) {

    }

    /**
     * 是否需要重试
     *
     * @param context        上下文
     * @param requestBizData 请求业务数据
     * @param response       响应
     * @return 是否重试
     */
    protected boolean needRetry(ApiCallContext context, RequestBizData requestBizData, ApiCallResponse<ResponseBizData> response) {
        ErrorCodes errorCodes = ErrorCodes.create(response.getSubCode());
        return ErrorCodes.POLYAPINETWORK.equals(errorCodes);
    }

    // endregion

    // region 私有方法

    /**
     * 请求
     *
     * @param context        上下文
     * @param requestBizData 请求业务数据
     * @return 响应
     */
    private ApiCallResponse<ResponseBizData> call(ApiCallContext context, RequestBizData requestBizData) {
        // 执行请求
        ApiCallResponse<ResponseBizData> response = this.doCall(context, requestBizData);

        // 请求成功，直接返回
        if (ApiCallConstantUtils.POLY_SUCCESS_CODE.equals(response.getCode())) {
            return response;
        }

        // 请求失败，但不需要重试，直接返回
        if (!this.needRetry(context, requestBizData, response)) {
            return response;
        }

        // 执行重试
        if (!ConfigKeyUtils.isActionApiBoolean(ConfigKeyEnum.ISCLOSE_POLYAPI_Retry)) {
            response = this.doRetry(context, requestBizData);
        }

        return response;
    }

    /**
     * 店铺级封装上下文
     *
     * @param memberName 会员名
     * @param shopId     店铺Id
     * @return 上下文
     */
    protected ApiCallContext shopFormatContext(String memberName, int shopId) {
        // 上下文
        ApiCallContext context = new ApiCallContext();
        context.setMemberName(memberName);
        context.setApiType(this.getApiType());
        context.setShopId(shopId);

        // 查询店铺基础信息
        ApiShopBaseDto apiShopBaseDto = ShopInfoUtils.singleByOutShopId(memberName, shopId);
        if (apiShopBaseDto != null) {
            context.setPlat(apiShopBaseDto.getPlat());
            context.setShopToken(apiShopBaseDto.getToken());
        }

        // AppKey & AppSecret
        PlatAuthorizeAdapter platAuthorizeAdapter = new PlatAuthorizeAdapter();
        ApiCallPlatAuthorize platAuthorize = platAuthorizeAdapter.getPlatAuthorize(context.getApiType(), context.getPlat(), context.getMemberName(), context.getShopId());
        if (platAuthorize != null) {
            context.setAppKey(platAuthorize.getAppKey());
            context.setAppSecret(platAuthorize.getAppSecret());
            // 菠萝派商城逻辑兼容
            if(StringUtils.isNotEmpty(platAuthorize.getShopToken())){
                context.setShopToken(platAuthorize.getShopToken());
            }
        }
        return context;
    }

    /**
     * 执行请求
     *
     * @param context        上下文
     * @param requestBizData 请求业务数据
     * @return 响应
     */
    protected ApiCallResponse<ResponseBizData> doCall(ApiCallContext context, RequestBizData requestBizData) {
        // 请求扩展数据
        ApiCallExtendedInfo callExtendedInfo = new ApiCallExtendedInfo();
        // 请求前执行
        this.onBeforeRequest(context, requestBizData, callExtendedInfo);

        // 响应
        ApiCallResponse<ResponseBizData> response = new ApiCallResponse<>();

        // 获取已注册的过滤器
        List<IApiCallFilter<RequestBizData, ResponseBizData>> filters = new ApiCallFilterRegister<RequestBizData, ResponseBizData>().getFilters();

        // 责任链调用
        if (CollectionUtils.isNotEmpty(filters)) {
            ApiCallFilterChain<RequestBizData, ResponseBizData> filterChain = new ApiCallFilterChain<>(filters, this);
            filterChain.doFilter(context, requestBizData, callExtendedInfo, response, filterChain);
        }
        // 直接调用
        else {
            response = this.onRequest(context, requestBizData, callExtendedInfo);
        }

        // 请求后执行
        this.onAfterRequest(context, requestBizData, response);

        return response;
    }

    /**
     * 执行重试
     *
     * @param context        上下文
     * @param requestBizData 请求业务数据
     * @return 响应
     */
    protected ApiCallResponse<ResponseBizData> doRetry(ApiCallContext context, RequestBizData requestBizData) {
        ApiCallResponse<ResponseBizData> response = null;

        int retryTime = 0;
        for (int i = 0; i < 3; i++) {

            // 暂停以继续重试
            try {
                Thread.sleep(500 * (retryTime + 1));
            } catch (InterruptedException e) {
                ApiCallLogUtils.writeErrorLog(context, "请求菠萝派接口重试时异常", e);
            }

            // 执行请求
            response = this.doCall(context, requestBizData);

            // 请求成功，直接返回
            if (ApiCallConstantUtils.POLY_SUCCESS_CODE.equals(response.getCode())) {
                return response;
            }

            // 请求失败，但不需要重试或达到最大重试次数，直接返回
            if (!this.needRetry(context, requestBizData, response)) {
                return response;
            }

            // 重试次数+1
            retryTime++;

            // 当发生重试时记录日志
            String logContent = String.format("请求菠萝派接口(%s)发生重试，当前第%s次重试", this.getClass().getSimpleName(), (i + 1));
            ApiCallLogUtils.writeErrorLog(context, logContent, null);
        }

        return response;
    }

    /**
     * 创建实例
     *
     * @param context 上下文
     * @return 实例
     */
    private IApiCallProcessor<RequestBizData, ResponseBizData> createInstance(ApiCallContext context) {
        // 判断接口调用是否使用feign调用
        // if (YunConfigUtils.enableFeignApiCall(context.getPlat().toString(), context.getMemberName(), Integer.toString(context.getApiType().getOpenAPIID()))) {
        //     processor = SpringResolveManager.resolve(FeignApiCallProcessor.class);
        // }

        return new HttpApiCallProcessor<>();
    }

    // endregion
}
