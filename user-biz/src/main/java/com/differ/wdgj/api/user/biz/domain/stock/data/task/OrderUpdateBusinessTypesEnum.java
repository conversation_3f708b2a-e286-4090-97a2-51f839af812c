package com.differ.wdgj.api.user.biz.domain.stock.data.task;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 订单更新业务类型
 * 对应表字段g_api_tradeBusinessUpdate字段businessType
 *
 * <AUTHOR>
 * @date 2024-03-20 19:54
 */
public enum OrderUpdateBusinessTypesEnum implements ValueEnum {
    /**
     * 抖音BIC
     */
    Bic(0),

    /**
     * 米家跨境订单
     */
    MiJiaCrossBorderOrder(1),

    /**
     * 拼多多延迟发货
     */
    PddDelayShipping(2),

    /**
     * 自动确认订单
     */
    AutoConfirmOrder(3),

    /**
     * 多包裹上传
     */
    UploadMultiPackage(4),

    /**
     * 淘宝更新活动商品时间
     */
    TaobaoUpdateActivityGoodsTime(5),

    /**
     * 京东计算订单金额
     */
    JdCalcOrderMoney(1),

    ;

    /**
     * 构造
     * @param value 枚举值
     */
    OrderUpdateBusinessTypesEnum(Integer value){
        this.value = value;
    }

    /**
     * 枚举值 - 数据库存储
     */
    private final Integer value;

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }
}
