package com.differ.wdgj.api.component.wait.data;

/**
 * <AUTHOR>
 * @description ：查询排队和执行中总数
 * @date ：2022-11-30 10:25
 */
public class WaitAndExecSumQueryResponseBizData {
    /**
     * 排队数量
     */
    private Long waitSum;
    /**
     * 执行中数量
     */
    private Long execSum;

    public Long getWaitSum () {
        return waitSum;
    }

    public void setWaitSum (Long waitSum) {
        this.waitSum = waitSum;
    }

    public Long getExecSum () {
        return execSum;
    }

    public void setExecSum (Long execSum) {
        this.execSum = execSum;
    }

}
