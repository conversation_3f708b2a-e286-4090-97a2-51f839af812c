package com.differ.wdgj.api.component.redis;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.BooleanSupplier;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 支持redis多库的缓存组件
 * <AUTHOR>
 * @date 2020/12/14 15:53
 */
public interface MultiRedis extends com.differ.wdgj.api.component.redis.HashExpireOperate, com.differ.wdgj.api.component.redis.ListOperate {
    /**
     * 根据缓存键获取RedisTemplate
     *
     * @param cacheKey 缓存键
     * @return StringRedisTemplate
     */
    StringRedisTemplate getRedisTemplate(String cacheKey);

    /**
     * 根据缓存类型获取RedisTemplate
     *
     * @param cacheType 缓存类型
     * @return StringRedisTemplate
     */
    StringRedisTemplate getRedisTemplateByCacheType(String cacheType);

    /**
     * 不存在则设置
     *
     * @param cacheKey
     * @param cacheValue
     * @param expireMilliSecond 毫秒
     * @return
     */
    boolean setIfAbsent(String cacheKey, String cacheValue, long expireMilliSecond);

    /**
     * 不存在则设置
     *
     * @param cacheKey
     * @param cacheValue
     * @param expireSecond 秒
     * @return
     */
    boolean setIfAbsentOnSecond(String cacheKey, String cacheValue, int expireSecond);

    /**
     * 获取缓存
     *
     * @param cacheKey 缓存键
     */
    String get(String cacheKey);

    /**
     * 设置缓存
     *
     * @param cacheKey 缓存键
     * @param value    缓存值
     * @
     */
    void set(String cacheKey, String value);

    /**
     * 设置缓存
     *
     * @param cacheKey 缓存键
     * @param value    缓存值
     * @param timeout  过期时间(秒)
     * @
     */
    void set(String cacheKey, String value, long timeout);

    /**
     * 获取缓存
     *
     * @param cacheKey 缓存键
     * @return 缓存值 @
     */
    String getOnSingleRedis(String cacheKey);

    /**
     * 批量获取缓存(如果key不存在会返回null项),Redis库取第一个缓存键所在的Redis。
     *
     * @param cacheKeys 缓存键集
     * @return 缓存值集
     */
    List<String> getOnSingleRedis(String... cacheKeys);

    /**
     * 判断是否具有某个缓存键
     *
     * @param cacheKey 缓存键
     * @return 是否具有某个缓存键 @
     */
    boolean has(String cacheKey);

    /**
     * 删除缓存
     *
     * @param cacheKey 缓存键
     */
    void delete(String cacheKey);

    /**
     * 批量删除缓存,Redis库取第一个缓存键所在的Redis
     *
     * @param cacheKeys 缓存键集合
     */
    void deleteOnSingleRedis(String... cacheKeys);

    /**
     * 从当前缓存实例获取所有缓存键。
     *
     * @param cacheType 缓存类别
     * @param pattern   匹配模式
     * @return 缓存值
     */
    @Deprecated
    Set<String> getKeys(String cacheType, String pattern);

    // region　hash操作

    /**
     * 缓存同步字符串。
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @param value    缓存值
     * @return 同步成功数量
     */
    int hashSyncStrValue(String cacheKey, String hashKey, String value);

    /**
     * 缓存批量同步字符串。
     *
     * @param cacheKey 缓存键
     * @param values   值集合
     * @return 同步成功数量
     */
    int hashSyncStrValues(String cacheKey, Map<String, String> values);

    /**
     * 批量同步Hash缓存(只有当缓存不存在或DataVersion不一致才同步)
     *
     * @param cacheKey 缓存键
     * @param values   值集合
     * @return 缓存是否添加成功
     */
    <T extends com.differ.wdgj.api.component.redis.ICacheDataItem> int hashSyncByCompareDataVersion(String cacheKey, List<T> values);

    /**
     * 批量同步Hash缓存(不管缓存不存在)
     *
     * @param cacheKey 缓存键
     * @param values   值集合
     * @return 缓存同步成功数量
     */
    <T> int hashSync(String cacheKey, Map<String, T> values);

    /**
     * 批量移除Hash缓存多个项。
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 移除是否成功
     */
    Boolean hashRemove(String cacheKey, String... hashKeys);

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @return 值
     */
    String hashGetStrValue(String cacheKey, String hashKey);

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 值集合
     */
    Map<String, String> hashGetStrValues(String cacheKey, List<String> hashKeys);

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 值集合
     */
    List<Object> hashGetListStrValues(String cacheKey, List<String> hashKeys);

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @return 值
     */
    <T> T hashGet(String cacheKey, String hashKey, Class<T> type);

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 值集合
     */
    <T> List<T> hashGet(String cacheKey, List<String> hashKeys, Class<T> type);

    /**
     * 获取整个Hash缓存,使用 hashScanAll 代替
     *
     * @param cacheKey 缓存键
     * @return 值集合
     */
    @Deprecated
    <T> Map<String, T> hashGetAll(String cacheKey, Class<T> type);

    /**
     * 分页获扫描某个Hash缓存下所有字段,并且处理
     *
     * @param cacheKey 缓存键
     * @param pageSize 分页大小，每页scan的数据量
     * @param handler  hash的key,value的处理
     */
    void hashScanAll(String cacheKey, int pageSize, BiConsumer<String, String> handler);

    /**
     * 分页获扫描某个Hash缓存下所有字段,并且处理
     *
     * @param cacheKey     缓存键
     * @param matchPattern 过滤扫描结果
     * @param pageSize     分页大小，每页scan的数据量
     * @param handler      hash的key,value的处理
     */
    void hashScanAll(String cacheKey, String matchPattern, int pageSize, BiConsumer<String, String> handler);

    /**
     * 根据缓存键scan
     *
     * @param cacheKey 缓存键
     * @param pageSize 分页大小
     * @param type     值类型
     * @param handler  hash的key,value的批量处理
     */
    <T> void hashScanAll(String cacheKey, String matchPattern, int pageSize, Class<T> type, Consumer<Map<String, T>> handler);

    /**
     * 获取Hash集合数量
     *
     * @param cacheKey 缓存键
     * @return 值集合数量
     */
    Long hashCount(String cacheKey);

    /**
     * 获取Hash集合的键集合
     *
     * @param cacheKey 缓存键
     * @return 键集合
     */
    Set<String> hashAllKeys(String cacheKey);

    /**
     * 判断Hash键是否存在
     *
     * @param cacheKey 缓存键
     * @param hashKey  hash键
     * @return 键集合
     */
    Boolean hashExist(String cacheKey, String hashKey);

    //endregion

    // region SortedSet缓存

    /**
     * SortedSet缓存删除
     *
     * @param cacheKey   缓存键
     * @param startScore score(超过17位会丢失精度，请慎重使用)
     * @param endScore   score(超过17位会丢失精度，请慎重使用)
     * @return 删除的个数
     */
    long sortedSetRemove(String cacheKey, double startScore, double endScore);

    /**
     * SortedSet缓存同步(SortedSet数据值重复会覆盖)。。
     *
     * @param cacheKey        缓存键
     * @param score           score(超过17位会丢失精度，请慎重使用)
     * @param value           值(唯一)
     * @param isRemoveByScore 是否根据Score删除旧数据
     * @param expireSeconds   过期时间(秒)，大于0则设置过期时间；否则为不过期
     * @return 同步是否成功
     */
    <T> Boolean sortedSetSync(String cacheKey, double score, T value, boolean isRemoveByScore, int expireSeconds);

    /**
     * 批量SortedSet缓存同步(SortedSet数据值重复会覆盖)。
     *
     * @param cacheKey        缓存键
     * @param scoreValues     值和score的键值对
     * @param isRemoveByScore 是否根据Score删除旧数据
     * @param expireSeconds   过期时间(秒)，大于0则设置过期时间；否则为不过期
     * @return 同步是否成功
     */
    <T> Boolean sortedSetSync(String cacheKey, Map<T, Double> scoreValues, boolean isRemoveByScore, int expireSeconds);

    /**
     * 根据Score判断SortedSet缓存是否存在
     *
     * @param cacheKey   缓存键
     * @param startScore 起始score(-1/0)
     * @param endScore   截止score(1/0)
     * @return 是否存在
     */
    Boolean sortedSetExist(String cacheKey, double startScore, double endScore);

    /**
     * 根据Score获取SortedSet缓存
     *
     * @param cacheKey   缓存键
     * @param startScore 起始score(-1/0)
     * @param endScore   截止score(1/0)
     * @return SortedSet缓存
     */
    <T> Set<T> sortedSetGet(String cacheKey, double startScore, double endScore, Class<T> type);

    /**
     * 根据index获取SortedSet缓存
     *
     * @param cacheKey   缓存键
     * @param startIndex 起始位置
     * @param endIndex   截止位置   (-1为最后一个，-2为倒数第二个...)
     * @return SortedSet缓存
     */
    <T> Set<T> sortedSetGetByIndex(String cacheKey, long startIndex, long endIndex, Class<T> type);

    /**
     * 获取长度
     *
     * @param cacheKey 缓存键
     * @return SortedSet缓存
     */
    long sortedSetGetLength(String cacheKey);

    /**
     * 根据值获取分数
     *
     * @param cacheKey 缓存键
     * @param value    值
     * @param <T>      泛型
     * @return 结果
     */
    <T> double sortedSetGetScore(String cacheKey, T value);

    //endregion

    // region hash统计

    /**
     * 统计自增。
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @param value    计数
     * @return 当前计数
     */
    long hashIncrement(String cacheKey, String hashKey, long value);

    /**
     * 获取统计。
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @return 当前计数
     */
    long hashGetIncrement(String cacheKey, String hashKey);

    /**
     * 获取指定缓存键下所有的统计。
     *
     * @param cacheKey 缓存键
     * @return 所有计数
     */
    Map<String, Long> hashGetAllIncrement(String cacheKey);

    /**
     * 批量删除统计。
     *
     * @param cacheKey 缓存键
     * @return 所有计数
     */
    void hashRemoveIncrement(String cacheKey, String... hashKeys);

    //endregion

    /**
     * 设置缓存键超时时间(单位:秒)
     *
     * @param cacheKey 缓存键
     * @param timeout  过期时间(秒)
     * @return 是否成功设置 1 成功；0 失败
     */
    boolean setExpire(String cacheKey, int timeout);

    /**
     * 获取缓存键过期时间（单位秒）
     *
     * @param cacheKey 缓存键
     * @return 过期时间（小于 0 说明没有过期时间或缓存键不存在）
     */
    Long getExpire(String cacheKey);

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey 锁定键(根据业务确定唯一)
     * @param funExec 执行的业务方法
     * @return 锁定成功且执行funExec的结果
     */
    boolean tryLock(String lockKey, BooleanSupplier funExec);

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey    锁定键(根据业务确定唯一)
     * @param expireTime 分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @param funExec    执行的业务方法
     * @return 锁定成功且执行funExec的结果
     */
    boolean tryLock(String lockKey, int expireTime, BooleanSupplier funExec);

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey     锁定键(根据业务确定唯一)
     * @param lockTimeout 锁定的默认超时时间（单位毫秒，默认20秒）
     * @param expireTime  分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @param funExec     执行的业务方法
     * @return 锁定成功且执行funExec的结果
     */
    boolean tryLock(String lockKey, long lockTimeout, int expireTime, BooleanSupplier funExec);

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey     锁定键(根据业务确定唯一)
     * @param lockTimeout 锁定的默认超时时间（单位毫秒，默认20秒）
     * @param expireTime  分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @param funExec     执行的业务方法
     * @return 锁定成功且执行funExec的结果
     */
    <R> R tryLock(String lockKey, long lockTimeout, int expireTime, Supplier<R> funExec);

    /**
     * 删除存在key,value相同的键
     *
     * @param cacheKey
     * @param value    唯一标识
     * @return
     * 释放锁
     */
    boolean deleteIfValueEquals(String cacheKey, String value);

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param cacheKey
     * @param script   lua脚本
     * @param keys     缓存键参数
     * @param args     其他参数在后
     * @return 脚本执行结果
     */
    Object executeScript(String cacheKey, RedisScript script, final List<String> keys, final List<String> args);

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param script lua脚本
     * @param keys   缓存键参数
     * @param args   其他参数在后
     * @return 脚本执行结果
     */
    Object executeScript(RedisScript script, final List<String> keys, final List<String> args);

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param script lua脚本
     * @param keys   缓存键参数
     * @return 脚本执行结果
     */
    Object executeScript(RedisScript script, final List<String> keys);

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param cacheKey
     * @param script   lua脚本
     * @param keyCount 缓存键参数个数
     * @param params   参数集合，缓存键在前，其他参数在后，并且跟脚本顺序对应
     * @return 脚本执行结果
     */
    Object executeScript(String cacheKey, RedisScript script, final int keyCount, final String... params);
}
