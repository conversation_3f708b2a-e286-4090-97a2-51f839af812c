package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.kafka;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueueEnum;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;

import java.util.Arrays;

/**
 * kafka主体枚举
 *
 * <AUTHOR>
 * @date 2024/4/10 10:35
 */
public enum KafkaTopicEnum {

    COMMON("wdgj-api-common", ConfigTemplate.BEAN_API_COMMON_KAFKAPROPERTIES_TEMPLATE, SubQueueEnum.KAFKA, "api.multi.demo", "api.multi.demodelay", "api.multi.demoretry"),
    ;

    /**
     * 主题
     */
    private String topic;
    /**
     * 配置属性模板的bean名
     */
    private String templatePropertiesBean;
    /**
     * 子队列枚举
     */
    private SubQueueEnum subQueueEnum;

    /**
     * 多个code的对应topic
     */
    private String[] multiCodes;

    KafkaTopicEnum(String topic, String templatePropertiesBean, SubQueueEnum subQueueEnum, String... multiCodes) {
        this.topic = topic;
        this.templatePropertiesBean = templatePropertiesBean;
        this.subQueueEnum = subQueueEnum;
        this.multiCodes = multiCodes;
    }

    /**
     * 取topic
     *
     * @param multiCode
     * @param subQueueEnum
     * @return
     */
    public static KafkaTopicEnum getTopic(String multiCode, SubQueueEnum subQueueEnum) {
        KafkaTopicEnum[] values = KafkaTopicEnum.values();
        for (KafkaTopicEnum value : values) {
            // 支持多个code的对应topic
            if (value.subQueueEnum.equals(subQueueEnum) && Arrays.stream(value.multiCodes).anyMatch(p -> p.equals(multiCode))) {
                return value;
            }
        }
        // 默认topic
        return COMMON;
    }

    /**
     * 取主题
     * @return
     */
    public String getTopic() {
        // 如有不同环境设置不同主题的需求，这里根据环境修改
        return topic;
    }

    /**
     * 取kafka配置模板
     * @return
     */
    public KafkaProperties createKafkaProperties() {
        // 取通用配置
        KafkaProperties templateProperties = BeanContextUtil.getBean(this.templatePropertiesBean, KafkaProperties.class);
        return templateProperties;
    }
}
