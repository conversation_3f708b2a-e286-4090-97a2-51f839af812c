package com.differ.wdgj.api.user.biz.infrastructure.external.qywx;

import com.alibaba.fastjson.JSONObject;
import com.differ.wdgj.api.component.util.http.HttpTools;
import com.differ.wdgj.api.component.util.http.core.HttpTlsEnum;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信机器人操作类
 * <a href="https://developer.work.weixin.qq.com/document/path/91770">接口文档</a>
 *
 * <AUTHOR>
 * @date 2025/3/20 上午10:13
 */
public class QyWeixinRobotOperator {
    //region 常量
    /**
     * 请求地址
     */
    private static final String WEBSITE = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=%s";

    /**
     * 超时时间
     */
    private static final int TIMEOUT = 10000;
    //endregion

    //region 构造
    private QyWeixinRobotOperator() {
    }
    //endregion

    /**
     * 发送机器人消息
     */
    public static boolean sendRobotMessage(QyWeixinRobotMessage message, String webHook) {
        try {
            // 构建请求头
            Map<String, String> headerData = new HashMap<>();
            headerData.put("Content-Type", "application/json;charset=utf-8");

            // 构建动态请求参数
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msgtype", message.getMsgType());
            jsonObject.put(message.getMsgType(), message);
            String postData = jsonObject.toJSONString();

            // 发起请求
            HttpTools.postDataCommon(
                    String.format(WEBSITE, webHook),
                    postData,
                    headerData,
                    HttpTlsEnum.TLS2,
                    TIMEOUT,
                    StandardCharsets.UTF_8
            );
        } catch (Exception ex) {
            LogFactory.error("发送机器人消息",String.format("Key：%s；异常信息：%s", webHook, JsonUtils.toJson(message)), ex);
            return false;
        }
        return true;
    }
}
