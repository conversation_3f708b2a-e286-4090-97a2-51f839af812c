package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 售后单下载类型
 *
 * <AUTHOR>
 * @date 2024/9/18 下午4:12
 */
public enum AfterSaleLoadTypeEnum implements ValueEnum {
    /**
     * 店铺级下载（店铺配置按时间区间下载） - 对应下载订单页面下载按钮
     */
    SHOP_LOAD(0),

    /**
     * 按时间区间下载 - 对应下载订单页面自定义按时间下载
     */
    TIME_RANGE(1),

    /**
     * 按售后单号下载 - 消息推送/下载订单页面自定义按售后单号下载
     */
    AFTER_SALE_NO(2),
    ;

    /**
     * 值
     */
    private final int value;

    AfterSaleLoadTypeEnum(int value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
