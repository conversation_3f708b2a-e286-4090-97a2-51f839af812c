package com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core;

import com.differ.jackyun.framework.component.utils.id.IdWorkerUtil;
import com.differ.wdgj.api.component.util.hash.HashUtil;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogContextIdUtil;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ExecuteEvent;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ExecuteStatus;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * @Description 全局的执行监控队列抽象类, 相关定时任务:LocalExecuteMonitorJob，设计：https://s.jkyun.biz/PtwivXO 执行监控基础组件
 * <AUTHOR>
 * @Date 2023/12/14 17:14
 */
public abstract class AbstractExecuteQueueMonitorCenter implements ExecuteQueueMonitorCenter {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractExecuteQueueMonitorCenter.class);
    /**
     * 执行队列组的集合
     */
    protected Map<Long, TimeoutQueue> mapEvent = new ConcurrentHashMap<>();
    /**
     * 监控业务代码，用于特殊配置
     */
    protected String monitorCode;

    /**
     * 监控名，用于特殊配置
     *
     * @param monitorCode
     */
    @Override
    public void initCode(String monitorCode) {
        this.monitorCode = monitorCode;
    }

    /**
     * 用标题初始化跟踪队列
     *
     * @param caption   标题
     * @param eventData 跟踪事件内容，为空时不添加事件
     * @return 跟踪队列唯一键,null表示监控功能关闭
     */
    @Override
    public Long initWithCaption(String caption, String eventData) {
        // 判断监控功能是否开启
        if(!this.monitorEnable(caption)){
            // 监控功能关闭
            return null;
        }
        return doInitWithCaption(caption,eventData);
    }

    /**
     * 用标题初始化跟踪队列
     *
     * @param caption      标题
     * @param funEventData 跟踪事件内容，为空时不添加事件
     * @return 跟踪队列唯一键
     */
    @Override
    public Long initWithCaption(String caption, Supplier<StringBuilder> funEventData) {
        // 判断监控功能是否开启
        if(!this.monitorEnable(caption)){
            // 监控功能关闭
            return null;
        }

        return doInitWithCaption(caption,funEventData.get().toString());
    }

    /**
     * 用标题初始化跟踪队列
     *
     * @param caption      标题
     * @param eventData 跟踪事件内容，为空时不添加事件
     * @return 跟踪队列唯一键
     */
    private Long doInitWithCaption(String caption, String eventData) {
        // 标题不能为空
        if (StringUtils.isBlank(caption)){
            return null;
        }

        // 初始化队列
        LocalDateTime startTime = LocalDateTime.now();
        long uniqueNo = this.createUniqueNo(caption);
        TimeoutQueue eventQueue = mapEvent.computeIfAbsent(uniqueNo, no -> new TimeoutQueue());
        eventQueue.initWithCaption(caption, startTime);

        // 添加事件记录
        if (StringUtils.isNotBlank(eventData)) {
            ExecuteEvent event = new ExecuteEvent();
            event.setUniqueNo(uniqueNo);
            event.setEventData(eventData);
            event.setStatus(ExecuteStatus.DOING);
            event.setEventTime(startTime);
            this.putWithQueue(eventQueue, event);
        }

        return uniqueNo;
    }

    /**
     * 外部触发添加执行事件，超过限制时，丢弃非完成状态的数据
     *
     * @param event
     */
    @Override
    public void put(ExecuteEvent event) {
        if (event == null) {
            return;
        }
        if (!event.valid()) {
            return;
        }
        TimeoutQueue eventQueue = mapEvent.computeIfAbsent(event.getUniqueNo(), uniqueNo -> new TimeoutQueue());
        putWithQueue(eventQueue, event);
    }

    /**
     * 外部触发添加执行事件，超过限制时，丢弃非完成状态的数据
     *
     * @param event
     */
    protected void putWithQueue(TimeoutQueue eventQueue, ExecuteEvent event) {
        if (eventQueue == null) {
            return;
        }
        if (event == null) {
            return;
        }
        if (!event.valid()) {
            return;
        }
        if (event.getStatus() == ExecuteStatus.DOING) {
            if (eventQueue.getSize() < getQueueCapacity()) {
                // 执行中的，未超过容量时才加
                eventQueue.add(event);
            } else {
                // 累计忽略数
                eventQueue.incrementIgnore();
            }
            return;
        }

        // 已完成的都添加
        eventQueue.add(event);
        try {
            // 完成状态进行处理（成功或失败）
            this.doFinish(eventQueue, event.getStatus() == ExecuteStatus.SUCCESS);
        } catch (Throwable t) {
            LOG.error("执行监控完成处理异常", t);
        } finally {
            // 处理后需要清除数据
            this.clear(event.getUniqueNo());
        }

    }

    /**
     * 执行监控的最大超时时间(秒)
     *
     * @return
     */
    protected int getMaxTimeoutSeconds() {
        // 非spring测试代码：return 3;
        return Integer.parseInt(ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.EXEC_MONITOR_MAX_TIMEOUT, this.monitorCode, "300"));
    }

    /**
     * 定时任务触发检测超时数据,并返回总记录数
     *
     * @return
     */
    @Override
    public int checkTimeoutAndGetTotalCount() {
        int total = 0;
        int timeoutSeconds = this.getMaxTimeoutSeconds();
        Set<Map.Entry<Long, TimeoutQueue>> entrySet = mapEvent.entrySet();
        for (Map.Entry<Long, TimeoutQueue> keyValue : entrySet) {
            TimeoutQueue eventQueue = keyValue.getValue();
            // 累计事件记录数
            total += eventQueue.getSize();
            if (eventQueue.checkTimeout(timeoutSeconds)) {
                try {
                    // 执行超时处理
                    this.doTimeout(eventQueue, timeoutSeconds);
                } catch (Throwable t) {
                    LOG.error("执行监控超时处理异常", t);
                } finally {
                    // 处理后需要清除数据
                    if (this.needClearOnTimeout(eventQueue)) {
                        this.clear(keyValue.getKey());
                    }
                }
            }
        }
        return total;
    }


    /**
     * 是否使用本地任务监控
     *
     * @return
     */
    @Override
    public boolean useJobMonitor() {
        return true;
    }

    /**
     * 当完成一个任务链时，执行处理
     *
     * @param queue
     * @param success
     */
    protected abstract void doFinish(TimeoutQueue queue, boolean success);

    /**
     * 是否超时，用于外部定时任务检测,
     * 注意：当超时的时候，如果不想清除队列中的数据，业务处理中不要使用queue.getQueue().poll(),应使用queue.getQueue().toArray()处理数据
     *
     * @param queue             执行队列
     * @param maxTimeoutSeconds 超时的阈值
     */
    protected abstract void doTimeout(TimeoutQueue queue, int maxTimeoutSeconds);

    /**
     * 每个队列的最大容量，超过限制时，丢弃非完成状态的数据
     *
     * @return
     */
    protected int getQueueCapacity() {
        return 3;
    }

    /**
     * 清空队列
     *
     * @param uniqueNo
     */
    protected void clear(Long uniqueNo) {
        TimeoutQueue queue = mapEvent.remove(uniqueNo);
        if (queue != null) {
            queue.clear();
        }
    }

    /**
     * 超时的时候是否清理数据，默认清理
     *
     * @param queue 要清除的队列
     * @return
     */
    protected boolean needClearOnTimeout(TimeoutQueue queue) {
        return true;
    }

    /**
     * 根据标题创建唯一键
     *
     * @param caption 标题，可为空
     * @return
     */
    protected long createUniqueNo(String caption) {
        String key = String.format("%s_%s_%d_%d", this.monitorCode, caption, LogContextIdUtil.initLogIdIfNoExists(), IdWorkerUtil.getId());
        return HashUtil.murMurHash(key);
    }

    /**
     * 根据业务标题判断是否要监控
     *
     * @param caption
     * @return
     */
    protected boolean monitorEnable(String caption) {
        return true;
    }
}
