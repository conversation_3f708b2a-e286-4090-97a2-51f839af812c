package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;

/**
 * 菠萝派接口类型
 *
 * <AUTHOR>
 * @date 2024/9/25 上午11:19
 */
public enum AfterSaleQueryTypeEnum implements CodeEnum {
    /**
     * 菠萝派退货退款单下载接口
     * 对应{@link PolyAPITypeEnum}中BUSINESS_GETREFUND
     */
    POLY_GET_REFUND("POLY_GET_REFUND", "菠萝派接口退货退款单下载"),
    /**
     * 换货单下载接口
     * 对应{@link PolyAPITypeEnum}中BUSINESS_GETEXCHANGE
     */
    POLY_GET_EXCHANGE("POLY_GET_EXCHANGE", "菠萝派接口换货单下载接口"),
    /**
     * 淘宝RDS退货退款单下载
     */
    TAOBAO_RDS_GET_REFUND("TAOBAO_RDS_GET_REFUND", "淘宝RDS退货退款单下载"),
    /**
     * 淘宝RDS换货单下载
     */
    TAOBAO_RDS_GET_EXCHANGE("TAOBAO_RDS_GET_EXCHANGE", "淘宝RDS换货单下载"),
    ;

    /**
     * 枚举标识
     */
    private final String code;
    /**
     * 枚举标识
     */
    private final String name;

    AfterSaleQueryTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Override
    public String getCode() {
        return code;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static AfterSaleQueryTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, AfterSaleQueryTypeEnum.class, EnumConvertType.CODE);
    }
}
