'可访问性说明
'private -
'protect #
'package private ~
'public +

'类关系说明参见：类图6大关系使用说明.puml

'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

'缓存类图设计

@startuml

interface IDataProxy {
    int syncAll() :自动同步所有缓存
    boolean isNeedRefurData() ：判断是否需要刷新数据
    int refurDataToMemory() ：刷新数据到本地内存
    boolean canRefurDataToMemory() ：是否可以刷新数据到本地内存
    Set<String> getKeys(DataCacheTypeEnum cacheType, String pattern)
    String getCacheKey()
    Boolean removeCache(String... cacheKeys)
    Boolean hashClear(Object... args)
    Boolean hashRemove(String[] hashKeys, Object... args)
    Boolean hashRemove(String hashKey, Object... args)
    Set<String> hashAllKeys(Object... args)
    ICacheDataItem hashGet2(String hashKey, Object... args)
    Object hashGet3(String hashKey, Object... args)
    String hashGetStrValue(String hashKey, Object... args)
    int hashSyncStrValue(String hashKey, String strValue, Object... args)
    boolean tryLock(String cacheKey, String lockKey, int expireTime, BooleanSupplier funExec)
}
class BaseDataProxy{
    #String[] createCacheKeyArgument(T hashItem)
    #List<T> getAllSourceData()
    #List<T> getAllSourceData(String... cacheKeyArgs)
}
class BaseMemoryDataProxy <<定时刷入内存的缓存>> {
    +boolean canRefurDataToMemory() :Override
    +boolean isNeedRefurData() : override
    +int refurDataToMemory() : Override

    +List<T> getAllFromMemory()
    +List<T> getDataFromMemory(Predicate<T> predicate)
    +T getDataFromMemory(String hashKey)
    +Optional<T> getFirstFromMemory(Predicate<T> predicate)

    #protected String getLastRefurTime()
    #void setLastRefurTime(String lastRefurTime)
}


IDataProxy <|-- BaseDataProxy
BaseDataProxy <|-- BaseMemoryDataProxy

@enduml