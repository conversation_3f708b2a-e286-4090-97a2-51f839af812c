package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;

/**
 * 库存同步菠萝派结果集合
 *
 * <AUTHOR>
 * @date 2024-03-01 15:52
 */
public class StockSyncResultComposite {
    //region 构建
    /**
     * 构造方法
     *
     * @param matchEnhance 匹配数据
     */
    public StockSyncResultComposite(GoodsMatchEnhance matchEnhance) {
        this.matchEnhance = matchEnhance;
    }
    /**
     * 构造方法
     *
     */
    public StockSyncResultComposite() {}
    //endregion

    /**
     * 匹配数据
     */
    private GoodsMatchEnhance matchEnhance;

    /**
     * 库存同步量详情
     */
    private String detailCount;

    /**
     * 平台请求
     */
    private BusinessBatchSyncStockRequestGoodInfo platRequest;

    /**
     * 平台响应
     */
    private BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse;

    /**
     * 平台响应错误码
     */
    private SyncStockPolyErrorCodeEnum responseErrorCode;

    /**
     * 库存同步结果 - 数据库更新对象 - 匹配表
     */
    private StockSyncApiSysMatchResult apiSysMatchResult;

    /**
     * 库存同步结果 - 数据库更新对象 - 匹配扩展表
     */
    private StockSyncApiSysMatchExtResult apiSysMatchExtResult;

    /**
     * 库存同步结果 - 数据库更新对象 - 库存同步日志
     */
    private ApiPlatSysHisDO stockSyncLog;

    //region get/set
    public GoodsMatchEnhance getMatchEnhance() {
        return matchEnhance;
    }

    public void setMatchEnhance(GoodsMatchEnhance matchEnhance) {
        this.matchEnhance = matchEnhance;
    }

    public BusinessBatchSyncStockRequestGoodInfo getPlatRequest() {
        return platRequest;
    }

    public void setPlatRequest(BusinessBatchSyncStockRequestGoodInfo platRequest) {
        this.platRequest = platRequest;
    }

    public BusinessBatchSyncStockResponseGoodSyncStockResultItem getPlatResponse() {
        return platResponse;
    }

    public void setPlatResponse(BusinessBatchSyncStockResponseGoodSyncStockResultItem platResponse) {
        this.platResponse = platResponse;
    }

    public SyncStockPolyErrorCodeEnum getResponseErrorCode() {
        return responseErrorCode;
    }

    public void setResponseErrorCode(SyncStockPolyErrorCodeEnum responseErrorCode) {
        this.responseErrorCode = responseErrorCode;
    }

    public String getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(String detailCount) {
        this.detailCount = detailCount;
    }

    public StockSyncApiSysMatchResult getApiSysMatchResult() {
        return apiSysMatchResult;
    }

    public void setApiSysMatchResult(StockSyncApiSysMatchResult apiSysMatchResult) {
        this.apiSysMatchResult = apiSysMatchResult;
    }

    public StockSyncApiSysMatchExtResult getApiSysMatchExtResult() {
        return apiSysMatchExtResult;
    }

    public void setApiSysMatchExtResult(StockSyncApiSysMatchExtResult apiSysMatchExtResult) {
        this.apiSysMatchExtResult = apiSysMatchExtResult;
    }

    public ApiPlatSysHisDO getStockSyncLog() {
        return stockSyncLog;
    }

    public void setStockSyncLog(ApiPlatSysHisDO stockSyncLog) {
        this.stockSyncLog = stockSyncLog;
    }
    //endregion
}
