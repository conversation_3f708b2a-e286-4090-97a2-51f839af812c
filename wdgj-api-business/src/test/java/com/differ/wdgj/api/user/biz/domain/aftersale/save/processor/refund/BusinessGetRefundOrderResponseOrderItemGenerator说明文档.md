# BusinessGetRefundOrderResponseOrderItemGenerator 说明文档

## 概述

`BusinessGetRefundOrderResponseOrderItemGenerator` 是一个专门用于生成 `BusinessGetRefundOrderResponseOrderItem`（退货退款单）随机测试数据的工具类。该工具类严格按照电商业务场景设计，生成的数据符合实际业务逻辑和约束。

## 核心特性

### ✅ **1. 必定随机生成字段**
- **退款单号 (refundNo)**: 格式 `RF + yyyyMMddHHmmss + 4位随机数`
- **平台订单号 (platOrderNo)**: 格式 `PO + yyyyMMddHHmmss + 4位随机数`

### ✅ **2. 智能模板支持**
- 支持传入模板对象，不为空的属性将被保留
- 为空的属性会被随机生成
- 完全兼容现有测试代码

### ✅ **3. 业务场景符合性**
- 金额逻辑合理（退款金额 ≤ 总金额）
- 时间逻辑正确（更新时间 ≥ 创建时间）
- 售后类型与业务行为匹配
- 地址信息真实可信

## 使用方法

### 基础用法

```java
// 生成完全随机的退货退款单
BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
```

### 模板用法

```java
// 创建模板，设置固定值
BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
template.setBuyerNick("固定买家昵称");
template.setTotalAmount(new BigDecimal("999.99"));
template.setRefundType("JH_04");

// 使用模板生成数据（模板中的非空字段会被保留）
BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);

// 结果：
// - buyerNick = "固定买家昵称" (来自模板)
// - totalAmount = 999.99 (来自模板)
// - refundType = "JH_04" (来自模板)
// - refundNo = "RF202412191430451234" (随机生成)
// - platOrderNo = "PO202412191430455678" (随机生成)
// - sellerNick = "官方旗舰店" (随机生成)
```

## 生成的数据结构

### 基础信息
| 字段 | 生成规则 | 示例值 |
|------|----------|--------|
| refundNo | **必定随机**: RF + 时间戳 + 随机数 | RF202412191430451234 |
| platOrderNo | **必定随机**: PO + 时间戳 + 随机数 | PO202412191430455678 |
| totalAmount | 随机金额 50-5000元 | 1299.99 |
| refundAmount | 随机金额 10-总金额 | 899.99 |
| payAmount | 等于总金额 | 1299.99 |

### 买家信息
| 字段 | 生成规则 | 示例值 |
|------|----------|--------|
| buyerNick | 前缀 + 6位随机数 | tb_123456 |
| buyerOpenUid | uid_ + 16位随机字符 | uid_a1b2c3d4e5f6g7h8 |

### 时间信息
| 字段 | 生成规则 | 业务逻辑 |
|------|----------|---------|
| createTime | 当前时间-30天内随机 | 退款申请时间 |
| updateTime | createTime + 48小时内 | 更新时间 ≥ 创建时间 |
| rdsCreateTime | createTime + 60分钟内 | 推送库创建时间 |
| rdsModifyTime | updateTime + 60分钟内 | 推送库更新时间 |

### 售后类型与业务逻辑

#### 退货退款单 (JH_04)
```java
// 当 refundType = "JH_04" 时：
item.setHasGoodsReturn(true);           // 需要退货
item.setLogisticName("顺丰速运");        // 生成物流信息
item.setLogisticNo("SF1234567890");     // 生成物流单号
item.setLogisticCode("SF");             // 物流公司编码
```

#### 仅退款单 (JH_03)
```java
// 当 refundType = "JH_03" 时：
item.setHasGoodsReturn(false);          // 不需要退货
item.setLogisticName(null);             // 无物流信息
```

#### 换货单 (JH_05)
```java
// 当 refundType = "JH_05" 时：
item.setHasGoodsReturn(true);           // 需要退货
// 生成完整的物流信息
```

### 商品信息
| 字段 | 生成规则 | 示例值 |
|------|----------|--------|
| productName | 从商品池随机选择 | iPhone 15 Pro Max 256GB |
| productNum | 1-5个随机数量 | 2 |
| platProductId | PROD_ + 6位随机数 | PROD_123456 |
| sku | 从SKU规格池选择 | 颜色:黑色;尺寸:XL |
| price | 10-2000元随机价格 | 299.99 |

### 地址信息
```java
// 买家地址
item.setAddress("广东省深圳市福田区某某街道123号");

// 卖家收货地址
item.setSellerReceiveProvince("广东省");
item.setSellerReceiveCity("深圳市");
item.setSellerReceiveArea("福田区");
item.setSellerAddress("广东省深圳市福田区商家收货地址88号");
```

### 退款商品明细
```java
// 自动生成 1-3 个退款商品
List<BusinessGetRefundResponseRefundGoodInfo> refundGoods = [
    {
        platProductId: "PROD_123456_1",
        productName: "Nike Air Jordan 1",
        refundAmount: 299.99,
        price: 599.99,
        productNum: 1,
        refundProductNum: 1,
        reason: "商品质量问题",
        isGift: false
    }
];
```

## 业务场景数据池

### 买家昵称前缀
```java
"tb_", "user_", "buyer_", "客户_", "用户_", "买家_", "会员_"
```

### 卖家昵称
```java
"官方旗舰店", "品牌专营店", "优选商城", "精品小店", "全球购", "海外直营"
```

### 商品名称
```java
"iPhone 15 Pro Max 256GB", "华为Mate60 Pro", "Nike Air Jordan 1", 
"雅诗兰黛小棕瓶精华", "戴森V15吸尘器", "优衣库羽绒服"
```

### 退款原因
```java
"商品质量问题", "商品与描述不符", "收到商品破损", "尺寸不合适",
"颜色不喜欢", "买错了", "不需要了", "发错货了"
```

### 物流公司
```java
"顺丰速运", "圆通快递", "中通快递", "申通快递", "韵达快递"
```

## 数据一致性保证

### 金额逻辑
```java
// 确保退款金额不超过总金额
refundAmount ≤ totalAmount

// 支付金额通常等于总金额
payAmount = totalAmount
```

### 时间逻辑
```java
// 时间顺序合理
createTime ≤ updateTime ≤ rdsModifyTime
```

### 关联性逻辑
```java
// 子订单号包含主订单号
subPlatOrderNo = platOrderNo + "-" + suffix

// 商品明细与主订单关联
refundGoods[i].platProductId = platProductId + "_" + (i+1)
```

## 测试用例

### 基础功能测试
```java
@Test
public void testGenerateRandom_BasicFunctionality() {
    BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
    
    // 验证必定随机生成的字段
    Assert.assertTrue("退款单号应以RF开头", item.getRefundNo().startsWith("RF"));
    Assert.assertTrue("平台订单号应以PO开头", item.getPlatOrderNo().startsWith("PO"));
    
    // 验证业务逻辑
    Assert.assertTrue("退款金额不应超过总金额", 
                     item.getRefundAmount().compareTo(item.getTotalAmount()) <= 0);
}
```

### 模板功能测试
```java
@Test
public void testGenerateRandom_WithTemplate() {
    BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
    template.setBuyerNick("固定买家昵称");
    template.setTotalAmount(new BigDecimal("999.99"));
    
    BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
    
    // 验证模板值被保留
    Assert.assertEquals("买家昵称应该使用模板值", "固定买家昵称", item.getBuyerNick());
    Assert.assertEquals("总金额应该使用模板值", new BigDecimal("999.99"), item.getTotalAmount());
    
    // 验证退款单号仍然随机生成
    Assert.assertTrue("退款单号应以RF开头", item.getRefundNo().startsWith("RF"));
}
```

### 唯一性测试
```java
@Test
public void testGenerateRandom_UniquenessOfOrderNumbers() {
    Set<String> refundNos = new HashSet<>();
    
    for (int i = 0; i < 100; i++) {
        BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        Assert.assertFalse("退款单号应该是唯一的", refundNos.contains(item.getRefundNo()));
        refundNos.add(item.getRefundNo());
    }
}
```

## 扩展性设计

### 添加新的数据池
```java
// 可以轻松扩展商品名称池
private static final String[] PRODUCT_NAMES = {
    // 现有商品...
    "新增商品1", "新增商品2"
};
```

### 添加新的生成规则
```java
// 可以添加新的字段生成逻辑
private static String generateCustomField() {
    // 自定义生成逻辑
    return "custom_" + RANDOM.nextInt(1000);
}
```

### 支持不同平台的特殊逻辑
```java
// 可以根据平台类型生成不同的数据
private static void generatePlatformSpecificData(BusinessGetRefundOrderResponseOrderItem item, String platform) {
    switch (platform) {
        case "TAOBAO":
            // 淘宝特殊逻辑
            break;
        case "JD":
            // 京东特殊逻辑
            break;
    }
}
```

## 最佳实践

### 1. 在测试中使用
```java
// 在 BaseSaveRefundOrderProcessorTest 中使用
@Test
public void testSaveOrder() {
    // 生成随机测试数据
    BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
    
    // 执行测试
    executeSaveOrderTest(Arrays.asList(item), "api2017", 1446);
}
```

### 2. 批量生成测试数据
```java
// 生成多个测试数据
List<BusinessGetRefundOrderResponseOrderItem> items = new ArrayList<>();
for (int i = 0; i < 10; i++) {
    items.add(BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom());
}
```

### 3. 特定场景测试
```java
// 测试特定的售后类型
BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
template.setRefundType("JH_04"); // 退货退款单
BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
```

## 总结

`BusinessGetRefundOrderResponseOrderItemGenerator` 提供了：

### ✅ **核心功能**
1. **必定随机生成**: refundNo 和 platOrderNo 始终随机
2. **智能模板支持**: 保留非空字段，生成空字段
3. **业务逻辑正确**: 符合电商退款业务场景

### ✅ **数据质量**
1. **唯一性保证**: 时间戳+随机数确保唯一性
2. **业务合理性**: 金额、时间、关联关系都符合逻辑
3. **真实性**: 使用真实的商品名称、地址、物流公司

### ✅ **易用性**
1. **简单调用**: 一行代码生成完整数据
2. **灵活配置**: 支持模板定制
3. **测试友好**: 专为测试场景设计

这个工具类大大简化了测试数据的准备工作，提高了测试的效率和质量，同时确保了测试数据的真实性和一致性。
