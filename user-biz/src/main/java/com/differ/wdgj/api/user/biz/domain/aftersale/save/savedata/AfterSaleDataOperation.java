package com.differ.wdgj.api.user.biz.domain.aftersale.save.savedata;

import com.differ.wdgj.api.component.util.hash.HashUtil;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.data.AfterSaleBasicInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IAfterSaleEncryptOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IAfterSaleInfoOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IAfterSaleLogOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IBasicsAfterSaleOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl.AfterSaleEncryptOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl.AfterSaleInfoOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl.AfterSaleLogOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl.BasicsAfterSaleOperationService;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.CheckAfterSaleHashCodeResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.QueryDbOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderDataResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleOrderHashItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetSaveOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderBasicInfo;
import com.differ.wdgj.api.user.biz.domain.order.common.subdomian.IBasicsOrderOperationService;
import com.differ.wdgj.api.user.biz.domain.order.common.subdomian.impl.BasicsOrderOperationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.*;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.HashCodeBizTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiBizHashCodeMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 保存售后单到DB操作类-实现
 * https://s.jkyun.biz/yN2ml5C 【管家java】开发设计 - 售后单保存 - 订单数据落地操作类
 *
 * <AUTHOR>
 * @date 2024-06-06 14:53
 */
public class AfterSaleDataOperation implements IAfterSaleDataOperation {

    //region 常量

    /**
     * 日志
     */
    private final Logger log = LoggerFactory.getLogger(AfterSaleDataOperation.class);

    /**
     * 上下文
     */
    private final AfterSaleSaveContext context;

    /**
     * 业务hash仓储
     */
    private final ApiBizHashCodeMapper bizHashCodeMapper = BeanContextUtil.getBean(ApiBizHashCodeMapper.class);

    /**
     * 售后单基础操作
     */
    private final IBasicsAfterSaleOperationService afterSaleOperation = new BasicsAfterSaleOperationService();

    /**
     * 售后单密文操作
     */
    private final IAfterSaleEncryptOperationService afterSaleEncryptOperation = new AfterSaleEncryptOperationService();

    /**
     * 售后单信息操作
     */
    private final IAfterSaleInfoOperationService afterSaleInfoOperation = new AfterSaleInfoOperationService();

    /**
     * 售后单日志操作
     */
    private final IAfterSaleLogOperationService afterSaleLogOperation = new AfterSaleLogOperationService();

    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public AfterSaleDataOperation(final AfterSaleSaveContext context) {
        this.context = context;
    }
    //endregion

    //region 校验hash

    /**
     * 校验hash
     *
     * @param sourceOrders 原始售后单列表
     * @return 校验结果
     */
    @Override
    public <T> SaveAfterSaleResult<List<CheckAfterSaleHashCodeResult>> checkPolyOrderHashCode(final List<SourceAfterSaleOrderItem<T>> sourceOrders) {
        if (CollectionUtils.isEmpty(sourceOrders)) {
            return SaveAfterSaleResult.failed("原始售后单列表为空");
        }

        String memberName = context.getMemberName();
        PolyPlatEnum plat = context.getPlat();
        Integer outShopId = context.getShopId();
        List<String> afterSaleNos = sourceOrders.stream().map(SourceAfterSaleOrderItem::getAfterSaleNo).collect(Collectors.toList());

        try {
            // 验证10分钟内店铺配置是否出现变化
            AfterSalesShopConfig shopConfig = context.getAfterSalesShopConfig();
            boolean isShopConfigChange = shopConfig != null && shopConfig.getModifiedTime() != null && shopConfig.getModifiedTime().plusMinutes(10).isAfter(LocalDateTime.now());
            // 配置键验证是否关闭hash过滤
            boolean isCloseHashCheck = AfterSaleConfigKeyUtils.isNeedRetryMessage(plat, memberName);

            // 批量获取hashCode数据
            List<ApiTradeCodeDO> afterSaleHashCodes = new ArrayList<>();
            DBSwitchUtil.doDBWithUser(memberName, () -> {
                afterSaleHashCodes.addAll(bizHashCodeMapper.selectByBizOrderNos(HashCodeBizTypeEnum.AFTER_SALE_ORDER.getValue(), outShopId, afterSaleNos));
            });

            // 初始化结果
            List<CheckAfterSaleHashCodeResult> results = new ArrayList<>();
            sourceOrders.forEach(item ->{
                String afterSaleNo = item.getAfterSaleNo();

                // 生成hashCode
                String newHashCode = Long.toString(HashUtil.murMurHash(JsonUtils.toJson(item.getPloyOrder())));

                // 4、检验hash是否出现变化
                ApiTradeCodeDO oldDbOrder = afterSaleHashCodes.stream().filter(x -> x.getTradeNo().equals(afterSaleNo)).findFirst().orElse(null);
                if (oldDbOrder != null) {
                    String oldHashCode = oldDbOrder.getHashCode();
                    if(!isShopConfigChange && !isCloseHashCheck && oldHashCode.equals(newHashCode)){
                        results.add(CheckAfterSaleHashCodeResult.failed(afterSaleNo, String.format("售后单hashcode(%s)已存在", newHashCode)));
                        return;
                    }
                }

                AfterSaleOrderHashItem orderHashItem = new AfterSaleOrderHashItem();
                orderHashItem.setOldHashCode(oldDbOrder);
                orderHashItem.setNewHashCode(new ApiTradeCodeDO(outShopId, HashCodeBizTypeEnum.AFTER_SALE_ORDER.getValue(), afterSaleNo, newHashCode));

                // 校验hashCode通过
                results.add(CheckAfterSaleHashCodeResult.success(afterSaleNo, orderHashItem));
            });

            return SaveAfterSaleResult.success(results);
        } catch (Exception e){
            String logContent = String.format("【%s】【%s】售后单校验hash失败，单号：%s", memberName, outShopId, String.join(",", afterSaleNos));
            log.error(logContent, e);
            return SaveAfterSaleResult.failed(logContent);
        }
    }

    //endregion

    //region 获取历史售后单
    /**
     * 获取历史售后单
     *
     * @param sourceOrders 原始售后单列表
     * @return 历史售后单列表
     */
    @Override
    public <T> SaveAfterSaleResult<List<QueryDbOrderResult>> getOldDbOrderItems(final List<SourceAfterSaleOrderItem<T>> sourceOrders) {
        if (CollectionUtils.isEmpty(sourceOrders)) {
            return SaveAfterSaleResult.success(new ArrayList<>());
        }

        // 基础数据
        String memberName = context.getMemberName();
        Integer outShopId = context.getShopId();
        List<String> afterSaleNos = new ArrayList<>();
        List<String> platOrderNos = new ArrayList<>();
        sourceOrders.forEach(item -> {
            if(StringUtils.isNotEmpty(item.getAfterSaleNo())){
                afterSaleNos.add(item.getAfterSaleNo());
            }
            if(StringUtils.isNotEmpty(item.getPlatOrderNo())){
                platOrderNos.add(item.getPlatOrderNo());
            }
        });

        try {

            // 售后单查询
            List<AfterSaleBasicInfo> afterSaleBasicInfos = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(afterSaleNos)) {
                afterSaleBasicInfos.addAll(afterSaleOperation.queryAfterSale(memberName, outShopId, afterSaleNos));
            }

            // 原始单查询
            IBasicsOrderOperationService orderOperationService = new BasicsOrderOperationService();
            List<OrderBasicInfo> apiTrades = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(platOrderNos)) {
                apiTrades.addAll(orderOperationService.queryOrder(memberName, outShopId, platOrderNos));
            }

            // 构建历史订单
            List<QueryDbOrderResult> getOldDbOrderResults = new ArrayList<>();
            for (SourceAfterSaleOrderItem<T> afterSaleBasicInfo : sourceOrders) {
                String afterSaleNo = afterSaleBasicInfo.getAfterSaleNo();
                String platOrderNo = afterSaleBasicInfo.getPlatOrderNo();
                DbAfterSaleOrderItem dbOrderItem = new DbAfterSaleOrderItem();

                // 查询历史售后单
                if(StringUtils.isNotEmpty(afterSaleNo)){
                    AfterSaleBasicInfo afterSaleOrder = afterSaleBasicInfos.stream().filter(x -> afterSaleNo.equals(x.getApiReturnListDO().getRefundId())).findFirst().orElse(null);
                    if (afterSaleOrder != null) {
                        dbOrderItem.setAfterSaleOrder(afterSaleOrder.getApiReturnListDO());
                        dbOrderItem.setReturnGoods(afterSaleOrder.getReturnGoods());
                        dbOrderItem.setExchangeGoods(afterSaleOrder.getExchangeGoods());
                        dbOrderItem.setAfterSaleTableType(afterSaleOrder.getAfterSaleTableType());
                    }
                }
                // 查询原始单数据
                if(StringUtils.isNotEmpty(platOrderNo)){
                    OrderBasicInfo apiTrade = apiTrades.stream().filter(x -> StringUtils.equals(platOrderNo, x.getApiTrade().getTradeNo())).findFirst().orElse(null);
                    if (apiTrade != null) {
                        dbOrderItem.setApiTrade(apiTrade.getApiTrade());
                        dbOrderItem.setApiTradeGoodsList(apiTrade.getApiTradeGoodsList());
                    }
                }

                getOldDbOrderResults.add(new QueryDbOrderResult(afterSaleNo, dbOrderItem));
            }

            return SaveAfterSaleResult.success(getOldDbOrderResults);
        } catch (Exception e) {
            String logContent = String.format("【%s】【%s】查询历史售后单失败，单号：%s", context.getMemberName(), context.getShopId(), String.join(",", afterSaleNos));
            log.error(logContent, e);
            return SaveAfterSaleResult.failed(logContent);
        }
    }
    //endregion

    //region 保存售后
    /**
     * 保存数据
     *
     * @param convertOrderResults 售后单组合列表
     */
    @Override
    public SaveAfterSaleResult<List<SaveOrderDataResult>> saveData(final List<TargetSaveOrderItem> convertOrderResults) {
        if(CollectionUtils.isEmpty(convertOrderResults)){
            return SaveAfterSaleResult.success(new ArrayList<>());
        }

        // 初始化结果
        Map<String, SaveOrderDataResult> saveOrderResultMap = new HashMap<>(convertOrderResults.size());
        convertOrderResults.forEach(x -> saveOrderResultMap.put(x.getAfterSaleNo(), new SaveOrderDataResult(x.getAfterSaleNo())));

        // 根据处理类别聚合售后单
        Map<AfterSaleProcessTypeEnum, List<TargetSaveOrderItem>> orderCompositeGroup = convertOrderResults.stream().collect(Collectors.groupingBy(TargetSaveOrderItem::getProcessType));
        orderCompositeGroup.forEach((key, value) -> {
            // 1、批量保存数据
            List<TargetSaveOrderItem> errorAfterSaleOrderList = new ArrayList<>();
            try {
                saveDataToDb(key, value, saveOrderResultMap);
            } catch (Exception e) {
                // 记录失败日志
                List<String> errorAfterSaleNos = value.stream().map(TargetSaveOrderItem::getAfterSaleNo).collect(Collectors.toList());
                processErrorCatch(errorAfterSaleNos, e);
                // 只有单条时直接返回，记录订单级异常
                if (CollectionUtils.isNotEmpty(value) && value.size() == 1) {
                    TargetSaveOrderItem item = value.stream().findFirst().orElse(null);
                    saveOrderResultMap.get(item.getAfterSaleNo()).failed(String.format("保存售后单到数据库失败，异常信息：%s", e.getMessage()));
                    return;
                }

                // 执行单条插入，防止因批量导致的失败
                errorAfterSaleOrderList.addAll(value);
            }

            // 2、异常数据执行单个保存，防止因批量导致的失败
            for (TargetSaveOrderItem item : errorAfterSaleOrderList) {
                try {
                    saveDataToDb(key, Collections.singletonList(item), saveOrderResultMap);
                } catch (Exception e) {
                    // 记录失败日志
                    processErrorCatch(Collections.singletonList(item.getAfterSaleNo()), e);
                    // 记录订单级结果
                    saveOrderResultMap.get(item.getAfterSaleNo()).failed(String.format("保存售后单到数据库失败，异常信息：%s", e.getMessage()));
                }
            }
        });

        return SaveAfterSaleResult.success(new ArrayList<>(saveOrderResultMap.values()));
    }

    /**
     * 保存售后单数据
     *
     * @param processType         处理类别
     * @param convertOrderResults 售后单列表
     */
    private void saveDataToDb(final AfterSaleProcessTypeEnum processType, final List<TargetSaveOrderItem> convertOrderResults, final Map<String, SaveOrderDataResult> saveOrderResultMap) {
        // 开启事务
        DBSwitchUtil.doTransaction(context.getMemberName(), () -> {
            // 1、保存hash数据，并验证并发
            List<TargetSaveOrderItem> newOrderItemList = saveHashCodeAndVerifyConcurrency(convertOrderResults, saveOrderResultMap);

            // 2、保存基础数据
            if(CollectionUtils.isNotEmpty(newOrderItemList)){
                saveBaseData(processType, newOrderItemList, saveOrderResultMap);
            }

            return true;
        }, true);
    }

    /**
     * 保存hash数据，并验证并发
     *
     * @param convertOrderTargets 售后单组合列表
     * @return 需要保存的售后单列表
     */
    private List<TargetSaveOrderItem> saveHashCodeAndVerifyConcurrency(final List<TargetSaveOrderItem> convertOrderTargets, final Map<String, SaveOrderDataResult> saveOrderResultMap) {
        // 保存hash数据，并做并发判断
        List<TargetSaveOrderItem> newOrderItemList = new ArrayList<>();
        for (TargetSaveOrderItem convertOrderTarget : convertOrderTargets) {
            AfterSaleOrderHashItem orderHashCode = convertOrderTarget.getOrderHashCode();
            if (orderHashCode != null && orderHashCode.getNewHashCode() != null) {
                ApiTradeCodeDO bizHashCode = orderHashCode.getNewHashCode();
                ApiTradeCodeDO oldHashCode = orderHashCode.getOldHashCode();
                // 验证hash数据是否存在
                if (BooleanUtils.isTrue(bizHashCodeMapper.isExist(bizHashCode.getBizType(), bizHashCode.getShopId(), bizHashCode.getTradeNo()))) {
                    String oldHashCodeValue = oldHashCode != null
                            ? oldHashCode.getHashCode()
                            : StringUtils.EMPTY;
                    // 更新，根据业务单号和老hashCode更新hashCode
                    int successCount = bizHashCodeMapper.updateHashCode(bizHashCode.getBizType(), bizHashCode.getShopId(), bizHashCode.getTradeNo(), oldHashCodeValue, bizHashCode.getHashCode());
                    if (successCount > 0) {
                        newOrderItemList.add(convertOrderTarget);
                    } else {
                        // 未更新，说明老hashcode被其他单子更新，出现并发
                        saveOrderResultMap.get(convertOrderTarget.getAfterSaleNo()).failed("更新订单hash值冲突");
                    }
                } else {
                    // 新增
                    bizHashCodeMapper.insert(bizHashCode);
                    newOrderItemList.add(convertOrderTarget);
                }
            }
        }

        return newOrderItemList;
    }

    /**
     * 保存基础业务数据
     *
     * @param processType     处理类别
     * @param targetOrderList 需要保存的售后单列表
     */
    private void saveBaseData(final AfterSaleProcessTypeEnum processType, final List<TargetSaveOrderItem> targetOrderList, final Map<String, SaveOrderDataResult> saveOrderResultMap) {
        // 基础数据
        String memberName = context.getMemberName();

        //region 数据集合

        // 售后单
        List<ApiReturnListDO> afterSaleOrders = new ArrayList<>();
        // 售后单退货商品
        List<ApiReturnDetailDO> returnGoods = new ArrayList<>();
        // 需要删除的售后单退货商品Id列表
        List<Integer> deleteReturnGoodsIds = new ArrayList<>();
        // 售后单换货商品
        List<ApiReturnDetailTwoDO> exchangeGoods = new ArrayList<>();
        // 需要删除的售后单换货商品Id列表
        List<Integer> deleteExchangeGoodsIds = new ArrayList<>();
        // 售后单日志
        List<ApiReturnLogDO> afterSaleOrderLogs = new ArrayList<>();
        // 密文数据
        List<ApiEncryptTradeDO> encryptTrades = new ArrayList<>();
        // 退款单信息数据列表
        List<ApiReturnInfoDO> returnInfos = new ArrayList<>();

        //endregion

        // 转换数据
        targetOrderList.forEach(targetOrder -> {
            if (targetOrder.getAfterSaleOrder() != null) {
                afterSaleOrders.add(targetOrder.getAfterSaleOrder());
            }
            if (CollectionUtils.isNotEmpty(targetOrder.getReturnGoods())) {
                returnGoods.addAll(targetOrder.getReturnGoods());
            }
            if (CollectionUtils.isNotEmpty(targetOrder.getDeleteReturnGoodsIds())) {
                deleteReturnGoodsIds.addAll(targetOrder.getDeleteReturnGoodsIds());
            }
            if (CollectionUtils.isNotEmpty(targetOrder.getExchangeGoods())) {
                exchangeGoods.addAll(targetOrder.getExchangeGoods());
            }
            if (CollectionUtils.isNotEmpty(targetOrder.getDeleteExchangeGoodsIds())) {
                deleteExchangeGoodsIds.addAll(targetOrder.getDeleteExchangeGoodsIds());
            }
            if (CollectionUtils.isNotEmpty(targetOrder.getAfterSaleOrderLogs())) {
                afterSaleOrderLogs.addAll(targetOrder.getAfterSaleOrderLogs());
            }
            if (targetOrder.getEncryptTrade() != null && StringUtils.isNotEmpty(targetOrder.getEncryptTrade().getTradeNo())) {
                encryptTrades.add(targetOrder.getEncryptTrade());
            }
            if (CollectionUtils.isNotEmpty(targetOrder.getReturnInfos())) {
                returnInfos.addAll(targetOrder.getReturnInfos());
            }
        });

        //region 删除业务数据

        // 删除业务数据
        // 1、删除退货商品
        if (CollectionUtils.isNotEmpty(deleteReturnGoodsIds)) {
            afterSaleOperation.batchDeleteRefundGoods(memberName, deleteReturnGoodsIds);
        }
        // 2、删除换货商品
        if (CollectionUtils.isNotEmpty(deleteExchangeGoodsIds)) {
            afterSaleOperation.batchDeleteExchangeGoods(memberName, deleteExchangeGoodsIds);
        }

        //endregion

        //region 保存业务数据

        // 保存业务数据
        // 1、保存售后单
        if (CollectionUtils.isNotEmpty(afterSaleOrders)) {
            afterSaleOperation.batchSaveOrder(memberName, afterSaleOrders);
        }
        // 1.1、处理关联主键
        targetOrderList.forEach(targetOrder -> {
            int billId = targetOrder.getAfterSaleOrder().getBillId();
            targetOrder.getExchangeGoods().forEach(goods -> goods.setBillId(billId));
            targetOrder.getReturnGoods().forEach(goods -> goods.setBillId(billId));
            targetOrder.getAfterSaleOrderLogs().forEach(orderLog -> orderLog.setBillId(billId));
            targetOrder.getReturnInfos().forEach(returnInfo -> returnInfo.setBillId(billId));
        });
        // 2、保存售后单退货商品
        if (CollectionUtils.isNotEmpty(returnGoods)) {
            afterSaleOperation.batchSaveRefundGoods(memberName, returnGoods);
        }
        // 3、保存售后单换货数据
        if (CollectionUtils.isNotEmpty(exchangeGoods)) {
            afterSaleOperation.batchSaveExchangeGoods(memberName, exchangeGoods);
        }
        // 4、保存售后单密文数据
        if (CollectionUtils.isNotEmpty(encryptTrades)) {
            afterSaleEncryptOperation.batchSaveEncrypt(memberName, encryptTrades);
        }
        // 5、保存售后单日志
        if (CollectionUtils.isNotEmpty(afterSaleOrderLogs)) {
            afterSaleLogOperation.batchInsertLog(memberName, afterSaleOrderLogs);
        }
        // 6、保存退款单信息数据
        if (CollectionUtils.isNotEmpty(returnInfos)) {
            afterSaleInfoOperation.batchSaveInfo(memberName, returnInfos);
        }

        //endregion
    }
    //endregion

    //region 私有方法

    /**
     * 记录失败日志
     *
     * @param errorAfterSaleNos 失败售后单号
     * @param e                 异常
     */
    private void processErrorCatch(List<String> errorAfterSaleNos, Exception e) {
        String logContent = String.format("【%s】【%s】保存售后单到数据库失败，单号：%s", context.getMemberName(), context.getShopId(), String.join(",", errorAfterSaleNos));
        log.error(logContent, e);
    }
    //endregion
}
