package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.batch;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPerBatchProcessOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 前置批量处理 - 平台级特殊处理
 *
 * <AUTHOR>
 * @date 2025/3/5 上午10:19
 */
public class RefundPlatSpecialBatchHandle extends AbstractPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public RefundPlatSpecialBatchHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult perBatchQueryProcess(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return String.format("平台【%s】退货退款单订单级转换-前置批量处理", context.getPlat().getName());
    }
}
