package com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.core;

import java.util.List;

/**
 * 平台业务特性二级业务
 *
 * <AUTHOR>
 * @date 2024/8/14 下午3:01
 */
public class PlatFeatureBizDto {
    /**
     * API业务值
     */
    private String bizValue;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 菠萝派业务值
     */
    private String polyValue;

    /// <summary>
    /// 下载订单原始状态
    /// </summary>
    private List<PlatSourceOrderTypeDto> sourceOrderTypeList;

    //region get/set
    public String getBizValue() {
        return bizValue;
    }

    public void setBizValue(String bizValue) {
        this.bizValue = bizValue;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getPolyValue() {
        return polyValue;
    }

    public void setPolyValue(String polyValue) {
        this.polyValue = polyValue;
    }

    public List<PlatSourceOrderTypeDto> getSourceOrderTypeList() {
        return sourceOrderTypeList;
    }

    public void setSourceOrderTypeList(List<PlatSourceOrderTypeDto> sourceOrderTypeList) {
        this.sourceOrderTypeList = sourceOrderTypeList;
    }
    //endregion
}
