package com.differ.wdgj.api.user.biz.infrastructure.external.qywx;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.external.qywx.message.MarkdownRobotMessage;
import com.differ.wdgj.api.user.biz.infrastructure.external.qywx.message.TextQyWeixinRobotMessage;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;

/**
 * 企业微信机器人操作类
 *
 * <AUTHOR>
 * @date 2025/3/20 下午4:34
 */
@Ignore
public class QyWeixinRobotOperatorTest extends AbstractSpringTest {
    //region 常量
    /**
     * 机器人key
     */
    private static final String TEST_WEBHOOK = "505a792f-0d5b-4608-a0a4-3841568b73ec";
    //endregion

    /**
     * 测试发送文本消息
     */
    @Test
    public void testSendTextMessage() {
        // 准备测试数据
        TextQyWeixinRobotMessage message = new TextQyWeixinRobotMessage("这是一条测试消息", Collections.emptyList());
        QyWeixinRobotOperator.sendRobotMessage(message, TEST_WEBHOOK);
    }

    /**
     * 测试发送带有@用户的文本消息
     */
    @Test
    public void testSendTextMessageWithMention() {
        // 准备测试数据

        TextQyWeixinRobotMessage message = new TextQyWeixinRobotMessage("这是一条测试消息", Arrays.asList("18158510455", "18155691781"));
        QyWeixinRobotOperator.sendRobotMessage(message, TEST_WEBHOOK);
    }

    /**
     * 测试发送带有@ALL的文本消息
     */
    @Test
    public void testSendTextMessageWithAll() {
        // 准备测试数据
        TextQyWeixinRobotMessage message = new TextQyWeixinRobotMessage("这是一条测试消息", Collections.singletonList("@all"));
        QyWeixinRobotOperator.sendRobotMessage(message, TEST_WEBHOOK);
    }

    /**
     * 测试webhook发送消息
     */
    @Test
    public void testSendMessageWithInvalidWebhook() {
        // 准备测试数据
        MarkdownRobotMessage message = new MarkdownRobotMessage("这是一条测试消息");
        QyWeixinRobotOperator.sendRobotMessage(message, TEST_WEBHOOK);
    }
}
