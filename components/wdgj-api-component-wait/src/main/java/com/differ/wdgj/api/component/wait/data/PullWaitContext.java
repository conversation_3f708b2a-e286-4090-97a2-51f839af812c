package com.differ.wdgj.api.component.wait.data;

import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.wait.WaitExecutor;

/**
 * @Description 拉取模式的排队上下文
 * <AUTHOR>
 * @Date 2023/12/6 11:22
 */
public class PullWaitContext extends WaitContext {

    /**
     * 拉取模式的最大执行数
     */
    private static final int PULL_MAX_EXEC_COUNT = 1000000;

    /**
     * 拉取模式的排队上下文(默认不用外部主动调用来移除任务)
     * 执行过期时间，默认10分钟，过期时允许重新加入排队
     *
     * @param multiRedis redis
     * @param waitType   排队类型，用于区别任务
     */
    public PullWaitContext(MultiRedis multiRedis, String waitType) {
        super(multiRedis, waitType, null, null, true);
        this.setMaxExec(PULL_MAX_EXEC_COUNT);
    }

    /**
     * 拉取模式的排队上下文(默认不用外部主动调用来移除任务)
     *
     * @param multiRedis redis
     * @param waitType   排队类型，用于区别任务
     * @param executor   任务执行器
     */
    public PullWaitContext(MultiRedis multiRedis, String waitType, WaitExecutor executor, int executingExpire, boolean asyncCallbackResult) {
        super(multiRedis, waitType, executor, null, asyncCallbackResult);
        this.setExecutingExpire(executingExpire);
        this.setMaxExec(PULL_MAX_EXEC_COUNT);
    }
}
