package com.differ.wdgj.api.user.biz.infrastructure.alarm.notice;

import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.AlarmContent;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.enums.AlarmSenderTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.sender.AlarmSenderFactory;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.common.AlarmIntervalLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.common.AlarmIntervalCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.event.AlarmIntervalEvent;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.TaskRunnable;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报警操作类
 *
 * <AUTHOR>
 * @date 2025/3/19 下午2:36
 */
public class AlarmOperator {
    //region 常量
    /**
     * 刷新锁
     */
    private static final Object REFRESH_LOCK = new Object();

    /**
     * 报警发送器工厂
     */
    private final AlarmSenderFactory alarmSenderFactory = new AlarmSenderFactory();

    /**
     * 日志标题
     */
    private static final String CAPTION = "监控报警操作";
    //endregion

    //region 单例

    /**
     * 枚举单例
     *
     * @return 枚举单例
     */
    public static AlarmOperator singleton() {
        return AlarmOperator.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AlarmOperator instance;

        private SingletonEnum() {
            instance = new AlarmOperator();
        }
    }
    //endregion

    /**
     * 异步报警
     *
     * @param intervalTypeEnum  间隔报警类型
     * @param alarmDuplicateKey 同个间隔报警类型的去重唯一键
     * @param content           报警内容
     */
    public void alarmInterval(AlarmIntervalTypeEnum intervalTypeEnum, String alarmDuplicateKey, AlarmContent content) {
        // 报警业务中，和会员名无关
        TaskEnum.API_ALARM.execute(new TaskRunnable(alarmDuplicateKey) {
            @Override
            public void run() {
                alarmIntervalSync(intervalTypeEnum, alarmDuplicateKey, content);
            }
        });
    }

    /**
     * 事件报警
     *
     * @param event 事件
     */
    @EventListener
    public void handler(AlarmIntervalEvent event) {
        AlarmContent content = AlarmContent.build(event.getAlarmIntervalType(), event.getAlarmText());
        alarmInterval(event.getAlarmIntervalType(), event.getAlarmDuplicateKey(), content);
    }

    /**
     * 同步报警，通常不推荐使用，要求使用异步方法
     *
     * @param intervalTypeEnum  间隔报警类型
     * @param alarmDuplicateKey 同个间隔报警类型的去重唯一键
     * @param content           报警内容
     * @return true:真正报警,false:不需要报警
     */
    public boolean alarmIntervalSync(AlarmIntervalTypeEnum intervalTypeEnum, String alarmDuplicateKey, AlarmContent content) {
        try {
            // 是否需要报警
            if (!isNeedAlarm(intervalTypeEnum, alarmDuplicateKey)) {
                return false;
            }

            // 发送报警
            // 构建当前报警类型支持的报警类型
            String senderTypesStr = ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.ALARM_TYPE_TO_NITUFUCATION_TYPE, intervalTypeEnum.getCaption(), "|default=0|");
            List<AlarmSenderTypeEnum> senderTypes = Arrays.stream(StringUtils.split(senderTypesStr, ",")).map(AlarmSenderTypeEnum::create).collect(Collectors.toList());
            // 工厂创建轮询报警
            for (AlarmSenderTypeEnum senderType : senderTypes) {
                try {
                    boolean sendResult = alarmSenderFactory.getSender(senderType).send(content);
                    if (!sendResult) {
                        LogFactory.info(CAPTION, String.format("【%s】未发送成功，错误类型：%s, 报警唯一键：%s;", senderType.getCaption(), intervalTypeEnum.getCaption(), alarmDuplicateKey));
                    }
                } catch (Exception e) {
                    LogFactory.error(CAPTION, String.format("【%s】未发送成功，错误类型：%s, 报警唯一键：%s;", senderType.getCaption(), intervalTypeEnum.getCaption(), alarmDuplicateKey), e);
                }
            }

            // 刷新全局报警记录
            AlarmIntervalCache.singleton().syncValue(intervalTypeEnum.createAlarmKey(alarmDuplicateKey), String.valueOf(Instant.now().getEpochSecond()), intervalTypeEnum.getIntervalSecond());

            return true;

        } catch (Exception ex) {
            LogFactory.error(CAPTION, String.format("【%s】报警异常，报警唯一键：%s;", intervalTypeEnum.getCaption(), alarmDuplicateKey), ex);
        }
        return false;
    }

    //region 是否报警

    /**
     * 是否不需要报警
     *
     * @param typeEnum          报警类型
     * @param alarmDuplicateKey 同个间隔报警类型的去重唯一键
     * @return 是否不需要报警
     */
    private boolean isNeedAlarm(AlarmIntervalTypeEnum typeEnum, String alarmDuplicateKey) {
        // 整体报警开关
        if (!ConfigKeyUtils.isActionApiBoolean(ConfigKeyEnum.ALARM_ENABALE)) {
            return false;
        }

        // 类型 + 同个间隔报警类型的去重唯一键 报警开关
        boolean isConfigKeyNeedAlarm = ConfigKeyUtils.isActionApiMultipleMatchValue(ConfigKeyEnum.ALARM_ENABALE_TYPE_AND_MEMBER, new String[]{"@"}, typeEnum.name(), alarmDuplicateKey);
        boolean isConfigKeyExcludeAlarm = ConfigKeyUtils.isActionApiMultipleMatchValue(ConfigKeyEnum.ALARM_UNABALE_TYPE_AND_MEMBER, new String[]{"@"}, typeEnum.name(), alarmDuplicateKey);
        if (!isConfigKeyNeedAlarm || isConfigKeyExcludeAlarm) {
            LogFactory.info(CAPTION, String.format("【%s】配置键关闭，报警唯一键：%s;", typeEnum.getCaption(), alarmDuplicateKey));
            return false;
        }

        // 间隔去重
        String alarmKey = typeEnum.createAlarmKey(alarmDuplicateKey);
        if (typeEnum.isInterval()) {
            // 判断缓存上次报警时间
            if (isUnAlarmIntervalLocal(alarmKey, typeEnum)) {
                return false;
            }
            // 间隔报警并发问题处理，考虑报警量少，所以直接使用锁，没必要区分不同的报警key
            synchronized (REFRESH_LOCK) {
                // 本地缓存的redis时间戳可能是旧的，所以再次刷新后再判断
                AlarmIntervalLocalCache.singleton().invalidate(Collections.singletonList(alarmKey));
                if (isUnAlarmIntervalLocal(alarmKey, typeEnum)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 本地信息判断是否未达到报警间隔
     *
     * @param alarmKey 报警key
     * @param typeEnum 报警类型
     * @return 是否报警
     */
    private boolean isUnAlarmIntervalLocal(String alarmKey, AlarmIntervalTypeEnum typeEnum) {
        // 内存缓存获取数据
        String alarmTime = AlarmIntervalLocalCache.singleton().getCacheThenSource(alarmKey);
        if (!StringUtils.isEmpty(alarmTime)) {
            long lastTime = Long.parseLong(alarmTime);
            return Instant.now().getEpochSecond() - lastTime <= typeEnum.getIntervalSecond();
        }

        return false;
    }
    //endregion
}
