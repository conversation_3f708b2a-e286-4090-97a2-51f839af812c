package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade;

import java.util.Map;

/**
 * 淘宝订单发货信息
 *
 * <AUTHOR>
 * @date 2025/6/26 09:56
 */
public class RdsTbTradeSendInfo {
    /**
     * 订单号
     */
    private Long tId;

    /**
     * 是否全部发货
     */
    private Boolean bAllSend;

    /**
     * 子订单维度是否发货
     * Key为子订单号，Value为是否发货
     */
    private Map<Long, Boolean> subOrderSendMap;

    public Long getTId() {
        return tId;
    }

    public void setTId(Long tId) {
        this.tId = tId;
    }

    public Boolean getBAllSend() {
        return bAllSend;
    }

    public void setBAllSend(Boolean bAllSend) {
        this.bAllSend = bAllSend;
    }

    public Map<Long, Boolean> getSubOrderSendMap() {
        return subOrderSendMap;
    }

    public void setSubOrderSendMap(Map<Long, Boolean> subOrderSendMap) {
        this.subOrderSendMap = subOrderSendMap;
    }
}
