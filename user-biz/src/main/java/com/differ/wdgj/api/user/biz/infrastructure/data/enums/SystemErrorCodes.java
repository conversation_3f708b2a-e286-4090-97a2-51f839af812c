package com.differ.wdgj.api.user.biz.infrastructure.data.enums;


import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;

/**
 * 错误码枚举
 * <p>
 * 错误码规则,预留字段：0  子系统编号：23 系统子模块：00 错误等级：0~9 错误码：0000，如：0230190001
 * <p>
 * 系统子模块定义：公共模块00、对接第三方平台如菠萝派01、委外核心业务02、定时任务=70
 * <p>
 * 错误等级定义：0-9从低到高越大严重级别越高，0表示警告不影响业务进行，1-3表示一般性错误不影响系统正常工作，4-6表示严重错误会引发系统不稳定或不能正常工作，7-9表示最高级别错误比如系统存储空间不足，连接池耗尽等
 * <p>
 * 错误码定义：从0001开始往下递增
 * <p>
 * 错误说明：务必措辞得当，言简意赅
 * <p>
 * 模板：0-23-00-4-0000，请去掉-号
 *
 * <AUTHOR>
 * @since 2019-12-20  17:26
 */
public enum SystemErrorCodes implements ErrorCode {

    /**
     * 成功
     */
    SUCCESS("200", "成功"),

    //region 系统级(0040001~0040100)

    /**
     * 未知错误，一般是指捕捉到的系统异常
     */
    UNKNOWERROR("0040001", "未知错误"),
    /**
     * 逻辑错误，一般是主动抛出的异常
     */
    LOGICERROR("0040002", "逻辑错误"),
    /**
     * 逻辑错误，一般是主动抛出的异常
     */
    SYSTEMERROR("0040003", "系统错误"),
    /**
     * 参数错误
     */
    PARAM_ERROR("0040004", "参数错误"),
    /**
     * 签名错误
     */
    SIGN_ERROR("0040005", "签名错误"),
    /**
     * 非法的代码定义
     */
    ILLEGAL_CODE("0040006", "非法的代码定义"),
    /**
     * 非法的代码定义
     */
    FILE_DELETE_FAIL("0040007", "删除文件失败"),
    //endregion

    //region 组件相关(0040101~0040200)

    /**
     * 未实现的加解密类型
     */
    UNCONSUMMATEDCRYPTOGRAPHY("0040101", "未实现的加解密类型"),
    /**
     * MQ异常
     */
    MQFAIL("0040102", "MQ异常"),
    /**
     * 缓存异常
     */
    CACHEFAIL("0040103", "缓存异常"),
    /**
     * 加解密失败
     */
    EDECRYPTFAIL("0040104", "加解密失败"),
    /**
     * 创建文件夹失败
     */
    CREATEDIRFAIL("0040105", "创建文件夹失败"),
    /**
     * OSS文件上传组件失败
     */
    OSSFAIL("0040106", "创建文件夹失败"),
    /**
     * 生成图形验证码出错
     */
    VERIFYCODEERROR("0040107", "生成图形验证码出错"),
    /**
     * ES查询失败
     */
    ELASTIC_SEARCH_ERROR("0040108", "ES查询失败"),
    /**
     * ES查询参数错误
     */
    ELASTIC_SEARCH_PARAM_ERROR("0040109", "ES查询参数错误"),

    /**
     * SSL初始化策略失败
     */
    HTTP_SSL_INIT_ERROR("0040110", "SSL初始化策略失败"),

    /**
     * http请求错误
     */
    HTTP_REQUEST_ERROR("0040112", "http请求失败"),

    /**
     * 线程意外中断
     */
    THREAD_INTERRUPT_ERROR("0040113", "线程意外中断"),

    //endregion

    //region DB相关(0040201~0040300)

    /**
     * 添加数据失败
     */
    ADDDATAFAIL("0040201", "添加数据失败"),
    /**
     * 修改数据失败
     */
    MODIFYDATAFAIL("0040202", "修改数据失败"),
    /**
     * 操作数据库失败
     */
    DBFAIL("0040203", "操作数据库失败"),
    /**
     * 未查询到数据
     */
    SEARCHDBFAIL("0040204", "未查询到数据"),
    /**
     * 非法操作
     */
    ILLEGALOPERATION("0040205", "非法操作"),

    //endregion

    //region 请求平台、菠萝派或开放接口(0040301~0040400)

    /**
     * 请求平台失败
     */
    CALLPLATFAIL("0040301", "请求平台失败"),

    //endregion

    // region 枚举相关 (0040401~0040500)

    ENUMFAIL("0040401", "枚举转换失败"),

    ENUMFINDNULL("0040402", "没有找到对应枚举"),
    // endregion

    //region 授权相关(0040501~0040600)

    AUTHORIZATIONERROR("0040501", "授权失败"),

    //endregion

    //region 运维相关(0040601~0040700)
    /**
     * 用户类别错误
     */
    USERTYPEERROR("0040601", "用户类别错误"),
    /**
     * 用户状态错误
     */
    USERSTATUSERROR("0040602", "用户已禁用或未通过审核"),
    /**
     * 用户名密码错误
     */
    USERNAMEANDPASSWORDERROR("0040603", "用户名或密码错误"),
    /**
     * 验证码错误
     */
    VERIFYCODERROR("0040603", "验证码错误"),
    /**
     * 时间戳过期
     */
    ADMINTIMESTAMPEXPIRE("0040605", "时间戳过期"),

    /**
     * 用户名已存在
     */
    USEREXIS("0040606", "用户名或手机号已存在"),

    //endregion

    ;

    /**
     * 构造器
     *
     * @param code        错误码
     * @param description 错误码说明
     */
    private SystemErrorCodes(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 系统级错误码前缀：000
     */
    private static final String PREFIX = "000";
    /**
     * 错误码
     */
    private final String code;
    /**
     * 错误码说明
     */
    private final String description;

    /**
     * 获取错误码
     *
     * @return 错误码
     */
    @Override
    public String getCode() {
        return PREFIX + this.code;
    }

    /**
     * 获取 异常级别值
     *
     * @return 错误码说明
     */
    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public String toString() {
        return this.getCode();
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param code 值
     * @return 对应的枚举
     */
    public static SystemErrorCodes convert(String code) {
        return EnumConvertCacheUtil.convert(SystemErrorCodes.class, code, EnumConvertType.CODE);
    }
}

