package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor.RdsTbRefundConvertor;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.RdsTbRefundOutRequest;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbAfterSaleRdsPageQueryResponse;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbRefundJdpResponseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbRefundRdsDo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbRefundRdsMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝Rds - 退货退款相关操作（jdp_tb_refund）
 *
 * <AUTHOR>
 * @date 2025/3/28 下午1:31
 */
public class RdsTbRefundDomain {
    //region 常量
    /**
     * 会员名
     */
    private final String memberName;
    //endregion

    //region 构造
    public RdsTbRefundDomain(String memberName) {
        this.memberName = memberName;
    }
    //endregion

    /**
     * 查询淘宝退货退款单总数
     *
     * @param request 请求参数
     * @return 淘宝退货退款单总数
     */
    public int queryPolyRefundOrderCount(RdsTbRefundOutRequest request){
        // 查询推送库售后单列表
        if (request == null) {
            return 0;
        }

        TbRefundRdsMapper tbRefundRdsMapper = BeanContextUtil.getBean(TbRefundRdsMapper.class);
        return DBSwitchUtil.doDBWithRds(memberName, () -> tbRefundRdsMapper.getRefundsCountByModified(request.getStartUpdateTime(), request.getEndUpdateTime(), request.getSellNick()));
    }

    /**
     * 查询淘宝退货退款单（转换为菠萝派对象）
     *
     * @param request 请求参数
     * @return 淘宝退货退款单列表
     */
    public TbAfterSaleRdsPageQueryResponse queryPolyRefundOrder(RdsTbRefundOutRequest request) {
        // 基础返回
        TbAfterSaleRdsPageQueryResponse response = new TbAfterSaleRdsPageQueryResponse();
        // 查询推送库售后单列表
        if (request == null) {
            return response;
        }

        // 批量获取推送库数据
        TbRefundRdsMapper tbRefundRdsMapper = BeanContextUtil.getBean(TbRefundRdsMapper.class);
        int pageSize = request.getPageSize();
        int start = (request.getPageIndex() - 1) * pageSize;
        List<TbRefundRdsDo> tbRefundRdsDos = DBSwitchUtil.doDBWithRds(memberName, () -> tbRefundRdsMapper.getRDSRefundOrderDataByModified(request.getStartUpdateTime(), request.getEndUpdateTime(), request.getSellNick(), start, pageSize));

        // 推送库售后单转换
        List<BusinessGetRefundOrderResponseOrderItem> polyRefundOrders = new ArrayList<>();
        for (TbRefundRdsDo tbRefundRdsDo : tbRefundRdsDos) {
            if (StringUtils.isNotEmpty(tbRefundRdsDo.getJdpResponse())) {
                TbRefundJdpResponseDto tbRefundOrder = JsonUtils.deJson(tbRefundRdsDo.getJdpResponse(), TbRefundJdpResponseDto.class);
                BusinessGetRefundOrderResponseOrderItem polyRefundOrder = RdsTbRefundConvertor.convertPolyRefundOrder(tbRefundOrder);
                polyRefundOrder.setRdsCreateTime(tbRefundRdsDo.getJdpCreated());
                polyRefundOrder.setRdsModifyTime(tbRefundRdsDo.getJdpModified());
                polyRefundOrders.add(polyRefundOrder);
            }
        }

        response.setRefunds(polyRefundOrders);
        return response;
    }
}
