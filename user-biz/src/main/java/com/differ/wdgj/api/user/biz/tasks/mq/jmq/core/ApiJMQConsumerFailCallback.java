package com.differ.wdgj.api.user.biz.tasks.mq.jmq.core;

import com.differ.jackyun.framework.component.jmq.core.consumer.JMQMessage;
import com.differ.jackyun.framework.component.jmq.core.consumer.retry.ConsumerFailCallback;

/**
 * JMQ消息消费重试达到最大次数回调处理
 *
 * 1.当消息重试后失败回调不是异常回调
 * 2.默认立即重试一次或设置了消费者最大重试次数达到最大重试次数会调用该方法
 *
 * <AUTHOR>
 * @since 2023-07-20 17:56:00
 */
public class ApiJMQConsumerFailCallback implements ConsumerFailCallback<String> {

    /**
     * 消费失败回调
     *
     * @param message 消息通知
     */
    @Override
    public void failCallback(JMQMessage<String> message) {
    }
}
