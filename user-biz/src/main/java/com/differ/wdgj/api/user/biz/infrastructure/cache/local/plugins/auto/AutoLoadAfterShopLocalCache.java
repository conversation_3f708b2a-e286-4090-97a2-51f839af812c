package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.auto;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.auto.AutoLoadAfterShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.AutoLoadAfterSaleAllShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.AutoLoadAfterSaleShopDto;
import jodd.util.MathUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 自动下载售后单店铺-内存缓存
 *
 * <AUTHOR>
 * @date 2024/9/18 下午2:10
 */
public class AutoLoadAfterShopLocalCache extends AbstractLocalCache<String, List<AutoLoadAfterSaleShopDto>> {
    //region 构造
    private AutoLoadAfterShopLocalCache() {
        expire = MathUtil.randomInt(600, 1200);
        timeUnit = TimeUnit.SECONDS;
        // 最大数据个数
        cacheMaxSize = 10000;
    }
    //endregion

    //region 单例

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static AutoLoadAfterShopLocalCache singleton() {
        return AutoLoadAfterShopLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AutoLoadAfterShopLocalCache instance;

        private SingletonEnum() {
            instance = new AutoLoadAfterShopLocalCache();
        }
    }

    //endregion

    //region 实现基类逻辑
    @Override
    protected List<AutoLoadAfterSaleShopDto> loadSource(String member) {
        AutoLoadAfterSaleAllShopDto autoLoadAfterSaleAllShop = AutoLoadAfterShopCache.singleton().getAndSyncIfAbsent(member);
        if(autoLoadAfterSaleAllShop != null && CollectionUtils.isNotEmpty(autoLoadAfterSaleAllShop.getAutoLoadAfterSaleShops())){
            return autoLoadAfterSaleAllShop.getAutoLoadAfterSaleShops();
        }
        return Collections.emptyList();
    }
    //endregion

    //region 公共方法
    /**
     * 会员开启自动任务
     *
     * @return 会员开启自动任务
     */
    public boolean enableAutoTask(String memberName) {
        try {
            // 查询缓存
            List<AutoLoadAfterSaleShopDto> autoShops = this.getCacheThenSource(memberName);
            return CollectionUtils.isNotEmpty(autoShops);
        } catch (Exception ex) {
            LogFactory.error("自动下载售后单店铺", String.format("【%s】判断会员开启自动任务异常", memberName), ex);
            return false;
        }
    }
    //endregion
}
