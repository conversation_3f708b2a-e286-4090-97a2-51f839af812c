-- 支持过期的hash批量更新
-- User: chuff
-- Date: 2021/11/29 14:25
--

local cacheKey = KEYS[1]
-- 缓存键过期时间
local cacheKeyExpire = tonumber(ARGV[1])
-- 当前时间
local currentTime = tonumber(ARGV[2])

-- 缓存键的key后缀
local cacheKeyTail = math.floor(currentTime / cacheKeyExpire)

local cacheKeyWithTail = cacheKey .. cacheKeyTail
local cacheKeyWithBefore = cacheKey .. (cacheKeyTail - 1)

-- 数据格式:{时间戳内容}#{数据版本内容}#{数据内容}
local function toHashValue(timestamp, version, data)
    return string.format("%s#%s#%s", timestamp, version, data)
end

local len = #ARGV
local ret = 0
for i = 3, len, 4 do
    -- 同参数对应取值
    local hashField = ARGV[i]
    local hashFieldExpire = tonumber(ARGV[i + 1])
    local realValue = ARGV[i + 2]
    local newVersion = ARGV[i + 3]
    local newTimestamp = currentTime + hashFieldExpire;
    -- 循环更新
    redis.call("hdel", cacheKeyWithBefore, hashField)
    ret = ret + redis.call("hset", cacheKeyWithTail, hashField, toHashValue(newTimestamp,newVersion,realValue))
end
redis.call("expire", cacheKeyWithTail, cacheKeyExpire)
return ret