package com.differ.wdgj.api.component.redis.limtier;

import com.differ.wdgj.api.component.redis.MultiRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.List;

/**
 * redis限流抽象类
 * <AUTHOR>
 * @date 2021/6/16 11:29
 */
public abstract class AbstractRedisRateLimiter implements RedisRateLimiter {

    protected DefaultRedisScript<Integer> redisScript;

    protected DefaultRedisScript<List> script;

    protected MultiRedis multiRedis;

    protected Logger log = LoggerFactory.getLogger(AbstractRedisRateLimiter.class);

    public AbstractRedisRateLimiter(MultiRedis multiRedis) {
        this.multiRedis = multiRedis;
    }

    @Deprecated
    protected void initScript(String luaUrl) {
        redisScript = new DefaultRedisScript();
        redisScript.setLocation(new ClassPathResource(luaUrl));
        redisScript.setResultType(Integer.class);
    }

    protected void initRedisScript(String luaUrl) {
        script = new DefaultRedisScript();
        script.setLocation(new ClassPathResource(luaUrl));
        script.setResultType(List.class);
    }

    /**
     * 限流方法,请求个数为1(限流关键字)
     *
     * @param key 限流关键字
     * @return 允许通过的请求个数，0：不通过，大于0表示通过
     */
    @Override
    public int acquire(String key) {
        return acquire(key, 1);
    }

    /**
     * 限流方法(限流关键字,请求个数)
     *
     * @param key          限流关键字
     * @param requestCount 请求个数
     * @return 允许通过的请求个数，0：不通过，大于0表示通过
     */
    @Override
    public int acquire(String key, int requestCount) {
        if (redisScript == null) {
            throw new RedisComponentException("redis限流脚本未初始化");
        }
        String permitsCount = String.valueOf(requestCount);
        // 调用Redis
        Object acquired = multiRedis.executeScript(key,
                redisScript,
                // Lua脚本中的Key列表
                getKeys(key),
                // Lua脚本args列表
                getArgs(permitsCount)
        );
        log.debug("限流请求:{},{},返回：{}", key, requestCount, acquired);
        return Integer.parseInt(acquired.toString());
    }

    @Override
    public LimitResult checkLimit(String key, int requestCount) {

        if (script == null) {
            throw new RedisComponentException("redis限流脚本未初始化");
        }
        String permitsCount = String.valueOf(requestCount);
        // 调用Redis
        Object acquired = multiRedis.executeScript(key,
                script,
                // Lua脚本中的Key列表
                getKeys(key),
                // Lua脚本args列表
                getArgs(permitsCount)
        );

        List<?> listResult = (List<?>) acquired;
        if (listResult != null && listResult.size() == 2) {
            String passCount = listResult.get(0).toString();
            String available = listResult.get(1).toString();
            log.debug("限流请求:{},{},返回：{},{}", key, requestCount, passCount, available);
            return LimitResult.of(Integer.parseInt(passCount) <= 0, Integer.valueOf(available));
        }
        return LimitResult.of(false, -1);
    }

    /**
     * 获取脚本的keys参数
     *
     * @param key
     * @return
     */
    protected abstract List<String> getKeys(String key);

    /**
     * 获取脚本的args参数
     *
     * @param permitsCount
     * @return
     */
    protected abstract List<String> getArgs(String permitsCount);
}
