package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.DefaultWorkMutex;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;

/**
 * 售后下载任务互斥
 *
 * <AUTHOR>
 * @date 2024/9/30 下午4:05
 */
public class LoadAfterSaleWorkMutex extends DefaultWorkMutex {
    /**
     * 创建任务
     *
     * @param workData    工作数据
     * @param dataOperate 数据操作工具
     * @return 创建结果
     */
    @Override
    public CreateResult create(WorkData<?> workData, WorkDataOperate dataOperate) {
        // 基础数据
        WorkContext workContext = workData.getWorkContext();
        AfterSaleLoadArgs args = (AfterSaleLoadArgs)workData.getData();

        // 消息推送不互斥
        if(args != null){
            AfterSaleLoadTypeEnum loadType = args.getLoadType();
            TriggerTypeEnum triggerType = workContext.getTriggerType();
            if(triggerType == TriggerTypeEnum.MESSAGE_NOTIFICATION && loadType == AfterSaleLoadTypeEnum.AFTER_SALE_NO){
                return dataOperate.create(workData);
            }
        }
        // 基类创建逻辑
        CreateResult superCreateResult = super.create(workData, dataOperate);
        if(superCreateResult != null){
            return superCreateResult;
        }
        // 超时待执行任务检测，超时时间为0代表不超时
        int timeoutPeriod = LoadAfterSaleConfigKeyUtils.getWorkTaskUnRunTimeoutPeriod();
        if (timeoutPeriod > 0 && dataOperate.existsUnRunTimeout(workData, timeoutPeriod)) {
            return dataOperate.create(workData);
        }
        
        return null;
    }
}
