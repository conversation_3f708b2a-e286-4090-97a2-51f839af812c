package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.GloSplitServiceDto;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菠萝派网关配置内存缓存
 *
 * <AUTHOR>
 * @date 2024/12/19 下午5:48
 */
@Ignore
public class CallSplitServiceLocalCacheTest {
    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     */
    @Test
    public void loadSourceTest(){
        String str = "[{\"splitserviceid\":290,\"categoryid\":158,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"199\",\"methodvalue\":\"9998\",\"aliyunurl\":\"http://aliyun.polyapi.com/openapi/do\",\"jdurl\":\"http://aliyun.polyapi.com/openapi/do\",\"priority\":100000,\"isenable\":true,\"remark\":\"独立商城java没有维护\",\"createtime\":\"2021-11-26T16:10:15\",\"lastmodifytime\":\"2024-10-28T10:22:04\",\"dataversion\":\"2021/11/26 16:10:15\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":126,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"57#156#160#1041#1045#1049#1050#1051#1052#1053#1054#1055#1056#1058#1060#1061#1062#1064#1072#1077#1082#1084#1088#1095#1097#1131#1135#1142#1147#1149#1155#1168#1185#1196\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"JAVA正式(记录7)\",\"createtime\":\"2020-09-02T18:22:17\",\"lastmodifytime\":\"2024-12-19T17:08:46\",\"dataversion\":\"2020/9/2 18:22:17\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":272,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"110\",\"methodvalue\":\"10\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"pry\",\"createtime\":\"2021-04-22T10:08:32\",\"lastmodifytime\":\"2021-05-28T14:30:15\",\"dataversion\":\"2021/4/22 10:08:32\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":336,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"2\",\"methodvalue\":\"9996\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100000,\"isenable\":true,\"remark\":\"京东获取sessionkey转java正式\",\"createtime\":\"2024-01-23T10:23:46\",\"lastmodifytime\":\"2024-02-04T11:03:18\",\"dataversion\":\"2024/1/23 10:23:46\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":294,\"categoryid\":158,\"func\":0,\"outaccount\":\"yisheng123\",\"platvalue\":\"94\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://aliyun.polyapi.com/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com/online/openapi/do\",\"priority\":500,\"isenable\":true,\"remark\":\"国美自营测试\",\"createtime\":\"2022-03-14T11:43:42\",\"lastmodifytime\":\"2024-10-28T10:23:05\",\"dataversion\":\"2022/3/14 11:43:42\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":268,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"74\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":120,\"isenable\":true,\"remark\":\"田野 有赞测试\",\"createtime\":\"2021-04-12T10:31:47\",\"lastmodifytime\":\"2021-04-27T09:49:54\",\"dataversion\":\"2021/4/12 10:31:47\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":293,\"categoryid\":157,\"func\":0,\"outaccount\":\"zg05\",\"platvalue\":\"1\",\"methodvalue\":\"10#13#25#26#31\",\"aliyunurl\":\"http://api.polyapi.com:30000/openapi/do\",\"jdurl\":\"http://api.polyapi.com:30000/openapi/do\",\"priority\":1000000,\"isenable\":false,\"remark\":\"天猫优仓测试\",\"createtime\":\"2022-01-18T11:54:08\",\"lastmodifytime\":\"2024-08-28T10:52:30\",\"dataversion\":\"2022/1/18 11:54:08\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":61,\"categoryid\":164,\"func\":0,\"outaccount\":\"airice#blackdos#***********\",\"platvalue\":\"198\",\"methodvalue\":\"63#64#66#68\",\"aliyunurl\":\"http://39.100.133.171:30005/OpenAPI/do\",\"jdurl\":\"http://39.100.133.171:30005/OpenAPI/do\",\"priority\":10200,\"isenable\":false,\"remark\":\"airice菠萝派商城消息订阅，需要保证订阅接口优先取到该配置值\",\"createtime\":\"2020-08-17T16:01:16\",\"lastmodifytime\":\"2024-09-02T11:31:38\",\"dataversion\":\"2020/8/17 16:01:16\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":324,\"categoryid\":157,\"func\":0,\"outaccount\":\"***********#hangchuan\",\"platvalue\":\"47\",\"methodvalue\":\"13\",\"aliyunurl\":\"http://api.polyapi.com:30000/openapi/do\",\"jdurl\":\"http://api.polyapi.com:30000/openapi/do\",\"priority\":1000,\"isenable\":false,\"remark\":\"拼多多下载商品功能灰度\",\"createtime\":\"2023-05-26T09:41:35\",\"lastmodifytime\":\"2024-08-29T10:01:52\",\"dataversion\":\"2023/5/26 9:41:35\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":62,\"categoryid\":164,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"ALL\",\"methodvalue\":\"63#64#66#68\",\"aliyunurl\":\"http://39.98.97.253/OpenAPI/do\",\"jdurl\":\"http://39.98.97.253/OpenAPI/do\",\"priority\":10000,\"isenable\":true,\"remark\":\"消息订阅，需要保证订阅接口优先取到该配置值\",\"createtime\":\"2020-08-17T16:02:20\",\"lastmodifytime\":\"2021-01-27T14:23:02\",\"dataversion\":\"2020/8/17 16:02:20\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":291,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"176#1016\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://apijd.polyapi.com/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com/openapi/do\",\"priority\":200,\"isenable\":true,\"remark\":\"京东印尼2.0接口测试\",\"createtime\":\"2021-12-09T14:47:54\",\"lastmodifytime\":\"2024-09-02T11:38:46\",\"dataversion\":\"2021/12/9 14:47:54\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":326,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"114#126\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"https://doudian.polyapi.com/openapi/do\",\"jdurl\":\"https://doudian.polyapi.com/openapi/do\",\"priority\":10000,\"isenable\":false,\"remark\":\"抖店走https接口\",\"createtime\":\"2023-06-26T14:18:12\",\"lastmodifytime\":\"2023-08-04T09:05:28\",\"dataversion\":\"2023/6/26 14:18:12\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":173,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"ALL\",\"methodvalue\":\"10006\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"同步店铺状态\",\"createtime\":\"2020-12-15T20:51:04\",\"lastmodifytime\":\"2024-08-29T10:37:08\",\"dataversion\":\"2020/12/15 20:51:04\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":193,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1#9#47#137#1068\",\"methodvalue\":\"9993#9995#9997#9998\",\"aliyunurl\":\"http://10.0.16.24/OpenAPI/do\",\"jdurl\":\"http://116.196.111.7/OpenAPI/do\",\"priority\":500,\"isenable\":true,\"remark\":\"donet转java平台的授权必须在donet的在聚合环境的这里配置下平台-wt\",\"createtime\":\"2021-01-05T10:36:44\",\"lastmodifytime\":\"2024-05-06T10:15:18\",\"dataversion\":\"2021/1/5 10:36:44\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":277,\"categoryid\":157,\"func\":0,\"outaccount\":\"dpbag\",\"platvalue\":\"2\",\"methodvalue\":\"9995\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"临时转发\",\"createtime\":\"2021-09-18T18:29:13\",\"lastmodifytime\":\"2021-09-18T18:29:13\",\"dataversion\":\"2021/9/18 18:29:13\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":100,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"39#108#112#135#138#144#145#148#149#152#153#154#159#162#163#164#165#166#167#169#170#171#174#175#176#177#178#179#180#181#182#183#185#186#187#189#190#191#192#194#195#1000#1001#1003#1004#1005#1006#1007#1009#1010#1012#1013#1014#1015#1017#1018#1019#1020#1021#1023#1024#1025#1026#1029#1030#1034#1092#1110\",\"methodvalue\":\"9993#9995#9996#9998#10001\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":30,\"isenable\":true,\"remark\":\"java正式-平台授权记录\",\"createtime\":\"2020-08-19T11:35:54\",\"lastmodifytime\":\"2021-12-08T19:04:00\",\"dataversion\":\"2020/8/19 11:35:54\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":206,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"82\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"java平台迁移-记录3\",\"createtime\":\"2021-01-12T09:53:46\",\"lastmodifytime\":\"2021-03-30T13:53:00\",\"dataversion\":\"2021/1/12 9:53:46\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":339,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"13\",\"methodvalue\":\"63#66#68\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\\t\",\"priority\":10001,\"isenable\":true,\"remark\":\"需要走聚合java环境订阅的平台\",\"createtime\":\"2024-03-28T10:26:26\",\"lastmodifytime\":\"2024-03-28T10:26:26\",\"dataversion\":\"2024/3/28 10:26:26\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":182,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"12#17#22#30#41#46#48#50#58#65#97#105#111#114#117#123#124#126#128#129#143#1047#1065#1083#1090#1092#1111\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"java平台配置-记录3\",\"createtime\":\"2020-12-24T09:37:00\",\"lastmodifytime\":\"2021-08-24T14:06:46\",\"dataversion\":\"2020/12/24 9:37:00\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":341,\"categoryid\":157,\"func\":0,\"outaccount\":\"haigaojiancai#yys2015\",\"platvalue\":\"2\",\"methodvalue\":\"22#55#231\",\"aliyunurl\":\"https://apijd.polyapi.com/openapi/do\",\"jdurl\":\"https://apijd.polyapi.com/openapi/do\",\"priority\":********,\"isenable\":false,\"remark\":\"京东https网关测试\",\"createtime\":\"2024-08-21T11:57:47\",\"lastmodifytime\":\"2024-08-28T10:51:22\",\"dataversion\":\"2024/8/21 11:57:47\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":148,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"5#9#15#28#31#37#55#69#79#89#95#99#113#139#142#145#178#181#182#183#185#186#187#189#190#191#192#194#195#1000#1001#1003#1004#1005#1006#1007#1009#1010#1013#1014#1015#1017#1018#1020#1021#1023#1024#1025#1026#1029#1031#1038#1042#1057#1059#1070#1076\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":30,\"isenable\":true,\"remark\":\"java平台配置-记录2(已满)\",\"createtime\":\"2020-10-27T12:05:57\",\"lastmodifytime\":\"2021-03-24T09:48:57\",\"dataversion\":\"2020/10/27 12:05:57\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":95,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"6#33#54#66#81#84#95#104#107#118#123#1164#1171#1202#1203#1208\",\"methodvalue\":\"10#13#14#22#25#26#31#55\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":50,\"isenable\":true,\"remark\":\"java正式环境(记录4)\",\"createtime\":\"2020-08-19T11:07:47\",\"lastmodifytime\":\"2024-12-13T09:43:41\",\"dataversion\":\"2020/8/19 11:07:47\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":342,\"categoryid\":157,\"func\":0,\"outaccount\":\"shxpt#haigaojiancai#yys2015\",\"platvalue\":\"2\",\"methodvalue\":\"10#13#14#22#25#26#31#10001#55#231\",\"aliyunurl\":\"https://apijd.polyapi.com/openapi/do\",\"jdurl\":\"https://apijd.polyapi.com/openapi/do\",\"priority\":********,\"isenable\":true,\"remark\":\"京东https接口\\n替换原有 http://apijd.polyapi.com/openapi/do  拆分服务ID=157\\n\",\"createtime\":\"2024-08-21T16:32:37\",\"lastmodifytime\":\"2024-08-28T10:52:13\",\"dataversion\":\"2024/8/21 16:32:37\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":340,\"categoryid\":157,\"func\":0,\"outaccount\":\"wasd262\",\"platvalue\":\"2\",\"methodvalue\":\"13#14#22#26#31#10001#55#231\",\"aliyunurl\":\" https://apijd.polyapi.com/openapi/do\",\"jdurl\":\" https://apijd.polyapi.com/openapi/do\",\"priority\":********,\"isenable\":false,\"remark\":\"京东https网关灰度\",\"createtime\":\"2024-08-20T15:37:29\",\"lastmodifytime\":\"2024-08-21T15:48:30\",\"dataversion\":\"2024/8/20 15:37:29\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":338,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"52\",\"methodvalue\":\"62\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"诚信通转发接口\",\"createtime\":\"2024-03-21T13:19:58\",\"lastmodifytime\":\"2024-03-21T13:19:58\",\"dataversion\":\"2024/3/21 13:19:58\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":174,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"52\",\"methodvalue\":\"10#13#14#22#25#26#31#9994#9995#9996#9999#10001\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"诚信通-wt-pwj\",\"createtime\":\"2020-12-17T10:06:16\",\"lastmodifytime\":\"2022-10-08T14:49:19\",\"dataversion\":\"2020/12/17 10:06:16\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":299,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"47\",\"methodvalue\":\"9998#10001\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":501,\"isenable\":true,\"remark\":\"拼多多账号同步，方舟原因需要切换到聚合的java环境\",\"createtime\":\"2022-08-12T16:22:00\",\"lastmodifytime\":\"2022-10-11T16:15:22\",\"dataversion\":\"2022/8/12 16:22:00\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":111,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"63\",\"methodvalue\":\"10#13#14#22#25#26#31\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":50,\"isenable\":true,\"remark\":\"分期乐java正式\",\"createtime\":\"2020-08-19T11:49:58\",\"lastmodifytime\":\"2021-03-23T09:13:24\",\"dataversion\":\"2020/8/19 11:49:58\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":344,\"categoryid\":157,\"func\":0,\"outaccount\":\"daerwei\",\"platvalue\":\"73\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":150,\"isenable\":true,\"remark\":\"JF202409240144 验证切java功能是否正常 2024-09-26前删除\",\"createtime\":\"2024-09-24T15:40:55\",\"lastmodifytime\":\"2024-09-24T15:41:33\",\"dataversion\":\"2024/9/24 15:40:55\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":343,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"176#1016#1105\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"https://apijd.polyapi.com/openapi/do\",\"jdurl\":\"https://apijd.polyapi.com/openapi/do\",\"priority\":201,\"isenable\":false,\"remark\":\"京东https地址替换\\n替换地址：http://apijd.polyapi.com/openapi/do \\n原拆分服务ID=291,317\",\"createtime\":\"2024-08-28T10:56:32\",\"lastmodifytime\":\"2024-09-02T11:38:35\",\"dataversion\":\"2024/8/28 10:56:32\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":203,\"categoryid\":169,\"func\":0,\"outaccount\":\"wdgj2016\",\"platvalue\":\"1059\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://differcom.gnway.cc:33114/openapi/do\",\"jdurl\":\"http://differcom.gnway.cc:33114/openapi/do\",\"priority\":2000,\"isenable\":false,\"remark\":\"小鹅拼拼\\n\\n地址无法访问，进行关闭\",\"createtime\":\"2021-01-11T16:23:43\",\"lastmodifytime\":\"2024-08-29T10:46:21\",\"dataversion\":\"2021/1/11 16:23:43\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":114,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"82\",\"methodvalue\":\"26#30#34#45#55\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":90,\"isenable\":true,\"remark\":\"省仓\\nhsl666#zrxl#gzmksw#zmsm#shanghaijica#jx682admin#youlibang#guoben#mashairi2015#ybl1856s#56xyy#lpstqjd#wonderful#fanxi1688#larastyle#wenting#***********#oxila\",\"createtime\":\"2020-08-20T09:50:38\",\"lastmodifytime\":\"2021-06-21T10:03:59\",\"dataversion\":\"2020/8/20 9:50:38\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":322,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"114#126\",\"methodvalue\":\"9998\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\\t\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\\t\",\"priority\":10001,\"isenable\":true,\"remark\":\"抖店授权同步账号转到阿里环境\",\"createtime\":\"2023-03-06T11:25:07\",\"lastmodifytime\":\"2023-03-06T11:32:54\",\"dataversion\":\"2023/3/6 11:25:07\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":278,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"110\",\"methodvalue\":\"ALL\",\"aliyunurl\":\" http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":9999,\"isenable\":true,\"remark\":\"shopee2.0接口测试\",\"createtime\":\"2021-10-12T16:30:26\",\"lastmodifytime\":\"2022-04-18T09:42:58\",\"dataversion\":\"2021/10/12 16:30:26\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":296,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"198\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100000,\"isenable\":true,\"remark\":\"聚合测试java接口\",\"createtime\":\"2022-06-21T17:07:19\",\"lastmodifytime\":\"2022-06-30T16:25:11\",\"dataversion\":\"2022/6/21 17:07:19\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":289,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"ALL\",\"methodvalue\":\"9998\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":499,\"isenable\":true,\"remark\":\"平台账号同步\",\"createtime\":\"2021-11-24T11:53:34\",\"lastmodifytime\":\"2022-03-10T09:45:34\",\"dataversion\":\"2021/11/24 11:53:34\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":288,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1127\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://api.polyapi.com:30000/openapi/do\",\"jdurl\":\"http://api.polyapi.com:30000/openapi/do\",\"priority\":1000,\"isenable\":false,\"remark\":\"易久批\",\"createtime\":\"2021-11-22T20:46:56\",\"lastmodifytime\":\"2024-08-29T10:03:19\",\"dataversion\":\"2021/11/22 20:46:56\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":224,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"73\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://api.polyapi.com:30000/openapi/do\",\"jdurl\":\"http://api.polyapi.com:30000/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"TST - pry\",\"createtime\":\"2021-01-15T13:44:53\",\"lastmodifytime\":\"2021-01-15T13:44:53\",\"dataversion\":\"2021/1/15 13:44:53\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":323,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1008\",\"methodvalue\":\"9997\",\"aliyunurl\":\"http://api.polyapi.com:30000/openapi/do\",\"jdurl\":\"http://api.polyapi.com:30000/openapi/do\",\"priority\":10023,\"isenable\":false,\"remark\":\"快手小店修复店铺授权\",\"createtime\":\"2023-05-14T20:30:47\",\"lastmodifytime\":\"2024-08-29T10:02:15\",\"dataversion\":\"2023/5/14 20:30:47\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":250,\"categoryid\":158,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"ALL\",\"methodvalue\":\"68\",\"aliyunurl\":\"http://testtrace.polyapi.com/openapi/do\",\"jdurl\":\"http://testtrace.polyapi.com/openapi/do\",\"priority\":20000,\"isenable\":true,\"remark\":\"查询店铺订阅\",\"createtime\":\"2021-01-28T09:30:46\",\"lastmodifytime\":\"2021-01-28T09:30:46\",\"dataversion\":\"2021/1/28 9:30:46\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":287,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1110\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com/online/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"营流宝要走java\",\"createtime\":\"2021-11-19T15:46:18\",\"lastmodifytime\":\"2021-11-19T15:59:47\",\"dataversion\":\"2021/11/19 15:46:18\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":245,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":120,\"isenable\":true,\"remark\":\"java淘宝-所有会员转发记录\",\"createtime\":\"2021-01-26T09:29:00\",\"lastmodifytime\":\"2021-04-27T13:53:19\",\"dataversion\":\"2021/1/26 9:29:00\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":265,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"161\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":120,\"isenable\":true,\"remark\":\"田野 有赞零售 迁移测试\\nbaskincake\",\"createtime\":\"2021-03-18T15:43:16\",\"lastmodifytime\":\"2021-05-19T10:46:40\",\"dataversion\":\"2021/3/18 15:43:16\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":56,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"82\",\"methodvalue\":\"10#13#21#22#25#31#33#9993#9995#9996#9998#65\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":90,\"isenable\":true,\"remark\":\"唯品会JIT转java-xgy-xbl\",\"createtime\":\"2020-08-13T13:17:01\",\"lastmodifytime\":\"2021-06-01T14:25:48\",\"dataversion\":\"2020/8/13 13:17:01\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":175,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1068\",\"methodvalue\":\"9993#9995#9998\",\"aliyunurl\":\"http://online.polyapi.com/OpenApi/do\",\"jdurl\":\"http://online.polyapi.com/OpenApi/do\",\"priority\":1000,\"isenable\":false,\"remark\":\"零售通账号同步走.net\",\"createtime\":\"2020-12-17T11:21:44\",\"lastmodifytime\":\"2024-09-02T11:39:32\",\"dataversion\":\"2020/12/17 11:21:44\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":321,\"categoryid\":157,\"func\":0,\"outaccount\":\"***********\",\"platvalue\":\"47\",\"methodvalue\":\"10\",\"aliyunurl\":\"http://api.polyapi.com:30000/openapi/do\",\"jdurl\":\"http://api.polyapi.com:30000/openapi/do\",\"priority\":99999,\"isenable\":false,\"remark\":\"拼多多抓单灰度-zyj\",\"createtime\":\"2023-02-14T10:17:27\",\"lastmodifytime\":\"2024-08-29T10:00:53\",\"dataversion\":\"2023/2/14 10:17:27\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":313,\"categoryid\":162,\"func\":0,\"outaccount\":\"hunheyuntest\",\"platvalue\":\"1\",\"methodvalue\":\"9998\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":1000,\"isenable\":false,\"remark\":\"淘宝账号同步到java环境，测试 wt zhangyuanjie\",\"createtime\":\"2022-11-14T11:29:48\",\"lastmodifytime\":\"2024-09-02T11:39:10\",\"dataversion\":\"2022/11/14 11:29:48\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":93,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"115#121#132#134#140#1035#1036#1041#1067#1068#1069#1071#1073#1074#1075#1077#1078#1079#1080#1086#1099#1100#1115#1119#1121#1128#1133#1139#1140#1141#1143#1153#1158#1171#1174#1182#1198\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":50,\"isenable\":true,\"remark\":\"java正式环境(记录5) 已满\\n\",\"createtime\":\"2020-08-19T11:04:53\",\"lastmodifytime\":\"2023-12-28T15:13:16\",\"dataversion\":\"2020/8/19 11:04:53\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":320,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"114#126\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://doudian.polyapi.com/openapi/do\",\"jdurl\":\"http://180.184.90.23/openapi/do\",\"priority\":9999,\"isenable\":true,\"remark\":\"抖店上云灰度\",\"createtime\":\"2023-02-13T10:35:27\",\"lastmodifytime\":\"2023-08-01T13:29:20\",\"dataversion\":\"2023/2/13 10:35:27\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":112,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"3#5#10#13#18#19#21#23#32#34#35#39#44#48#49#56#57#59#60#61#62#64#67#68#70#71#77#78#83#85#91#98#102#103#108#109#112#125#133#135#137#138#141#144#148#149#150#152#153#154#155#159#162#163#164#165#166#167#169#170#171#173#174#175#177#179#180#184#188#193#196#1002#1008#1011#1012#1019#1030#1033#1034#1040\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":30,\"isenable\":true,\"remark\":\"java平台配置-记录1(已满)\\n\",\"createtime\":\"2020-08-19T11:57:06\",\"lastmodifytime\":\"2021-03-24T16:23:12\",\"dataversion\":\"2020/8/19 11:57:06\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":311,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"47\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":106,\"isenable\":true,\"remark\":\"拼多多方舟切换\",\"createtime\":\"2022-08-20T10:08:51\",\"lastmodifytime\":\"2022-08-20T10:08:51\",\"dataversion\":\"2022/8/20 10:08:51\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":232,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"158\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":120,\"isenable\":true,\"remark\":\"庄子遥 美团外卖\",\"createtime\":\"2021-01-21T10:57:07\",\"lastmodifytime\":\"2021-03-23T09:18:02\",\"dataversion\":\"2021/1/21 10:57:07\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":82,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1016\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://apijd.polyapi.com/online/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com/online/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"京东泰国\",\"createtime\":\"2020-08-19T10:38:17\",\"lastmodifytime\":\"2021-06-21T09:58:06\",\"dataversion\":\"2020/8/19 10:38:17\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":312,\"categoryid\":157,\"func\":0,\"outaccount\":\"ryb168#ryb168■1\",\"platvalue\":\"2\",\"methodvalue\":\"13#26#327\",\"aliyunurl\":\"http://apijd.polyapi.com:30005/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com:30005/openapi/do\",\"priority\":1000000,\"isenable\":true,\"remark\":\"京东自营下载商品切灰度\",\"createtime\":\"2022-10-28T16:43:32\",\"lastmodifytime\":\"2022-10-28T17:56:33\",\"dataversion\":\"2022/10/28 16:43:32\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":110,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"6#33#81#84#95#104#1039#1045#1048#1049#1050#1051#1052#1053#1055#1056#1058#1060#1061#1062#1066#1108#1120#1127#1130#1136#1138#1152#1156#1160#1163#1164#1166#1173#1177#1179#1184#1186#1187#1188#1196#1210#1218#1221#1233\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":30,\"isenable\":true,\"remark\":\"java正式环境-记录6\",\"createtime\":\"2020-08-19T11:48:51\",\"lastmodifytime\":\"2024-12-16T15:27:59\",\"dataversion\":\"2020/8/19 11:48:51\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":319,\"categoryid\":157,\"func\":0,\"outaccount\":\"pl2019\",\"platvalue\":\"119\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"新蛋商城转java测试\",\"createtime\":\"2023-02-03T09:14:08\",\"lastmodifytime\":\"2023-02-03T09:14:08\",\"dataversion\":\"2023/2/3 9:14:08\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":60,\"categoryid\":164,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"137\",\"methodvalue\":\"63#64#66#68\",\"aliyunurl\":\"http://39.100.133.171:30005/OpenAPI/do\",\"jdurl\":\"http://39.100.133.171:30005/OpenAPI/do\",\"priority\":10500,\"isenable\":false,\"remark\":\"名融秀购商城 消息订阅网关，\\n需要保证订阅接口优先取到该配置值\",\"createtime\":\"2020-08-17T15:59:17\",\"lastmodifytime\":\"2024-09-02T11:31:45\",\"dataversion\":\"2020/8/17 15:59:17\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":292,\"categoryid\":157,\"func\":0,\"outaccount\":\"jiugou\",\"platvalue\":\"198\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://online.polyapi.com/OpenApi/do\",\"jdurl\":\"http://online.polyapi.com/OpenApi/do\",\"priority\":100,\"isenable\":false,\"remark\":\"对方平台改了DNS，需要聚合转到灰度\",\"createtime\":\"2022-01-17T19:11:28\",\"lastmodifytime\":\"2024-08-26T14:09:41\",\"dataversion\":\"2022/1/17 19:11:28\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":325,\"categoryid\":157,\"func\":0,\"outaccount\":\"hunheyuntest#bj87389906#xudali#wp19810523#h99999y#bjmyn2013#foxfujun#hnxx#***********#youbeiaier#991312xi#rollingbaby#ndy888#futurekick#*********#shijian#luciatacci#hunter#sykj18189418#wdgj21#voilshop#junesam#yunqi#waiwailife#qw97hu#enenxiong#qstexpress#wangsheng1\",\"platvalue\":\"114#126\",\"methodvalue\":\"10\",\"aliyunurl\":\"https://doudian.polyapi.com/openapi/do\",\"jdurl\":\"https://doudian.polyapi.com/openapi/do\",\"priority\":10001,\"isenable\":true,\"remark\":\"https测试\",\"createtime\":\"2023-06-13T11:15:52\",\"lastmodifytime\":\"2023-08-04T09:05:16\",\"dataversion\":\"2023/6/13 11:15:52\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":261,\"categoryid\":162,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"121\",\"methodvalue\":\"26\",\"aliyunurl\":\"http://10.0.88.117:30003/openapi/do\",\"jdurl\":\"http://10.0.88.117:30003/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"海拍客-java库存同步测试  alima#***********#modomoma\",\"createtime\":\"2021-03-08T14:10:35\",\"lastmodifytime\":\"2021-04-26T11:54:04\",\"dataversion\":\"2021/3/8 14:10:35\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":157,\"categoryid\":165,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"2\",\"methodvalue\":\"10#13#14#22#25#26#31#10001#55#231\",\"aliyunurl\":\"http://apijd.polyapi.com/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com/openapi/do\",\"priority\":100,\"isenable\":true,\"remark\":\"京东转java测试-wt-pry-记录1\\n\",\"createtime\":\"2020-11-23T14:24:16\",\"lastmodifytime\":\"2024-02-27T12:23:19\",\"dataversion\":\"2020/11/23 14:24:16\",\"cachehashkey\":\"*************\"},{\"splitserviceid\":317,\"categoryid\":157,\"func\":0,\"outaccount\":\"ALL\",\"platvalue\":\"1105\",\"methodvalue\":\"ALL\",\"aliyunurl\":\"http://apijd.polyapi.com/openapi/do\",\"jdurl\":\"http://apijd.polyapi.com/openapi/do\",\"priority\":101,\"isenable\":true,\"remark\":\"京东全渠道\\n替换完成，停用 替换id=343\",\"createtime\":\"2022-12-01T15:12:47\",\"lastmodifytime\":\"2024-08-28T10:58:04\",\"dataversion\":\"2022/12/1 15:12:47\",\"cachehashkey\":\"*************\"}]";
        List<GloSplitServiceDto> allCallSplitServices = JsonUtils.deJson(str, new TypeReference<List<GloSplitServiceDto>>(){});
        // 启用的菠萝派网关配置
        List<GloSplitServiceDto> enableCallSplitServices = allCallSplitServices.stream().filter(GloSplitServiceDto::isEnable).collect(Collectors.toList());
        // 转换为有序集合
        enableCallSplitServices.sort(Comparator.comparingInt(GloSplitServiceDto::getPriority).reversed());
    }
}
