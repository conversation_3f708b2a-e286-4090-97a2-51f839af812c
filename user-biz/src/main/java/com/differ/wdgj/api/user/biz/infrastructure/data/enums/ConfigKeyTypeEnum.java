package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;

/**
 * 配置键分类
 *
 * <AUTHOR>
 * @date 2024-03-12 11:03
 */
public enum ConfigKeyTypeEnum implements ValueEnum, NameEnum {
    /**
     * 新API
     */
    API("新API", 0),

    /**
     * 管家
     */
    WDGJ("管家", 1),

    /**
     * 新门店
     */
    NewMendian("新门店", 2),

    ;

    //region 构造
    /**
     * 构造函数
     *
     * @param name           平台类别名称
     * @param value          平台类别值
     */
    ConfigKeyTypeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }
    //endregion

    //region 属性
    /**
     * 标题。
     */
    private final String name;

    /**
     * 值。
     */
    private final int value;
    //endregion

    //region 公共方法

    /**
     * 获取枚举值
     *
     * @return
     */
    @Override
    public Integer getValue() {
        return this.value;
    }
    /**
     * 获取枚举名称
     *
     * @return
     */
    @Override
    public String getName() {
        return name();
    }

    /**
     * 枚举值字符串
     *
     * @return S
     */
    @Override
    public String toString() {
        return getName();
    }


    /**
     * 根据值获取对应的枚举。
     *
     * @param name 枚举名
     * @return 对应的枚举
     */
    public static ConfigKeyTypeEnum create(String name) {
        ConfigKeyTypeEnum enumObject = EnumConvertCacheUtil.convert(ConfigKeyTypeEnum.class, name, EnumConvertType.NAME);

        if (null == enumObject) {
            throw new AppException(SystemErrorCodes.LOGICERROR, "未能从字符串“" + name + "”创建枚举对象ConfigKeyEnum");
        }

        return enumObject;
    }


    //endregion
}
