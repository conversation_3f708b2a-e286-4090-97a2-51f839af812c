package com.differ.wdgj.api.user.biz.domain.aftersale.save.operation;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolySpecialRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * RefundTypeCovertOperation 单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午6:00
 */
public class RefundTypeCovertOperationTest {

    @Mock
    private AfterSaleSaveContext mockContext;

    private RefundTypeCovertOperation refundTypeCovertOperation;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        refundTypeCovertOperation = new RefundTypeCovertOperation();
    }

    /**
     * 测试退货退款单转换
     * 当菠萝派售后单类型为JH_04（退货退款单）时，应该转换为REFUND
     */
    @Test
    public void testCovertApiAfterSaleType_RefundType_ShouldReturnRefund() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_04);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("退货退款单应该转换为REFUND", ApiAfterSaleTypeEnum.REFUND, result);
    }

    /**
     * 测试退款单转换
     * 当菠萝派售后单类型为JH_03（退款单）时，应该转换为REFUND_PAY
     */
    @Test
    public void testCovertApiAfterSaleType_RefundPayType_ShouldReturnRefundPay() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_03);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("退款单应该转换为REFUND_PAY", ApiAfterSaleTypeEnum.REFUND_PAY, result);
    }


    /**
     * 测试补寄单转换     * 当菠萝派售后单类型为JH_SUPPLEMENT_SEND（补寄）时，应该转换为REFUND_SUPPLEMENT
     */
    @Test
    public void testCovertApiAfterSaleType_SupplementType_ShouldReturnRefundSupplement() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_SUPPLEMENT_SEND);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("补寄单应该转换为REFUND_SUPPLEMENT", ApiAfterSaleTypeEnum.REFUND_SUPPLEMENT, result);
    }

    /**
     * 测试价保订单转换
     * 当菠萝派售后单类型为JH_10（价保订单）时，应该转换为REFUND_BJ
     */
    @Test
    public void testCovertApiAfterSaleType_PriceProtectType_ShouldReturnRefundBj() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_10);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("价保订单应该转换为REFUND_BJ", ApiAfterSaleTypeEnum.REFUND_BJ, result);
    }

    /**
     * 测试维修单转换     * 当菠萝派售后单类型为JH_WEIXIU（维修单）时，应该转换为REPAIR
     */
    @Test
    public void testCovertApiAfterSaleType_RepairType_ShouldReturnRepair() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_WEIXIU);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("维修单应该转换为REPAIR", ApiAfterSaleTypeEnum.REPAIR, result);
    }

    /**
     * 测试特殊售后单转换- 保价
     * 当特殊售后单类型为JH_PRICE_PROTECT（保价）时，应该转换为REFUND_BJ
     */
    @Test
    public void testCovertApiAfterSaleType_SpecialPriceProtect_ShouldReturnRefundBj() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_SPECIALREFUND);
        ployOrder.setSpecialRefundType(PolySpecialRefundTypeEnum.JH_PRICE_PROTECT.getCode());
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("特殊售后类型保价应该转换为REFUND_BJ", ApiAfterSaleTypeEnum.REFUND_BJ, result);
    }

    /**
     * 测试未知类型转换
     * 当菠萝派售后单类型为未知类型时，应该转换为REFUND_ALL
     */
    @Test
    public void testCovertApiAfterSaleType_UnknownType_ShouldReturnRefundAll() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_99);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("未知类型应该转换为REFUND_ALL", ApiAfterSaleTypeEnum.REFUND_ALL, result);
    }

    /**
     * 测试所有枚举值的覆盖
     * 确保所有PolyRefundTypeEnum枚举值都能正常处理
     */
    @Test
    public void testCovertApiAfterSaleType_AllEnumValues() {
        PolyRefundTypeEnum[] allTypes = PolyRefundTypeEnum.values();
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        for (PolyRefundTypeEnum polyType : allTypes) {
            // 跳过特殊售后单，因为它需要额外的specialRefundType
            if (polyType == PolyRefundTypeEnum.JH_SPECIALREFUND) {
                continue;
            }

            BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(polyType);

            // 执行测试
            ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                    mockContext, ShopTypeEnum.DEFAULT, ployOrder, dbOrder);

            // 验证结果不为空
            Assert.assertNotNull(String.format("枚举类型s应该能正常转换", polyType), result);
        }
    }

    /**
     * 测试边界条件 - ployOrder为null
     */
    @Test
    public void testCovertApiAfterSaleType_PolyOrderNull_ShouldReturnRefundAll() {
        // 准备测试数据
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, null, dbOrder);

        // 验证结果
        Assert.assertEquals("ployOrder为null时应该返回REFUND_ALL", ApiAfterSaleTypeEnum.REFUND_ALL, result);
    }

    /**
     * 测试边界条件 - dbOrder为null
     */
    @Test
    public void testCovertApiAfterSaleType_DbOrderNull_ShouldReturnRefundAll() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_04);

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, ShopTypeEnum.DEFAULT, ployOrder, null);

        // 验证结果
        Assert.assertEquals("dbOrder为null时应该返回REFUND_ALL", ApiAfterSaleTypeEnum.REFUND_ALL, result);
    }

    /**
     * 测试边界条件 - shopType为null
     */
    @Test
    public void testCovertApiAfterSaleType_ShopTypeNull_ShouldReturnRefundAll() {
        // 准备测试数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = createPlolyOrder(PolyRefundTypeEnum.JH_04);
        DbAfterSaleOrderItem dbOrder = createDbOrder();

        // 执行测试
        ApiAfterSaleTypeEnum result = refundTypeCovertOperation.covertApiAfterSaleType(
                mockContext, null, ployOrder, dbOrder);

        // 验证结果
        Assert.assertEquals("shopType为null时应该返回REFUND_ALL", ApiAfterSaleTypeEnum.REFUND_ALL, result);
    }

    /**
     * 创建测试用的菠萝派订单对象
     *
     * @param refundType 退款类别
     * @return 菠萝派订单对象
     */
    private BusinessGetRefundOrderResponseOrderItem createPlolyOrder(PolyRefundTypeEnum refundType) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = new BusinessGetRefundOrderResponseOrderItem();
        ployOrder.setRefundType(refundType.getCode());
        ployOrder.setRefundNo("test-refund-id-001");
        ployOrder.setPlatOrderNo("test-order-id-001");
        ployOrder.setRefundAmount(new BigDecimal("100.00"));

        // 添加退款商品信息
        List<BusinessGetRefundResponseRefundGoodInfo> refundGoods = new ArrayList<>();
        BusinessGetRefundResponseRefundGoodInfo goodInfo = new BusinessGetRefundResponseRefundGoodInfo();
        goodInfo.setPlatProductId("1");
        goodInfo.setProductName("测试商品");
        goodInfo.setRefundProductNum(1);
        goodInfo.setStatus(PolyRefundGoodsStatusEnum.BUYER_RECEIVED.getCode());
        refundGoods.add(goodInfo);
        ployOrder.setRefundGoods(refundGoods);

        return ployOrder;
    }

    /**
     * 创建测试用的数据库订单对
     *
     * @return 数据库订单对
     */
    private DbAfterSaleOrderItem createDbOrder() {
        DbAfterSaleOrderItem dbOrder = new DbAfterSaleOrderItem();

        // 创建ApiTrade
        ApiTradeDO apiTrade = new ApiTradeDO();
        apiTrade.setBillId(1001);
        apiTrade.setTradeNo("test-trade-no-001");
        apiTrade.setShopId(2001);
        apiTrade.setbSplit(0);
        apiTrade.setSynStatus(4); // SUCCESS
        dbOrder.setApiTrade(apiTrade);

        // 创建ApiTradeGoods列表
        List<ApiTradeGoodsDO> apiTradeGoodsList = new ArrayList<>();
        ApiTradeGoodsDO apiTradeGoods = new ApiTradeGoodsDO();
        apiTradeGoods.setRecId(3001);
        apiTradeGoods.setGoodsId(1);
        apiTradeGoods.setPlatGoodsID("1");
        apiTradeGoods.setTradeGoodsName("测试商品");
        apiTradeGoods.setGoodsCount(new BigDecimal("1"));
        apiTradeGoods.setbSend(2); // SUCCESS
        apiTradeGoodsList.add(apiTradeGoods);
        dbOrder.setApiTradeGoodsList(apiTradeGoodsList);

        return dbOrder;
    }
}
