package com.differ.wdgj.api.user.biz.infrastructure.utils;

import com.differ.wdgj.api.component.redis.util.RedisJsonUtil;
import com.differ.wdgj.api.component.util.json.JsonUtils;

/**
 * <AUTHOR>
 * @date 2024/1/26 15:51
 */
public class RedisJsonUtilImpl implements RedisJsonUtil {


    /**
     * 对象转换为json
     *
     * @param obj
     * @return
     */
    @Override
    public String toJson(Object obj) {
        return JsonUtils.toJson(obj);
    }

    /**
     * json转换为对象
     *
     * @param json json
     * @param cls  转换的类
     * @return json字符串
     */
    @Override
    public <T> T deJson(String json, Class<T> cls) {
        return JsonUtils.deJson(json, cls);
    }
}
