package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbTradeRdsDo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 淘宝推送库 - 订单 仓储
 * sys_info.jdp_tb_trade
 *
 * <AUTHOR>
 * @date 2025/6/26 10:48
 */
public interface TbTradeRdsMapper {

    /**
     * 根据订单号批量查询订单
     *
     * @param tIds 订单号列表
     * @return 订单列表
     */
    List<TbTradeRdsDo> getTradeListByTIds(@Param("tIds") List<Long> tIds);
}
