package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleGoodsSysMatchItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractExchangeGoodsConvertHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 换货单-换货商品基础信息转换
 *
 * <AUTHOR>
 * @date 2024/8/8 下午4:18
 */
public class ExchangeGoodsCovert extends AbstractExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ExchangeGoodsCovert(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类逻辑

    /**
     * 转换商品级信息
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后退货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {

        BusinessGetExchangeResponseExchangeGoodInfo polyExchangeGoods = goodsItem.getPloyExchangeGoods();

        // 商品基础信息赋值
        exchangeGoods.setOuterId(StringUtils.defaultIfEmpty(polyExchangeGoods.getOutSkuId(), polyExchangeGoods.getOuterId()));
        exchangeGoods.setGoodsTitle(polyExchangeGoods.getProductName());
        exchangeGoods.setSku(polyExchangeGoods.getSkuSpec());
        exchangeGoods.setPlatGoodsId(polyExchangeGoods.getPlatProductId());
        exchangeGoods.setPlatSkuId(polyExchangeGoods.getSku());
        exchangeGoods.setOid(polyExchangeGoods.getSubTradeNo());
        exchangeGoods.setPrice(BigDecimal.ZERO);
        exchangeGoods.setGoodsCount(BigDecimal.valueOf(polyExchangeGoods.getProductNum()));

        // 匹配表信息兼容
        AfterSaleGoodsSysMatchItem goodsSysMatch = goodsItem.getGoodsSysMatchItem();
        if(goodsSysMatch != null){
            // 商品编码
            String sysMatchOutId = StringUtils.defaultIfEmpty(goodsSysMatch.getSkuOuterID(), goodsSysMatch.gettBOuterID());
            exchangeGoods.setOuterId(StringUtils.defaultIfEmpty(exchangeGoods.getOuterId(), sysMatchOutId));
            // 商品名称
            exchangeGoods.setGoodsTitle(StringUtils.defaultIfEmpty(exchangeGoods.getGoodsTitle(), goodsSysMatch.gettBName()));
            // 规格名称
            exchangeGoods.setSku(StringUtils.defaultIfEmpty(exchangeGoods.getSku(), goodsSysMatch.gettBSku()));
            // 平台商品Id
            exchangeGoods.setPlatGoodsId(StringUtils.defaultIfEmpty(exchangeGoods.getPlatGoodsId(), goodsSysMatch.getNumiid()));
            // 平台规格Id
            exchangeGoods.setPlatSkuId(StringUtils.defaultIfEmpty(exchangeGoods.getPlatSkuId(), goodsSysMatch.getSkuID()));
        }

        // 历史主键
        if (CollectionUtils.isNotEmpty(orderItem.getDbOrder().getExchangeGoods())) {
            ApiReturnDetailTwoDO oldExchangeGoods = orderItem.getDbOrder().getExchangeGoods().stream().filter(x -> AfterSaleGoodsMatchOperation.isMatchHistoryExchangeGoods(exchangeGoods, x)).findFirst().orElse(null);
            if (oldExchangeGoods != null) {
                exchangeGoods.setRecId(oldExchangeGoods.getRecId());
            }
        }

        return GoodsConvertHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "换货单-售后商品商品匹配";
    }
    //endregion
}
