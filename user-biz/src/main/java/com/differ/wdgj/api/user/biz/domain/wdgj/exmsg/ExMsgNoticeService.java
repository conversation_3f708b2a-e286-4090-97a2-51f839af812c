package com.differ.wdgj.api.user.biz.domain.wdgj.exmsg;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockRestrictedModeEnum;
import com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.data.SyncStockFailNoticeInfo;
import com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.data.enums.ExMsgTypeEnum;
import com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.data.enums.SysAutoIdTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.GCfgErrMessageSetDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.GServicesCaseListDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.GCfgErrMessageSetMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.GCfgExceptMessageMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.GCfgSysMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.GServicesCaseListMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 异常消息通知管理器
 *
 * <AUTHOR>
 * @since 2024-03-18 9:35
 */
public class ExMsgNoticeService {

    // region 常量

    /**
     * 发货失败 间隔提醒 配置类型
     */
    private static final int SEND_ORDER_FAILED_INTERVAL_ERR_TYPE = 13;

    private static final Logger log = LoggerFactory.getLogger(ExMsgNoticeService.class);

    // endregion

    // region 公共方法

    /**
     * 通知库存同步失败
     *
     * @param memberName 会员名
     * @param failNotice 库存同步失败信息集合
     */
    public void noticeSyncStockFail(String memberName, List<SyncStockFailNoticeInfo> failNotice) {

        try {

            if (CollectionUtils.isEmpty(failNotice)) {
                return;
            }

            // 查询需要通知的人
            String[] noticeStaff = this.queryNoticeStaffs(memberName, ExMsgTypeEnum.SYNC_STOCK_FAILED);
            if (noticeStaff == null || noticeStaff.length == 0) {
                return;
            }

            LocalDateTime nowTime = LocalDateTime.now();

            for (SyncStockFailNoticeInfo failInfo : failNotice) {

                String summary = String.format("[新API]%s库存同步失败", failInfo.isAuto() ? "自动" : "手动");
                String caption = failInfo.getModeEnum() == null || SyncStockRestrictedModeEnum.None.equals(failInfo.getModeEnum()) ? "库存同步失败" : failInfo.getModeEnum().getRestrictedReason();
                String caseInfo = String.format("%s：%s，店铺：%s，平台商品名称：%s", caption, failInfo.getSyncMsg(), failInfo.getShopName(), failInfo.getTbName());

                // 按人通知
                for (String oneStaff : noticeStaff) {
                    this.insertExMsgNotice(memberName, failInfo.getShopId(), oneStaff, nowTime, "API库存同步失败", summary, caseInfo);
                }
            }

        } catch (Exception ex) {
            log.error("通知库存同步失败异常", ex);
        }

    }

    // endregion

    // region 私有方法

    /**
     * 插入一条异常消息通知
     *
     * @param memberName 会员名
     * @param shopId     店铺id
     * @param userId     被通知人
     * @param noticeTime 通知时间
     * @param summary    通知内容标题
     * @param caseInfo   详细内容
     */
    private void insertExMsgNotice(String memberName, int shopId, String userId, LocalDateTime noticeTime, String csseType, String summary, String caseInfo) {

        String caseTableName = SysAutoIdTypeEnum.G_SERVICES_CASE_LIST_CASE_ID.getTableName();
        String caseFieldName = SysAutoIdTypeEnum.G_SERVICES_CASE_LIST_CASE_ID.getFieldName();

        String caseIdStr = DBSwitchUtil.doDBWithUser(memberName, () -> BeanContextUtil.getBean(GCfgSysMapper.class).getSysAutoId(caseTableName, caseFieldName));
        if (StringUtils.isNotBlank(caseIdStr)) {
            int caseId = NumberUtils.toInt(caseIdStr);

            GServicesCaseListDO caseEntity = new GServicesCaseListDO();
            caseEntity.setCaseid(caseId);
            caseEntity.setCasetype(csseType);
            caseEntity.setRegtime(noticeTime);
            caseEntity.setRegoperator(userId);
            caseEntity.setShopid(shopId);
            caseEntity.setSummary(summary);
            caseEntity.setCaseinfo(caseInfo);

            caseEntity.setCurstatus(0);
            caseEntity.setTradeid(0);
            caseEntity.setCustomernick("");
            caseEntity.setCustomername("");
            caseEntity.setAdr("");
            caseEntity.setTel("");
            caseEntity.setTradeno2("");

            DBSwitchUtil.doTransaction(memberName, () -> {
                BeanContextUtil.getBean(GServicesCaseListMapper.class).insert(caseEntity);
                BeanContextUtil.getBean(GCfgExceptMessageMapper.class).insertExceptCase(caseId, userId, 0);
                return true;
            });
        }
    }

    /**
     * 查询需要通知的工作人员
     *
     * @param memberName    会员名
     * @param exMsgTypeEnum 异常类型
     * @return
     */
    private String[] queryNoticeStaffs(String memberName, ExMsgTypeEnum exMsgTypeEnum) {

        if (exMsgTypeEnum == null || exMsgTypeEnum.getWdgjValue() == null || exMsgTypeEnum.getWdgjValue() < 0) {
            return new String[0];
        }

        int wdgjVal = exMsgTypeEnum.getWdgjValue();

        GCfgErrMessageSetMapper errMsgSetMapper = BeanContextUtil.getBean(GCfgErrMessageSetMapper.class);

        // 数据库操作
        if (ExMsgTypeEnum.SEND_ORDER_FAILED.equals(exMsgTypeEnum)) {

            GCfgErrMessageSetDO config = DBSwitchUtil.doDBWithUser(memberName, () -> errMsgSetMapper.queryErrMessageSet(wdgjVal, SEND_ORDER_FAILED_INTERVAL_ERR_TYPE));
            if (config != null && StringUtils.isNotBlank(config.getTostaff1())) {
                return StringUtils.split(config.getTostaff1(), ",");
            }

            return new String[0];
        }

        String staff = DBSwitchUtil.doDBWithUser(memberName, () -> errMsgSetMapper.queryNoticeStaff(wdgjVal));
        if (StringUtils.isNotBlank(staff)) {
            return StringUtils.split(staff, ",");
        }
        return new String[0];
    }

    // endregion

}
