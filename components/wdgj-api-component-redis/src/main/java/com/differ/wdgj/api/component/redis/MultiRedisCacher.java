package com.differ.wdgj.api.component.redis;

import com.differ.wdgj.api.component.redis.converter.CacheKeyToCacheTypeConverter;
import com.differ.wdgj.api.component.redis.creator.RedisConnectionFactoryCreator;
import com.differ.wdgj.api.component.redis.hash.expire.DefaultHashExpireValueConverter;
import com.differ.wdgj.api.component.redis.hash.expire.HashExpireRedisScripter;
import com.differ.wdgj.api.component.redis.hash.expire.HashExpireValue;
import com.differ.wdgj.api.component.redis.hash.expire.OriginVersionHashValue;
import com.differ.wdgj.api.component.redis.trace.RedisTrace;
import com.differ.wdgj.api.component.redis.util.RedisJsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.util.StopWatch;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisCommands;

import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.BooleanSupplier;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 支持redis多库的缓存组件
 * <AUTHOR>
 * @date 2020/12/11 14:21
 */
public class MultiRedisCacher implements MultiRedis {

    private static final Logger log = LoggerFactory.getLogger(MultiRedisCacher.class);

    //region 变量

    /**
     * 释放锁成功返回值
     */
    private static final Integer RELEASE_LOCK_SUCCESS = 1;
    /**
     * 自动过期释放锁成功返回值
     */
    private static final Integer RELEASE_LOCK_AUTO_SUCCESS = 0;

    /**
     * 分布式锁，锁定的默认超时时间（默认20000毫秒）
     */
    private static final Integer DEFAULT_LOCK_TIMEOUT = 20000;

    /**
     * 分布式锁缓存键的默认过期时间（默认300000毫秒）
     */
    private static final Integer DEFAULT_LOCK_EXPIRE = 300000;

    /**
     * 自动过期释放锁成功返回值
     */
    private static final String SET_IF_ABSENT_SUCCESS = "OK";
    /**
     * 即当key不存在时进行set操作；若key已经存在则不做任何操作
     */
    private static final String SET_IF_NOT_EXIST = "NX";
    /**
     * 即当key存在时进行set操作；若key不存在则不做任何操作
     */
    private static final String SET_IF_EXIST = "XX";
    /**
     * 表示将分布式锁的Key加上过期时间(毫秒)。
     */
    private static final String SET_EXPIRE_TIME_MILLISECONDS = "PX";
    /**
     * 将Key加上过期时间(秒)。
     */
    private static final String SET_EXPIRE_TIME_SECONDS = "EX";

    /**
     * 释放锁lua脚本
     */
    private static final String SCRIPT_RELEASE_LOCK = "if redis.call(\"get\",KEYS[1]) == ARGV[1] then return redis.call(\"del\",KEYS[1]) else return 0 end";

    /**
     * setIfAbsent带过期时间的lua脚本
     */
    private static final String SCRIPT_SET_IF_ABSENT = "return redis.call(\"set\",KEYS[1],ARGV[1],\"NX\",\"PX\",ARGV[2])";

    /**
     * 多redis类型转换器
     */
    private CacheKeyToCacheTypeConverter keyToCacheTypeConverter;

    /**
     * Redis连接工厂的创建器
     */
    private RedisConnectionFactoryCreator redisConnectionFactoryCreator;

    /**
     * 类型与RedisTemplate的映射集合
     */
    private Map<String, StringRedisTemplate> cacheTypeMap = new HashMap<>();

    /**
     * hash值无版本号的值
     */
    private static final String NO_HASH_VALUE_VERSION = "";
    /**
     * hash批量更新最大个数
     */
    private static final int HASH_MULTI_SET_MAX = 100;
    /**
     * 支持hash过期的缓存键默认过期时间,一天
     */
    private static final String DEFAULT_HASH_CACHE_EXPIRE = "86400";
    /**
     * hash过期数据格式转换
     */
    private HashExpireValueConverter hashExpireValueConverter = new DefaultHashExpireValueConverter();

    /**
     * redis执行监控，用于数据埋点
     */
    private RedisTrace redisTrace;

    private RedisJsonUtil jsonUtil;
    //endregion

    //region 构造

    public MultiRedisCacher(CacheKeyToCacheTypeConverter keyToCacheTypeConverter, RedisConnectionFactoryCreator redisConnectionFactoryCreator) {
        this.keyToCacheTypeConverter = keyToCacheTypeConverter;
        this.redisConnectionFactoryCreator = redisConnectionFactoryCreator;
    }

    public MultiRedisCacher(CacheKeyToCacheTypeConverter keyToCacheTypeConverter, RedisConnectionFactoryCreator redisConnectionFactoryCreator, RedisTrace redisTrace, RedisJsonUtil jsonUtil) {
        this.keyToCacheTypeConverter = keyToCacheTypeConverter;
        this.redisConnectionFactoryCreator = redisConnectionFactoryCreator;
        this.redisTrace = redisTrace;
        this.jsonUtil = jsonUtil;
    }

    //endregion

    // region 接口实现 getRedisTemplate

    /**
     * 根据缓存键获取RedisTemplate
     *
     * @param cacheKey 缓存键
     * @return StringRedisTemplate
     */
    @Override
    public StringRedisTemplate getRedisTemplate(String cacheKey) {
        String cacheType = keyToCacheTypeConverter.toCacheType(cacheKey);
        if (redisTrace != null) {
            redisTrace.traceOnExec(cacheKey, cacheType);
        }
        return getRedisTemplateByCacheType(cacheType);
    }

    /**
     * 根据缓存类型获取RedisTemplate
     *
     * @param cacheType 缓存类型
     * @return StringRedisTemplate
     */
    @Override
    public StringRedisTemplate getRedisTemplateByCacheType(String cacheType) {
        StringRedisTemplate redisTemplate = cacheTypeMap.get(cacheType);
        if (redisTemplate == null) {
            redisTemplate = createAndAddRedisTemplateSync(cacheType);
        }
        return redisTemplate;
    }

    /**
     * 当对应cacheType没创建redisTemplate时,创建并添加到集合，返回redisTemplate
     *
     * @param cacheType 缓存类型
     * @return StringRedisTemplate
     */
    private StringRedisTemplate createAndAddRedisTemplateSync(String cacheType) {
        synchronized (this) {
            return cacheTypeMap.computeIfAbsent(cacheType, key -> {
                // 根据缓存键获取redis连接工厂
                RedisConnectionFactory connectionFactory = redisConnectionFactoryCreator.create(key);
                // 创建redisTemplate
                return new StringRedisTemplate(connectionFactory);
            });
        }
    }

    // endregion

    // region 接口实现：Redis数据操作

    // region简单键值操作

    /**
     * 获取缓存
     *
     * @param cacheKey 缓存键
     */
    @Override
    public String get(String cacheKey) {
        return this.getRedisTemplate(cacheKey).opsForValue().get(cacheKey);
    }

    /**
     * 设置缓存
     *
     * @param cacheKey 缓存键
     * @param value    缓存值
     */
    @Override
    public void set(String cacheKey, String value) {
        this.getRedisTemplate(cacheKey).opsForValue().set(cacheKey, value);
    }

    /**
     * 设置带过期时间的缓存
     *
     * @param cacheKey 缓存键
     * @param value    缓存值
     * @param timeout  过期时间(单位：秒)
     */
    @Override
    public void set(String cacheKey, String value, long timeout) {
        this.getRedisTemplate(cacheKey).opsForValue().set(cacheKey, value, timeout, TimeUnit.SECONDS);
    }

    /**
     * 批量获取缓存(如果key不存在会返回null项)。
     *
     * @param cacheKeys 缓存键集
     * @return 缓存值集
     */
    @Override
    public List<String> getOnSingleRedis(String... cacheKeys) {
        if (ArrayUtils.isEmpty(cacheKeys)) {
            return Collections.emptyList();
        }
        return this.getRedisTemplate(cacheKeys[0]).opsForValue().multiGet(Arrays.asList(cacheKeys));
    }

    /**
     * 获取缓存
     *
     * @param cacheKey 缓存键
     * @return 缓存值
     */
    @Override
    public String getOnSingleRedis(String cacheKey) {
        return this.getRedisTemplate(cacheKey).opsForValue().get(cacheKey);
    }

    /**
     * 判断是否具有某个缓存键
     *
     * @param cacheKey 缓存键
     * @return 是否具有某个缓存键
     */
    @Override
    public boolean has(String cacheKey) {
        return this.getRedisTemplate(cacheKey).hasKey(cacheKey);
    }

    /**
     * 删除缓存
     *
     * @param cacheKey 缓存键
     */
    @Override
    public void delete(String cacheKey) {
        this.getRedisTemplate(cacheKey).delete(cacheKey);
    }

    /**
     * 删除缓存
     *
     * @param cacheKeys 缓存键集合
     */
    @Override
    public void deleteOnSingleRedis(String... cacheKeys) {
        if (cacheKeys.length == 0) {
            return;
        }
        this.getRedisTemplate(cacheKeys[0]).delete(Arrays.asList(cacheKeys));
    }

    /**
     * 从当前缓存实例获取所有缓存键。
     *
     * @param cacheType 缓存类别
     * @param pattern   匹配模式
     * @return 缓存值
     */
    @Override
    public Set<String> getKeys(String cacheType, String pattern) {
        return this.getRedisTemplateByCacheType(cacheType).keys(pattern);
    }

    /**
     * 设置缓存键超时时间(单位:秒)
     *
     * @param cacheKey 缓存键
     * @param timeout  过期时间(秒)
     * @return 是否成功设置 1 成功；0 失败
     */
    @Override
    public boolean setExpire(String cacheKey, int timeout) {
        return Boolean.TRUE.equals(this.getRedisTemplate(cacheKey).expire(cacheKey, timeout, TimeUnit.SECONDS));
    }

    /**
     * 获取缓存键过期时间（单位秒）
     *
     * @param cacheKey 缓存键
     * @return 过期时间（小于 0 说明没有过期时间或缓存键不存在）
     */
    @Override
    public Long getExpire(String cacheKey) {
        return this.getRedisTemplate(cacheKey).getExpire(cacheKey, TimeUnit.SECONDS);
    }

    // endregion

    // region Hash相关操作

    /**
     * 缓存同步字符串。
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @param value    缓存值
     * @return 同步成功数量
     */
    @Override
    public int hashSyncStrValue(String cacheKey, String hashKey, String value) {
        this.getRedisTemplate(cacheKey).opsForHash().put(cacheKey, hashKey, value);
        return 1;
    }

    /**
     * 缓存批量同步字符串。
     *
     * @param cacheKey 缓存键
     * @param values   值集合
     * @return 同步成功数量
     */
    @Override
    public int hashSyncStrValues(String cacheKey, Map<String, String> values) {
        if (values.isEmpty()) {
            return 0;
        }

        // 批量同步
        this.getRedisTemplate(cacheKey).opsForHash().putAll(cacheKey, values);
        return values.size();
    }

    /**
     * 批量同步Hash缓存(只有当缓存不存在或DataVersion不一致才同步)
     *
     * @param cacheKey 缓存键
     * @param values   值集合
     * @return 缓存同步成功数量
     */
    @SuppressWarnings("unchecked")
    @Override
    public <T extends ICacheDataItem> int hashSyncByCompareDataVersion(String cacheKey, List<T> values) {
        if (values.isEmpty()) {
            return 0;
        }

        List<String> hashKeys = new ArrayList<>(values.size());
        for (T t : values) {
            String hashKey = t.getHashKey();
            if (StringUtils.isNotBlank(hashKey)) {
                hashKeys.add(hashKey);
            }
        }

        // 先从缓存获取旧数据。
        List<T> oldValues = getOldValuesByDataVersion(cacheKey, values, hashKeys);

        // 如果未找到旧数据或数据版本不一致则同步。
        Map<String, String> beSyncHash = new HashMap<>(values.size());
        for (T value : values) {
            if (needToSync(oldValues, value)) {
                beSyncHash.put(value.getHashKey(), this.jsonUtil.toJson(value));
            }
        }
        // 如果没有任何需要同步则直接返回。
        if (beSyncHash.size() == 0) {
            return 0;
        }

        // 批量同步
        this.getRedisTemplate(cacheKey).opsForHash().putAll(cacheKey, beSyncHash);
        int lenSync = beSyncHash.size();

        // 释放资源。
        if (null != oldValues) {
            oldValues.clear();
        }

        beSyncHash.clear();
        hashKeys.clear();

        return lenSync;
    }

    /**
     * 批量同步Hash缓存(只有当缓存不存在或DataVersion不一致才同步)
     *
     * @param cacheKey 缓存键
     * @param values   值集合
     * @return 缓存同步成功数量
     */
    @SuppressWarnings("unchecked")
    @Override
    public <T> int hashSync(String cacheKey, Map<String, T> values) {
        if (values.isEmpty()) {
            return 0;
        }
        // 转换对象为字符串存储
        Map<String, String> beSyncHash = new HashMap<>(values.size());
        for (Map.Entry<String, T> item : values.entrySet()) {
            beSyncHash.put(item.getKey(), this.jsonUtil.toJson(item.getValue()));
        }
        // 批量同步
        this.getRedisTemplate(cacheKey).opsForHash().putAll(cacheKey, beSyncHash);
        int lenSync = beSyncHash.size();

        // 释放资源。
        beSyncHash.clear();

        return lenSync;
    }

    private <T extends ICacheDataItem> List<T> getOldValuesByDataVersion(String cacheKey, List<T> values, List<String> hashKeys) {
        List<T> oldValues = null;
        try {
            oldValues = this.hashGet(cacheKey, hashKeys, (Class<T>) values.get(0).getClass());
        } catch (Exception e) {
            oldValues = new ArrayList<>();
            log.error("获取Redis缓存失败", e);
        }
        return oldValues;
    }

    /**
     * 判断版本是否需要同步
     *
     * @param oldValues
     * @param value
     * @param <T>
     * @return
     */
    private <T extends ICacheDataItem> boolean needToSync(List<T> oldValues, T value) {
        boolean isBeSync = true;
        for (T oldValue : oldValues) {
            // 根据HashKey找到老数据。
            if (null != oldValue && oldValue.getHashKey().equalsIgnoreCase(value.getHashKey())) {
                // 如果数据版本相同则不需要同步。
                if (oldValue.getDataVersion() == null || oldValue.getDataVersion().equals(value.getDataVersion())) {
                    isBeSync = false;
                }
                break;
            }
        }
        return isBeSync;
    }

    /**
     * 批量移除Hash缓存多个项。
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 移除是否成功
     */
    @Override
    public Boolean hashRemove(String cacheKey, String... hashKeys) {
        return this.getRedisTemplate(cacheKey).opsForHash().delete(cacheKey, hashKeys) > 0;
    }

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @return 值
     */
    @Override
    public String hashGetStrValue(String cacheKey, String hashKey) {
        Object value = this.getRedisTemplate(cacheKey).opsForHash().get(cacheKey, hashKey);
        if (value == null) {
            return null;
        }

        return value.toString();
    }

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 值集合
     */
    @Override
    public Map<String, String> hashGetStrValues(String cacheKey, List<String> hashKeys) {
        List<Object> hashValues = this.getRedisTemplate(cacheKey).opsForHash().multiGet(cacheKey, new ArrayList<>(hashKeys));
        Map<String, String> results = new HashMap<>();
        if (hashKeys.size() == hashValues.size()) {
            for (int i = 0; i < hashKeys.size(); i++) {
                Object hashValue = hashValues.get(i);
                results.put(hashKeys.get(i), hashValue == null ? null : hashValue.toString());
            }
        }

        return results;
    }

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 值集合
     */
    @Override
    public List<Object> hashGetListStrValues(String cacheKey, List<String> hashKeys) {
        return this.getRedisTemplate(cacheKey).opsForHash().multiGet(cacheKey, new ArrayList<>(hashKeys));
    }

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @return 值
     */
    @Override
    public <T> T hashGet(String cacheKey, String hashKey, Class<T> type) {
        Object hashValue = this.getRedisTemplate(cacheKey).opsForHash().get(cacheKey, hashKey);
        if (hashValue == null) {
            return null;
        }
        return this.jsonUtil.deJson(hashValue.toString(), type);
    }

    /**
     * 获取Hash缓存指定键值
     *
     * @param cacheKey 缓存键
     * @param hashKeys Hash键集合
     * @return 值集合
     */
    @Override
    public <T> List<T> hashGet(String cacheKey, List<String> hashKeys, Class<T> type) {
        List<Object> objectList = new ArrayList<>(hashKeys);
        List<Object> hashValues = this.getRedisTemplate(cacheKey).opsForHash().multiGet(cacheKey, objectList);
        return hashValues.stream().map(item -> {
            try {
                if (item == null) {
                    return null;
                }
                return this.jsonUtil.deJson(item.toString(), type);
            } catch (Exception ex) {
                log.error(String.format("获取整个Hash缓存反序列化值时异常：%s", item.toString()), ex);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取整个Hash缓存
     *
     * @param cacheKey 缓存键
     * @return 值集合
     */
    @Override
    public <T> Map<String, T> hashGetAll(String cacheKey, Class<T> type) {
        Map<Object, Object> hashValues = this.getRedisTemplate(cacheKey).opsForHash().entries(cacheKey);
        Map<String, T> map = new HashMap<>();
        for (Map.Entry<Object, Object> item : hashValues.entrySet()) {
            try {
                if (item.getKey() == null || item.getValue() == null) {
                    continue;
                }
                map.put(item.getKey().toString(), this.jsonUtil.deJson(item.getValue().toString(), type));
            } catch (Exception ex) {
                log.error(String.format("获取整个Hash缓存反序列化值时异常：%s", item.toString()), ex);
            }
        }
        return map;
    }

    /**
     * 分页获扫描某个Hash缓存下所有字段,并且处理
     *
     * @param cacheKey 缓存键
     * @param pageSize 分页大小，每页scan的数据量
     * @param handler  key,value的处理
     */
    @Override
    public void hashScanAll(String cacheKey, int pageSize, BiConsumer<String, String> handler) {
        hashScanAll(cacheKey, "*", pageSize, handler);
    }

    /**
     * 分页获扫描某个Hash缓存下所有字段,并且处理
     *
     * @param cacheKey     缓存键
     * @param matchPattern 过滤扫描结果
     * @param pageSize     分页大小，每页scan的数据量
     * @param handler      key,value的处理
     */
    @Override
    public void hashScanAll(String cacheKey, String matchPattern, int pageSize, BiConsumer<String, String> handler) {
        Cursor<Map.Entry<Object, Object>> cursor = null;
        try {
            cursor = this.getRedisTemplate(cacheKey).opsForHash().scan(cacheKey, ScanOptions.scanOptions().match(matchPattern).count(pageSize).build());
            cursor.forEachRemaining(entry -> {
                String key = (String) entry.getKey();
                String value = (String) entry.getValue();
                handler.accept(key, value);
            });
        } catch (Exception e) {
            log.error(String.format("hash-Scan异常, cacheKey: %s, ex: %s", cacheKey, e));
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (IOException e) {
                    log.error(String.format("hash-Scan关闭失败，cacheKey：%s", cacheKey), e);
                }
            }
        }
    }

    /**
     * 根据缓存键scan
     *
     * @param cacheKey 缓存键
     * @param pageSize 分页大小
     * @param type     值类型
     * @param handler  值处理
     */
    @Override
    public <T> void hashScanAll(String cacheKey, String matchPattern, int pageSize, Class<T> type, Consumer<Map<String, T>> handler) {
        Cursor<Map.Entry<Object, Object>> cursor = null;
        try {
            Map<String, T> values = new HashMap<>();
            cursor = this.getRedisTemplate(cacheKey).opsForHash().scan(cacheKey, ScanOptions.scanOptions().match(matchPattern).count(pageSize).build());
            while (cursor.hasNext()) {
                Map.Entry<Object, Object> entry = cursor.next();
                if (entry.getKey() == null) {
                    continue;
                }

                values.put(entry.getKey().toString(), entry.getValue() == null ? null : this.jsonUtil.deJson(entry.getValue().toString(), type));
            }

            handler.accept(values);
        } catch (Exception e) {
            log.error(String.format("Scan hash failed, cacheKey: %s, ex: %s", cacheKey, e));
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (IOException e) {
                    log.error(String.format("游标关闭失败，cacheKey：%s", cacheKey), e);
                }
            }
        }
    }

    /**
     * 获取Hash集合数量
     *
     * @param cacheKey 缓存键
     * @return 值集合数量
     */
    @Override
    public Long hashCount(String cacheKey) {
        return this.getRedisTemplate(cacheKey).opsForHash().size(cacheKey);
    }

    /**
     * 获取Hash集合的键集合
     *
     * @param cacheKey 缓存键
     * @return 键集合
     */
    @Override
    public Set<String> hashAllKeys(String cacheKey) {
        return this.getRedisTemplate(cacheKey).opsForHash().keys(cacheKey).stream().map(Object::toString).collect(Collectors.toSet());
    }

    /**
     * 判断Hash键是否存在
     *
     * @param cacheKey 缓存键
     * @param hashKey  hash键
     * @return 键集合
     */
    @Override
    public Boolean hashExist(String cacheKey, String hashKey) {
        return this.getRedisTemplate(cacheKey).opsForHash().hasKey(cacheKey, hashKey);
    }

    // endregion

    // region SortedSet 相关操作

    /**
     * SortedSet缓存删除
     *
     * @param cacheKey   缓存键
     * @param startScore score(超过17位会丢失精度，请慎重使用)
     * @param endScore   score(超过17位会丢失精度，请慎重使用)
     * @return 删除的个数
     */
    @Override
    public long sortedSetRemove(String cacheKey, double startScore, double endScore) {
        return this.getRedisTemplate(cacheKey).opsForZSet().removeRangeByScore(cacheKey, startScore, endScore);
    }

    /**
     * SortedSet缓存同步(SortedSet数据值重复会覆盖)。。
     *
     * @param cacheKey        缓存键
     * @param score           score(超过17位会丢失精度，请慎重使用)
     * @param value           值(唯一)
     * @param isRemoveByScore 是否根据Score删除旧数据
     * @param expireSeconds   过期时间(秒)，大于0则设置过期时间；否则为不过期
     * @return 同步是否成功
     */
    @Override
    public <T> Boolean sortedSetSync(String cacheKey, double score, T value, boolean isRemoveByScore, int expireSeconds) {
        ZSetOperations<String, String> opsForZSet = this.getRedisTemplate(cacheKey).opsForZSet();
        //判断当前缓存键是否存在
        boolean isExist = true;
        if (expireSeconds > 0) {
            isExist = this.has(cacheKey);
        }
        if (isRemoveByScore) {
            //先删除对应score的旧数据
            opsForZSet.removeRangeByScore(cacheKey, score, score);
        }
        //添加新数据
        opsForZSet.add(cacheKey, this.jsonUtil.toJson(value), score);
        //如果是第一次则设置过期时间
        if (expireSeconds > 0 && !isExist) {
            //设置过期时间
            this.setExpire(cacheKey, expireSeconds);
        }
        return true;
    }

    /**
     * 批量SortedSet缓存同步(SortedSet数据值重复会覆盖)。
     *
     * @param cacheKey        缓存键
     * @param scoreValues     值和score的键值对
     * @param isRemoveByScore 是否根据Score删除旧数据
     * @param expireSeconds   过期时间(秒)，大于0则设置过期时间；否则为不过期
     * @return 同步是否成功
     */
    @Override
    public <T> Boolean sortedSetSync(String cacheKey, Map<T, Double> scoreValues, boolean isRemoveByScore, int expireSeconds) {
        //判断当前缓存键是否存在
        boolean isExist = true;
        if (expireSeconds > 0) {
            isExist = this.has(cacheKey);
        }
        ZSetOperations<String, String> opsForZSet = this.getRedisTemplate(cacheKey).opsForZSet();
        Set<ZSetOperations.TypedTuple<String>> scoreStrValues = new HashSet<>();
        for (Map.Entry<T, Double> entry : scoreValues.entrySet()) {
            if (isRemoveByScore) {
                //先删除对应score的旧数据
                opsForZSet.removeRangeByScore(cacheKey, entry.getValue(), entry.getValue());
            }
            //新数据实体转化为json数据
            scoreStrValues.add(new DefaultTypedTuple<String>(this.jsonUtil.toJson(entry.getKey()), entry.getValue()));
        }
        //批量添加新数据
        opsForZSet.add(cacheKey, scoreStrValues);
        //如果是第一次则设置过期时间
        if (expireSeconds > 0 && !isExist) {
            //设置过期时间
            this.setExpire(cacheKey, expireSeconds);
        }
        return true;
    }

    /**
     * 根据Score判断SortedSet缓存是否存在
     *
     * @param cacheKey   缓存键
     * @param startScore 起始score(-1/0)
     * @param endScore   截止score(1/0)
     * @return 是否存在
     */
    @Override
    public Boolean sortedSetExist(String cacheKey, double startScore, double endScore) {
        return this.getRedisTemplate(cacheKey).opsForZSet().count(cacheKey, startScore, endScore) > 0;
    }

    /**
     * 根据Score获取SortedSet缓存
     *
     * @param cacheKey   缓存键
     * @param startScore 起始score(-1/0)
     * @param endScore   截止score(1/0)
     * @return SortedSet缓存
     */
    @Override
    public <T> Set<T> sortedSetGet(String cacheKey, double startScore, double endScore, Class<T> type) {
        Set<T> dataSet = new LinkedHashSet<>();
        Set<String> dataStrSet = this.getRedisTemplate(cacheKey).opsForZSet().rangeByScore(cacheKey, startScore, endScore);
        for (String data : dataStrSet) {
            dataSet.add(this.jsonUtil.deJson(data, type));
        }
        return dataSet;
    }

    /**
     * 根据index获取SortedSet缓存
     *
     * @param cacheKey   缓存键
     * @param startIndex 起始位置
     * @param endIndex   截止位置   (-1为最后一个，-2为倒数第二个...)
     * @return SortedSet缓存
     */
    @Override
    public <T> Set<T> sortedSetGetByIndex(String cacheKey, long startIndex, long endIndex, Class<T> type) {
        Set<T> dataSet = new LinkedHashSet<>();
        Set<String> dataStrSet = this.getRedisTemplate(cacheKey).opsForZSet().range(cacheKey, startIndex, endIndex);
        for (String data : dataStrSet) {
            dataSet.add(this.jsonUtil.deJson(data, type));
        }
        return dataSet;
    }

    /**
     * 获取长度
     *
     * @param cacheKey 缓存键
     * @return SortedSet缓存
     */
    @Override
    public long sortedSetGetLength(String cacheKey) {
        return this.getRedisTemplate(cacheKey).opsForZSet().size(cacheKey);
    }

    /**
     * 根据值获取分数
     *
     * @param cacheKey 缓存键
     * @param value    值
     * @param <T>      泛型
     * @return 结果
     */
    @Override
    public <T> double sortedSetGetScore(String cacheKey, T value) {
        return this.getRedisTemplate(cacheKey).opsForZSet().score(cacheKey, this.jsonUtil.toJson(value));
    }

    // endregion

    // region List 相关操作

    /**
     * 查询 List 缓存（按范围查询）
     *
     * @param cacheKey   缓存键
     * @param startIndex 起始索引
     * @param endIndex   结束索引
     * @param type       类型
     * @param <T>        泛型
     * @return 结果
     */
    @Override
    public <T> List<T> listGetByRange(String cacheKey, long startIndex, long endIndex, Class<T> type) {
        List<T> results = new ArrayList<>();
        List<String> values = this.getRedisTemplate(cacheKey).opsForList().range(cacheKey, startIndex, endIndex);
        for (String value : values) {
            results.add(this.jsonUtil.deJson(value, type));
        }

        return results;
    }

    /**
     * 查询 List 缓存（按范围查询）
     *
     * @param cacheKey   缓存键
     * @param startIndex 起始索引
     * @param endIndex   结束索引
     * @return 结果
     */
    @Override
    public List<String> listGetByRange(String cacheKey, long startIndex, long endIndex) {
        return this.getRedisTemplate(cacheKey).opsForList().range(cacheKey, startIndex, endIndex);
    }

    /**
     * 查询 List 缓存（按索引查询）
     *
     * @param cacheKey 缓存键
     * @param index    索引
     * @param type     类型
     * @param <T>      泛型
     * @return 结果
     */
    @Override
    public <T> T listGetByIndex(String cacheKey, long index, Class<T> type) {
        String value = this.getRedisTemplate(cacheKey).opsForList().index(cacheKey, index);
        return this.jsonUtil.deJson(value, type);
    }

    /**
     * 查询 List 缓存长度
     *
     * @param cacheKey 缓存键
     * @return 结果
     */
    @Override
    public Long listGetSize(String cacheKey) {
        return this.getRedisTemplate(cacheKey).opsForList().size(cacheKey);
    }

    /**
     * 同步 List 缓存（增量添加数据）
     *
     * @param cacheKey 缓存键
     * @param values   缓存值
     * @param leftPush 队首插入
     * @param <T>      泛型
     */
    @Override
    public <T> void listSyncByIncrement(String cacheKey, List<T> values, boolean leftPush) {
        List<String> strValues = values.stream().map(this.jsonUtil::toJson).collect(Collectors.toList());
        if (leftPush) {
            this.getRedisTemplate(cacheKey).opsForList().leftPushAll(cacheKey, strValues);
        } else {
            this.getRedisTemplate(cacheKey).opsForList().rightPushAll(cacheKey, strValues);
        }
    }

    /**
     * 同步 List 缓存（根据下标进行修改）
     *
     * @param cacheKey 缓存键
     * @param index    索引
     * @param value    值
     * @param <T>      泛型
     */
    @Override
    public <T> void listSyncByIndex(String cacheKey, long index, T value) {
        this.getRedisTemplate(cacheKey).opsForList().set(cacheKey, index, this.jsonUtil.toJson(value));
    }

    /**
     * 弹出 List 缓存元素
     *
     * @param cacheKey 缓存键
     * @param timeout  没有值时阻塞超时时间（0 不阻塞）
     * @param unit     没有值时阻塞超时时间单位（null 不阻塞）
     * @param leftPop  队首弹出
     * @param type     类型
     * @param <T>      泛型
     * @return 结果
     */
    @Override
    public <T> T listPop(String cacheKey, long timeout, TimeUnit unit, boolean leftPop, Class<T> type) {
        String value;
        if (leftPop) {
            if (timeout > 0 && unit != null) {
                value = this.getRedisTemplate(cacheKey).opsForList().leftPop(cacheKey);
            } else {
                value = this.getRedisTemplate(cacheKey).opsForList().leftPop(cacheKey, timeout, unit);
            }
        } else {
            if (timeout > 0 && unit != null) {
                value = this.getRedisTemplate(cacheKey).opsForList().rightPop(cacheKey);
            } else {
                value = this.getRedisTemplate(cacheKey).opsForList().rightPop(cacheKey, timeout, unit);
            }
        }

        return this.jsonUtil.deJson(value, type);
    }

    // endregion

    // region 统计

    /**
     * 统计自增。
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @param value    计数
     * @return 当前计数
     */
    @Override
    public long hashIncrement(String cacheKey, String hashKey, long value) {
        return this.getRedisTemplate(cacheKey).opsForHash().increment(cacheKey, hashKey, value);
    }

    /**
     * 获取统计。
     *
     * @param cacheKey 缓存键
     * @param hashKey  Hash键
     * @return 当前计数
     */
    @Override
    public long hashGetIncrement(String cacheKey, String hashKey) {
        return Long.parseLong(this.getRedisTemplate(cacheKey).opsForHash().get(cacheKey, hashKey).toString());
    }

    /**
     * 获取指定缓存键下所有的统计。
     *
     * @param cacheKey 缓存键
     * @return 所有计数
     */
    @Override
    public Map<String, Long> hashGetAllIncrement(String cacheKey) {
        Map<Object, Object> allValues = this.getRedisTemplate(cacheKey).opsForHash().entries(cacheKey);
        Map<String, Long> mapLong = new HashMap<>(allValues.size());
        for (Map.Entry<Object, Object> entry : allValues.entrySet()) {
            mapLong.put(entry.getKey().toString(), Long.parseLong(entry.getValue().toString()));
        }
        return mapLong;
    }

    /**
     * 批量删除统计。
     *
     * @param cacheKey 缓存键
     * @return 所有计数
     */
    @Override
    public void hashRemoveIncrement(String cacheKey, String... hashKeys) {
        this.hashRemove(cacheKey, hashKeys);
    }

    // endregion

    //region 分布式锁

    /**
     * setIfAbsent带过期时间的实现
     *
     * @param cacheKey          缓存键
     * @param cacheValue        缓存值
     * @param expireMilliSecond 缓存键过期时间
     * @return
     */
    @Override
    public boolean setIfAbsent(String cacheKey, String cacheValue, long expireMilliSecond) {
        return this.setIfAbsent(this.getRedisTemplate(cacheKey), cacheKey, cacheValue, expireMilliSecond);
    }

    /**
     * setIfAbsent带过期时间的实现
     *
     * @param cacheKey     缓存键
     * @param cacheValue   缓存值
     * @param expireSecond 缓存键过期时间
     * @return
     */
    @Override
    public boolean setIfAbsentOnSecond(String cacheKey, String cacheValue, int expireSecond) {
        return this.setIfAbsentOnSecond(this.getRedisTemplate(cacheKey), cacheKey, cacheValue, expireSecond);
    }

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey     锁定键(根据业务确定唯一)
     * @param lockTimeout 锁定的默认超时时间（单位毫秒，默认20秒）
     * @param expireTime  分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @param funExec     执行的业务方法
     */
    @Override
    public boolean tryLock(String lockKey, long lockTimeout, int expireTime, BooleanSupplier funExec) {
        // 验证时间输入并设置默认值
        if (lockTimeout <= 0) {
            lockTimeout = DEFAULT_LOCK_TIMEOUT;
        }
        if (expireTime <= 0) {
            expireTime = DEFAULT_LOCK_EXPIRE;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String lockValue = null;
        StringRedisTemplate redisTemplate = this.getRedisTemplate(lockKey);
        try {
            // 尝试多次锁定
            for (; ; ) {
                // 被中断时，不再尝试
                if (Thread.interrupted()) {
                    return false;
                }
                // 锁定并返回锁定值
                lockValue = lock(redisTemplate, lockKey, expireTime);
                if (StringUtils.isNotEmpty(lockValue)) {
                    // 锁定成功时，执行业务
                    return funExec.getAsBoolean();
                }
                if (stopWatch.getTotalTimeMillis() > lockTimeout) {
                    break;
                }
                // 使得不一直占用线程。
                Thread.yield();
            }
            return false;
        } finally {
            if (StringUtils.isNotEmpty(lockValue)) {
                releaseLock(redisTemplate, lockKey, lockValue);
            }
        }
    }

    /**
     * 删除存在key,value相同的键
     *
     * @param cacheKey
     * @param value    唯一标识
     * @return
     * 释放锁
     */
    @Override
    public boolean deleteIfValueEquals(String cacheKey, String value) {
        return releaseLock(this.getRedisTemplate(cacheKey), cacheKey, value);
    }

    /**
     * 释放分布式锁
     *
     * @param redisTemplate
     * @param lockKey       锁
     * @param value         唯一标识
     * @return
     * 释放锁
     */
    private boolean releaseLock(RedisTemplate redisTemplate, String lockKey, String value) {
        Object result = this.executeScript(redisTemplate, SCRIPT_RELEASE_LOCK, 1, lockKey, value);
        //释放锁成功，或锁自动过期
        return RELEASE_LOCK_SUCCESS.equals(result) || RELEASE_LOCK_AUTO_SUCCESS.equals(result);
    }

    /**
     * 锁定分布式锁
     *
     * @param redisTemplate
     * @param lockKey       锁定键(根据业务确定唯一)
     * @param expireTime    分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @return
     */
    private String lock(RedisTemplate redisTemplate, String lockKey, int expireTime) {
        if (expireTime == 0) {
            // 未设置时默认300秒
            expireTime = DEFAULT_LOCK_EXPIRE;
        }
        // 生成随机的缓存值。
        String lockValue = Double.toString(Math.random());
        // 缓存键不存在时，写入。
        if (setIfAbsent(redisTemplate, lockKey, lockValue, expireTime)) {
            return lockValue;
        }
        return null;
    }

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey 锁定键(根据业务确定唯一)
     * @param funExec 执行的业务方法
     */
    @Override
    public boolean tryLock(String lockKey, BooleanSupplier funExec) {
        return tryLock(lockKey, DEFAULT_LOCK_EXPIRE, funExec);
    }

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey    锁定键(根据业务确定唯一)
     * @param expireTime 分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @param funExec    执行的业务方法
     */
    @Override
    public boolean tryLock(String lockKey, int expireTime, BooleanSupplier funExec) {
        return tryLock(lockKey, DEFAULT_LOCK_TIMEOUT, expireTime, funExec);
    }

    /**
     * 分布式锁，锁定的默认超时时间默认20秒，分布式锁缓存键的默认过期时间默认300秒。
     *
     * @param lockKey     锁定键(根据业务确定唯一)
     * @param lockTimeout 锁定的默认超时时间（单位毫秒，默认20秒）
     * @param expireTime  分布式锁缓存键的默认过期时间（单位毫秒，默认300秒）
     * @param funExec     执行的业务方法
     */
    @Override
    public <R> R tryLock(String lockKey, long lockTimeout, int expireTime, Supplier<R> funExec) {
        // 验证时间输入并设置默认值
        if (lockTimeout <= 0) {
            lockTimeout = DEFAULT_LOCK_TIMEOUT;
        }
        if (expireTime <= 0) {
            expireTime = DEFAULT_LOCK_EXPIRE;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String lockValue = null;
        StringRedisTemplate redisTemplate = this.getRedisTemplate(lockKey);
        try {
            // 尝试多次锁定
            for (; ; ) {
                // 被中断时，不再尝试
                if (Thread.interrupted()) {
                    return null;
                }
                // 锁定并返回锁定值
                lockValue = lock(redisTemplate, lockKey, expireTime);
                if (StringUtils.isNotEmpty(lockValue)) {
                    // 锁定成功时，执行业务
                    return funExec.get();
                }
                if (stopWatch.getTotalTimeMillis() > lockTimeout) {
                    break;
                }
                // 使得不一直占用线程。
                Thread.yield();
            }
            return null;
        } finally {
            if (StringUtils.isNotEmpty(lockValue)) {
                releaseLock(redisTemplate, lockKey, lockValue);
            }
        }
    }

    /**
     * setIfAbsent带过期时间的实现
     *
     * @param redisTemplate
     * @param cacheKey      缓存键
     * @param cacheValue    缓存值
     * @param expireTime    缓存键过期时间
     * @return
     */
    private boolean setIfAbsent(RedisTemplate redisTemplate, String cacheKey, String cacheValue, long expireTime) {

        String result = this.set(redisTemplate, cacheKey, cacheValue, SET_IF_NOT_EXIST, SET_EXPIRE_TIME_MILLISECONDS, expireTime);
        //释放锁成功，或锁自动过期
        return SET_IF_ABSENT_SUCCESS.equals(result);
    }

    /**
     * setIfAbsent带过期时间的实现
     *
     * @param redisTemplate
     * @param cacheKey      缓存键
     * @param cacheValue    缓存值
     * @param expireSecond  缓存键过期时间
     * @return
     */
    private boolean setIfAbsentOnSecond(RedisTemplate redisTemplate, String cacheKey, String cacheValue, int expireSecond) {

        String result = this.set(redisTemplate, cacheKey, cacheValue, SET_IF_NOT_EXIST, SET_EXPIRE_TIME_SECONDS, expireSecond);
        //释放锁成功，或锁自动过期
        return SET_IF_ABSENT_SUCCESS.equals(result);
    }

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param redisTemplate
     * @param script        lua脚本
     * @param keyCount      缓存键参数个数
     * @param params        参数集合，缓存键在前，其他参数在后，并且跟脚本顺序对应
     * @return 脚本执行结果
     */
    private Object executeScript(final RedisTemplate redisTemplate, final String script, final int keyCount, final String... params) {
        return redisTemplate.execute((RedisCallback) connection -> {
            Object nativeConnection = connection.getNativeConnection();
            // 集群模式和单机模式分开处理
            if (nativeConnection instanceof JedisCluster) {
                // 集群模式
                return ((JedisCluster) nativeConnection).eval(script, keyCount, params);
            } else if (nativeConnection instanceof Jedis) {
                // 单机模式
                return ((Jedis) nativeConnection).eval(script, keyCount, params);
            }
            return null;
        });
    }

    private Object executeScriptSha(final RedisTemplate redisTemplate, final String sha1, final int keyCount, final String... params) {
        return redisTemplate.execute((RedisCallback) connection -> {
            Object nativeConnection = connection.getNativeConnection();
            // 集群模式和单机模式分开处理
            if (nativeConnection instanceof JedisCluster) {
                // 集群模式
                return ((JedisCluster) nativeConnection).evalsha(sha1, keyCount, params);
            } else if (nativeConnection instanceof Jedis) {
                // 单机模式
                return ((Jedis) nativeConnection).evalsha(sha1, keyCount, params);
            }
            return null;
        });
    }

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param cacheKey
     * @param script   lua脚本
     * @param keys     缓存键参数
     * @param args     其他参数在后
     * @return 脚本执行结果
     */
    @Override
    public Object executeScript(String cacheKey, RedisScript script, final List<String> keys, final List<String> args) {
        // 新建List，避免外部的List不可更改而导致异常
        List<String> paramsList;
        if (args == null) {
            paramsList = new ArrayList<>();
        } else {
            paramsList = new ArrayList<>(args);
        }
        paramsList.addAll(0, keys);
        String[] allParams = paramsList.toArray(new String[paramsList.size()]);
        return this.executeScript(cacheKey, script, keys.size(), allParams);
    }

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param script lua脚本
     * @param keys   缓存键参数
     * @param args   其他参数在后
     * @return 脚本执行结果
     */
    @Override
    public Object executeScript(RedisScript script, final List<String> keys, final List<String> args) {
        // 必须包含keys
        if (keys.size() <= 0) {
            throw new RuntimeException("lua脚本必须包含Key");
        }
        return this.executeScript(keys.get(0), script, keys, args);
    }

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param script lua脚本
     * @param keys   缓存键参数
     * @return 脚本执行结果
     */
    @Override
    public Object executeScript(RedisScript script, final List<String> keys) {
        return this.executeScript(keys.get(0), script, keys, null);
    }

    /**
     * RedisTemplate调用脚本，支持集群和单点
     * 没有直接使用RedisTemplate.execute(RedisScript<T> script, List<K> keys, Object... args)的原因：集群模式调用脚本时不支持
     *
     * @param cacheKey
     * @param script   lua脚本
     * @param keyCount 缓存键参数个数
     * @param params   参数集合，缓存键在前，其他参数在后，并且跟脚本顺序对应
     * @return 脚本执行结果
     */
    @Override
    public Object executeScript(String cacheKey, RedisScript script, final int keyCount, final String... params) {

        StringRedisTemplate redisTemplate = this.getRedisTemplate(cacheKey);
        try {
            return executeScriptSha(redisTemplate, script.getSha1(), keyCount, params);
        } catch (Throwable ex) {
            if (exceptionContainsNoScriptError(ex)) {
                return executeScript(redisTemplate, script.getScriptAsString(), keyCount, params);
            }
            throw ex;
        }
    }

    private boolean exceptionContainsNoScriptError(Throwable e) {
        Throwable current = e;
        while (current != null) {

            String exMessage = current.getMessage();
            if (exMessage != null && exMessage.contains("NOSCRIPT")) {
                return true;
            }

            current = current.getCause();
        }

        return false;
    }

    /**
     * @param redisTemplate
     * @param key
     * @param value
     * @param nxxx
     * @param expx
     * @param time
     * @return
     */
    private String set(final RedisTemplate redisTemplate, String key, String value, String nxxx, String expx, long time) {
        return (String) redisTemplate.execute((RedisCallback) connection -> {
            Object nativeConnection = connection.getNativeConnection();
            // 集群模式和单机模式都一样
            if (nativeConnection instanceof JedisCommands) {
                return ((JedisCommands) nativeConnection).set(key, value, nxxx, expx, time);
            }
            return "";
        });
    }

    //endregion

    //region 支持过期的hash操作

    @Override
    public String hashExpireGet(String cacheKey, String hashField) {
        Map<String, String> map = hashExpireMultiGet(cacheKey, hashField);
        if (map.isEmpty()) {
            return null;
        }
        return map.get(hashField);
    }

    @Override
    public Map<String, String> hashExpireMultiGet(String cacheKey, String... hashFields) {
        try {
            // 缓存键过期时间
            String cacheKeyExpire = DEFAULT_HASH_CACHE_EXPIRE;
            // 设置lua参数,顺序与lua脚本一致
            List<String> keys = Arrays.asList(cacheKey);
            String currentTime = String.valueOf(Instant.now().getEpochSecond());
            List<String> values = new ArrayList<>();
            values.add(cacheKeyExpire);
            values.add(currentTime);
            values.addAll(Arrays.asList(hashFields));

            Object luaResult = this.executeScript(
                    HashExpireRedisScripter.singleton().redisScriptHashExpireMultiGet(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            if (luaResult == null) {
                return new HashMap<>();
            }
            List<Object> listResult = (List<Object>) luaResult;
            Map<String, String> result = new HashMap<>();
            int size = hashFields.length;
            if (listResult != null && listResult.size() == size) {
                for (int i = 0; i < size; i++) {
                    Object hashValue = listResult.get(i);
                    result.put(hashFields[i], hashValue == null ? null : hashValue.toString());
                }
            }
            return result;
        } catch (Exception ex) {
            log.error(String.format("过期hash批量查询失败,cacheKey:%s,hashFields:%s", cacheKey, StringUtils.join(hashFields, ",")), ex);
        }
        return new HashMap<>();
    }

    @Override
    public void hashExpireScan(String cacheKey, int pageSize, Consumer<String> handler) {
        hashExpireScan(cacheKey, "*", pageSize, handler);
    }

    @Override
    public void hashExpireScan(String cacheKey, String matchPattern, int pageSize, Consumer<String> handler) {
        hashExpireScanAndDealExpireField(cacheKey, matchPattern, pageSize, handler, null);
    }

    /**
     * 遍历hash字段并处理过期的字段，没有handlerExpireField时，默认同步清除
     *
     * @param cacheKey
     * @param matchPattern
     * @param pageSize
     * @param handlerValue
     * @param handlerExpireField
     */
    @Override
    public void hashExpireScanAndDealExpireField(String cacheKey, String matchPattern, int pageSize, Consumer<String> handlerValue, Consumer<List<String>> handlerExpireField) {
        long current = Instant.now().getEpochSecond();
        long cacheKeyTail = current / Integer.parseInt(DEFAULT_HASH_CACHE_EXPIRE);
        String cacheKeyWithTail = String.format("%s%d", cacheKey, cacheKeyTail);
        String cacheKeyWithBefore = String.format("%s%d", cacheKey, cacheKeyTail - 1);
        List<String> expireFields = hashExpireScanAndGetExpireField(cacheKeyWithBefore, matchPattern, pageSize, handlerValue, current);
        expireFields.addAll(hashExpireScanAndGetExpireField(cacheKeyWithTail, matchPattern, pageSize, handlerValue, current));
        if (!expireFields.isEmpty()) {
            if (handlerExpireField == null) {
                // 默认处理方式：为了触发被动检查，将过期的字段清除
                List<List<String>> partition = Lists.partition(expireFields, pageSize);
                for (List<String> stringList : partition) {
                    this.hashExpireMultiGet(cacheKey, stringList.toArray(new String[0]));
                }
            } else {
                handlerExpireField.accept(expireFields);
            }
        }
    }

    /**
     * 遍历hash字段并返回过期的字段
     *
     * @param cacheKey
     * @param matchPattern
     * @param pageSize
     * @param handler
     * @param current
     * @return
     */
    private List<String> hashExpireScanAndGetExpireField(String cacheKey, String matchPattern, int pageSize, Consumer<String> handler, long current) {
        StringRedisTemplate redisTemplate = this.getRedisTemplate(cacheKey);
        Cursor<Map.Entry<Object, Object>> cursor = null;
        List<String> listExpireField = new ArrayList<>();
        try {
            cursor = redisTemplate.opsForHash().scan(cacheKey, ScanOptions.scanOptions().match(matchPattern).count(pageSize).build());
            while (cursor.hasNext()) {
                Map.Entry<Object, Object> next = cursor.next();
                Object nextValue = next.getValue();
                if (nextValue == null) {
                    continue;
                }
                HashExpireValue expireValue = null;
                try {
                    expireValue = hashExpireValueConverter.analyze(nextValue.toString());
                } catch (Exception e) {
                    log.error("解析hash原值失败", e);
                }
                if (expireValue == null) {
                    continue;
                }
                if (current > expireValue.getExpire()) {
                    listExpireField.add(next.getKey().toString());
                    continue;
                }
                handler.accept(expireValue.getRealValue());
            }
        } catch (Exception ex) {
            log.error(String.format("过期hash-scan操作失败,cacheKey:%s", cacheKey), ex);
        } finally {
            if (cursor != null) {
                try {
                    cursor.close();
                } catch (IOException e) {
                    log.error(String.format("过期hash-scan游标关闭失败,cacheKey:%s", cacheKey), e);
                }
            }
        }
        return listExpireField;
    }

    @Override
    public boolean hashExpireSet(String cacheKey, String hashField, String hashValue, long hashExpire, TimeUnit unit) {
        Map<String, HashExpireValue> fieldValues = new HashMap<>();
        long expireSecond = unit.toSeconds(hashExpire);
        fieldValues.put(hashField, new HashExpireValue(expireSecond, hashValue));
        return hashExpireMultiSet(cacheKey, fieldValues) > 0;
    }

    @Override
    public int hashExpireMultiSet(String cacheKey, Map<String, String> hashFieldValues, long hashExpire, TimeUnit unit) {
        if (hashFieldValues.size() > HASH_MULTI_SET_MAX) {
            throw new IllegalArgumentException(String.format("hash批量更新个数不能超过%d", HASH_MULTI_SET_MAX));
        }
        Map<String, HashExpireValue> fieldValues = new HashMap<>();
        long expireSecond = unit.toSeconds(hashExpire);
        for (Map.Entry<String, String> entry : hashFieldValues.entrySet()) {
            fieldValues.put(entry.getKey(), new HashExpireValue(expireSecond, entry.getValue()));
        }
        return hashExpireMultiSet(cacheKey, fieldValues);
    }

    /**
     * 批量分页，每页数
     */
    private static final int BATCH_PAGE_SIZE = 100;

    /**
     * 过期hash批量更新
     *
     * @param cacheKey
     * @param hashFieldValues 注意：版本号不能带字符#
     * @return
     */
    @Override
    public int hashExpireMultiSet(String cacheKey, Map<String, HashExpireValue> hashFieldValues) {
        if (hashFieldValues.size() <= BATCH_PAGE_SIZE) {
            return hashExpireMultiSetOnSingePage(cacheKey, hashFieldValues.entrySet());
        }
        // 分页
        List<Map.Entry<String, HashExpireValue>> lst = hashFieldValues.entrySet().stream().collect(Collectors.toList());
        int ret = 0;
        List<List<Map.Entry<String, HashExpireValue>>> partitions = Lists.partition(lst, BATCH_PAGE_SIZE);
        for (List<Map.Entry<String, HashExpireValue>> partition : partitions) {
            ret += hashExpireMultiSetOnSingePage(cacheKey, partition);
        }
        return ret;
    }

    /**
     * 过期hash批量更新
     *
     * @param cacheKey
     * @param hashFieldValues 注意：版本号不能带字符#
     * @return
     */
    private int hashExpireMultiSetOnSingePage(String cacheKey, Collection<Map.Entry<String, HashExpireValue>> hashFieldValues) {
        try {
            // 缓存键过期时间
            String cacheKeyExpire = DEFAULT_HASH_CACHE_EXPIRE;
            // 设置lua参数,支持集群
            List<String> keys = Arrays.asList(cacheKey);
            String currentTime = String.valueOf(Instant.now().getEpochSecond());
            List<String> values = new ArrayList<>();
            values.add(cacheKeyExpire);
            values.add(currentTime);
            for (Map.Entry<String, HashExpireValue> item : hashFieldValues) {
                // 这里赋值顺序与lua脚本一致
                values.add(item.getKey());
                values.add(String.valueOf(item.getValue().getExpire()));
                values.add(item.getValue().getRealValue());
                values.add(item.getValue().getVersion());
            }

            Object luaResult = this.executeScript(
                    HashExpireRedisScripter.singleton().redisScriptHashExpireMultiSet(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );
            if (luaResult == null) {
                return 0;
            }
            return Integer.parseInt(luaResult.toString());
        } catch (Exception ex) {
            log.error(String.format("过期hash批量更新失败,cacheKey:%s,hashFields:%s", cacheKey, StringUtils.join(hashFieldValues.stream().map(p -> p.getKey()), ",")), ex);
        }
        return 0;
    }

    @Override
    public int hashExpireMultiSetNx(String cacheKey, Map<String, HashExpireValue> hashFieldValues) {
        // TODO:wph
        return 0;
    }

    @Override
    public int hashExpireMultiSetOnVersion(String cacheKey, Map<String, OriginVersionHashValue> hashFieldValues) {
        // TODO:wph
        return 0;
    }

    @Override
    public int hashExpireRemove(String cacheKey, String... hashFields) {
        try {
            // 缓存键过期时间
            String cacheKeyExpire = DEFAULT_HASH_CACHE_EXPIRE;
            // 设置lua参数,顺序与lua脚本一致
            List<String> keys = Arrays.asList(cacheKey);
            String currentTime = String.valueOf(Instant.now().getEpochSecond());
            List<String> values = new ArrayList<>();
            values.add(cacheKeyExpire);
            values.add(currentTime);
            values.addAll(Arrays.asList(hashFields));

            Object luaResult = this.executeScript(
                    HashExpireRedisScripter.singleton().redisScriptHashExpireMultiDel(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );

            return luaResult == null ? 0 : Integer.parseInt(luaResult.toString());
        } catch (Exception ex) {
            log.error(String.format("过期hash批量删除失败,cacheKey:%s,hashFields:%s", cacheKey, StringUtils.join(hashFields, ",")), ex);
        }
        return 0;
    }

    @Override
    public int hashExpireRemove(String cacheKey) {
        try {
            // 缓存键过期时间
            String cacheKeyExpire = DEFAULT_HASH_CACHE_EXPIRE;
            // 设置lua参数,顺序与lua脚本一致
            List<String> keys = Arrays.asList(cacheKey);
            String currentTime = String.valueOf(Instant.now().getEpochSecond());
            List<String> values = new ArrayList<>();
            values.add(cacheKeyExpire);
            values.add(currentTime);

            Object luaResult = this.executeScript(
                    HashExpireRedisScripter.singleton().redisScriptHashExpireKeyDel(),
                    // Lua脚本中的Key列表
                    keys,
                    // Lua脚本args列表
                    values
            );

            return luaResult == null ? 0 : Integer.parseInt(luaResult.toString());
        } catch (Exception ex) {
            log.error(String.format("过期hash删除失败,cacheKey:%s", cacheKey), ex);
        }
        return 0;
    }

    /**
     * 取时间
     *
     * @param cacheKey
     * @return
     */
    @Override
    public String time(String cacheKey) {
        // 设置lua参数,顺序与lua脚本一致
        List<String> keys = Arrays.asList(cacheKey);
        List<String> values = new ArrayList<>();
        Object luaResult = this.executeScript(
                HashExpireRedisScripter.singleton().redisScriptHashExpireTest(),
                // Lua脚本中的Key列表
                keys,
                // Lua脚本args列表
                values
        );

        List<Object> listResult = (List<Object>) luaResult;
        if (listResult != null && listResult.isEmpty()) {
            return listResult.get(0).toString();
        }
        return null;
    }

    //endregion

    // endregion
}
