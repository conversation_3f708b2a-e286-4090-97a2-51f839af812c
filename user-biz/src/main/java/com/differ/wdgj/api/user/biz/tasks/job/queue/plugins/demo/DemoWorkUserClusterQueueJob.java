package com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.demo;

import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.condition.ConditionalOnEnvType;
import com.differ.wdgj.api.user.biz.infrastructure.data.SystemEnvTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.WorkFacade;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.AbstractQueueJob;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.SimpleQueueJobParameter;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.AddResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.JobResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.UserQueueJobData;
import com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue.UserClusterJobTaskSourceQueue;

import java.util.List;

/**
 * 示例：用户和集群两级排队的队列定时任务
 *
 * <AUTHOR>
 * @date 2024/5/29 11:59
 */

@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "job.queue.work.demo",
        cron = "0/3 * * * * ?"
)
@SimpleQueueJobParameter(jobCode = "job.queue.work.demo", JobQueue = UserClusterJobTaskSourceQueue.class)
@ConditionalOnEnvType({SystemEnvTypeEnum.DEV})
public class DemoWorkUserClusterQueueJob extends AbstractQueueJob<DemoWorkUserClusterQueueJob.DemoQueueData> {

    /**
     * 获取自动生成的初始化任务，强调 ：是自动的，不是外部加的，是集群任务定时初始化的，不是直接拿来用的
     *
     * @return 全部任务
     */
    @Override
    protected List<DemoQueueData> getAllAutoInitTasks() {
        return null;
    }

    public AddResult addTask(WorkData<DemoWorkData> workData) {
        WorkFacade facade = new WorkFacade();
        CreateResult createResult = facade.createWork(workData);
        if (!createResult.success()) {
            return AddResult.failResult(createResult.getErrorCode() != null ? createResult.getErrorCode() : "创建任务失败，请重试");
        }
        // 创建任务进度后必须把进度任务的ID带上，供执行用
        DemoQueueData demoQueueData = new DemoQueueData();
        demoQueueData.setContext(workData.getWorkContext());
        demoQueueData.setData(workData.getData().getData());
        demoQueueData.setMemberName(workData.getMemberName());

        // 加入任务失败时，要设置进度结果为失败
        AddResult addSuccess = super.addTask(demoQueueData);
        if (!addSuccess.isSuccess()) {
            String message = "存在执行中的任务,请稍后再试";
            return AddResult.failResult(message);
        }

        return addSuccess;

    }

    /**
     * 取线程池
     *
     * @return 线程池
     */
    @Override
    protected TaskEnum taskPool() {
        return TaskEnum.API_SYNC;
    }

    /**
     * 获取任务执行的最小时间（单位：秒）,大于0才会起效限制
     * 最小执行时间，当执行小于此时间时，会sleep后再执行完成回调
     *
     * @return 拉取执行频率
     */
    @Override
    protected int getMinExecSeconds() {
        return 0;
    }

    /**
     * 执行任务
     *
     * @param data 任务数据
     * @return 任务执行结果：完成，重入队
     */
    @Override
    protected JobResult execute(DemoQueueData data) {
        WorkFacade facade = new WorkFacade();
        DemoWorkData d = new DemoWorkData();
        d.setData(data.getData());
        WorkData<DemoWorkData> workData = WorkData.of(data.getContext(), d);

        facade.execWork(workData,data.getTaskId(),false);

        System.err.println("执行任务：" + data.getData());
        return JobResult.FINISH;
    }

    public static class DemoWorkData extends WorkArgs {

        private String data;

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }

        /**
         * 参数验证
         *
         * @return
         */
        @Override
        public String validArgs() {
            return null;
        }
    }

    public static class DemoQueueData extends UserQueueJobData {

        private WorkContext context;

        private String taskId;

        private String data;

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public WorkContext getContext() {
            return context;
        }

        public void setContext(WorkContext context) {
            this.context = context;
        }

        @Override
        public String uniqueSign() {
            return taskId;
        }

        /**
         * 任务唯一键ID
         *
         * @return
         */
        @Override
        public String taskUniqueId() {
            return taskId;
        }
    }
}
