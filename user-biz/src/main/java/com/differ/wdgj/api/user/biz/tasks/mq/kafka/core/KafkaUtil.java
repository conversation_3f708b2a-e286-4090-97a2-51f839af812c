package com.differ.wdgj.api.user.biz.tasks.mq.kafka.core;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/1/27 17:49
 */
public class KafkaUtil {

    /**
     * 设置默认初始值
     *
     * @return
     */
    public static KafkaProperties defaultKafkaConfigProperties(String consumerGroupId, String defaultServers) {
        KafkaProperties kafkaProperties = new KafkaProperties();
        // 设置默认服务器地址
        if (StringUtils.isNotEmpty(defaultServers)) {
            List<String> bootstrapServers = Arrays.stream(defaultServers.split(",")).collect(Collectors.toList());
            kafkaProperties.setBootstrapServers(bootstrapServers);
        }
        /*
        设置消费者默认值
         */
        KafkaProperties.Consumer consumerProperties = kafkaProperties.getConsumer();
        if (StringUtils.isNotEmpty(consumerGroupId)) {
            consumerProperties.setGroupId(consumerGroupId);
        } else {
            consumerProperties.setGroupId("wdgj-api-consumer");
        }
        // 关闭自动提交
        consumerProperties.setEnableAutoCommit(false);
        // 拉取最大记录数
        consumerProperties.setMaxPollRecords(200);

        Map<String, String> defaultProperties = new HashMap<>();
        // 不能设置太小，防止频繁引发rebalance
        defaultProperties.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, "600000");
        // 连接session
        defaultProperties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "60000");
        // 心跳
        defaultProperties.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, "6000");
        // 添加消费端拦截器
        defaultProperties.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG, ApiKafkaMessageInterceptor.class.getName());
        // 生产者 提交延时，后缀 producer.linger.ms
        defaultProperties.put(ProducerConfig.LINGER_MS_CONFIG, "100");
        kafkaProperties.setProperties(defaultProperties);

        /*
        设置生产者默认值
         */
        KafkaProperties.Producer producerProperties = kafkaProperties.getProducer();
        // 重试次数,后缀 producer.retries 对应ProducerConfig.RETRIES_CONFIG
        producerProperties.setRetries(3);
        // 生产端批量大小,积累的消息达到时会提交给kafka,后缀 producer.batch-size 对应ProducerConfig.BATCH_SIZE_CONFIG，16384改为1024
        producerProperties.setBatchSize(1024);
        // 生产端缓冲区大小,后缀 producer.retries 对应ProducerConfig.BUFFER_MEMORY_CONFIG
        producerProperties.setBufferMemory(33554432L);

        return kafkaProperties;
    }
}
