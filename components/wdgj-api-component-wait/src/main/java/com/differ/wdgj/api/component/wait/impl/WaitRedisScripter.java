package com.differ.wdgj.api.component.wait.impl;

import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description redis脚本处理
 * <AUTHOR>
 * @Date 2021/9/15 17:00
 */
public class WaitRedisScripter {

    /**
     * lua脚本对象集合,是否进入排队
     */
    private Map<String, DefaultRedisScript> redisScripts;

    /**
     * lua脚本路径,是否进入排队
     */
    private static final String LUA_IS_WAIT_AND_PUT = "scripts/wait/is_wait_and_put.lua";

    /**
     * lua脚本路径,是否加入排队（拉取模式）
     */
    private static final String LUA_ADD_WAIT_FOR_PULL = "scripts/wait/add_wait_for_pull.lua";

    /**
     * lua脚本路径,是否进入排队,支持插队
     */
    private static final String LUA_IS_WAIT_AND_PUT_JUMP_QUEUE = "scripts/wait/extend/is_wait_and_put_jump_queue.lua";

    /**
     * lua脚本路径,获取排队数据
     */
    private static final String LUA_PULL_NEXT_WAIT = "scripts/wait/pull_next_wait.lua";

    /**
     * lua脚本路径,删除排队数据
     */
    private static final String LUA_DELETE_WAIT_OR_EXEC = "scripts/wait/delete_wait_or_exec.lua";
    /**
     * lua脚本路径,批量删除排队数据
     */
    private static final String LUA_BATCH_DELETE_WAIT = "scripts/wait/batch_delete_wait_or_exec.lua";

    /**
     * lua脚本路径,批量查询排队状态
     */
    private static final String LUA_BATCH_IS_WAIT = "scripts/wait/batch_is_wait.lua";
    /**
     * lua脚本路径,排队监控数据
     */
    private static final String LUA_WAIT_MONITOR = "scripts/wait/wait_monitor.lua";
    /**
     * lua脚本路径,兜底数据检查
     */
    private static final String LUA_CHECK_DATA = "scripts/wait/check_data.lua";

    /**
     * lua脚本路径,批量查询排队数据
     */
    private static final String LUA_GET_WAIT_DATA = "scripts/wait/get_wait_data.lua";

    //region 构造和枚举单例

    private WaitRedisScripter() {
        // 私有，为了单例
    }

    /**
     * 枚举单例
     *
     * @return
     */
    public static WaitRedisScripter singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        SINGLETON;

        private WaitRedisScripter instance;

        private SingletonEnum() {
            WaitRedisScripter waitRedisScripter = new WaitRedisScripter();
            waitRedisScripter.initAllScript();
            instance = waitRedisScripter;
        }
    }

    //endregion

    /**
     * 初始lua
     */
    protected void initAllScript() {
        redisScripts = new HashMap<>();
        initScript(LUA_IS_WAIT_AND_PUT, Integer.class);
        initScript(LUA_ADD_WAIT_FOR_PULL, Integer.class);
        initScript(LUA_IS_WAIT_AND_PUT_JUMP_QUEUE, Integer.class);
        initScript(LUA_PULL_NEXT_WAIT, java.util.List.class);
        initScript(LUA_DELETE_WAIT_OR_EXEC, Integer.class);
        initScript(LUA_BATCH_DELETE_WAIT, Integer.class);
        initScript(LUA_BATCH_IS_WAIT, java.util.List.class);
        initScript(LUA_WAIT_MONITOR, java.util.List.class);
        initScript(LUA_CHECK_DATA, Integer.class);
        initScript(LUA_GET_WAIT_DATA, java.util.List.class);
    }

    /**
     * 初始单个lua
     */
    protected void initScript(String script, Class<?> resultType) {
        DefaultRedisScript redisScript = new DefaultRedisScript();
        redisScript.setLocation(new ClassPathResource(script));
        redisScript.setResultType(resultType);
        redisScripts.put(script, redisScript);
    }

    /**
     * lua脚本对象,是否进入排队
     */
    public DefaultRedisScript redisScriptIsWaitAndPut() {
        return redisScripts.get(LUA_IS_WAIT_AND_PUT);
    }

    /**
     * lua脚本对象,是否进入排队(拉取模式)
     */
    public DefaultRedisScript redisScriptAddWaitForPull() {
        return redisScripts.get(LUA_ADD_WAIT_FOR_PULL);
    }

    /**
     * lua脚本对象,是否进入排队,支持插队
     */
    public DefaultRedisScript redisScriptIsWaitAndPutJumpQueue() {
        return redisScripts.get(LUA_IS_WAIT_AND_PUT_JUMP_QUEUE);
    }

    /**
     * lua脚本对象,获取排队数据
     */
    public DefaultRedisScript redisScriptPullNextWait() {
        return redisScripts.get(LUA_PULL_NEXT_WAIT);
    }

    /**
     * lua脚本对象,删除排队数据
     */
    public DefaultRedisScript redisScriptDeleteWaitOrExec() {
        return redisScripts.get(LUA_DELETE_WAIT_OR_EXEC);
    }

    /**
     * lua脚本对象,批量删除排队数据
     */
    public DefaultRedisScript redisScriptBatchDeleteWait() {
        return redisScripts.get(LUA_BATCH_DELETE_WAIT);
    }

    /**
     * lua脚本对象,批量查询排队状态
     */
    public DefaultRedisScript redisScriptBatchIsWait() {
        return redisScripts.get(LUA_BATCH_IS_WAIT);
    }

    /**
     * lua脚本对象,排队监控数据
     */
    public DefaultRedisScript redisScriptWaitMonitor() {
        return redisScripts.get(LUA_WAIT_MONITOR);
    }

    /**
     * lua脚本对象,兜底数据检查
     */
    public DefaultRedisScript redisScriptCheckData() {
        return redisScripts.get(LUA_CHECK_DATA);
    }

    /**
     * lua脚本对象,批量查询排队数据
     */
    public DefaultRedisScript redisScriptGetWaitData() {
        return redisScripts.get(LUA_GET_WAIT_DATA);
    }
}
