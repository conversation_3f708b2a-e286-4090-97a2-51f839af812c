package com.differ.wdgj.api.user.biz.domain.operation;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.RefundTypeCovertOperationTest;
import com.differ.wdgj.api.user.biz.domain.order.common.operation.OrderSendStatusOperationTest;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * Operation类测试套件
 * 统一运行所有Operation相关的单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午7:00
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    RefundTypeCovertOperationTest.class,        // RefundTypeCovertOperation单元测试
    OrderSendStatusOperationTest.class          // OrderSendStatusOperation单元测试
})
public class OperationTestSuite {
    // 测试套件类，无需实现内容
}
