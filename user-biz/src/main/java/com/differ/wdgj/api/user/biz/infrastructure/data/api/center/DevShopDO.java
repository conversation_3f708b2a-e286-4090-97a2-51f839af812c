package com.differ.wdgj.api.user.biz.infrastructure.data.api.center;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;

import java.time.LocalDateTime;

/**
 * 店铺基础数据（表dev_shop）
 * <AUTHOR>
 * @date 2024-03-21 16:11
 */
public class DevShopDO {
    //region 属性
    /**
    * 店铺id。
    */
    private int shopId;

    /**
    * 外部平台的店铺ID。
    */
    private String outShopId;

    /**
    * 外部账号名
    */
    private String outAccount;

    /**
    * 外部平台类型{@link OuterApiEnum}
    */
    private int outPlatTypes;

    /**
    * 店铺名称。
    */
    private String shopName;

    /**
    * 店铺联系手机。
    */
    private String mobie;

    /**
    * 客户店铺唯一标识(有用户的APIKey+(由本站AppKey+外部Token生成)。
    */
    private String token;

    /**
    * 平台枚举值(BusinessPlats或LogisticsPlats枚举值之一)。
    */
    private int platValue;

    /**
    * 店铺是否启用。
    */
    private boolean isActived;

    /**
    * 是否删除。
    */
    private boolean isDelete;

    /**
    * 用户名称。
    */
    private String userName;

    /**
    * 代理商平台AppKey(仅用于授权型平台)，加密。
    */
    private String thirdAppKey;

    /**
    * 代理商平台密钥(仅用于授权型平台)，加密。
    */
    private String thirdAppSecret;

    /**
    * 会话编号(仅用于自用型平台) ，加密。
    */
    private String sessionKey;

    /**
    * SessionKey过期时间。
    */
    private LocalDateTime sessionKeyExpireTime;

    /**
    * SessionKey有效周期(单位：秒)。
    */
    private int sessionKeyTimeout;

    /**
    * 用于刷新失效的SessionKey  ，加密。
    */
    private String refreshToken;

    /**
    * 刷新失效的SessionKey过期时间。
    */
    private LocalDateTime refreshTokenExpireTime;

    /**
    * 授权持续时间(单位：秒)。
    */
    private int authorizationDuration;

    /**
    * 授权时间。
    */
    private LocalDateTime authorizationTime;

    /**
    * 订购到期时间
    */
    private LocalDateTime subscriptionExpireTime;

    /**
    * 自用型平台自定义参数(参数中文名称#@^@#参数英文名称#@^@#值，多个之间以*#$^$#*号分隔，如：应用key#@^@#appkey*#$^$#*token#@^@#token，值加密)。
    */
    private String customParms;

    /**
    * 其他参数。
    */
    private String otherParms;

    /**
    * 创建时间。
    */
    private LocalDateTime createTime;

    /**
    * 最后修改时间。
    */
    private LocalDateTime lastModifyTime;

    /**
    /// 设置或获取 拼音关键字
    */
    private String pinYinKeyWords;

    /**
    * 通道密钥(目前只有淘宝)。
    */
    private String topSecret;

    /**
    * 授权状态(对应枚举值ShopAuthStatusEnum)
    */
    private int authStatus;

    /**
    * 平台店铺ID
    */
    private String platShopId;

    /**
    * 平台店铺ID
    */
    private String platShopName;
    //endregion

    //region get/set
    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public String getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(String outShopId) {
        this.outShopId = outShopId;
    }

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public int getOutPlatTypes() {
        return outPlatTypes;
    }

    public void setOutPlatTypes(int outPlatTypes) {
        this.outPlatTypes = outPlatTypes;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getMobie() {
        return mobie;
    }

    public void setMobie(String mobie) {
        this.mobie = mobie;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getPlatValue() {
        return platValue;
    }

    public void setPlatValue(int platValue) {
        this.platValue = platValue;
    }

    public boolean isActived() {
        return isActived;
    }

    public void setActived(boolean actived) {
        isActived = actived;
    }

    public boolean isDelete() {
        return isDelete;
    }

    public void setDelete(boolean delete) {
        isDelete = delete;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getThirdAppKey() {
        return thirdAppKey;
    }

    public void setThirdAppKey(String thirdAppKey) {
        this.thirdAppKey = thirdAppKey;
    }

    public String getThirdAppSecret() {
        return thirdAppSecret;
    }

    public void setThirdAppSecret(String thirdAppSecret) {
        this.thirdAppSecret = thirdAppSecret;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public LocalDateTime getSessionKeyExpireTime() {
        return sessionKeyExpireTime;
    }

    public void setSessionKeyExpireTime(LocalDateTime sessionKeyExpireTime) {
        this.sessionKeyExpireTime = sessionKeyExpireTime;
    }

    public int getSessionKeyTimeout() {
        return sessionKeyTimeout;
    }

    public void setSessionKeyTimeout(int sessionKeyTimeout) {
        this.sessionKeyTimeout = sessionKeyTimeout;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public LocalDateTime getRefreshTokenExpireTime() {
        return refreshTokenExpireTime;
    }

    public void setRefreshTokenExpireTime(LocalDateTime refreshTokenExpireTime) {
        this.refreshTokenExpireTime = refreshTokenExpireTime;
    }

    public int getAuthorizationDuration() {
        return authorizationDuration;
    }

    public void setAuthorizationDuration(int authorizationDuration) {
        this.authorizationDuration = authorizationDuration;
    }

    public LocalDateTime getAuthorizationTime() {
        return authorizationTime;
    }

    public void setAuthorizationTime(LocalDateTime authorizationTime) {
        this.authorizationTime = authorizationTime;
    }

    public LocalDateTime getSubscriptionExpireTime() {
        return subscriptionExpireTime;
    }

    public void setSubscriptionExpireTime(LocalDateTime subscriptionExpireTime) {
        this.subscriptionExpireTime = subscriptionExpireTime;
    }

    public String getCustomParms() {
        return customParms;
    }

    public void setCustomParms(String customParms) {
        this.customParms = customParms;
    }

    public String getOtherParms() {
        return otherParms;
    }

    public void setOtherParms(String otherParms) {
        this.otherParms = otherParms;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getPinYinKeyWords() {
        return pinYinKeyWords;
    }

    public void setPinYinKeyWords(String pinYinKeyWords) {
        this.pinYinKeyWords = pinYinKeyWords;
    }

    public String getTopSecret() {
        return topSecret;
    }

    public void setTopSecret(String topSecret) {
        this.topSecret = topSecret;
    }

    public int getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(int authStatus) {
        this.authStatus = authStatus;
    }

    public String getPlatShopId() {
        return platShopId;
    }

    public void setPlatShopId(String platShopId) {
        this.platShopId = platShopId;
    }

    public String getPlatShopName() {
        return platShopName;
    }

    public void setPlatShopName(String platShopName) {
        this.platShopName = platShopName;
    }
    //endregion
}
