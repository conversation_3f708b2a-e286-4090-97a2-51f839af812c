'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

interface SubQueue {
    boolean currentEnable() : 队列当前环境是否可用
    boolean limited() : 队列是否触发限流
    boolean sendToCurrentQueue(T message, QueueHeader header) : 发送数据到当前队列
    void initMessageListener(Class<T> dataClazz, Function<QueueResult, T, QueueHeader> fun) : 注册消息处理器
    void initDelayFun(FunctionOne<DelayGradeEnum, QueueHeader> funGetDelay) : 注册延迟时间函数
    SubQueueContext getContext() : 获取当前队列上下文
}
interface MultiQueueHandler {
    void init() ： 初始化
    QueueResult receiveMulti(T message, QueueHeader header) ： 执行消息
    void sendMulti(T message) ： 发送消息
}

interface ApiMultiMQ <<多队列注解>> {

        String code() : 队列标识
        SubQueueEnum[] subQueues() :子队列的的列表，列表顺序表示发送优先顺序
        boolean limitEnable() : 是否开启限流
        String[] sitesToSend() : 发送站点
        String[] sitesToReceive() ：接收站点
        Class<? extends MultiSendInterceptor> multiSendInterceptor() ： 消息发送拦截器
        Class<? extends MultiReceiveInterceptor> multiReceiveInterceptor() : 消息消费拦截器
        Class<? extends MultiConsumerFailCallback> consumerFailCallback() : 消息消费重试达到最大次数回调处理
        DelayGradeEnum delayTime() : 消费延时级别
}

abstract class AbstractMultiQueue {

    #DelayGradeEnum getDelayGrade(QueueHeader header) ： 延迟消息的延迟级别
    #SubQueue<T> getSubQueue(SubQueueEnum subQueueEnum) : 用于需要由业务控制指定子队列处理逻辑的场景
}

abstract class AbstractSubQueue {
    #boolean sendToSubQueue(T message, QueueHeader header) : 发送到当前子队列
    #final QueueResult execConsume(T data, QueueHeader header) : 执行消息处理
}

abstract class AbstractPullSubQueue {
    # void registerListener() : 注册监听器
    # void triggerTaskAsync() : 注册后，由定时任务触发，线程池拉取消息
    # abstract void triggerTask() : 注册后，由定时任务触发，拉取消息
    # abstract TaskEnum getConsumerPool() ： 消费者线程池
}

AbstractMultiQueue <|.. DemoMultiQueue  :继承
MultiQueueHandler <|.. AbstractMultiQueue  :继承

ApiMultiMQ <.. AbstractMultiQueue :依赖（代码体现：注解）

SubQueue <--* AbstractMultiQueue :组合

SubQueue <|.. AbstractSubQueue
AbstractSubQueue <|.. JmqSubQueue :继承
SubQueue <|.. AbstractPullSubQueue
AbstractPullSubQueue <|.. DbSubQueue  :继承
AbstractPullSubQueue <|.. KafkaSubQueue  :继承
DbSubQueue <|.. DbCenterSubQueue  :继承
DbSubQueue <|.. DbUserSubQueue  :继承
DbSubQueue <|.. DbUserRetryAdapterSubQueue  :继承

JmqAdapter <.. JmqSubQueue :依赖（代码体现：成员变量）
KafkaAdapter <.. KafkaSubQueue :依赖（代码体现：成员变量）


@enduml