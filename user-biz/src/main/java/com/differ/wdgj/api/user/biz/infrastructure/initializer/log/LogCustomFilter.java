package com.differ.wdgj.api.user.biz.infrastructure.initializer.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.Marker;

/**
 * @Description 日志过滤器
 * <AUTHOR>
 * @Date 2022/10/11 11:55
 */
public interface LogCustomFilter {

    /**
     * 初始化
     */
    default void init() {};

    /**
     * 是否过滤写日志
     * @param marker
     * @param logger
     * @param level
     * @param format
     * @param params
     * @param t
     * @return
     */
    FilterReply filter(Marker marker, ch.qos.logback.classic.Logger logger, Level level, String format, Object[] params, Throwable t);
}
