package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work;

import java.time.LocalDateTime;

/**
 * 售后单下载工作任务结果 - 子任务维度
 *
 * <AUTHOR>
 * @date 2024/9/26 下午4:11
 */
public class LoadAfterSaleDetail {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 子任务类型key,要求能区别于同一任务下的不同的子任务
     */
    private String subTypeKey;

    /**
     * 子任务类型key - 展示
     */
    private String subTypeCaption;

    /**
     * 当前子任务下载数据大小
     */
    private int dataSize;

    /**
     * 最后成功的数据时间
     */
    private LocalDateTime lastSuccessDataTime;

    //region 构造
    public LoadAfterSaleDetail(boolean success, String message) {
        this.message = message;
        this.success = success;
    }
    public LoadAfterSaleDetail() {

    }
    //endregion

    //region 公共方法

    /**
     * 构建子任务结果
     *
     * @param subTask    子任务
     * @param loadResult 下载结果
     * @return 子任务结果
     */
    public LoadAfterSaleDetail of(AfterSaleLoadSubTask subTask, LoadAfterSaleSubTaskResult loadResult) {
        // 业务赋值
        this.subTypeKey = subTask.getSubTypeKey();
        this.subTypeCaption = subTask.getSubTypeName();

        if (loadResult != null && loadResult.getPageArgs() != null) {
            AfterSalePageArgs pageArgs = loadResult.getPageArgs();
            // 下载总数
            this.dataSize += pageArgs.getDataSize();
            // 最后成功时间
            if (pageArgs.getLastSuccessDataTime() != null) {
                if (lastSuccessDataTime == null || pageArgs.getLastSuccessDataTime().isAfter(lastSuccessDataTime)) {
                    this.lastSuccessDataTime = pageArgs.getLastSuccessDataTime();
                }
            }
        }
        return this;
    }

    /**
     * 构建子任务结果
     *
     * @param detail 子任务结果
     */
    public void margeDetail(LoadAfterSaleDetail detail) {
        this.subTypeKey = detail.getSubTypeKey();
        this.dataSize += detail.getDataSize();
        // 最后成功时间
        if (detail.getLastSuccessDataTime() != null) {
            if (lastSuccessDataTime == null || detail.getLastSuccessDataTime().isAfter(lastSuccessDataTime)) {
                this.lastSuccessDataTime = detail.getLastSuccessDataTime();
            }
        }
        // 只记录最初一次失败的结果
        if (!this.success) {
            return;
        }
        this.success = detail.isSuccess();
        this.message = detail.getMessage();
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSubTypeKey() {
        return subTypeKey;
    }

    public void setSubTypeKey(String subTypeKey) {
        this.subTypeKey = subTypeKey;
    }

    public String getSubTypeCaption() {
        return subTypeCaption;
    }

    public void setSubTypeCaption(String subTypeCaption) {
        this.subTypeCaption = subTypeCaption;
    }

    public int getDataSize() {
        return dataSize;
    }

    public void setDataSize(int dataSize) {
        this.dataSize = dataSize;
    }

    public LocalDateTime getLastSuccessDataTime() {
        return lastSuccessDataTime;
    }

    public void setLastSuccessDataTime(LocalDateTime lastSuccessDataTime) {
        this.lastSuccessDataTime = lastSuccessDataTime;
    }

    //endregion
}
