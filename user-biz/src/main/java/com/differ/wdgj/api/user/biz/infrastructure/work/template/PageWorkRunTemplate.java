package com.differ.wdgj.api.user.biz.infrastructure.work.template;

import com.differ.wdgj.api.user.biz.infrastructure.work.business.page.PageWorkBusinessProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.*;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.AbstractPageWorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.RunMode;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDetailOperate;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkSubTaskOperate;

import java.util.List;

/**
 * 工作任务执行模板（按页下载）
 * 模板流程：
 * 1、开启并同步一次工作心跳
 * 2、初始化任务，更新任务状态、初始化进度
 * 3、创建子任务
 * 4、执行所有子任务
 * 5、更新任务状态为完成，包含进度的最后兜底完成
 * 6、关闭工作心跳
 *
 * <AUTHOR>
 * @date 2024/6/24 13:42
 */
public class PageWorkRunTemplate extends AbstractWorkRunTemplate {

    private AbstractPageWorkFactory workFactory;

    public PageWorkRunTemplate(WorkEnum workEnum, AbstractPageWorkFactory workFactory) {
        super(workEnum);
        this.workFactory = workFactory;
    }

    /**
     * 查询工作任务
     *
     * @param member
     * @param taskId
     * @return 任务ID
     */
    @Override
    protected WorkData<?> getWork(String member, String taskId) {
        WorkSubTaskOperate dataOperate = workFactory.createDataOperate();
        return dataOperate.getWork(member, taskId);
    }

    /**
     * 初始化工作任务为执行状态
     *
     * @param workData
     * @param taskId
     */
    @Override
    protected void initToExecute(WorkData<?> workData, String taskId) {
        DataOperateContext dataOperateContext = DataOperateContext.of(workData.getMemberName(), workData.getWorkType(), taskId);
        WorkDetailOperate workDetailOperate = workFactory.createDataOperate().getDetailOperate(dataOperateContext);
        workDetailOperate.initToExecute(workData.getMemberName(), taskId);
    }

    /**
     * 执行所任务，默认同步执行
     *
     * @param workData
     * @param taskId
     * @return
     */
    @Override
    protected WorkResult execWork(WorkData<?> workData, String taskId) {
        PageWorkBusinessProcessor businessProcessor = workFactory.createBusinessProcessor(workData);
        // 创建子任务
        List<SubPageTask> subTasks = businessProcessor.createSubTask(workData);

        // 分摊每个任务所占百分比
        int size = subTasks.isEmpty() ? 1 : subTasks.size();
        int taskPercent = 100 / size;

        // 初始化结果
        WorkResult workResult = businessProcessor.initResult();

        //  执行子任务
        for (SubPageTask subTask : subTasks) {
            subTask.setTotalProgress(taskPercent);
            RunMode runMode = businessProcessor.createRunMode(taskId, workData, subTask);
            if (runMode != null) {
                WorkResult subResult = runMode.run(subTask);
                workResult = businessProcessor.merge(workResult, subResult);
            }
        }

        //  返回合并后的结果
        return workResult;
    }

    /**
     * 完成工作任务
     *
     * @param workData
     * @param taskId
     * @param workResult
     */
    @Override
    protected void complete(WorkData<?> workData, String taskId, WorkResult workResult) {
        DataOperateContext dataOperateContext = DataOperateContext.of(workData.getMemberName(), workData.getWorkType(), taskId);
        WorkDetailOperate workDetailOperate = workFactory.createDataOperate().getDetailOperate(dataOperateContext);
        workDetailOperate.complete(workData.getMemberName(), taskId, workResult);
    }

}
