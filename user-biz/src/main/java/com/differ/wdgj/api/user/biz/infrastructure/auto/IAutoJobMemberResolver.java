package com.differ.wdgj.api.user.biz.infrastructure.auto;

import java.util.List;
import java.util.Set;

/**
 * 自动任务会员解析器
 *
 * <AUTHOR>
 * @date 2023-11-07 11:53
 */
public interface IAutoJobMemberResolver {
    /**
     * 解析开启自动的会员
     *
     * @return 开启自动的会员集合
     */
    Set<String> resolveAutoMembers();

    /**
     * 处理店铺变更
     *
     * @param memberName 会员名
     * @param shopIds    变更店铺
     * @return 是否开启自动
     */
    boolean dealShopChanged(String memberName, List<Long> shopIds);
}
