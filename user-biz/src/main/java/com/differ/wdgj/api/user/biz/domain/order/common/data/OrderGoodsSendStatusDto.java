package com.differ.wdgj.api.user.biz.domain.order.common.data;

/**
 * 订单商品发货同步结果
 *
 * <AUTHOR>
 * @date 2025/6/19 15:04
 */
public class OrderGoodsSendStatusDto {
    /**
     * 自增主键
     */
    private int recId;

    /**
     * 是否已经全部发货
     */
    private boolean bAllSend;

    /**
     * 待发货同步数量
     */
    private int waitSendCount;

    /**
     * 发货同步成功数量
     */
    private int successSendCount;

    /**
     * 发货失败成功数量
     */
    private int failSendCount;

    //region get/set
    public int getRecId() {
        return recId;
    }

    public void setRecId(int recId) {
        this.recId = recId;
    }

    public boolean isbAllSend() {
        return bAllSend;
    }

    public void setbAllSend(boolean bAllSend) {
        this.bAllSend = bAllSend;
    }

    public int getWaitSendCount() {
        return waitSendCount;
    }

    public void setWaitSendCount(int waitSendCount) {
        this.waitSendCount = waitSendCount;
    }

    public int getSuccessSendCount() {
        return successSendCount;
    }

    public void setSuccessSendCount(int successSendCount) {
        this.successSendCount = successSendCount;
    }

    public int getFailSendCount() {
        return failSendCount;
    }

    public void setFailSendCount(int failSendCount) {
        this.failSendCount = failSendCount;
    }
    //endregion
}
