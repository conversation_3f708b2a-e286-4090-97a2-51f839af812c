package com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush;

import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.goods.TbGoodsJdpResponseDto;

import java.time.LocalDateTime;

/**
 * 淘宝推送库 - 普通商品数据实体
 *
 * <AUTHOR>
 * @date 2024-03-22 10:00
 */
public class TbGoodsRdsDo {

    // region 属性

    /***
     * 平台商品id
     */
    private String numiid;
    /**
     * 店铺昵称
     */
    private String nick;
    /**
     * 上下架状态
     */
    private String approveStatus;
    /**
     * 分类id
     */
    private String cid;
    /**
     * 商品详细信息json {@link TbGoodsJdpResponseDto}
     */
    private String jdpResponse;

    /**
     * 商品哈希值
     */
    private String jdpHashcode;

    /**
     * 是否删除
     */
    private Byte jdpDelete;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * 订单修改时间
     */
    private LocalDateTime orderModifiedTime;

    /**
     * 推送库修改时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 推送库创建时间
     */
    private LocalDateTime createTime;

    // endregion

    // region 属性方法

    public String getNumiid() {
        return numiid;
    }

    public void setNumiid(String numiid) {
        this.numiid = numiid;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public String getJdpResponse() {
        return jdpResponse;
    }

    public void setJdpResponse(String jdpResponse) {
        this.jdpResponse = jdpResponse;
    }

    public String getJdpHashcode() {
        return jdpHashcode;
    }

    public void setJdpHashcode(String jdpHashcode) {
        this.jdpHashcode = jdpHashcode;
    }

    public Byte getJdpDelete() {
        return jdpDelete;
    }

    public void setJdpDelete(Byte jdpDelete) {
        this.jdpDelete = jdpDelete;
    }

    public LocalDateTime getOrderCreateTime() {
        return orderCreateTime;
    }

    public void setOrderCreateTime(LocalDateTime orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public LocalDateTime getOrderModifiedTime() {
        return orderModifiedTime;
    }

    public void setOrderModifiedTime(LocalDateTime orderModifiedTime) {
        this.orderModifiedTime = orderModifiedTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

// endregion
}
