package com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.sender.plugins;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.AlarmContent;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.sender.IAlarmSender;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.external.qywx.QyWeixinRobotOperator;
import com.differ.wdgj.api.user.biz.infrastructure.external.qywx.message.TextQyWeixinRobotMessage;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 企业微信Text报警发送者
 *
 * <AUTHOR>
 * @date 2025/3/19 下午4:20
 */
public class QyWeixinTextAlarmSender implements IAlarmSender {
    //region 常量
    /**
     * 发送报警
     *
     * @param alarmContent 报警上下文
     * @return 发送结果
     */
    @Override
    public boolean send(AlarmContent alarmContent) {
        try {
            // 基础信息
            AlarmIntervalTypeEnum alarmIntervalType = alarmContent.getAlarmIntervalType();

            // 获取当前报警类型应该发送到的机器人
            String webHook = ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.ALARM_TYPE_TO_QYWX_TEXT_WEBHOOK, alarmIntervalType.getCaption(), "|default=505a792f-0d5b-4608-a0a4-3841568b73ec|");
            // 获取当前报警类型应该通知哪些人
            String mobile = ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.ALARM_TYPE_TO_QYWX_TEXT_MOBILE, alarmIntervalType.getCaption(), "");
            // 构建报警信息
            String content = alarmContent.toString();
            TextQyWeixinRobotMessage robotMessage = new TextQyWeixinRobotMessage(content, Arrays.stream(mobile.split(",")).collect(Collectors.toList()));

            // 发送企业微信机器人text信息
            return QyWeixinRobotOperator.sendRobotMessage(robotMessage, webHook);

        } catch (Exception ex) {
            LogFactory.error("企业微信Text报警发送者", "发送异常，发送信息：" + JsonUtils.toJson(alarmContent), ex);
            return false;
        }
    }
}
