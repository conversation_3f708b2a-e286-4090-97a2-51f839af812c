package com.differ.wdgj.api.user.biz.infrastructure.work.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkStatus;

/**
 * 工作任务运行结果状态
 *
 * <AUTHOR>
 * @date 2024/7/5 13:24
 */
public enum RunStatusEnum implements ValueEnum {
    /**
     * 任务执行中或者需要继续执行
     */
    RUNNING(1, WorkStatus.RUNNING),
    /**
     * 任务中断
     */
    BREAK(2,WorkStatus.BREAK),
    /**
     * 任务失败
     */
    FAILED(3,WorkStatus.FAILED),
    /**
     * 任务成功
     */
    SUCCESS(10,WorkStatus.COMPLETED),
    ;

    private Integer value;

    private WorkStatus workStatus;

    RunStatusEnum(Integer value, WorkStatus workStatus) {
        this.value = value;
        this.workStatus = workStatus;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public WorkStatus getWorkStatus() {
        return workStatus;
    }
}
