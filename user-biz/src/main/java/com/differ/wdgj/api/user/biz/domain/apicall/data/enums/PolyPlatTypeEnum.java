package com.differ.wdgj.api.user.biz.domain.apicall.data.enums;

import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 所有平台类别对应枚举。
 *
 * <AUTHOR>
 * @date 2024-03-05 11:01
 */
public enum PolyPlatTypeEnum implements NameEnum, ValueEnum {
    //region 枚举

    NONE("无", 0, false),

    BUSINESS("电商平台", 1, true),

    CUSTOMS("海关平台", 2, false),

    INVOICE("发票平台", 3, false),

    LOGISTICS("物流平台", 4, true),

    STORAGE("仓储平台", 5, true),

    TAKEAWAY("外卖平台", 6, true),

    TRADE("支付平台", 7, false);

    //endregion

    /**
     * 构造函数
     *
     * @param name           平台类别名称
     * @param value          平台类别值
     * @param isPlatFeatures 是否有平台特性
     */
    PolyPlatTypeEnum(String name, int value, boolean isPlatFeatures) {
        this.name = name;
        this.value = value;
        this.isPlatFeatures = isPlatFeatures;
    }

    /**
     * 标题。
     */
    private final String name;

    /**
     * 值。
     */
    private final int value;

    /**
     * 是否有平台特性。
     */
    private final boolean isPlatFeatures;


    /**
     * 获取是否有平台特性。
     * @return 是否有平台特性
     */
    public boolean getIsPlatFeatures() {
        return this.isPlatFeatures;
    }

    /**
     * 获取平台类别值。
     * @return 值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取平台类别名称
     * @return 平台类别名称
     */
    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 重载toString方法。
     * @return 字符串
     */
    @Override
    public String toString() {
        return this.getValue().toString();
    }
}
