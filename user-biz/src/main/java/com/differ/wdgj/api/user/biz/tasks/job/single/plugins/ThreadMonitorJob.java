package com.differ.wdgj.api.user.biz.tasks.job.single.plugins;

import com.differ.wdgj.api.component.task.single.core.JobExecTimeStrategy;
import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.monitor.ThreadPoolMonitor;
import com.differ.wdgj.api.user.biz.tasks.job.single.core.BaseSingleJob;
import org.apache.commons.lang3.RandomUtils;

/**
 * 线程池监控定时任务
 * 参考吉客云：com.differ.jackyun.omsapi.user.biz.tasks.single.plugins.monitorstatistics ThreadTimeoutJob
 *
 * <AUTHOR>
 * @date 2025/3/24 上午11:40
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "ThreadMonitorJob",
        cron = "0/10 * * * * ?"
)
public class ThreadMonitorJob extends BaseSingleJob {
    /**
     * 获取执行时间策略
     *
     * @return 执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        int frequency = RandomUtils.nextInt(60, 120);
        execTimeStrategy.setRunFrequency(frequency);
        return execTimeStrategy;
    }

    /**
     * 业务处理
     */
    @Override
    protected void doWork() {
        for (TaskEnum taskEnum : TaskEnum.values()) {
            // 线程池信息调试信息
            ThreadPoolMonitor.debugThreadPoolInfo(taskEnum);

            // 线程超时校验
            ThreadPoolMonitor.checkThreadTimeout(taskEnum);
        }
    }
}
