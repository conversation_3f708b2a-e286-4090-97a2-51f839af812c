package com.differ.wdgj.api.component.redis.config;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

/**
 * redis集群和非集群连接信息
 * <AUTHOR>
 * @date 2020-09-07 15:27
 */
public interface RedisProperties {
    /**
     * 单点地址
     */
    String getHost();

    /**
     * 单点端口
     */
    int getPort();

    /**
     * 密码
     */
    String getPassword();

    /**
     * 单点数据库索引
     */
    int getDatabase();

    /**
     * 集群节点，示例：**************:6380,**************:6381,**************:6382
     */
    String getNodes();

    /**
     * 跨集群执行命令时要遵循的最大重定向数量
     *
     * @return
     */
    default int getMaxRedirects() {
        return 5;
    }

    /**
     * 连接池的最大连接数
     *
     * @return
     */
    default int getMaxTotal() {
        return GenericObjectPoolConfig.DEFAULT_MAX_TOTAL;
    }

    /**
     * 连接池中的最大空闲连接
     *
     * @return
     */
    default int getMaxIdle() {
        return GenericObjectPoolConfig.DEFAULT_MAX_IDLE;
    }

    /**
     * 连接池中的最小空闲连接
     *
     * @return
     */
    default int getMinIdle() {
        return GenericObjectPoolConfig.DEFAULT_MIN_IDLE;
    }

    /**
     * 是否开启ssl
     *
     * @return
     */
    default Boolean getSsl() {
        return false;
    }

    /**
     * 连接超时时间（毫秒）
     *
     * @return
     */
    default int getTimeout() {
        return 10000;
    }
}
