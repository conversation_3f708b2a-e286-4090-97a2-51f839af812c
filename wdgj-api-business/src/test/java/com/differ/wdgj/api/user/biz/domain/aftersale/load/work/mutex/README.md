# LoadAfterSaleWorkMutex 单元测试说明

## 测试文件组织

为了便于维护和理解，`LoadAfterSaleWorkMutex` 的单元测试被分为三个批次：

### 第一批：基础功能测试 (`LoadAfterSaleWorkMutexTest.java`)
- **测试目标**: 验证核心业务逻辑
- **主要测试场景**:
  - 消息推送不互斥场景（MESSAGE_NOTIFICATION + AFTER_SALE_NO）
  - 非特殊场景的处理逻辑
  - args为null的边界情况
- **特点**: 不涉及配置键模拟，专注于基础逻辑验证

### 第二批：集成测试 (`LoadAfterSaleWorkMutexIntegrationTest.java`)
- **测试目标**: 验证完整的业务流程和配置行为
- **主要测试场景**:
  - 默认配置下的行为验证
  - 消息推送不互斥的完整流程
  - 父类创建逻辑的完整流程
  - 所有枚举值组合的覆盖测试
  - 边界条件测试
- **特点**: 使用真实的配置键值，不使用MockedStatic，采用集成测试方式

### 第三批：简单测试 (`LoadAfterSaleWorkMutexSimpleTest.java`)
- **测试目标**: 提供简单的功能验证
- **主要测试场景**:
  - 消息推送不互斥的基本验证
  - 其他场景的基本验证
  - 存在未完成任务时的基本验证
- **特点**: 不继承AbstractSpringTest，纯单元测试，快速验证基本功能

## 测试套件 (`LoadAfterSaleWorkMutexTestSuite.java`)
- 统一运行所有测试类
- 便于CI/CD集成

## 运行测试

### 运行单个测试类
```bash
mvn test -Dtest=LoadAfterSaleWorkMutexTest
mvn test -Dtest=LoadAfterSaleWorkMutexTimeoutTest
mvn test -Dtest=LoadAfterSaleWorkMutexComprehensiveTest
```

### 运行整个测试套件
```bash
mvn test -Dtest=LoadAfterSaleWorkMutexTestSuite
```

### 运行所有相关测试
```bash
mvn test -Dtest="*LoadAfterSaleWorkMutex*"
```

## 测试覆盖率

这些测试覆盖了 `LoadAfterSaleWorkMutex` 类的以下方面：
- ✅ 消息推送不互斥逻辑
- ✅ 父类创建逻辑调用
- ✅ 超时任务检测逻辑
- ✅ 配置键获取和使用
- ✅ 所有枚举值组合
- ✅ 边界条件和异常情况
- ✅ 方法参数验证
- ✅ 返回值验证
- ✅ Mock对象交互验证

## 注意事项

1. **MockedStatic使用**: 第二批和第三批测试使用了 `MockedStatic` 来模拟静态方法调用，确保测试环境支持此功能
2. **配置键模拟**: 通过模拟 `LoadAfterSaleConfigKeyUtils.getWorkTaskUnRunTimeoutPeriod()` 方法来测试不同的超时配置
3. **测试隔离**: 每个测试方法都是独立的，使用 `@Before` 方法重置测试环境
4. **异常处理**: 测试包含了对异常情况的验证，确保代码的健壮性

## 维护建议

1. 当 `LoadAfterSaleWorkMutex` 类的业务逻辑发生变化时，优先更新对应批次的测试
2. 新增功能时，根据功能特点选择合适的测试类进行扩展
3. 定期运行完整的测试套件，确保所有功能正常
4. 保持测试代码的可读性和维护性，及时更新注释和文档
