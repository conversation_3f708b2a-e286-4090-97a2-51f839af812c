package com.differ.wdgj.api.user.biz.infrastructure.work.operate.cache;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.DataOperateContext;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 本地内存的进度缓存,用于进度事件的性能提升，减少查询进度的redis请求
 *
 * <AUTHOR>
 * @date 2024/7/25 17:27
 */
public class LocalProgressCache {

    /**
     * 内存进度缓存MAP
     */
    Map<DataOperateContext, AtomicInteger> cache = new ConcurrentHashMap<>();

    //region 构造和枚举单例

    private LocalProgressCache() {
        // 私有，为了单例
    }

    /**
     * 枚举单例
     *
     * @return
     */
    public static LocalProgressCache singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        SINGLETON;

        private LocalProgressCache instance;

        private SingletonEnum() {
            instance = new LocalProgressCache();
        }
    }

    //endregion

    /**
     * +
     * 设置进度
     *
     * @param context
     * @param value
     */
    public void setProgress(DataOperateContext context, int value) {
        AtomicInteger progress = cache.get(context);
        if (progress != null) {
            progress.set(value);
        } else {
            cache.put(context, new AtomicInteger(value));
        }
    }

    /**
     * 增量增加进度
     *
     * @param context
     * @param value
     */
    public void increProgress(DataOperateContext context, int value) {
        AtomicInteger progress = cache.get(context);
        if (progress != null) {
            progress.getAndAdd(value);
        } else {
            cache.put(context, new AtomicInteger(value));
        }
    }

    /**
     * 初始化进度，初始化后最后必须得有对应的remove操作，否则会积压在内存中
     *
     * @param context
     */
    public void initProgress(DataOperateContext context) {
        cache.put(context, new AtomicInteger(0));
    }

    /**
     * 获取上下文的进度值
     *
     * @param context
     * @return
     */
    public Integer getProgress(DataOperateContext context) {
        AtomicInteger progress = cache.get(context);
        if (progress != null) {
            return progress.get();
        } else {
            return null;
        }
    }

    /**
     * 对应的上下文最后必须有remove操作，否则会积压在内存中
     *
     * @param context
     */
    public void remove(DataOperateContext context) {
        cache.remove(context);
    }
}
