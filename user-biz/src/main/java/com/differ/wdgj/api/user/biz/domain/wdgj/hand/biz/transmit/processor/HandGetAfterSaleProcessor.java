package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.processor;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.LoadAfterSaleFacade;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.IHandBizTransmitProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitMqDto;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitResult;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandGetAfterSaleRequest;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import org.apache.commons.collections.CollectionUtils;

/**
 * 手动下载售后单
 *
 * <AUTHOR>
 * @date 2024/10/28 下午6:01
 */
public class HandGetAfterSaleProcessor implements IHandBizTransmitProcessor {
    /**
     * 执行业务
     *
     * @param dto 业务参数
     * @return 同步结果
     */
    @Override
    public HandBizTransmitResult execute(HandBizTransmitMqDto dto) {
        // 反序列化业务参数
        HandGetAfterSaleRequest getAfterSaleRequest = JsonUtils.deJson(dto.getHandBizRequest(), HandGetAfterSaleRequest.class);
        if (getAfterSaleRequest == null) {
            return HandBizTransmitResult.failedResult("反序列化业务参数失败");
        }

        ApiShopBaseDto shop = ShopInfoUtils.singleByOutShopId(dto.getOutAccount(), dto.getOutShopId());
        if(shop == null){
            return HandBizTransmitResult.failedResult("店铺信息不存在");
        }
        if(!shop.isActived() || shop.isDelete()){
            return HandBizTransmitResult.failedResult("店铺停用或删除");
        }

        LoadAfterSaleParam param = new LoadAfterSaleParam();
        // 基础数据
        param.setMember(dto.getOutAccount());
        param.setPlat(shop.getPlat());
        param.setShopId(dto.getOutShopId());
        param.setTriggerType(TriggerTypeEnum.HAND);
        param.setCreator(dto.getCreator());
        // 下载业务参数
        AfterSaleLoadArgs loadArgs = new AfterSaleLoadArgs();
        loadArgs.setLoadType(getLoadType(getAfterSaleRequest));
        loadArgs.setAfterSaleNos(getAfterSaleRequest.getAfterSaleNos());
        loadArgs.setStartTime(getAfterSaleRequest.getStartTm());
        loadArgs.setEndTime(getAfterSaleRequest.getEndTm());
        loadArgs.setRefundType(getAfterSaleRequest.getRefundType());
        loadArgs.setTriggerType(OrderTriggerTypeEnum.MANUAL);
        param.setLoadArgs(loadArgs);

        // 创建排队下载任务
        LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
        LoadResult queueResult = facade.createQueueTask(param);
        if(queueResult.isFailed()){
            return HandBizTransmitResult.successResult(queueResult.getMessage());
        }

        return HandBizTransmitResult.successResult();
    }

    //region 私有方法
    /**
     * 构建下载类型
     *
     * @param getAfterSaleRequest 业务参数
     * @return 下载类型
     */
    private AfterSaleLoadTypeEnum getLoadType(HandGetAfterSaleRequest getAfterSaleRequest) {
        if (getAfterSaleRequest.getIsDownLoadByTime()) {
            return AfterSaleLoadTypeEnum.TIME_RANGE;
        }

        if (CollectionUtils.isNotEmpty(getAfterSaleRequest.getAfterSaleNos())) {
            return AfterSaleLoadTypeEnum.AFTER_SALE_NO;
        }

        return AfterSaleLoadTypeEnum.SHOP_LOAD;
    }
    //endregion
}
