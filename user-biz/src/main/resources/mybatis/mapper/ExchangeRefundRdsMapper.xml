<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ExchangeRefundRdsMapper" >
    <resultMap id="ExchangeRefundResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.ExchangeRefundRdsDo">
        <id property="disputeId" column="dispute_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="buyerNick" column="buyer_nick"/>
        <result property="status" column="status"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="jdpHashcode" column="jdp_hashcode"/>
        <result property="jdpResponse" column="jdp_response"/>
        <result property="jdpCreated" column="jdp_created"/>
        <result property="jdpModified" column="jdp_modified"/>
    </resultMap>

    <!-- 查询换货退款单总数 -->
    <select id="getExchangeRefundsCountByModified" resultType="int">
        <![CDATA[
        SELECT COUNT(*)
        FROM jdp_exchange_refund
        WHERE jdp_modified BETWEEN #{dtStartTime} AND #{dtEndTime}
          AND seller_nick = #{sellNick}
        ]]>
    </select>

    <!-- 查询换货退款单分页数据 -->
    <select id="getExchangeRefundDataByModified" resultMap="ExchangeRefundResultMap">
        <![CDATA[
        SELECT dispute_id, seller_nick, buyer_nick, status, created, modified,
               jdp_hashcode, jdp_response, jdp_created, jdp_modified
        FROM jdp_exchange_refund
        WHERE jdp_modified BETWEEN #{dtStartTime} AND #{dtEndTime}
          AND seller_nick = #{sellNick}
        ORDER BY jdp_modified
        LIMIT #{start}, #{pageSize}
        ]]>
    </select>
</mapper>