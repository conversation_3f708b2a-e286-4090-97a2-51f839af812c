package com.differ.wdgj.api.component.wait.data;

/**
 * @Description 支持插队的排队实体
 * <AUTHOR>
 * @Date 2021/12/29 10:49
 */
public class JumpQueueWaitEntity extends CommonWaitEntity{

    public JumpQueueWaitEntity(String waitUniqueId, String data, boolean jumpQueue) {
        super(waitUniqueId, data);
        this.jumpQueue = jumpQueue;
    }

    /**
     * 是否要插队
     */
    private boolean jumpQueue;

    public boolean isJumpQueue() {
        return jumpQueue;
    }
}
