package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core;

import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.AbstractUserSimpleDistributeJob;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.data.UserFlowJobContext;

import java.lang.reflect.InvocationTargetException;

/**
 * 用户流式任务抽象基类
 *
 * <AUTHOR>
 * @date 2022/9/6 14:16
 */
public abstract class AbstractUserFlowJob<T extends UserFlowJobContext> extends AbstractUserSimpleDistributeJob {

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {

        // 新建任务上下文
        T flowJobContext = null;
        try {
            flowJobContext = this.getClazz().getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            LogFactory.get(logCaption()).error("创建流式任务上下文失败", e);
        }

        // 上下文为空，直接结束
        if (flowJobContext == null) {
            return;
        }

        // 设置会员名
        flowJobContext.setMemberName(memberName);

        do {

            // 单次任务执行
            this.singleTaskExecute(flowJobContext);
        }
        // 是否继续执行任务
        while (this.continueRun(flowJobContext));
    }

    // region 供子类重写

    /**
     * 任务上下文类型
     *
     * @return 类型
     */
    protected abstract Class<T> getClazz();

    /**
     * 单次任务执行
     *
     * @param flowJobContext 上下文
     */
    protected abstract void singleTaskExecute(T flowJobContext);

    /**
     * 是否继续执行
     *
     * @param flowJobContext 上下文
     * @return 结果
     */
    protected abstract boolean continueRun(T flowJobContext);

    // endregion
}
