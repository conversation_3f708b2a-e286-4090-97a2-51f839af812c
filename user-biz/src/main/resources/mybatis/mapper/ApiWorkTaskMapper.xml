<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiWorkTaskMapper">
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="taskId" property="taskId" jdbcType="VARCHAR"/>
        <result column="triggerType" property="triggerType" jdbcType="TINYINT"/>
        <result column="shopId" property="shopId" jdbcType="TINYINT"/>
        <result column="taskType" property="taskType" jdbcType="INTEGER"/>
        <result column="gmtExec" property="gmtExec" jdbcType="TIMESTAMP"/>
        <result column="gmtFinish" property="gmtFinish" jdbcType="TIMESTAMP"/>
        <result column="taskStatus" property="taskStatus" jdbcType="TINYINT"/>
        <result column="jsonResult" property="jsonResult" jdbcType="VARCHAR"/>
        <result column="taskInfo" property="taskInfo" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="gmtCreate" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmtModified" property="gmtModified" jdbcType="TIMESTAMP"/>
    </resultMap>
    <!-- 字段语句块-->
    <sql id="BaseColumnList">
        id, taskId, triggerType, shopId, taskType, gmtExec, gmtFinish, taskStatus, jsonResult, taskInfo, creator, gmtCreate, gmtModified
    </sql>

    <insert id="addExec" parameterType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        insert ignore into g_api_work_task (`taskId`, `triggerType`, `shopId`, `taskType`, `gmtExec`,
        `taskStatus`,`creator`,`taskInfo`) value
        (
        #{dto.taskId},
        #{dto.triggerType},
        #{dto.shopId},
        #{dto.taskType},
        NOW(),
        #{dto.taskStatus},
        #{dto.creator},
        #{dto.taskInfo}
        )
    </insert>

    <insert id="addWait" parameterType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        insert ignore into g_api_work_task (`taskId`, `triggerType`, `shopId`, `taskType`,`taskStatus`,`creator`,`taskInfo`) value
        (
        #{dto.taskId},
        #{dto.triggerType},
        #{dto.shopId},
        #{dto.taskType},
        #{dto.taskStatus},
        #{dto.creator},
        #{dto.taskInfo}
        )
    </insert>

    <select id="existsUnComplete" resultType="integer">
        select 1 from g_api_work_task where taskType = #{taskType} and shopId = #{shopId} and taskStatus in (0,1,2) limit 1
    </select>

    <select id="getUnRunTimeoutWorkTask" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        select <include refid="BaseColumnList"/>from g_api_work_task
        <![CDATA[where taskType = #{taskType} and shopId = #{shopId} and taskStatus = 0 and gmtCreate < date_add(now(),INTERVAL -#{timeoutPeriod} second)]]>
        limit 1
    </select>

    <update id="updateToExec">
        update g_api_work_task SET `taskStatus` = #{taskStatus},gmtExec = NOW() where taskId = #{taskId}
    </update>

    <update id="updateToFinish">
        update g_api_work_task SET `taskStatus` = #{taskStatus},`jsonResult` = #{jsonResult},gmtFinish = NOW() where
        taskId = #{taskId}
    </update>

    <select id="listToCheckWork" resultType="string">
        select `taskId` from g_api_work_task where taskType = #{taskType} and taskStatus in (1,2)
    </select>

    <!--查询店铺级最后一条已完成任务列表-->
    <select id="queryAutoShopLastFinishTask" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        select
        <include refid="BaseColumnList"/>
        from (select * from g_api_work_task where taskStatus in (3, 10) and gmtModified > date_add(now(),INTERVAL -7 day) and taskType = #{taskType} and triggerType = #{triggerType} order by gmtModified desc) a
        group by shopId
    </select>

    <!--查询店铺级最后一条已完成任务列表-->
    <select id="singleAutoShopLastFinishTask" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        select
        <include refid="BaseColumnList"/>
        from (select * from g_api_work_task where taskStatus = 10 and gmtModified > date_add(now(),INTERVAL -7 day) and taskType = #{taskType} and shopId = #{shopId} order by gmtModified desc) a
        group by shopId
    </select>

    <!--查询店铺级最后一条已完成任务列表-->
    <select id="singleByTaskId" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO">
        select
        <include refid="BaseColumnList"/>
        from g_api_work_task
        where taskId = #{taskId}
    </select>

    <!-- 删除X天数数据，单次上限1000条-->
    <delete id="DeleteExpiredData" parameterType="java.lang.Integer">
        delete from g_api_work_task where taskType = #{taskType} and  date_add(now(),INTERVAL - #{expiredDay} day) > gmtCreate
        limit 1000
    </delete>

</mapper>