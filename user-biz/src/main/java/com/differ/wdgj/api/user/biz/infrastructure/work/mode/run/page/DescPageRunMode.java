package com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.page;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.RunPageResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubPageTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;

/**
 * 增量倒序分页下载模式
 *
 * <AUTHOR>
 * @date 2024/7/5 15:33
 */
public class DescPageRunMode extends AbstractPageRunMode {

    public DescPageRunMode(String taskId, WorkData<?> workData) {
        super(taskId, workData);
    }

    /**
     * 初始化分页信息
     *
     * @param subTask 子任务
     * @param result  子任务执行结果
     */
    @Override
    protected void initPageResult(SubPageTask subTask, RunPageResult result, boolean firstPage) {
        if(firstPage){
            subTask.getDynamicStatus().setPageSize(1);
        }
    }

    /**
     * 处理分页结果
     *
     * @param subTask 子任务
     * @param result  子任务执行结果
     */
    @Override
    protected void processPageResult(SubPageTask subTask, RunPageResult result) {
        // 基类调用
        super.processPageResult(subTask, result);

        // 从业务最大总页数开始下载
        if (subTask.getDynamicStatus().getPageIndex() == 1) {
            subTask.getDynamicStatus().setTotalPages(subTask.getFirstTotalPage());
        }
    }

    /**
     * 移到子任务的下一页
     *
     * @param subTask 子任务
     * @param result 执行结果
     * @param firstPage 是否第一页
     */
    @Override
    protected void moveNextPage(SubPageTask subTask, RunPageResult result, boolean firstPage) {
        if (firstPage) {
            // 首页下载完成 需要从最后一页开始下载
            subTask.getDynamicStatus().setPageIndex(subTask.getDynamicStatus().getTotalPages());
        } else {
            subTask.getDynamicStatus().setPageIndex(subTask.getDynamicStatus().getPageIndex() - 1);
        }
    }

    /**
     * 是否还有下一页
     *
     * @param subTask 子任务
     * @param result  子任务执行结果
     * @return 结果
     */
    @Override
    protected boolean hasNextPage(SubPageTask subTask, RunPageResult result) {
        if (result.getRunStatus() != RunStatusEnum.RUNNING) {
            // 非运行状态，不执行
            return false;
        }
        // 首页索引基于1，倒序有下一页：当前页>=1
        return result.getRunStatus() == RunStatusEnum.RUNNING && subTask.getDynamicStatus().getPageIndex() >= 1;
    }

}
