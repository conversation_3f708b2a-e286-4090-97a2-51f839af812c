package com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue;


import com.differ.wdgj.api.component.util.functional.zero.Function;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.QueueJobData;

import java.util.List;
import java.util.Map;

/**
 * 负载均衡任务队列
 *
 * <AUTHOR>
 * @date 2023-12-01 17:53
 */
public interface JobTaskSourceQueue<T extends QueueJobData> {

    /**
     * 初始化队列配置
     *
     * @param jobCode
     * @param clazz
     * @param funExecTimeout
     */
    void init(String jobCode, Class<T> clazz, Function<Integer> funExecTimeout);

    /**
     * 添加任务
     *
     * @param task 任务数据
     * @return 是否成功
     */
    boolean addTask(T task);

    /**
     * 添加任务
     *
     * @param tasks 任务数据
     * @return 是否成功
     */
    void addTasks(List<T> tasks);

    /**
     * 拉取下一个排队数据
     *
     * @return 返回排队的key和数据
     */
    Map<String, T> pullNext();

    /**
     * 完成任务
     *
     * @param waitUniqueId 排队的任务key
     * @param task         任务数据
     */
    void complete(String waitUniqueId, T task);
}
