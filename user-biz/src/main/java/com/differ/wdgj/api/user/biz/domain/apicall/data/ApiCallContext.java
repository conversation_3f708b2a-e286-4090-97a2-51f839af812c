package com.differ.wdgj.api.user.biz.domain.apicall.data;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;

import java.util.Map;

/**
 * API调用上下文
 *
 * <AUTHOR>
 * @date 2022-03-09 10:13
 */
public class ApiCallContext {

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 接口类型
     */
    private PolyAPITypeEnum apiType;

    /**
     * 平台值
     */
    private PolyPlatEnum plat;

    /**
     * 店铺Id
     */
    private Integer shopId;

    /**
     * 店铺Token
     */
    private String shopToken;

    /**
     * APP KEY
     */
    private String appKey;

    /**
     * APP SECRET
     */
    private String appSecret;

    // region getter & setter

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public PolyAPITypeEnum getApiType() {
        return apiType;
    }

    public void setApiType(PolyAPITypeEnum apiType) {
        this.apiType = apiType;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getShopToken() {
        return shopToken;
    }

    public void setShopToken(String shopToken) {
        this.shopToken = shopToken;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    // endregion
}
