package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkStatus;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;

import java.time.LocalDateTime;

/**
 * API业务工作任务表
 *
 * <AUTHOR>
 * @date 2024/9/6 下午3:15
 */
public class ApiWorkTaskDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 触发方式（手动，自动）
     * {@link TriggerTypeEnum}
     */
    private Byte triggerType;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 任务类型（比如：下载商品，匹配商品）
     * {@link  WorkEnum}
     */
    private Integer taskType;

    /**
     * 执行开始时间（数据库时间） 排查问题用看排队时间
     */
    private LocalDateTime gmtExec;

    /**
     * 完成时间（数据库时间）
     */
    private LocalDateTime gmtFinish;

    /**
     * 状态
     * 状态枚举{@link WorkStatus}
     */
    private byte taskStatus;

    /**
     * 描述
     */
    private String jsonResult;

    /**
     * 总任务信息（失败可重试）
     */
    private String taskInfo;

    /**
     * 创造人，操作人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    //region get/set
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }


    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public LocalDateTime getGmtExec() {
        return gmtExec;
    }

    public void setGmtExec(LocalDateTime gmtExec) {
        this.gmtExec = gmtExec;
    }

    public String getJsonResult() {
        return jsonResult;
    }

    public void setJsonResult(String jsonResult) {
        this.jsonResult = jsonResult;
    }

    public String getTaskInfo() {
        return taskInfo;
    }

    public void setTaskInfo(String taskInfo) {
        this.taskInfo = taskInfo;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Byte getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(Byte triggerType) {
        this.triggerType = triggerType;
    }

    public LocalDateTime getGmtFinish() {
        return gmtFinish;
    }

    public void setGmtFinish(LocalDateTime gmtFinish) {
        this.gmtFinish = gmtFinish;
    }

    public Byte getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Byte taskStatus) {
        this.taskStatus = taskStatus;
    }

    public LocalDateTime getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(LocalDateTime gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public LocalDateTime getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(LocalDateTime gmtModified) {
        this.gmtModified = gmtModified;
    }
    //endregion
}
