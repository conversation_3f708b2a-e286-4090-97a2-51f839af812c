<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wdgj-api</artifactId>
        <groupId>com.differ.wdgj.api</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>user-biz</artifactId>
    <description>业务核心库</description>
    <packaging>jar</packaging>
    <version>1.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.differ.wdgj.api</groupId>
            <artifactId>wdgj-api-component-multidb</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.differ.wdgj.api</groupId>
            <artifactId>wdgj-api-component-redis</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.differ.wdgj.api</groupId>
            <artifactId>wdgj-api-component-util</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.differ.wdgj.api</groupId>
            <artifactId>wdgj-api-component-task-single</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.differ.wdgj.api</groupId>
            <artifactId>wdgj-api-component-wait</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- 通用mapper逆向工具 -->
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>2.1.5</version>
        </dependency>

        <dependency>
            <groupId>com.differ.jackyun.framework</groupId>
            <artifactId>component-jmq-rabbit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.differ.jackyun.framework</groupId>
            <artifactId>component-jmq-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.differ.jackyun.framework</groupId>
            <artifactId>component-jmq-balance-msg</artifactId>
        </dependency>



    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

</project>