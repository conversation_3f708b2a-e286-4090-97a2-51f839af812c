package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.factory;


import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.kafka.KafkaAdapter;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.kafka.KafkaConsumerContainer;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.kafka.KafkaTopicEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.KafkaSubQueue;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueue;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueueContext;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;


/**
 * JMQ子队列创建工厂
 *
 * <AUTHOR>
 * @date 2024/4/1 11:04
 */
public class SubQueueCommonKafkaFactory implements SubQueueFactory {
    @Override
    public <T> SubQueue<T> createSubQueue(SubQueueContext subQueueContext, Class<T> dataClazz) {
        // 根据队列代码和子队列类型创建主题类型
        KafkaTopicEnum topicEnum = KafkaTopicEnum.getTopic(subQueueContext.getMultiCode(), subQueueContext.getSubQueueEnum());
        // 取配置模板
        KafkaProperties templateProperties = topicEnum.createKafkaProperties();
        // 设置当前站点为kafka的消费分组
        String serviceCode = SystemAppConfig.get().getSiteTypeEnum().getServiceCode();
        templateProperties.getConsumer().setGroupId(serviceCode);
        // 获取对应topic
        String topic = topicEnum.getTopic();
        // 创建适配器，相同地址、主题和消费分组的共用一个适配器实例
        KafkaAdapter kafkaAdapter = KafkaConsumerContainer.getTopicGroupConsumer(topic, templateProperties);
        // 创建子队列实例
        return new KafkaSubQueue<>(subQueueContext, kafkaAdapter);
    }
}
