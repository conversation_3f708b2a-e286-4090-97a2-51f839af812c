# isBDistinguishShippedAndUnshipped 测试场景说明

## 概述

为 `RefundTypeCovertOperation` 类新增了针对 `isBDistinguishShippedAndUnshipped` 配置为 `true` 的测试场景，确保该配置项的所有逻辑分支都得到充分测试。

## 新增测试方法

### 1. `testCovertApiAfterSaleType_DistinguishShippedEnabled_JH03_ShouldReturnRefundPaySend()`
**测试场景**: 当 `isBDistinguishShippedAndUnshipped=true` 且退款类型为 `JH_03` 时
**预期结果**: 直接转换为 `REFUND_PAY_SEND`
**对应代码逻辑**: 第121-123行的特定平台场景处理

### 2. `testCovertApiAfterSaleType_DistinguishShippedEnabled_JH01_ShouldReturnRefundPaySend()`
**测试场景**: 当 `isBDistinguishShippedAndUnshipped=true` 且退款类型为 `JH_01` 时
**预期结果**: 直接转换为 `REFUND_PAY_SEND`
**对应代码逻辑**: 第121-123行的特定平台场景处理

### 3. `testCovertApiAfterSaleType_DistinguishShippedDisabled_ShouldUseDefaultLogic()`
**测试场景**: 当 `isBDistinguishShippedAndUnshipped=false` 时
**预期结果**: 按照原有逻辑转换为 `REFUND_PAY`
**对应代码逻辑**: 验证配置关闭时不影响原有逻辑

### 4. `testCovertApiAfterSaleType_ConfigNull_ShouldUseDefaultLogic()`
**测试场景**: 当配置对象为 `null` 时
**预期结果**: 按照原有逻辑转换为 `REFUND_PAY`
**对应代码逻辑**: 验证配置为空时的容错处理

### 5. `testCovertApiAfterSaleType_NonRefundPayType_NotAffectedByDistinguishConfig()`
**测试场景**: 当售后类型不是退款类型时（如 `JH_04` 退货退款单）
**预期结果**: 不受 `isBDistinguishShippedAndUnshipped` 配置影响，仍转换为 `REFUND`
**对应代码逻辑**: 验证配置只影响退款类型的处理

## 代码逻辑覆盖

### 第一个逻辑分支（第53-66行）
```java
if (apiAfterSaleType == ApiAfterSaleTypeEnum.REFUND_PAY) {
    AfterSalesConfigContent afterSalesConfigPlatFeature = context.getAfterSalesConfigPlatFeature(shopType.getCode());
    if (afterSalesConfigPlatFeature != null && afterSalesConfigPlatFeature.isBDistinguishShippedAndUnshipped()) {
        RefundPayOrderSendStatusEnum refundPayOrderSendStatusEnum = checkIsOrderSend(ployOrder, dbOrder.getApiTrade(), dbOrder.getApiTradeGoodsList());
        switch (refundPayOrderSendStatusEnum) {
            case SEND:
                apiAfterSaleType = ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
                break;
            case NOT_SEND:
                apiAfterSaleType = ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND;
                break;
        }
    }
}
```
**测试覆盖**: 暂未直接测试此分支，因为需要先有 `REFUND_PAY` 的基础转换结果

### 第二个逻辑分支（第121-123行）
```java
AfterSalesConfigContent afterSalesConfigPlatFeature = context.getAfterSalesConfigPlatFeature(shopType.getCode());
if (afterSalesConfigPlatFeature != null && afterSalesConfigPlatFeature.isBDistinguishShippedAndUnshipped()) {
    return ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
}
```
**测试覆盖**: ✅ 已通过测试方法1和2覆盖

## 测试数据构造

### AfterSalesConfigContent 模拟
```java
AfterSalesConfigContent configContent = new AfterSalesConfigContent();
configContent.setBDistinguishShippedAndUnshipped(true/false);
Mockito.when(mockContext.getAfterSalesConfigPlatFeature(ShopTypeEnum.TAOBAO.getCode()))
       .thenReturn(configContent);
```

### 测试用例组合
| 退款类型 | isBDistinguishShippedAndUnshipped | 配置对象 | 预期结果 |
|---------|----------------------------------|---------|---------|
| JH_03   | true                             | 非null  | REFUND_PAY_SEND |
| JH_01   | true                             | 非null  | REFUND_PAY_SEND |
| JH_03   | false                            | 非null  | REFUND_PAY |
| JH_03   | -                                | null    | REFUND_PAY |
| JH_04   | true                             | 非null  | REFUND（不受影响）|

## 运行测试

### 运行新增的测试方法
```bash
# 运行单个测试方法
mvn test -Dtest=RefundTypeCovertOperationTest#testCovertApiAfterSaleType_DistinguishShippedEnabled_JH03_ShouldReturnRefundPaySend

# 运行所有相关测试
mvn test -Dtest=RefundTypeCovertOperationTest -Dtest.methods="*DistinguishShipped*"

# 运行整个测试类
mvn test -Dtest=RefundTypeCovertOperationTest
```

## 测试价值

### 1. **配置驱动逻辑验证**
- 确保 `isBDistinguishShippedAndUnshipped` 配置能正确影响业务逻辑
- 验证配置开启和关闭时的不同行为

### 2. **边界条件覆盖**
- 测试配置对象为 `null` 的容错处理
- 验证配置只影响特定类型的售后单

### 3. **业务规则准确性**
- 确保 `JH_01` 和 `JH_03` 在特定配置下的转换规则正确
- 验证非退款类型不受配置影响

### 4. **回归测试保障**
- 为后续代码修改提供回归测试保障
- 确保配置逻辑的稳定性

## 注意事项

1. **Mock配置**: 使用 `Mockito.when()` 模拟配置获取，确保测试的独立性
2. **测试隔离**: 每个测试方法都独立设置配置，避免相互影响
3. **断言准确**: 使用具体的枚举值进行断言，确保转换结果的准确性
4. **覆盖完整**: 涵盖了配置开启、关闭、为空等所有可能的场景

## 后续扩展

如需进一步测试第53-66行的逻辑分支（`REFUND_PAY` 根据发货状态的进一步转换），可以考虑：

1. 创建能够产生 `REFUND_PAY` 基础转换结果的测试场景
2. 模拟不同的发货状态（已发货/未发货）
3. 验证 `checkIsOrderSend` 方法的调用和结果处理

这将需要更复杂的测试数据构造和发货状态模拟。
