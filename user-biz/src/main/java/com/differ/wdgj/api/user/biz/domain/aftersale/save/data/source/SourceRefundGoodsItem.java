package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;

/**
 * 退货/退款商品源数据
 *
 * <AUTHOR>
 * @date 2024/7/11 下午3:55
 */
public class SourceRefundGoodsItem<T> {
    /**
     * 售后商品
     */
    private T ployRefundGoods;

    /**
     * 原始单货品
     */
    private ApiTradeGoodsDO apiTradeGoods;

    //region 构造

    /**
     * 构造
     *
     * @param ployRefundGoods 售后商品
     */
    public SourceRefundGoodsItem(T ployRefundGoods) {
        this.ployRefundGoods = ployRefundGoods;
    }

    /**
     * 构造
     *
     * @param ployAfterSaleGoods 退货商品
     * @param apiTradeGoods  原始单货品
     */
    public SourceRefundGoodsItem(T ployAfterSaleGoods, ApiTradeGoodsDO apiTradeGoods) {
        this.ployRefundGoods = ployAfterSaleGoods;
        this.apiTradeGoods = apiTradeGoods;
    }
    //e

    /**
     * 构造
     *
     * @param apiTradeGoods  原始单货品
     */
    public SourceRefundGoodsItem(ApiTradeGoodsDO apiTradeGoods) {
        this.apiTradeGoods = apiTradeGoods;
    }
    //endregion

    //region get/set
    public T getPloyRefundGoods() {
        return ployRefundGoods;
    }

    public void setPloyRefundGoods(T ployRefundGoods) {
        this.ployRefundGoods = ployRefundGoods;
    }

    public ApiTradeGoodsDO getApiTradeGoods() {
        return apiTradeGoods;
    }

    public void setApiTradeGoods(ApiTradeGoodsDO apiTradeGoods) {
        this.apiTradeGoods = apiTradeGoods;
    }

    //endregion
}
