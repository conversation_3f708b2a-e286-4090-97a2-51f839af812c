package com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.sender;

import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.enums.AlarmSenderTypeEnum;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 报警发送器工厂
 *
 * <AUTHOR>
 * @date 2025/3/19 下午2:29
 */
public class AlarmSenderFactory {
    //region 常量
    /**
     * 报警发送器常量缓存
     */
    private final Map<AlarmSenderTypeEnum, IAlarmSender> senderMap = new ConcurrentHashMap<>();
    //endregion

    /**
     * 根据发送类别获取发送器
     *
     * @param senderType 发送类别
     * @return 发送器
     */
    public IAlarmSender getSender(AlarmSenderTypeEnum senderType) {
        return senderMap.computeIfAbsent(senderType, key ->{
            if(key.getSenderClass() != null){
                try {
                    return key.getSenderClass().newInstance();
                } catch (Exception e) {
                    // 不处理异常
                }
            }
            return null;
        });
    }
}
