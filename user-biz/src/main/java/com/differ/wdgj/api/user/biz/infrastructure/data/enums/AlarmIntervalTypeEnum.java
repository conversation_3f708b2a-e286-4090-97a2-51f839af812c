package com.differ.wdgj.api.user.biz.infrastructure.data.enums;


import com.differ.jackyun.framework.component.corpwx.common.enums.AlarmLevel;
import com.differ.jackyun.framework.component.corpwx.common.enums.AlarmType;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.function.Function;

/**
 * 间隔报警类型
 * <AUTHOR>
 * @Date 2021/6/17 18:09
 */
public enum AlarmIntervalTypeEnum implements ValueEnum {
    /**
     * 样例
     */
    DEMO("demo", AlarmType.SYSTEM, AlarmLevel.WARNING_LEVEL, "[样例]", -1),
    /**
     * 线程池
     */
    THREAD_POOL("thread.pool", AlarmType.RESOURCE, AlarmLevel.WARNING_LEVEL, "[线程池]", 1000),
    /**
     * 多队列大报文
     */
    MULTI_TEXT_LENGTH("multi.text.length", AlarmType.HANDLE, AlarmLevel.WARNING_LEVEL, "[多队列大报文]", 1001),
    /**
     * 多队列消费超时报警
     */
    MULTI_CONSUME_TIMEOUT("multi.consume.timeout", AlarmType.HANDLE, AlarmLevel.WARNING_LEVEL, "[多队列消费超时]", 1002),
    /**
     * 多队列失败回调
     */
    MULTI_FAIL_CALLBACK("multi.fail.callback", AlarmType.HANDLE, AlarmLevel.DANGER_LEVEL, "[多队列失败回调]", 1003),
    /**
     * 多队列失败重试多次报警
     */
    MULTI_FAIL_RETRY("multi.fail.retry", AlarmType.HANDLE, AlarmLevel.DANGER_LEVEL, "[多队列失败重试多次]", 1004),
    /**
     * 本地执行超时报警
     */
    LOCAL_EXEC_TIMEOUT("local.exec.timeout", AlarmType.NOTICE, AlarmLevel.WARNING_LEVEL, "[本地执行超时]", 1005),
    /**
     * 任务超时跳过
     */
    TASK_EXEC_EXPIRE_SKIP("task.exec.expire.skip", AlarmType.SPEED, AlarmLevel.WARNING_LEVEL, "[任务超时跳过]", 1006),
    /**
     * 线程池触发拒绝策略
     */
    THREAD_POOL_REJECT("thread.pool.reject", AlarmType.RESOURCE, AlarmLevel.WARNING_LEVEL, "[线程池拒绝]", 1007),
    /**
     * 线程池异常
     */
    THREAD_POOL_ERROR("thread.pool.error", AlarmType.RESOURCE, AlarmLevel.WARNING_LEVEL, "[线程池异常]", 1008),
    /**
     * 线程任务超时
     */
    THREAD_TASK_RUN_TIMEOUT("thread.task.timeout", AlarmType.SPEED, AlarmLevel.WARNING_LEVEL, "[线程任务超时]", 1009),
    /**
     * quartz任务
     */
    QUARTZ_TASK("quartz.task", AlarmType.SPEED, AlarmLevel.WARNING_LEVEL, "[quartz任务]", 1010),
    ;

    //region 常量
    /**
     * 缓存业务前缀
     */
    private final String cachePrefix;
    /**
     * 报警类型
     */
    private final AlarmType alarmType;
    /**
     * 报警级别
     */
    private final AlarmLevel alarmLevel;

    /**
     * 报警标题
     */
    private final String caption;

    /**
     * 是否要间隔去重
     */
    private final boolean interval;

    /**
     * 间隔时间秒
     */
    private final int intervalSecond;

    /**
     * 枚举值
     */
    private final int value;

    /**
     * 间隔时间动态取值
     */
    private static Function<String, Integer> funIntervalSecond;
    //endregion

    //region 构造
    AlarmIntervalTypeEnum(String cachePrefix, AlarmType alarmType, AlarmLevel alarmLevel, String caption, int value) {
        this(cachePrefix, alarmType, alarmLevel, caption, true, value);
    }

    AlarmIntervalTypeEnum(String cachePrefix, AlarmType alarmType, AlarmLevel alarmLevel, String caption, boolean interval, int value) {
        this(cachePrefix, alarmType, alarmLevel, caption, interval, 120, value);
    }

    AlarmIntervalTypeEnum(String cachePrefix, AlarmType alarmType, AlarmLevel alarmLevel, String caption, boolean interval, int intervalSecond, int value) {
        this.cachePrefix = cachePrefix;
        this.alarmType = alarmType;
        this.alarmLevel = alarmLevel;
        this.caption = caption;
        this.interval = interval;
        this.intervalSecond = intervalSecond;
        this.value = value;
    }
    //endregion

    //region 公共方法
    public AlarmType getAlarmType() {
        return alarmType;
    }

    public AlarmLevel getAlarmLevel() {
        return alarmLevel;
    }

    public String getCaption() {
        return caption;
    }

    public boolean isInterval() {
        return interval;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取具体的间隔报警时间
     *
     * @return 报警时间
     */
    public int getIntervalSecond() {
        // 先取动态值，再取固定值
        if (funIntervalSecond != null) {
            Integer seconds = funIntervalSecond.apply(this.cachePrefix);
            if (seconds > 0) {
                return seconds;
            }
        }
        return intervalSecond;
    }

    /**
     * 报警缓存键
     *
     * @param hashText 唯一标识
     * @return 报警缓存键
     */
    public String createAlarmKey(String hashText) {
        if (StringUtils.isEmpty(hashText)) {
            return String.format("alarm:%s", this.cachePrefix);
        }
        return String.format("alarm:%s:%d", this.cachePrefix, hashText.hashCode());
    }
    //endregion

    //region 公共静态方法
    /**
     * 动态设置报警时间间隔
     * @param funIntervalSecond 委托
     */
    public static void setFunIntervalSecond(Function<String, Integer> funIntervalSecond) {
        AlarmIntervalTypeEnum.funIntervalSecond = funIntervalSecond;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static AlarmIntervalTypeEnum create(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }

        return EnumConvertCacheUtil.convert(value, AlarmIntervalTypeEnum.class, EnumConvertType.VALUE);
    }
    //endregion
}