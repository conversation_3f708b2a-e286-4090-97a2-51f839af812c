package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.user;


import com.differ.wdgj.api.user.biz.infrastructure.common.CpuControl;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.SystemErrorCodes;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.AbstractUserFlowJob;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.data.CommonMaxLimitFlowContext;

/**
 * 通用执行上限控制流式任务抽象基类
 *
 * <AUTHOR>
 * @date 2022/9/6 15:21
 */
public abstract class AbstractCommonMaxLimitFlowJob extends AbstractUserFlowJob<CommonMaxLimitFlowContext> {

    // region 重写基类方法

    /**
     * 任务上下文类型
     *
     * @return 类型
     */
    @Override
    protected Class<CommonMaxLimitFlowContext> getClazz() {
        return CommonMaxLimitFlowContext.class;
    }

    /**
     * 单次任务执行
     *
     * @param flowJobContext 上下文
     */
    @Override
    protected void singleTaskExecute(CommonMaxLimitFlowContext flowJobContext) {

        // 执行业务
        int num = this.executeBiz(flowJobContext.getMemberName());

        // 单次任务执行完成，更新上下文
        flowJobContext.onceRunCompleted(num);

        // CPU 控制
        CpuControl.singleton().checkLoop();
    }

    /**
     * 是否继续执行
     *
     * @param flowJobContext 上下文
     * @return 结果
     */
    @Override
    protected boolean continueRun(CommonMaxLimitFlowContext flowJobContext) {

        // 校验上次任务执行业务数量，避免子类业务实现中单次执行任务后，没有正确更新上下文而导致冗余迭代执行
        if (flowJobContext.getLastOnceRunBizDataNum() <= 0) {
            return false;
        }

        // 校验【迭代执行次数】，小于 0 则抛出异常（保险措施，理论不会出现）
        if (flowJobContext.getTotalTimeHasRun().get() <= 0) {
            throw new AppException(SystemErrorCodes.SYSTEMERROR, "通用执行上限控制流式任务迭代执行次数校验失败");
        }

        // 最新单次执行业务数据数量 < 预设单次执行的业务数据【分页大小】：跳出执行
        if (flowJobContext.getLastOnceRunBizDataNum() < this.oncePageSize(flowJobContext.getMemberName())) {
            return false;
        }

        // 已迭代执行次数 >= 预设最大【迭代执行次数】限制：跳出执行
        if (flowJobContext.getTotalTimeHasRun().get() >= this.maxRunTimeLimit(flowJobContext.getMemberName())) {
            return false;
        }

        // 已执行的业务数据总数 >= 预设最大【已执行的业务数据总数】限制：跳出执行
        return flowJobContext.getTotalBizDataHasRun().get() < this.maxBizDataLimit(flowJobContext.getMemberName());
    }

    // endregion

    // region 供子类重写

    /**
     * 执行业务
     *
     * @param memberName 会员名
     * @return 单次执行业务数据数量
     */
    protected abstract int executeBiz(String memberName);

    /**
     * 预设单次执行的业务数据分页大小
     *
     * @param memberName 会员名
     * @return 结果
     */
    protected abstract int oncePageSize(String memberName);

    /**
     * 预设最大迭代执行次数限制
     *
     * @param memberName 会员名
     * @return 结果
     */
    protected abstract int maxRunTimeLimit(String memberName);

    /**
     * 预设最大已执行的业务数据总数限制（默认 10w）
     *
     * @param memberName 会员名
     * @return 结果
     */
    protected int maxBizDataLimit(String memberName) {
        return 100000;
    }

    // endregion
}
