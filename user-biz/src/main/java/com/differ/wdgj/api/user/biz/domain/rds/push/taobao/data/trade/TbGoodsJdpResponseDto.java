package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data;

/**
 * 淘宝商品数据
 * 对应表jdp_tb_trade字段jdp_response
 * <a href="https://open.taobao.com/v2/doc?spm=a219a.15212433.0.0.7d69669apbLtn4#/apiFile?docType=2&docId=24625">平台文档</a>
 *
 * <AUTHOR>
 * @date 2024-03-22 10:28
 */
public class TbGoodsJdpResponseDto {
    //region 变量

    private TBResponseItem item_get_response;

    //endregion

    //region 属性方法

    public TBResponseItem getItem_get_response() {
        return item_get_response;
    }

    public void setItem_get_response(TBResponseItem item_get_response) {
        this.item_get_response = item_get_response;
    }

    //endregion

    public static class TBResponseItem {
        //region 变量

        private Item item;

        //endregion

        //region 属性方法

        public Item getItem() {
            return item;
        }

        public void setItem(Item item) {
            this.item = item;
        }

        //endregion
    }

    public static class Item {
        //region 变量

        /**
         * 商品上传后的状态(onsale出售中，instock库中)。
         */
        private String approve_status;

        private String auction_point;

        /**
         * 商品所属的子类目Id。
         */
        private String cid;

        private String created;

        private String delist_time;

        private String detail_url;

        private String ems_fee;

        private String express_fee;

        private String freight_payer;

        /**
         * 支持会员打折(true false)。
         */
        private String has_discount;

        /**
         * 是否有发票(true false)。
         */
        private String has_invoice;

        /**
         * 橱窗推荐(true false)。
         */
        private String has_showcase;

        /**
         * 是否有保修(true false)。
         */
        private String has_warranty;

        private String input_pids;

        private String input_str;

        private String is_fenxiao;

        private String is_timing;

        /**
         * 虚拟商品的状态字段。
         */
        private String is_virtual;

        private Item_Imgs item_imgs;

        private String item_weight;

        private String list_time;

        private Location location;

        /**
         * 商品修改时间(格式：yyyy-MM-dd HH:mm:ss)。
         */
        private String modified;

        private String nick;

        /**
         * 商品数量。
         */
        private int num;

        /**
         * 商品数字Id。
         */
        private long num_iid;

        /**
         * 商家外部编码(可与商家外部系统对接)。需要授权才能获取。
         */
        private String outer_id;

        /**
         * 商品主图片地址(http://img03.taobao.net/bao/uploaded/i3/T1HXdXXgPSt0JxZ2.8_070458.jpg)。
         */
        private String pic_url;

        private String post_fee;

        /**
         * 宝贝所属的运费模板ID(没有返回则说明没有使用运费模板)。
         */
        private String postage_id;

        /**
         * 商品价格(格式：5.00；单位：元)。
         */
        private String price;

        private String product_id;

        private Prop_Imgs prop_imgs;

        private String property_alias;

        /**
         * 商品属性(格式：pid:vid;pid:vid)。
         */
        private String props;

        private String props_name;

        private String sell_point;

        /**
         * 商品所属的店铺内卖家自定义类目列表。
         */
        private String seller_cids;

        private Skus skus;

        private String stuff_status;

        private String sub_stock;

        private String template_id;

        /**
         * 商品标题(不能超过60字节)。
         */
        private String title;

        /**
         * 商品类型(fixed:一口价;auction:拍卖)。
         */
        private String type;

        private String violation;

        //endregion

        //region 属性方法

        public String getApprove_status() {
            return approve_status;
        }

        public void setApprove_status(String approve_status) {
            this.approve_status = approve_status;
        }

        public String getAuction_point() {
            return auction_point;
        }

        public void setAuction_point(String auction_point) {
            this.auction_point = auction_point;
        }

        public String getCid() {
            return cid;
        }

        public void setCid(String cid) {
            this.cid = cid;
        }

        public String getCreated() {
            return created;
        }

        public void setCreated(String created) {
            this.created = created;
        }

        public String getDelist_time() {
            return delist_time;
        }

        public void setDelist_time(String delist_time) {
            this.delist_time = delist_time;
        }

        public String getDetail_url() {
            return detail_url;
        }

        public void setDetail_url(String detail_url) {
            this.detail_url = detail_url;
        }

        public String getEms_fee() {
            return ems_fee;
        }

        public void setEms_fee(String ems_fee) {
            this.ems_fee = ems_fee;
        }

        public String getExpress_fee() {
            return express_fee;
        }

        public void setExpress_fee(String express_fee) {
            this.express_fee = express_fee;
        }

        public String getFreight_payer() {
            return freight_payer;
        }

        public void setFreight_payer(String freight_payer) {
            this.freight_payer = freight_payer;
        }

        public String getHas_discount() {
            return has_discount;
        }

        public void setHas_discount(String has_discount) {
            this.has_discount = has_discount;
        }

        public String getHas_invoice() {
            return has_invoice;
        }

        public void setHas_invoice(String has_invoice) {
            this.has_invoice = has_invoice;
        }

        public String getHas_showcase() {
            return has_showcase;
        }

        public void setHas_showcase(String has_showcase) {
            this.has_showcase = has_showcase;
        }

        public String getHas_warranty() {
            return has_warranty;
        }

        public void setHas_warranty(String has_warranty) {
            this.has_warranty = has_warranty;
        }

        public String getInput_pids() {
            return input_pids;
        }

        public void setInput_pids(String input_pids) {
            this.input_pids = input_pids;
        }

        public String getInput_str() {
            return input_str;
        }

        public void setInput_str(String input_str) {
            this.input_str = input_str;
        }

        public String getIs_fenxiao() {
            return is_fenxiao;
        }

        public void setIs_fenxiao(String is_fenxiao) {
            this.is_fenxiao = is_fenxiao;
        }

        public String getIs_timing() {
            return is_timing;
        }

        public void setIs_timing(String is_timing) {
            this.is_timing = is_timing;
        }

        public String getIs_virtual() {
            return is_virtual;
        }

        public void setIs_virtual(String is_virtual) {
            this.is_virtual = is_virtual;
        }

        public Item_Imgs getItem_imgs() {
            return item_imgs;
        }

        public void setItem_imgs(Item_Imgs item_imgs) {
            this.item_imgs = item_imgs;
        }

        public String getItem_weight() {
            return item_weight;
        }

        public void setItem_weight(String item_weight) {
            this.item_weight = item_weight;
        }

        public String getList_time() {
            return list_time;
        }

        public void setList_time(String list_time) {
            this.list_time = list_time;
        }

        public Location getLocation() {
            return location;
        }

        public void setLocation(Location location) {
            this.location = location;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public String getNick() {
            return nick;
        }

        public void setNick(String nick) {
            this.nick = nick;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public long getNum_iid() {
            return num_iid;
        }

        public void setNum_iid(long num_iid) {
            this.num_iid = num_iid;
        }

        public String getOuter_id() {
            return outer_id;
        }

        public void setOuter_id(String outer_id) {
            this.outer_id = outer_id;
        }

        public String getPic_url() {
            return pic_url;
        }

        public void setPic_url(String pic_url) {
            this.pic_url = pic_url;
        }

        public String getPost_fee() {
            return post_fee;
        }

        public void setPost_fee(String post_fee) {
            this.post_fee = post_fee;
        }

        public String getPostage_id() {
            return postage_id;
        }

        public void setPostage_id(String postage_id) {
            this.postage_id = postage_id;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getProduct_id() {
            return product_id;
        }

        public void setProduct_id(String product_id) {
            this.product_id = product_id;
        }

        public Prop_Imgs getProp_imgs() {
            return prop_imgs;
        }

        public void setProp_imgs(Prop_Imgs prop_imgs) {
            this.prop_imgs = prop_imgs;
        }

        public String getProperty_alias() {
            return property_alias;
        }

        public void setProperty_alias(String property_alias) {
            this.property_alias = property_alias;
        }

        public String getProps() {
            return props;
        }

        public void setProps(String props) {
            this.props = props;
        }

        public String getProps_name() {
            return props_name;
        }

        public void setProps_name(String props_name) {
            this.props_name = props_name;
        }

        public String getSell_point() {
            return sell_point;
        }

        public void setSell_point(String sell_point) {
            this.sell_point = sell_point;
        }

        public String getSeller_cids() {
            return seller_cids;
        }

        public void setSeller_cids(String seller_cids) {
            this.seller_cids = seller_cids;
        }

        public Skus getSkus() {
            return skus;
        }

        public void setSkus(Skus skus) {
            this.skus = skus;
        }

        public String getStuff_status() {
            return stuff_status;
        }

        public void setStuff_status(String stuff_status) {
            this.stuff_status = stuff_status;
        }

        public String getSub_stock() {
            return sub_stock;
        }

        public void setSub_stock(String sub_stock) {
            this.sub_stock = sub_stock;
        }

        public String getTemplate_id() {
            return template_id;
        }

        public void setTemplate_id(String template_id) {
            this.template_id = template_id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getViolation() {
            return violation;
        }

        public void setViolation(String violation) {
            this.violation = violation;
        }

        //endregion
    }

    public static class Item_Imgs {
        //region 变量

        private Item_Img[] item_img;

        //endregion

        //region 属性方法

        public Item_Img[] getItem_img() {
            return item_img;
        }

        public void setItem_img(Item_Img[] item_img) {
            this.item_img = item_img;
        }

        //endregion
    }

    public static class Item_Img {
        //region 变量

        private String id;

        private String position;

        private String url;

        //endregion

        //region 属性方法

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        //endregion
    }

    public static class Location {
        //region 变量

        private String city;

        private String state;

        //endregion

        //region 属性方法

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        //endregion
    }

    public static class Prop_Imgs {
        //region 变量

        private Prop_Img[] prop_img;

        //endregion

        //region 属性方法

        public Prop_Img[] getProp_img() {
            return prop_img;
        }

        public void setProp_img(Prop_Img[] prop_img) {
            this.prop_img = prop_img;
        }

        //endregion
    }

    public static class Prop_Img {
        //region 变量

        private String id;

        private String position;

        private String properties;

        private String url;

        //endregion

        //region 属性方法

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getPosition() {
            return position;
        }

        public void setPosition(String position) {
            this.position = position;
        }

        public String getProperties() {
            return properties;
        }

        public void setProperties(String properties) {
            this.properties = properties;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        //endregion
    }

    public static class Skus {
        //region 变量

        private Sku[] sku;

        //endregion

        //region 属性方法

        public Sku[] getSku() {
            return sku;
        }

        public void setSku(Sku[] sku) {
            this.sku = sku;
        }

        //endregion
    }

    public static class Sku {
        //region 变量

        private String created;

        private String modified;
        /**
         * 商家外部编码(可与商家外部系统对接)。需要授权才能获取。
         */
        private String outer_id;

        private String price;

        private String properties;

        private String properties_name;

        private int quantity;

        private String sku_id;

        private String with_hold_quantity;

        //endregion

        //region 属性方法

        public String getCreated() {
            return created;
        }

        public void setCreated(String created) {
            this.created = created;
        }

        public String getModified() {
            return modified;
        }

        public void setModified(String modified) {
            this.modified = modified;
        }

        public String getOuter_id() {
            return outer_id;
        }

        public void setOuter_id(String outer_id) {
            this.outer_id = outer_id;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getProperties() {
            return properties;
        }

        public void setProperties(String properties) {
            this.properties = properties;
        }

        public String getProperties_name() {
            return properties_name;
        }

        public void setProperties_name(String properties_name) {
            this.properties_name = properties_name;
        }

        public int getQuantity() {
            return quantity;
        }

        public void setQuantity(int quantity) {
            this.quantity = quantity;
        }

        public String getSku_id() {
            return sku_id;
        }

        public void setSku_id(String sku_id) {
            this.sku_id = sku_id;
        }

        public String getWith_hold_quantity() {
            return with_hold_quantity;
        }

        public void setWith_hold_quantity(String with_hold_quantity) {
            this.with_hold_quantity = with_hold_quantity;
        }


        //endregion
    }
}
