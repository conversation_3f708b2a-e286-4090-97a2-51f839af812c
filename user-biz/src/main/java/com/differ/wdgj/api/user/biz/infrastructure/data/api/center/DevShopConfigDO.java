package com.differ.wdgj.api.user.biz.infrastructure.data.api.center;

import java.time.LocalDateTime;

/**
 * 中心库 - 店铺配置
 *
 * <AUTHOR>
 * @date 2025/2/21 下午3:40
 */
public class DevShopConfigDO {

    /**
     * 用户名。
     */
    private String userName;

    /**
     * 店铺ID（如果类型为通用，则值为0）。
     */
    private int shopId;

    /**
     * 店铺配置类型（例如：公共配置=0, 下载订单=1, 反写备注=2 等）。
     */
    private int type;

    /**
     * 平台枚举值（BusinessPlats 或 LogisticsPlats 枚举值之一）。
     */
    private int platValue;

    /**
     * 配置是否启用。
     */
    private boolean isActived;

    /**
     * 配置值（JSON格式）。
     */
    private String configValue;

    /**
     * 创建时间。
     */
    private LocalDateTime createTime;

    /**
     * 最后修改时间。
     */
    private LocalDateTime lastModifyTime;

    //region get/set
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getPlatValue() {
        return platValue;
    }

    public void setPlatValue(int platValue) {
        this.platValue = platValue;
    }

    public boolean getIsActived() {
        return isActived;
    }

    public void setIsActived(boolean isActived) {
        this.isActived = isActived;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(LocalDateTime lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }
    //endregion
}
