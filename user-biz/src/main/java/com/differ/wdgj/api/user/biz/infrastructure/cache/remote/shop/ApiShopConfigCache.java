package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.auto.AutoLoadAfterShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashEnhanceCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopConfigKey;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiShopConfigDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.CfgShopListExtMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 会员店铺配置缓存
 *
 * <AUTHOR>
 * @date 2024-06-25 17:05
 */
public class ApiShopConfigCache extends AbstractHashEnhanceCache<ApiShopConfigDto> {

    //region 构造和枚举单例

    private ApiShopConfigCache(String outAccount) {
        super(String.format(DataCacheKeyEnum.USER_API_SHOP_CONFIG.getCode(), outAccount));
        this.outAccount = outAccount;

        // 过期时间 1 ~ 2 小时随机
        this.minCacheKeyTimeOut = 3600;
        this.maxCacheKeyTimeOut = 7200;
    }

    public static ApiShopConfigCache create(String outAccount) {
        return new ApiShopConfigCache(outAccount);
    }

    //endregion

    //region 常量

    /**
     * 外部会员名
     */
    private final String outAccount;

    /**
     * hashKey分割符
     */
    private static final String SPLIT_CHAR = "@";

    //endregion

    //region 公共方法

    /**
     * 查询
     * <p>
     * 当缓存中不存在时会从数据源获取并同步到缓存
     *
     * @param shopId  店铺id
     * @param bizType 业务类型
     * @return 结果
     */
    public ApiShopConfigDto getData(int shopId, ApiShopConfigBizTypes bizType) {

        // 校验入参
        if (shopId == 0 || bizType == null) {
            return null;
        }

        // 查询缓存
        return super.getAndSyncIfAbsent(this.createHashKey(shopId, bizType));
    }

    /**
     * 移除缓存-单个
     *
     * @param shopId  店铺id
     * @param bizType 业务类型
     */
    public void clearCache(int shopId, ApiShopConfigBizTypes bizType) {

        // 校验入参
        if (shopId == 0 || bizType == null) {
            return;
        }

        // 移除缓存
        super.removeCache(cacheKey, this.createHashKey(shopId, bizType));
        // 调用相同生命周期的缓存
        // 1、自动下载售后单的店铺
        AutoLoadAfterShopCache.singleton().clearCache(outAccount);
    }

    /**
     * 移除缓存-批量
     *
     * @param uniqueKeys 唯一键
     */
    public void clearCache(List<ApiShopConfigKey> uniqueKeys) {

        // 校验入参
        if (CollectionUtils.isEmpty(uniqueKeys)) {
            return;
        }

        // 封装哈希字段集合
        String[] hashFields = uniqueKeys.stream().map(x -> createHashKey(x.getShopId(), x.getBizType())).toArray(String[]::new);

        // 移除缓存
        super.removeCache(cacheKey, hashFields);
    }

    //endregion

    //region 实现基类方法

    /**
     * 获取数据源
     *
     * @param hashField 字段
     * @return 值
     */
    @Override
    protected ApiShopConfigDto loadSource(String hashField) {
        // 解析哈希字段
        ApiShopConfigKey apiShopConfigKey = hashKeyConvert(hashField);
        if (apiShopConfigKey == null) {
            return null;
        }

        // 获取店铺配置信息
        AtomicReference<ApiShopConfigDto> shopConfig = new AtomicReference<>();
        CfgShopListExtMapper shopListExtMapper = BeanContextUtil.getBean(CfgShopListExtMapper.class);
        DBSwitchUtil.doDBWithUser(outAccount,
                () ->{
                    switch (apiShopConfigKey.getBizType()){
                        case DOWNLOAD_ORDER:
                            shopConfig.set(shopListExtMapper.getDownloadOrderShopConfig(apiShopConfigKey.getShopId()));
                            break;
                        case AFTER_SALES:
                            shopConfig.set(shopListExtMapper.getAfterSaleShopConfig(apiShopConfigKey.getShopId()));
                            break;
                        default:
                            throw new IllegalStateException("API店铺配置-不存在的业务类型: " + apiShopConfigKey.getBizType().getName());
                    }
                });
        // 维护类型
        ApiShopConfigDto apiShopConfigDTO = shopConfig.get();
        if(apiShopConfigDTO != null){
            apiShopConfigDTO.setBizType(apiShopConfigKey.getBizType().getValue());
        }

        return apiShopConfigDTO;
    }

    /**
     * 批量获取数据源
     *
     * @param hashFields 字段集合
     * @return 值集合
     */
    @Override
    protected Map<String, ApiShopConfigDto> loadSource(List<String> hashFields) {
        return null;
    }

    /**
     * 获取值类型
     *
     * @return 值类型
     */
    @Override
    public Class<ApiShopConfigDto> getValueClazz() {
        return ApiShopConfigDto.class;
    }

    //endregion

    //region 私有方法

    /**
     * 生成hashKey
     *
     * @param shopId  店铺Id
     * @param bizType 业务类型
     * @return hashKey
     */
    private String createHashKey(int shopId, ApiShopConfigBizTypes bizType) {
        return String.format("%s%s%s", shopId, SPLIT_CHAR, bizType.getValue());
    }

    /**
     * hashKey转换成配置键枚举
     *
     * @param hashKey hashKey
     * @return 结果
     */
    private ApiShopConfigKey hashKeyConvert(String hashKey) {
        if (StringUtils.isEmpty(hashKey)) {
            return null;
        }
        String[] hashKeyArr = hashKey.split(SPLIT_CHAR);
        if (hashKeyArr.length < 2) {
            return null;
        }
        int shopId = Integer.parseInt(hashKeyArr[0]);
        ApiShopConfigBizTypes bizType = ApiShopConfigBizTypes.create(hashKeyArr[1]);
        return ApiShopConfigKey.create(shopId, bizType);
    }

    //endregion
}
