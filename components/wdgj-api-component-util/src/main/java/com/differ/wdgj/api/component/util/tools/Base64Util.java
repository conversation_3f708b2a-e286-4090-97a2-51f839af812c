package com.differ.wdgj.api.component.util.tools;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/2/3 11:20
 */
public class Base64Util {
    /**
     * 加密
     *
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public String encrypt(String text) throws UnsupportedEncodingException {
        byte[] data = text.getBytes(StandardCharsets.UTF_8);
        return Base64.getEncoder().encodeToString(data);
    }


}
