package com.differ.wdgj.api.user.biz.tasks.job.single.plugins;

import com.differ.wdgj.api.component.task.single.core.JobExecTimeStrategy;
import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.work.WorkFacade;
import com.differ.wdgj.api.user.biz.tasks.job.single.core.BaseSingleJob;
import org.apache.commons.lang3.RandomUtils;

/**
 * 心跳检测的定时任务
 *
 * <AUTHOR>
 * @date 2024/10/28 上午10:35
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "WorkHeartJob",
        cron = "0/10 * * * * ?"
)
public class WorkHeartJob extends BaseSingleJob {
    //region 常量
    /**
     * 工作任务门面
     */
    private final WorkFacade workFacade = new WorkFacade();
    //endregion

    /**
     * 获取执行时间策略
     *
     * @return 执行时间策略
     */
    @Override
    protected JobExecTimeStrategy getExecTimeStrategy() {
        int frequency = RandomUtils.nextInt(60, 120);
        execTimeStrategy.setRunFrequency(frequency);
        return execTimeStrategy;
    }

    /**
     * 业务处理
     */
    @Override
    protected void doWork() {
        // 刷新心跳
        workFacade.doHeart();
    }
}
