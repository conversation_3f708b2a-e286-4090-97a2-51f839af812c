package com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue;

import com.differ.wdgj.api.component.wait.data.CommonWaitEntity;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.common.ActiveMembersLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.UserQueueJobData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 两级队列，用户级和集群级，https://s.jkyun.biz/TehUtM6 拉取模式排队数据流程示意图
 *
 * <AUTHOR>
 * @date 2024/5/21 15:06
 */
public class UserClusterJobTaskSourceQueue<T extends UserQueueJobData> extends AbstractTaskSourceQueue<T> {
    /**
     * 用户队列
     */
    private Map<String, RemoteRedisWaitTaskSourceQueue<T>> userQueueMap = new HashMap<>();
    /**
     * 集群队列
     */
    private RemoteRedisWaitTaskSourceQueue<T> clusterQueue;

    /**
     * 初始化队列
     */
    @Override
    protected void initQueue() {
        // 初始集群级队列,jobCode外部传入时已带集群号
        this.clusterQueue = new RemoteRedisWaitTaskSourceQueue<>();
        this.clusterQueue.init(jobCode, this.jobDataClazz, funExecTimeout);
        this.clusterQueue.getPullWaitHandler().getWaitContext().setFunGetMaxExecCount(() -> getMaxClusterExecCount(jobCode));
    }

    /**
     * 添加任务
     *
     * @param task 任务数据
     * @return 是否成功
     */
    @Override
    public boolean addTask(T task) {
        boolean success = getUserQueue(task.getMemberName()).addTask(task);
        // 添加任务后，为下一级队列（集群队列）做数据准备
        prepareNext(task.getMemberName());
        return success;
    }

    /**
     * 添加任务
     *
     * @param tasks 任务数据
     * @return 是否成功
     */
    @Override
    public void addTasks(List<T> tasks) {
        for (T task : tasks) {
            addTask(task);
        }
    }

    /**
     * 拉取下一个排队数据
     */
    private void prepareNext(List<String> users) {
        for (String user : users) {
            prepareNext(user);
        }
    }

    /**
     * 拉取下一个排队数据
     */
    private void prepareNext(String user) {
        RemoteRedisWaitTaskSourceQueue<T> userQueue = getUserQueue(user);
        CommonWaitEntity next = userQueue.pullNextData();
        // 理论上可以循环，直到取不到待执行数据，为了更平均的分配任务，只取一次
        if (next != null) {
            clusterQueue.addTask(next.getWaitUniqueId(), next.getDataString());
        }
    }

    /**
     * 拉取下一个排队数据
     *
     * @return 返回排队的key和数据
     */
    @Override
    public Map<String, T> pullNext() {
        return clusterQueue.pullNext();
    }

    /**
     * 完成任务
     *
     * @param waitUniqueId 排队的任务key
     * @param task         任务数据
     */
    @Override
    public void complete(String waitUniqueId, T task) {
        // 回调全局
        clusterQueue.complete(waitUniqueId, task);
        // 回调会员级
        JobTaskSourceQueue<T> userQueue = getUserQueue(task.getMemberName());
        userQueue.complete(waitUniqueId, task);
        // 准备会员的下一个任务
        prepareNext(task.getMemberName());
    }

    /**
     * 获取用户队列
     *
     * @param user
     * @return
     */
    private RemoteRedisWaitTaskSourceQueue<T> getUserQueue(String user) {
        return userQueueMap.computeIfAbsent(user, k -> {
            RemoteRedisWaitTaskSourceQueue userQueue = new RemoteRedisWaitTaskSourceQueue<>();
            userQueue.init(String.format("%s:%s", jobCode, k), jobDataClazz, this.funExecTimeout);
            // 动态设置最大执行数的参数
            userQueue.getPullWaitHandler().getWaitContext().setFunGetMaxExecCount(() -> getMaxUserExecCount(jobCode, user));
            return userQueue;
        });
    }

    /**
     * 获取用户的最大执行数
     *
     * @param jobCode
     * @param user
     * @return
     */
    private Integer getMaxUserExecCount(String jobCode, String user) {
        String prefix = String.format("%s-%s", jobCode, user);
        int userCount = Integer.parseInt(ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.QUEUE_JOB_CODE_USER_MAX_EXEC, prefix, "0"));
        if(userCount > 0){
            return userCount;
        }

        return Integer.parseInt(ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.QUEUE_JOB_CODE_MAX_EXEC, jobCode, "5"));
    }

    /**
     * 获取集群的最大执行数
     *
     * @param jobCode
     * @return
     */
    private Integer getMaxClusterExecCount(String jobCode) {
        return Integer.parseInt(ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.QUEUE_JOB_CLUSTER_MAX_EXEC, jobCode,  "20"));
    }

    /**
     * 拉取下一个排队数据,集群无数据时，自动把会员队列的任务拉取到集群
     *
     * @return 返回排队的key和数据
     */
    private Map<String, T> pullNextWithMember() {
        // 1、先取集群任务
        Map<String, T> nextTask = clusterQueue.pullNext();
        if (MapUtils.isNotEmpty(nextTask)) {
            return nextTask;
        }

        // 2、集群未取到任务时，把会员队列的任务拉取到集群
        List<String> distributeMembers = ActiveMembersLocalCache.singleton().getActiveMembersCurrentCluster();
        if (CollectionUtils.isEmpty(distributeMembers)) {
            return null;
        }
        prepareNext(distributeMembers);

        // 3、再次取集群任务
        return clusterQueue.pullNext();
    }
}
