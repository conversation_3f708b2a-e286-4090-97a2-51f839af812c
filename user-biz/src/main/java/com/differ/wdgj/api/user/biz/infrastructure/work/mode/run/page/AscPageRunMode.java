package com.differ.wdgj.api.user.biz.infrastructure.work.mode.run.page;


import com.differ.wdgj.api.user.biz.infrastructure.work.data.*;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;

/**
 * 增量升序分页下载模式
 * 说明：升序基本使用创建时间，否则使用修改时间容易丢数据
 *
 * <AUTHOR>
 * @date 2024/7/5 15:33
 */
public class AscPageRunMode extends AbstractPageRunMode {

    public AscPageRunMode(String taskId, WorkData workData) {
        super(taskId, workData);
    }

    /**
     * 移到子任务的下一页
     *
     * @param subTask   子任务
     * @param result    执行结果
     * @param firstPage 是否第一页
     */
    @Override
    protected void moveNextPage(SubPageTask subTask, RunPageResult result, boolean firstPage) {
        subTask.getDynamicStatus().setPageIndex(subTask.getDynamicStatus().getPageIndex() + 1);
    }

    /**
     * 是否还有下一页
     *
     * @param subTask 子任务
     * @param result  子任务执行结果
     * @return 结果
     */
    @Override
    protected boolean hasNextPage(SubPageTask subTask, RunPageResult result) {
        if (result.getRunStatus() != RunStatusEnum.RUNNING) {
            // 非运行状态，不执行
            return false;
        }
        PageDynamicStatus statusData = subTask.getDynamicStatus();
        // 达到业务最大总页数不再下载下一页，后续依赖拆分逻辑继续执行
        if(subTask.getMaxTotalPages() > 0 && statusData.getPageIndex() >= subTask.getMaxTotalPages()){
            return false;
        }
        // 首页索引基于1，倒序有下一页：当前页<=总页数
        return statusData.getPageIndex() <= statusData.getTotalPages();
    }

}
