package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.QueryDbOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.core.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.goods.RefundGoodsAmountCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.goods.RefundGoodsCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.goods.RefundGoodsJammingCodeHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.goods.RefundGoodsMatchHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.order.RefundOrderCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter.RefundTypeFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.savedata.AfterSaleDataOperation;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后单保存插件
 *
 * <AUTHOR>
 * @date 2024/7/25 下午3:55
 */
@Ignore
public class SaveRefundOrderHandleTest extends BaseAfterSaleOrderTest {

    //region 变量
    /**
     * 上下文
     */
    private AfterSaleSaveContext context;

    /**
     * 原始售后单信息
     */
    private List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> sourceOrderList;

    /**
     * 基础测试数据地址
     */
    private final String basePath = "D:\\esapi-java\\管家java测试数据\\AfterSaleDataOperationTest\\%s";
    //endregion

    /**
     * 类前置执行
     */
    @Before
    public void setBeforeClass() throws Exception {
        String memberName = "api2017";
        int outShopId = 1446;
        context = getContext(memberName, outShopId);

        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "BusinessGetRefundOrderResponseOrderItem.json"))));
        AfterSaleDataOperation afterSaleDataOperation = new AfterSaleDataOperation(context);
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {});
        sourceOrderList = polyOrderList.stream().map(x -> new SourceAfterSaleOrderItem<>(x.getRefundNo(), x.getPlatOrderNo(), AfterSaleSaveBizType.getDefaultBizType(), x)).collect(Collectors.toList());
        SaveAfterSaleResult<List<QueryDbOrderResult>> oldDbOrderItems = afterSaleDataOperation.getOldDbOrderItems(sourceOrderList);
        sourceOrderList.forEach(order -> {
            QueryDbOrderResult queryDbOrderResult = oldDbOrderItems.getContent().stream().filter(x -> x.getAfterSaleNo().equals(order.getAfterSaleNo())).findFirst().orElse(null);
            if (queryDbOrderResult != null) {
                order.setDbOrder(queryDbOrderResult.getDbOrder());
            }
        });
    }

    //region 前置过滤插件

    /**
     * 退货退款单类别过滤器
     */
    @Test
    public void RefundTypeFilterHandleTest() {
        RefundTypeFilterHandle handle = new RefundTypeFilterHandle(context);
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceOrderList) {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            handle.preFiltrationOrder(sourceOrder,targetOrder);
        }
    }
    //endregion

    //region 订单级插件

    /**
     * 退货退款单-订单级基础数据转换插件
     */
    @Test
    public void RefundOrderCovertHandleTest() {
        RefundOrderCovertHandle handle = new RefundOrderCovertHandle(context);
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceOrderList) {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            handle.convertOrder(sourceOrder, targetOrder);
        }
    }
    //endregion

    //region 商品级插件

    /**
     * 退货退款单-退货商品基础数据转换插件
     */
    @Test
    public void RefundGoodsCovertHandleTest() {
        RefundGoodsCovertHandle handle = new RefundGoodsCovertHandle(context);
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceOrderList) {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
            for (BusinessGetRefundResponseRefundGoodInfo refundGoods : ployOrder.getRefundGoods()) {
                ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
                ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(sourceOrder.getDbOrder().getApiTradeGoodsList())
                        ? sourceOrder.getDbOrder().getApiTradeGoodsList().stream().filter(x -> AfterSaleGoodsMatchOperation.isRefundMatchApiTradeGoods(x, ployOrder, refundGoods)).findFirst().orElse(null)
                        : new ApiTradeGoodsDO();
                SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(refundGoods, apiTradeGoods);
                handle.convert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
            }
        }
    }

    /**
     * 退货退款单-退货商品金额转换插件
     */
    @Test
    public void RefundGoodsAmountCovertHandleTest() {
        RefundGoodsAmountCovertHandle handle = new RefundGoodsAmountCovertHandle(context);
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceOrderList) {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
            for (BusinessGetRefundResponseRefundGoodInfo refundGoods : ployOrder.getRefundGoods()) {
                ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
                ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(sourceOrder.getDbOrder().getApiTradeGoodsList())
                        ? sourceOrder.getDbOrder().getApiTradeGoodsList().stream().filter(x -> AfterSaleGoodsMatchOperation.isRefundMatchApiTradeGoods(x, ployOrder, refundGoods)).findFirst().orElse(null)
                        : new ApiTradeGoodsDO();
                SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(refundGoods, apiTradeGoods);
                handle.convert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
            }
        }
    }

    /**
     * 退货退款单-退货商品编码掩码处理插件
     */
    @Test
    public void RefundGoodsJammingCodeHandleTest() {
        RefundGoodsJammingCodeHandle handle = new RefundGoodsJammingCodeHandle(context);
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceOrderList) {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
            for (BusinessGetRefundResponseRefundGoodInfo refundGoods : ployOrder.getRefundGoods()) {
                ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
                targetRefundGoods.setOuterId("dazhizhu");
                ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(sourceOrder.getDbOrder().getApiTradeGoodsList())
                        ? sourceOrder.getDbOrder().getApiTradeGoodsList().stream().filter(x -> AfterSaleGoodsMatchOperation.isRefundMatchApiTradeGoods(x, ployOrder, refundGoods)).findFirst().orElse(null)
                        : new ApiTradeGoodsDO();
                SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(refundGoods, apiTradeGoods);
                handle.convert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
            }
        }
    }

    /**
     * 退货退款单-退货商品商品匹配插件
     */
    @Test
    public void RefundGoodsMatchHandleTest() {
        RefundGoodsMatchHandle handle = new RefundGoodsMatchHandle(context);
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceOrderList) {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
            for (BusinessGetRefundResponseRefundGoodInfo refundGoods : ployOrder.getRefundGoods()) {
                ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
                targetRefundGoods.setOuterId("dazhizhu");
                targetRefundGoods.setGoodsTitle("大蜘蛛");
                targetRefundGoods.setSku("");
                ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(sourceOrder.getDbOrder().getApiTradeGoodsList())
                        ? sourceOrder.getDbOrder().getApiTradeGoodsList().stream().filter(x -> AfterSaleGoodsMatchOperation.isRefundMatchApiTradeGoods(x, ployOrder, refundGoods)).findFirst().orElse(null)
                        : new ApiTradeGoodsDO();
                SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(refundGoods, apiTradeGoods);
                handle.convert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
            }
        }
    }
    //endregion
}
