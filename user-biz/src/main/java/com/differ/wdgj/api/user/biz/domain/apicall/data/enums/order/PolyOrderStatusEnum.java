package com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;

/**
 * 原始单状态
 *
 * <AUTHOR>
 * @date 2024/7/15 下午9:39
 */
public enum PolyOrderStatusEnum implements CodeEnum {

    /**
     * 等待买家付款。
     */
    WAITING_BUYER_PAYMENT("等待买家付款", "JH_01"),

    /**
     * 等待卖家发货。
     */
    WAITING_SELLER_SHIPMENT("等待卖家发货", "JH_02"),

    /**
     * 等待买家确认收货。
     */
    WAITING_BUYER_CONFIRM("等待买家确认收货", "JH_03"),

    /**
     * 交易成功。
     */
    TRADE_SUCCESS("交易成功", "JH_04"),

    /**
     * 交易关闭。
     */
    TRADE_CLOSED("交易关闭", "JH_05"),

    /**
     * 已暂停。
     */
    PAUSED("已暂停", "JH_06"),

    /**
     * 已锁定。
     */
    LOCKED("已锁定", "JH_07"),

    /**
     * 卖家部分发货。
     */
    PARTIALLY_SHIPPED("卖家部分发货", "JH_08"),

    /**
     * 订单冻结。
     */
    ORDER_FROZEN("订单冻结", "JH_09"),

    /**
     * 缺货订单。
     */
    OUT_OF_STOCK("缺货订单", "JH_10"),

    /**
     * 投诉订单。
     */
    COMPLAINT_ORDER("投诉订单", "JH_12"),

    /**
     * 已拆单。
     */
    SPLIT_ORDER("已拆单", "JH_13"),

    /**
     * 退换货订单。
     */
    RETURN_EXCHANGE_ORDER("退换货订单", "JH_14"),

    /**
     * 待开票订单。
     */
    PENDING_INVOICE("待开票订单", "JH_15"),

    /**
     * 处理中订单。
     */
    PROCESSING_ORDER("处理中订单", "JH_17"),

    /**
     * 未发货取消。
     */
    UNDISPATCHED_CANCELED("未发货取消", "JH_18"),

    /**
     * 已发货取消。
     */
    SHIPPED_CANCELED("已发货取消", "JH_19"),

    /**
     * 已揽收取消。
     */
    COLLECTED_CANCELED("已揽收取消", "JH_20"),

    /**
     * 无需发货。
     */
    NO_SHIPMENT_REQUIRED("无需发货", "JH_21"),

    /**
     * 取消待审核。
     */
    CANCELLATION_PENDING_REVIEW("取消待审核", "JH_22"),

    /**
     * 已拒收。
     */
    REJECTED("已拒收", "JH_23"),

    /**
     * 禁发订单。
     */
    BANNED_ORDER("禁发订单", "JH_24"),

    /**
     * 已取消。
     */
    CANCELED("已取消", "JH_25"),

    /**
     * 待确认。
     */
    PENDING_CONFIRMATION("待确认", "JH_26"),

    /**
     * 待平台发货。
     */
    PENDING_PLATFORM_SHIPMENT("待平台发货", "JH_27"),

    /**
     * 待平台收货。
     */
    PENDING_PLATFORM_RECEIPT("待平台收货", "JH_28"),

    /**
     * 待物流揽收。
     */
    PENDING_LOGISTICS_COLLECTION("待物流揽收", "JH_29"),

    /**
     * 清关中。
     */
    CUSTOMS_CLEARANCE("清关中", "JH_30"),

    /**
     * 其他。
     */
    OTHER("其他", "JH_98"),

    /**
     * 所有订单。
     */
    ALL_ORDERS("所有订单", "JH_99");

    private final String description;
    private final String code;

    /**
     * 构造函数初始化枚举常量。
     *
     * @param description 枚举的描述。
     * @param code        枚举的代码。
     */
    PolyOrderStatusEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }

    /**
     * 获取枚举的描述。
     *
     * @return 描述。
     */
    public String getDescription() {
        return description;
    }

    /**
     * 获取枚举的代码。
     *
     * @return 代码。
     */
    @Override
    public String getCode() {
        return code;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static PolyOrderStatusEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, PolyOrderStatusEnum.class, EnumConvertType.CODE);
    }
}
