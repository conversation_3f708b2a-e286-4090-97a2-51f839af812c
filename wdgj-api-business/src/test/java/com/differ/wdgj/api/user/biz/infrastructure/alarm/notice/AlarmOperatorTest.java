package com.differ.wdgj.api.user.biz.infrastructure.alarm.notice;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.AlarmContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import org.junit.Assert;
import org.junit.Test;

/**
 * 报警操作类
 *
 * <AUTHOR>
 * @date 2025/3/24 上午10:45
 */
public class AlarmOperatorTest extends AbstractSpringTest {
    /**
     * 异步报警
     */
    @Test
    public void alarmIntervalTest(){
        AlarmContent content = AlarmContent.build(AlarmIntervalTypeEnum.DEMO, "测试异步报警");
        AlarmOperator.singleton().alarmInterval(AlarmIntervalTypeEnum.DEMO, "", content);
        // 没报错就视为成功
        Assert.assertTrue(true);
    }

    /**
     * 同步报警
     */
    @Test
    public void alarmIntervalSyncTest(){
        AlarmContent content = AlarmContent.build(AlarmIntervalTypeEnum.DEMO, "测试同步报警");
        boolean result = AlarmOperator.singleton().alarmIntervalSync(AlarmIntervalTypeEnum.DEMO, "", content);
        Assert.assertTrue(result);
    }

    /**
     * 同步报警 - 内存去重拦截
     */
    @Test
    public void alarmIntervalSyncIntervalTest(){
        AlarmContent content = AlarmContent.build(AlarmIntervalTypeEnum.DEMO, "测试同步报警");
        boolean result = AlarmOperator.singleton().alarmIntervalSync(AlarmIntervalTypeEnum.DEMO, "", content);
        Assert.assertTrue(result);
        AlarmContent contentTwo = AlarmContent.build(AlarmIntervalTypeEnum.DEMO, "测试同步报警");
        boolean resultWto = AlarmOperator.singleton().alarmIntervalSync(AlarmIntervalTypeEnum.DEMO, "", contentTwo);
        Assert.assertFalse(resultWto);
    }
}
