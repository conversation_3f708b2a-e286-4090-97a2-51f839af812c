package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.auto.AutoLoadAfterShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiShopConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.AutoLoadAfterSaleAllShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import org.junit.Assert;
import org.junit.Test;

/**
 * 自动下载售后单店铺-Redis缓存
 *
 * <AUTHOR>
 * @date 2024/11/27 下午4:44
 */
public class AutoLoadAfterShopCacheTest extends AbstractSpringTest {

    /**
     * 查询
     */
    @Test
    public void getDataTest() {
        AutoLoadAfterSaleAllShopDto autoLoadAfterSaleAllShop = AutoLoadAfterShopCache.singleton().getAndSyncIfAbsent("api2017");
        Assert.assertNotNull(autoLoadAfterSaleAllShop);
    }

}
