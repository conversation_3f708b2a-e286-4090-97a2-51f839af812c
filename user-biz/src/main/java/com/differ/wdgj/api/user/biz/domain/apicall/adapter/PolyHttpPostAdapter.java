package com.differ.wdgj.api.user.biz.domain.apicall.adapter;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

/**
 * 适配器 - 菠萝派请求
 *
 * <AUTHOR>
 * @date 2022-03-09 22:59
 */
public class PolyHttpPostAdapter {

    /**
     * 获取版本号
     *
     * @param context 上下文
     * @return 版本号
     */
    public <RequestBizData extends BasePlatRequestBizData> String getVersion(ApiCallContext context, RequestBizData requestBizData) {
        return ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.API_PublicPolyAPI_Version);
    }

    /**
     * 获取请求业务参数
     *
     * @param context          请求
     * @param requestBizData   请求业务参数
     * @param <RequestBizData> 请求业务参数泛型
     * @return 业务参数
     * @throws Exception 异常
     */
    public <RequestBizData extends BasePlatRequestBizData> String getBizContent(ApiCallContext context, RequestBizData requestBizData) throws Exception {
        return JsonUtils.toJson(requestBizData);
    }

    /**
     * 获取平台值
     *
     * @param context 上下文
     * @return 平台值
     */
    public String getPlatId(ApiCallContext context) {

        // 接口类型
        PolyAPITypeEnum apiType = context.getApiType();

        // 是否测试环境
        boolean test = Boolean.TRUE.equals(SystemAppConfig.get().isTest());

        // 是否敏感接口
        boolean sensitiveApi = PolyAPITypeEnum.BUSINESS_BATCHSEND.equals(apiType) ||
                PolyAPITypeEnum.BUSINESS_BATCHSYNCSTOCK.equals(apiType);

        // 测试环境的敏感接口平台值传笛佛
        if (test && sensitiveApi) {
            return String.valueOf(PolyPlatEnum.BUSINESS_DifferBusiness);
        }

        // 默认返回真实平台值
        return String.valueOf(context.getPlat());
    }

    /**
     * 获取模拟类型
     *
     * @param context 上下文
     * @return 模拟类型
     */
    public String getSimulateType(ApiCallContext context) {

        PolyAPITypeEnum apiType = context.getApiType();

        // 非测试环境不传此参数
        if (!Boolean.TRUE.equals(SystemAppConfig.get().isTest())) {
            return "";
        }

        // 批量发货或批量库存同步
        if (PolyAPITypeEnum.BUSINESS_BATCHSEND.equals(apiType) || PolyAPITypeEnum.BUSINESS_BATCHSYNCSTOCK.equals(apiType)) {
            return "4";
        }

        // 查询配置键
        String versionConfig = ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.Poly_SimulateResponseType);
        if (StringUtils.isEmpty(versionConfig)) {
            return "4";
        }

        return versionConfig;
    }
}
