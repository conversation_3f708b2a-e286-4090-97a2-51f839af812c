package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsrefund;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractRefundGoodsConvertHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;

/**
 * 换货单-退货商品平台级特殊处理
 *
 * <AUTHOR>
 * @date 2024/8/9 下午2:31
 */
public class RefundGoodsPlatSpecialToExchangeHandle extends AbstractRefundGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseRefundGoodInfo> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public RefundGoodsPlatSpecialToExchangeHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类逻辑

    /**
     * 转换商品级信息
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, SourceRefundGoodsItem<BusinessGetExchangeResponseRefundGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        return GoodsConvertHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return String.format("平台【%s】换货单退货商品转换-平台级特殊处理", context.getPlat().getName());
    }
    //endregion
}
