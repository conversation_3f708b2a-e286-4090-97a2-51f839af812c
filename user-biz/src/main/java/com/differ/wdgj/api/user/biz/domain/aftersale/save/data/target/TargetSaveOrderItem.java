package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleOrderHashItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 售后单保存目标对象
 *
 * <AUTHOR>
 * @date 2024/7/17 下午2:58
 */
public class TargetSaveOrderItem {

    /**
     * 处理模式
     */
    private AfterSaleProcessTypeEnum processType;

    /**
     * 售后单业务hashCode
     */
    private AfterSaleOrderHashItem orderHashCode;

    //region 售后单基础数据
    /**
     * 售后单
     */
    private ApiReturnListDO afterSaleOrder;

    /**
     * 售后单密文
     */
    private ApiEncryptTradeDO encryptTrade;

    /**
     * 售后单退货商品列表
     */
    private List<ApiReturnDetailDO> returnGoods;

    /**
     * 售后单退货换货列表
     */
    private List<ApiReturnDetailTwoDO> exchangeGoods;

    /**
     * 需要删除的售后单退货商品Id列表
     */
    private List<Integer> deleteReturnGoodsIds;

    /**
     * 需要删除的售后单换货商品Id列表
     */
    private List<Integer> deleteExchangeGoodsIds;

    /**
     * 售后单日志列表
     */
    private List<ApiReturnLogDO> afterSaleOrderLogs;

    /**
     * 款单信息数据列表
     */
    private List<ApiReturnInfoDO> returnInfos;
    //endregion

    //region 公共方法

    /**
     * 创建对象
     *
     * @param covertOrder   售后单转换目标对象
     * @param orderHashCode 售后单业务hashCode
     * @return 售后单保存目标对象
     */
    public static TargetSaveOrderItem create(TargetCovertOrderItem covertOrder, AfterSaleOrderHashItem orderHashCode) {
        TargetSaveOrderItem targetSaveOrderItem = new TargetSaveOrderItem();
        targetSaveOrderItem.setOrderHashCode(orderHashCode);
        targetSaveOrderItem.setProcessType(covertOrder.getProcessType());
        targetSaveOrderItem.setAfterSaleOrder(covertOrder.getAfterSaleOrder());
        targetSaveOrderItem.setEncryptTrade(covertOrder.getEncryptTrade());
        targetSaveOrderItem.setReturnGoods(covertOrder.getRefundGoods());
        targetSaveOrderItem.setExchangeGoods(covertOrder.getExchangeGoods());
        targetSaveOrderItem.setDeleteReturnGoodsIds(covertOrder.getDeleteReturnGoodsIds());
        targetSaveOrderItem.setDeleteExchangeGoodsIds(covertOrder.getDeleteExchangeGoodsIds());
        targetSaveOrderItem.setAfterSaleOrderLogs(covertOrder.getAfterSaleOrderLogs());
        targetSaveOrderItem.setReturnInfos(covertOrder.getReturnInfos());
        return targetSaveOrderItem;
    }

    /**
     * 获取售后单单号
     *
     * @return 售后单单号
     */
    public String getAfterSaleNo() {
        if (afterSaleOrder != null) {
            return afterSaleOrder.getRefundId();
        }
        return StringUtils.EMPTY;
    }
    //endregion

    //region get/set

    public AfterSaleProcessTypeEnum getProcessType() {
        return processType;
    }

    public void setProcessType(AfterSaleProcessTypeEnum processType) {
        this.processType = processType;
    }

    public AfterSaleOrderHashItem getOrderHashCode() {
        return orderHashCode;
    }

    public void setOrderHashCode(AfterSaleOrderHashItem orderHashCode) {
        this.orderHashCode = orderHashCode;
    }

    public ApiReturnListDO getAfterSaleOrder() {
        return afterSaleOrder;
    }

    public void setAfterSaleOrder(ApiReturnListDO afterSaleOrder) {
        this.afterSaleOrder = afterSaleOrder;
    }

    public ApiEncryptTradeDO getEncryptTrade() {
        return encryptTrade;
    }

    public void setEncryptTrade(ApiEncryptTradeDO encryptTrade) {
        this.encryptTrade = encryptTrade;
    }

    public List<ApiReturnDetailDO> getReturnGoods() {
        return returnGoods;
    }

    public void setReturnGoods(List<ApiReturnDetailDO> returnGoods) {
        this.returnGoods = returnGoods;
    }

    public List<ApiReturnDetailTwoDO> getExchangeGoods() {
        return exchangeGoods;
    }

    public void setExchangeGoods(List<ApiReturnDetailTwoDO> exchangeGoods) {
        this.exchangeGoods = exchangeGoods;
    }

    public List<Integer> getDeleteReturnGoodsIds() {
        return deleteReturnGoodsIds;
    }

    public void setDeleteReturnGoodsIds(List<Integer> deleteReturnGoodsIds) {
        this.deleteReturnGoodsIds = deleteReturnGoodsIds;
    }

    public List<Integer> getDeleteExchangeGoodsIds() {
        return deleteExchangeGoodsIds;
    }

    public void setDeleteExchangeGoodsIds(List<Integer> deleteExchangeGoodsIds) {
        this.deleteExchangeGoodsIds = deleteExchangeGoodsIds;
    }

    public List<ApiReturnLogDO> getAfterSaleOrderLogs() {
        return afterSaleOrderLogs;
    }

    public void setAfterSaleOrderLogs(List<ApiReturnLogDO> afterSaleOrderLogs) {
        this.afterSaleOrderLogs = afterSaleOrderLogs;
    }

    public List<ApiReturnInfoDO> getReturnInfos() {
        return returnInfos;
    }

    public void setReturnInfos(List<ApiReturnInfoDO> returnInfos) {
        this.returnInfos = returnInfos;
    }
    //endregion
}
