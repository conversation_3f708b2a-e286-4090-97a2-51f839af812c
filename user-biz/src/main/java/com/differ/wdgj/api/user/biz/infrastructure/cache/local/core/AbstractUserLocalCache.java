package com.differ.wdgj.api.user.biz.infrastructure.cache.local.core;


import java.util.Objects;

/**
 * 网店管家数据键缓存
 * <AUTHOR>
 * @date 2021/1/13 13:49
 */
public abstract class AbstractUserLocalCache<K, V> extends AbstractLocalCache<AbstractUserLocalCache.UserKey<K>, V>  implements ILocalCache<K, V> {

    /**
     * 获取会员的数据，优先取缓存的有效数据
     *
     * @param key
     * @param wdgjUser
     * @return
     */
    @Override
    public V getData(String wdgjUser, K key) {
        return this.getCacheThenSource(new UserKey<>(wdgjUser, key));
    }

    /**
     * 刷新本地缓存
     * @param wdgjUser
     * @param key
     */
    @Override
    public void refresh(String wdgjUser, K key) {
        this.cache.refresh(new UserKey<>(wdgjUser,key));
    }

    @Override
    protected V loadSource(UserKey<K> key) {
        return loadUserData(key.getWdgjUser(),key.getKey());
    }

    /**
     * @param key
     * @return
     */
    protected abstract V loadUserData(String wdgjUser, K key);

    /**
     * 带会员的内部缓存键对象
     *
     * @param <K>
     */
    public static class UserKey<K> {

        public UserKey(String wdgjUser, K key) {
            this.key = key;
            this.wdgjUser = wdgjUser;
        }

        /**
         * 管家会员
         */
        private String wdgjUser;

        /**
         * 键
         */
        private K key;

        public K getKey() {
            return key;
        }

        public void setKey(K key) {
            this.key = key;
        }

        public String getWdgjUser() {
            return wdgjUser;
        }

        public void setWdgjUser(String wdgjUser) {
            this.wdgjUser = wdgjUser;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            UserKey<?> userKey = (UserKey<?>) o;
            return Objects.equals(key, userKey.key) &&
                    Objects.equals(wdgjUser, userKey.wdgjUser);
        }

        @Override
        public int hashCode() {
            return Objects.hash(key, wdgjUser);
        }
    }
}
