package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 带任务Id的执行器
 *
 * <AUTHOR>
 * @date 2025/3/24 上午10:17
 */
public abstract class AbstractTaskAble implements TaskAble {
    /**
     * 任务唯一标识列表
     */
    private List<String> taskSigns;

    public AbstractTaskAble(String... taskSigns){
        this.taskSigns = Arrays.asList(taskSigns);
    }

    @Override
    public String getTaskId() {
        return StringUtils.join(taskSigns, "-");
    }

    @Override
    public List<String> getTaskSigns() {
        return Collections.emptyList();
    }
}



