package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.goods;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractRefundGoodsConvertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 退货退款单-退货商品基础数据转换插件
 *
 * <AUTHOR>
 * @date 2024/7/18 下午2:16
 */
public class RefundGoodsCovertHandle extends AbstractRefundGoodsConvertHandle<BusinessGetRefundOrderResponseOrderItem, BusinessGetRefundResponseRefundGoodInfo> {
    //region 构造
    /**
     * 构造
     *
     * @param context 上下文
     */
    public RefundGoodsCovertHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 重写基类方法
    /**
     * 转换商品级信息
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    public GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem, SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        ApiTradeGoodsDO apiTradeGoods = goodsItem.getApiTradeGoods();
        BusinessGetRefundOrderResponseOrderItem ployOrder = orderItem.getPloyOrder();
        BusinessGetRefundResponseRefundGoodInfo polyRefundGoods = goodsItem.getPloyRefundGoods();

        // 商品信息赋值
        GoodsConvertHandleResult basicCovertResult = covertBasicReturnGoodsInfo(apiTradeGoods, ployOrder, polyRefundGoods, refundGoods);
        if (basicCovertResult.isFailed() || !basicCovertResult.isSaveGoods()) {
            return basicCovertResult;
        }

        // 兼容转换退货/退款商品数量
        BigDecimal refundGoodsCount = AfterSaleCovertUtils.covertRefundGoodsCount(apiTradeGoods, ployOrder, polyRefundGoods);
        refundGoods.setGoodsCount(refundGoodsCount);
        // 兼容转换退货/退款原因
        String refundReason = AfterSaleCovertUtils.covertRefundReason(ployOrder, polyRefundGoods);
        refundGoods.setRemark(AfterSaleCovertUtils.replaceEmoji(refundReason));

        // 历史主键
        if(CollectionUtils.isNotEmpty(orderItem.getDbOrder().getReturnGoods())){
            ApiReturnDetailDO oldRefundGoods = orderItem.getDbOrder().getReturnGoods().stream().filter(x -> AfterSaleGoodsMatchOperation.isMatchHistoryRefundGoods(refundGoods, x)).findFirst().orElse(null);
            if(oldRefundGoods != null){
                refundGoods.setRecId(oldRefundGoods.getRecId());
            }
        }

        return GoodsConvertHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "退货退款单退货商品基础数据转换";
    }
    //endregion

    //region 私有方法
    /**
     * 退货商品基础信息转换
     *
     * @param apiTradeGoods 原始单商品信息
     * @param ployOrder 菠萝派售后单信息
     * @param polyRefundGoods 菠萝派退货商品信息
     * @param refundGoods 转换结果
     * @return 结果
     */
    private GoodsConvertHandleResult covertBasicReturnGoodsInfo(ApiTradeGoodsDO apiTradeGoods, BusinessGetRefundOrderResponseOrderItem ployOrder, BusinessGetRefundResponseRefundGoodInfo polyRefundGoods, final ApiReturnDetailDO refundGoods) {
        // 商品基础信息赋值
        if (polyRefundGoods != null) {
            // 菠萝派商品级赋值
            refundGoods.setOuterId(StringUtils.defaultIfEmpty(polyRefundGoods.getOutSkuId(), polyRefundGoods.getOuterId()));
            refundGoods.setGoodsTitle(polyRefundGoods.getProductName());
            refundGoods.setSku(polyRefundGoods.getSkuSpec());
            refundGoods.setPlatGoodsId(polyRefundGoods.getPlatProductId());
            refundGoods.setPlatSkuId(polyRefundGoods.getSku());
            refundGoods.setOid(polyRefundGoods.getSubTradeNo());
        } else {
            // 订单级基础信息赋值
            refundGoods.setOuterId(ployOrder.getOuterId());
            refundGoods.setGoodsTitle(ployOrder.getProductName());
            refundGoods.setSku(ployOrder.getSku());
            refundGoods.setPlatGoodsId(ployOrder.getPlatProductId());
            refundGoods.setPlatSkuId(ployOrder.getSku());
            refundGoods.setOid(ployOrder.getSubPlatOrderNo());
        }

        // emoji表情字符处理
        refundGoods.setGoodsTitle(AfterSaleCovertUtils.replaceEmoji(refundGoods.getGoodsTitle()));
        refundGoods.setSku(AfterSaleCovertUtils.replaceEmoji(refundGoods.getSku()));

        // 原始单信息补充，优先使用售后单返回
        if (apiTradeGoods != null) {
            // 商品编码
            if(StringUtils.isEmpty(refundGoods.getOuterId())){
                refundGoods.setOuterId(apiTradeGoods.getTradeGoodsNO());
            }
            // 商品名称
            if(StringUtils.isEmpty(refundGoods.getGoodsTitle())){
                refundGoods.setGoodsTitle(apiTradeGoods.getTradeGoodsName());
            }
            // 商品规格名称
            if(StringUtils.isEmpty(refundGoods.getSku())){
                refundGoods.setSku(apiTradeGoods.getTradeGoodsSpec());
            }
            // 平台商品id
            if(StringUtils.isEmpty(refundGoods.getPlatGoodsId())){
                refundGoods.setPlatGoodsId(apiTradeGoods.getPlatGoodsID());
            }
            // 平台规格id
            if(StringUtils.isEmpty(refundGoods.getPlatSkuId())){
                refundGoods.setPlatSkuId(apiTradeGoods.getPlatSkuID());
            }
        }

        return GoodsConvertHandleResult.success();
    }
    //endregion
}
