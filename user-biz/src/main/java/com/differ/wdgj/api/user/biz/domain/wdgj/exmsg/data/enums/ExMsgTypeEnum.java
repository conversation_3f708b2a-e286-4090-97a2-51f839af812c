package com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.data.enums;

import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 异常消息类型
 *
 * <AUTHOR>
 * @since 2024-03-18 9:54
 */
public enum ExMsgTypeEnum implements NameEnum, ValueEnum {

    /**
     * 发货同步失败
     */
    SEND_ORDER_FAILED("发货同步失败", 0, 0),

    /**
     * 库存同步失败
     */
    SYNC_STOCK_FAILED("库存同步失败", 1, 1),

    /**
     * 商品自动匹配失败
     */
    GOODS_AUTO_MATCH_FAILED("商品自动匹配失败", 2, 11),

    /**
     * 淘宝前N有礼活动失败
     */
    TB_TOP_N_ACTIVITY_FAILED("淘宝前N有礼活动失败", 3, 2),

    /**
     * 自动递交失败
     */
    AUTO_POST_ORDER_FAILED("自动递交失败", 4, 14),

    /**
     * 店铺授权失效
     */
    SHOP_AUTH_INVALID("店铺授权失效", 5, 16),

    ;

    ExMsgTypeEnum(String name, Integer value, Integer wdgjValue) {
        this.name = name;
        this.value = value;
        this.wdgjValue = wdgjValue;
    }

    private final String name;

    private final Integer value;

    private final Integer wdgjValue;


    /**
     * 获取枚举名称
     *
     * @return
     */
    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 获取枚举值
     *
     * @return
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    public Integer getWdgjValue() {
        return wdgjValue;
    }

}
