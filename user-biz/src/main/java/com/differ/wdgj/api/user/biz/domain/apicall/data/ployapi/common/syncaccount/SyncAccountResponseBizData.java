package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.common.syncaccount;

import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;

import java.util.List;

/**
 * 平台账号同步出参
 *
 * <AUTHOR>
 * @date 2024/8/27 下午4:24
 */
public class SyncAccountResponseBizData extends BasePlatResponseBizData {
    /**
     * 冲突店铺
     */
    private List<SyncAccountResponseRepeatShop> repeatShops;

    public List<SyncAccountResponseRepeatShop> getRepeatShops() {
        return repeatShops;
    }

    public void setRepeatShops(List<SyncAccountResponseRepeatShop> repeatShops) {
        this.repeatShops = repeatShops;
    }
}
