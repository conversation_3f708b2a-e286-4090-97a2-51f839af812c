package com.differ.wdgj.api.component.wait.data;

import com.differ.wdgj.api.component.wait.AbstractWaitEntity;

/**
 * @Description 排队任务实体
 * <AUTHOR>
 * @Date 2021/9/10 14:35
 */
public class CommonWaitEntity extends AbstractWaitEntity {

    public CommonWaitEntity(String waitUniqueId, String data) {
        super(waitUniqueId, data);
    }

    @Override
    public String getDataString() {
        return (String)data;
    }
}
