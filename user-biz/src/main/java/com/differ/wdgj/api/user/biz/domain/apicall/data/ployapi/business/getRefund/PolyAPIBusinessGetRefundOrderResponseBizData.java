package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund;

import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;

import java.util.List;

/**
 * 下载退货退款单返回结果
 * {@link PolyAPITypeEnum} BUSINESS_GETREFUND
 *
 * <AUTHOR>
 * @date 2024/8/26 下午1:33
 */
public class PolyAPIBusinessGetRefundOrderResponseBizData extends BasePlatResponseBizData {
    //region 公有参数

    /**
     * 是否有下一页
     */
    private boolean isHasNextPage;

    /**
     * 获取下一页订单所需的token值
     */
    private String nextToken;

    /**
     * 订单总数量
     */
    private int totalCount;

    /**
     * 退货退款单集合
     */
    private List<BusinessGetRefundOrderResponseOrderItem> refunds;

    /**
     * 换货单集合
     */
    private List<BusinessGetExchangeOrderResponseOrderItem> exchanges;

    //endregion

    //region get/set
    public boolean isHasNextPage() {
        return isHasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        isHasNextPage = hasNextPage;
    }

    public String getNextToken() {
        return nextToken;
    }

    public void setNextToken(String nextToken) {
        this.nextToken = nextToken;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<BusinessGetRefundOrderResponseOrderItem> getRefunds() {
        return refunds;
    }

    public void setRefunds(List<BusinessGetRefundOrderResponseOrderItem> refunds) {
        this.refunds = refunds;
    }

    public List<BusinessGetExchangeOrderResponseOrderItem> getExchanges() {
        return exchanges;
    }

    public void setExchanges(List<BusinessGetExchangeOrderResponseOrderItem> exchanges) {
        this.exchanges = exchanges;
    }
//endregion
}
