package com.differ.wdgj.api.component.util.tools;

import ch.qos.logback.core.encoder.ByteArrayUtil;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Arrays;

/**
 * aes算法加解密，运算模式：ecb，填充模式：PKCS7。
 * </p>注：ecb运算模式不需要iv
 *
 * <AUTHOR>
 * @date 2021/2/3 11:20
 */
public class AESCommonECB {
    static {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    //region 常量

    private static final String ENCRYPT_ALG = "AES";

    private static final String CIPHER_MODE = "AES/ECB/PKCS7Padding";

    private static final int SECRET_KEY_SIZE = 16;

    private Cipher cipherEncrypt;

    private Cipher cipherDecrypt;

    //endregion

    /**
     * 初始化
     *
     * @param key 密钥
     */
    public void init(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("key参数错误");
        }
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            byte[] keyBuffer = Arrays.copyOf(key.getBytes(StandardCharsets.UTF_8), SECRET_KEY_SIZE);

            cipherEncrypt = Cipher.getInstance(CIPHER_MODE);
            cipherEncrypt.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(keyBuffer, ENCRYPT_ALG));

            cipherDecrypt = Cipher.getInstance(CIPHER_MODE);
            cipherDecrypt.init(Cipher.DECRYPT_MODE, new SecretKeySpec(keyBuffer, ENCRYPT_ALG));
        } catch (Exception e) {
            throw new RuntimeException("AESRijndael初始化失败", e);
        }
    }

    /**
     * 加密
     *
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public String encrypt(String text) {
        if (StringUtils.isEmpty(text)) {
            throw new RuntimeException("text不能为空");
        }

        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            return bytesToHexString(cipherEncrypt.doFinal(text.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败：" + text, e);
        }
    }

    /**
     * 解密
     *
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public String decrypt(String text) {
        if (StringUtils.isEmpty(text)) {
            throw new RuntimeException("text不能为空");
        }

        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            byte[] decodeBytes = ByteArrayUtil.hexStringToByteArray(text);
            byte[] realBytes = cipherDecrypt.doFinal(decodeBytes);
            return new String(realBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败:" + text, e);
        }
    }

    //region 其他方法
    /**
     * 字节转16进制
     *
     * @param bytes 字节数组
     * @return java.lang.String
     */
    public static String bytesToHexString(byte[] bytes) {
        if (bytes != null) {
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02X", b));
            }
            return sb.toString();
        }
        return StringUtils.EMPTY;
    }
    //endregion
}
