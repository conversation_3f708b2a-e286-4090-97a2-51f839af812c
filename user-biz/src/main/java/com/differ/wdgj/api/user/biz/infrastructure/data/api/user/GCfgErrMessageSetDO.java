package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import javax.persistence.*;

@Table(name = "g_cfg_errmessageset")
public class GCfgErrMessageSetDO {
    @Id
    @Column(name = "RecID")
    private Integer recid;

    /**
     * 0API发货失败  1API库存同步失败   2API下载订单失败  3API账户余额不足 4管家助理发送短信失败  5 管家助理发送邮件失败  6管家账户余额不住
     */
    @Column(name = "ErrType")
    private Integer errtype;

    @Column(name = "ToStaff1")
    private String tostaff1;

    @Column(name = "ToStaff2")
    private String tostaff2;

    /**
     *  通知时间 
     */
    @Column(name = "iHours")
    private Integer ihours;

    /**
     *  消息弹窗 
     */
    @Column(name = "bHintMessage")
    private Boolean bhintmessage;

    @Column(name = "bEnable")
    private Integer benable;

    /**
     * @return RecID
     */
    public Integer getRecid() {
        return recid;
    }

    /**
     * @param recid
     */
    public void setRecid(Integer recid) {
        this.recid = recid;
    }

    /**
     * 获取0API发货失败  1API库存同步失败   2API下载订单失败  3API账户余额不足 4管家助理发送短信失败  5 管家助理发送邮件失败  6管家账户余额不住
     *
     * @return ErrType - 0API发货失败  1API库存同步失败   2API下载订单失败  3API账户余额不足 4管家助理发送短信失败  5 管家助理发送邮件失败  6管家账户余额不住
     */
    public Integer getErrtype() {
        return errtype;
    }

    /**
     * 设置0API发货失败  1API库存同步失败   2API下载订单失败  3API账户余额不足 4管家助理发送短信失败  5 管家助理发送邮件失败  6管家账户余额不住
     *
     * @param errtype 0API发货失败  1API库存同步失败   2API下载订单失败  3API账户余额不足 4管家助理发送短信失败  5 管家助理发送邮件失败  6管家账户余额不住
     */
    public void setErrtype(Integer errtype) {
        this.errtype = errtype;
    }

    /**
     * @return ToStaff1
     */
    public String getTostaff1() {
        return tostaff1;
    }

    /**
     * @param tostaff1
     */
    public void setTostaff1(String tostaff1) {
        this.tostaff1 = tostaff1;
    }

    /**
     * @return ToStaff2
     */
    public String getTostaff2() {
        return tostaff2;
    }

    /**
     * @param tostaff2
     */
    public void setTostaff2(String tostaff2) {
        this.tostaff2 = tostaff2;
    }

    /**
     * 获取 通知时间 
     *
     * @return iHours -  通知时间 
     */
    public Integer getIhours() {
        return ihours;
    }

    /**
     * 设置 通知时间 
     *
     * @param ihours  通知时间 
     */
    public void setIhours(Integer ihours) {
        this.ihours = ihours;
    }

    /**
     * 获取 消息弹窗 
     *
     * @return bHintMessage -  消息弹窗 
     */
    public Boolean getBhintmessage() {
        return bhintmessage;
    }

    /**
     * 设置 消息弹窗 
     *
     * @param bhintmessage  消息弹窗 
     */
    public void setBhintmessage(Boolean bhintmessage) {
        this.bhintmessage = bhintmessage;
    }

    /**
     * @return bEnable
     */
    public Integer getBenable() {
        return benable;
    }

    /**
     * @param benable
     */
    public void setBenable(Integer benable) {
        this.benable = benable;
    }
}