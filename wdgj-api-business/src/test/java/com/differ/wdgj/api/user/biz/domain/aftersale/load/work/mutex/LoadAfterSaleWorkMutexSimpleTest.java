package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * LoadAfterSaleWorkMutex 简单测试
 * 用于验证基本功能是否正常
 *
 * <AUTHOR>
 * @date 2024/12/19 下午3:30
 */
public class LoadAfterSaleWorkMutexSimpleTest {

    /**
     * 简单测试 - 验证消息推送不互斥场景
     */
    @Test
    public void testMessageNotificationWithAfterSaleNo() {
        // 创建测试对象
        LoadAfterSaleWorkMutex mutex = new LoadAfterSaleWorkMutex();
        WorkDataOperate mockDataOperate = Mockito.mock(WorkDataOperate.class);
        
        // 准备测试数据
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(AfterSaleLoadTypeEnum.AFTER_SALE_NO);
        
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, args);
        CreateResult expectedResult = CreateResult.successResult("test-task-id");
        
        // 模拟依赖行为
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = mutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "test-task-id", result.getTaskId());
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 简单测试 - 验证其他场景走父类逻辑
     */
    @Test
    public void testOtherScenarios() {
        // 创建测试对象
        LoadAfterSaleWorkMutex mutex = new LoadAfterSaleWorkMutex();
        WorkDataOperate mockDataOperate = Mockito.mock(WorkDataOperate.class);
        
        // 准备测试数据
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            TriggerTypeEnum.HAND, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(AfterSaleLoadTypeEnum.SHOP_LOAD);
        
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, args);
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        
        // 模拟父类逻辑：不存在未完成任务，创建新任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = mutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 简单测试 - 验证存在未完成任务时返回null
     */
    @Test
    public void testExistsUnCompleteTask() {
        // 创建测试对象
        LoadAfterSaleWorkMutex mutex = new LoadAfterSaleWorkMutex();
        WorkDataOperate mockDataOperate = Mockito.mock(WorkDataOperate.class);
        
        // 准备测试数据
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            TriggerTypeEnum.AUTO, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(AfterSaleLoadTypeEnum.TIME_RANGE);
        
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, args);
        
        // 模拟存在未完成任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
        
        // 执行测试
        CreateResult result = mutex.create(workData, mockDataOperate);
        
        // 验证结果 - 由于配置的超时时间为0（默认值），不会检查超时任务，直接返回null
        Assert.assertNull("当存在未完成任务且超时时间为0时，应该返回null", result);
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        // 由于超时时间为0，不应该检查超时任务
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
        Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
    }
}
