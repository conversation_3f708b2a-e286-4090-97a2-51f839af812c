package com.differ.wdgj.api.user.biz.domain.stock.data.task;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 活动商品时间实体
 * 后续迁移至对应定时任务
 * <AUTHOR>
 * @date 2024-03-20 18:55
 */
public class ActivityGoodsTimeValueObject {
    /**
     * 店铺Id
     */
    private int outShopId;

    /**
     * 匹配表Id
     */
    private int apiSysMatchId;

    /**
     * 平台商品id
     */
    private String platGoodsId;

    /**
     * 活动结束时间
     */
    private LocalDateTime activityEndTime;

    //region get/set
    public int getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(int outShopId) {
        this.outShopId = outShopId;
    }

    public int getApiSysMatchId() {
        return apiSysMatchId;
    }

    public void setApiSysMatchId(int apiSysMatchId) {
        this.apiSysMatchId = apiSysMatchId;
    }

    public String getPlatGoodsId() {
        return platGoodsId;
    }

    public void setPlatGoodsId(String platGoodsId) {
        this.platGoodsId = platGoodsId;
    }

    public LocalDateTime getActivityEndTime() {
        return activityEndTime;
    }

    public void setActivityEndTime(LocalDateTime activityEndTime) {
        this.activityEndTime = activityEndTime;
    }
    //endregion

    //region equals/hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ActivityGoodsTimeValueObject that = (ActivityGoodsTimeValueObject) o;
        return outShopId == that.outShopId && Objects.equals(platGoodsId, that.platGoodsId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(outShopId, platGoodsId);
    }
    //endregion
}
