package com.differ.wdgj.api.user.biz.domain.order.common.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 订单商品级发货同步状态
 *
 * <AUTHOR>
 * @date 2025/6/19 13:39
 */
public enum OrderGoodsSendStatusEnum  implements ValueEnum {
    WAIT_SYNC(0, "待同步"),
    PARTIAL_SUCCESS(1, "部分发货成功"),
    SUCCESS(2, "发货成功"),
    Failed(3, "发货失败"),
    ;

    //region 常量
    /**
     * 值
     */
    private final int value;

    /**
     * 描述
     */
    private final String description;

    //endregion

    //region 构造
    OrderGoodsSendStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }
    //endregion

    //region 公共方法
    @Override
    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    //endregion



    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static OrderGoodsSendStatusEnum create(int value) {
        return EnumConvertCacheUtil.convert(value, OrderGoodsSendStatusEnum.class, EnumConvertType.VALUE);
    }
}
