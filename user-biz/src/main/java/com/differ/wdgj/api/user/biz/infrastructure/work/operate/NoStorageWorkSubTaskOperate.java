package com.differ.wdgj.api.user.biz.infrastructure.work.operate;

import com.differ.wdgj.api.component.util.tools.Md5Utils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.*;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import org.apache.commons.lang3.RandomUtils;

import java.util.List;

/**
 * 无数据存储的工作子任务操作
 *
 * <AUTHOR>
 * @date 2024/8/2 14:44
 */
public class NoStorageWorkSubTaskOperate<S extends SubTask> implements WorkSubTaskOperate<S> {
    /**
     * 创建工作任务
     *
     * @param workData 工作数据
     * @return 任务ID
     */
    @Override
    public CreateResult create(WorkData<?> workData) {
        long taskId = generateTaskId(workData);
        return CreateResult.successResult(String.valueOf(taskId));
    }

    /**
     * 是否存在未完成的工作任务
     *
     * @param workData 任务数据
     * @return true:存在
     */
    @Override
    public boolean existsUnComplete(WorkData<?> workData){
        return false;
    }

    /**
     * 是否存在未完成的工作任务
     *
     * @param workData      任务数据
     * @param timeoutPeriod 超时时间
     * @return true:存在
     */
    @Override
    public boolean existsUnRunTimeout(WorkData<?> workData, int timeoutPeriod) {
        return false;
    }

    /**
     * 查询待检测的未完成任务
     *
     * @param member 会员名
     * @param workEnum 工作类型
     * @return 未完成任务id列表
     */
    @Override
    public List<String> listToCheckWork(String member, WorkEnum workEnum) {
        return null;
    }

    /**
     * 删除任务
     *
     * @param member  会员名
     * @param taskId     任务ID
     * @param workResult 执行结果
     */
    @Override
    public void workComplete(String member, String taskId, WorkResult workResult) {

    }

    /**
     * 清理到期的任务
     *
     * @param member   会员名
     * @param workEnum 任务类型
     */
    @Override
    public int cleanExpired(String member, WorkEnum workEnum) {
        return 0;
    }

    /**
     * 获取工作任务的更新操作器
     *
     * @param context 上下文
     * @return 更新操作器
     */
    @Override
    public WorkDetailOperate<S> getDetailOperate(DataOperateContext context) {
        return new NoStorageWorkDetailOperate<>();
    }

    /**
     * 查询工作任务
     *
     * @param memberId 会员名
     * @param taskId 任务ID
     * @return 工作任务
     */
    @Override
    public WorkData<?> getWork(String memberId, String taskId) {
        return null;
    }

    /**
     * 生成任务ID
     *
     * @param workData 工作数据
     * @return 任务ID
     */
    protected long generateTaskId(WorkData<?> workData) {
        return Md5Utils.encryptHash(String.format("%s_%s_%s_%s_%d_%d", workData.getMemberName(), workData.getWorkType(), workData.getTriggerType(), workData.getTriggerType(), System.currentTimeMillis(), RandomUtils.nextInt(0, 99999)));
    }


    /**
     * 无数据存储的操作类
     * @param <S>
     */
    static class NoStorageWorkDetailOperate<S extends SubTask> implements WorkDetailOperate<S> {
        public NoStorageWorkDetailOperate() {

        }

        @Override
        public void setProgress(int value) {

        }

        @Override
        public void increProgress(int value) {

        }

        @Override
        public void initToExecute(String member, String taskId) {

        }

        @Override
        public void complete(String member, String taskId, WorkResult workResult) {

        }

        /**
         * 更新子任务和动态状态数据
         *
         * @param subTask
         */
        @Override
        public void updateSubTask(S subTask) {

        }

        @Override
        public void updateSubTask(S subTask, int increProgressValue) {

        }

        /**
         * 获取子任务的动态状态数据
         *
         * @param subTypeKey
         * @return
         */
        @Override
        public S getSubTaskBreakData(String subTypeKey, Class<? extends S> clazz) {
            return null;
        }


    }
}
