package com.differ.wdgj.api.user.biz.domain.apicall.plugins.aftersale;

import com.differ.wdgj.api.user.biz.domain.apicall.AbstractApiCall;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getExchange.PolyAPIBusinessGetExchangeOrderRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getExchange.PolyAPIBusinessGetExchangeOrderResponseBizData;

/**
 * 下载h换货单
 *
 * <AUTHOR>
 * @date 2024/9/25 下午6:04
 */
public class GetExchangeOrderApiCall extends AbstractApiCall<PolyAPIBusinessGetExchangeOrderRequestBizData, PolyAPIBusinessGetExchangeOrderResponseBizData> {
    /**
     * 获取接口类型
     *
     * @return 接口类型
     */
    @Override
    protected PolyAPITypeEnum getApiType() {
        return PolyAPITypeEnum.BUSINESS_GETEXCHANGE;
    }
}
