<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.CfgShopListExtMapper" >

    <!-- 根据店铺Id获取下载订单配置 -->
    <select id="getDownloadOrderShopConfig" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiShopConfigDto">
        select shopId,apiShopId,ApiDowmloadOrderConfig as configValue
        from g_cfg_shoplistext
        where shopId = #{shopId}
    </select>

    <!-- 根据店铺Id获取售后配置 -->
    <select id="getAfterSaleShopConfig" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiShopConfigDto">
        select shopId,apiShopId,ApiAfterSaleConfig as configValue
        from g_cfg_shoplistext
        where shopId = #{shopId}
    </select>

    <!-- 根据店铺Id获取售后配置 -->
    <select id="getApShopIdByShopId" resultType="java.lang.Integer">
        select apiShopId
        from g_cfg_shoplistext
        where shopId = #{shopId}
    </select>

    <!-- 获取会员级所有店铺 -->
    <select id="getAllShopDto" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiAllShopDto$ShopInfo">
        select a.shopId,a.apiShopId
        from g_cfg_shopListExt a
        join g_cfg_shopList b on a.shopId = b.shopId
        where b.setApi = 1
    </select>

    <!-- 更新下载订单配置 -->
    <update id="updateDownloadOrderShopConfig" parameterType="map">
        UPDATE g_cfg_shoplistext
        SET ApiDowmloadOrderConfig = #{configValue}
        WHERE shopId = #{shopId}
    </update>

    <!-- 更新售后配置 -->
    <update id="updateAfterSaleShopConfig" parameterType="map">
        UPDATE g_cfg_shoplistext
        SET ApiAfterSaleConfig = #{configValue}
        WHERE shopId = #{shopId}
    </update>

    <!-- 更新 apiShopId -->
    <update id="updateApShopIdByShopId" parameterType="map">
        UPDATE g_cfg_shoplistext
        SET apiShopId = #{apiShopId}
        WHERE shopId = #{shopId} and (apiShopId is null OR apiShopId = 0)
    </update>

</mapper>