--
-- User: chuff
-- Date: 2021/6/17 14:53
--

-- 限流键
local cacheKey = KEYS[1]
-- 限流时间周期
local ttl = tonumber(ARGV[1])
-- 限流上限
local capacity = tonumber(ARGV[2])
-- 当前请求通过数量
local requested = tonumber(ARGV[3])
-- 获取当前流量大小
local current_count = tonumber(redis.call('get', cacheKey))

if current_count == nil then
    redis.call("setex", cacheKey, ttl, 0)
    current_count = 0
end

-- 是否超出限流阈值
if current_count + requested > capacity then
    -- 拒绝
    return 0
else
    -- 通过，累计当前访问量
    redis.call("incrby", cacheKey, requested)
    return requested
end