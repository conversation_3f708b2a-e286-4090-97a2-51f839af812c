package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.strategy.sharding;


import com.differ.wdgj.api.component.util.hash.HashUtil;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.SimpleDistributeJobConsistentHashLocalCache;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.SimpleDistributeJobData;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 任务分片策略
 * <p>
 * 一致性hash的分片策略, 每个实际的服务器节点会扩充到n倍的虚拟几点，来保证均衡性
 *
 * <AUTHOR> wangz
 * @date 2021/11/15 14:39
 */
@Component
public class ConsistentHashJobShardingStrategy implements IJobShardingStrategy {

    // region 变量

    /**
     * 内存缓存
     */
    private SimpleDistributeJobConsistentHashLocalCache localCache = SimpleDistributeJobConsistentHashLocalCache.singleton();

    // endregion

    // region 接口实现

    /**
     * 获取当前分片任务数据集合
     *
     * @param allInstances    所有实例
     * @param currentInstance 当前实例
     * @param allData         所有任务数据
     * @param <T>             任务数据类型
     * @return 当前分片任务数据集合
     */
    @Override
    public <T extends SimpleDistributeJobData> List<T> getShardingData(List<String> allInstances, String currentInstance, List<T> allData) {

        // 当前分片任务数据集合
        List<T> taskData = new ArrayList<>();

        // 初始化虚拟节点
        TreeMap<Long, String> virtualNodes = this.localCache.getAllNodes(allInstances);
        for (T dataItem : allData) {

            // 任务数据唯一标识进行hash
            long hash = HashUtil.murMurHash(dataItem.uniqueSign());

            // 获取大于该hash值的第一个节点
            String matchedNode;
            Map.Entry<Long, String> ceilingEntry = virtualNodes.ceilingEntry(hash);
            if (ceilingEntry == null) {
                matchedNode = virtualNodes.get(virtualNodes.firstKey());
            } else {
                matchedNode = ceilingEntry.getValue();
            }

            // 判断匹配到的实例是否为当前实例
            if (currentInstance.equals(matchedNode)) {
                taskData.add(dataItem);
            }
        }

        return taskData;
    }

    // endregion
}
