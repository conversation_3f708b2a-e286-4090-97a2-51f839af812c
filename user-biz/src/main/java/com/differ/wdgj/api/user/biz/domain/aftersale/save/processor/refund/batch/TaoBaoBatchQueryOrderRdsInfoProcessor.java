package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.batch;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPerBatchProcessOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.RdsTbTradeDomain;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 前置批量处理 - 淘宝售后单批量查询订单推送库信息
 *
 * <AUTHOR>
 * @date 2025/6/25 16:50
 */
public class TaoBaoBatchQueryOrderRdsInfoProcessor extends AbstractPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public TaoBaoBatchQueryOrderRdsInfoProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult perBatchQueryProcess(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {
        // 获取仅退款售后单
        List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> refundPayOrders = orderItems.stream().filter(x -> ApiAfterSaleTypeEnum.REFUND_PAY == x.getBizType().getApiAfterSaleOrderType()).collect(Collectors.toList());
        // 获取平台单号
        List<Long> platOrderNos = refundPayOrders.stream().map(x -> NumberUtils.toLong(x.getPlatOrderNo(), 0L)).distinct().collect(Collectors.toList());
        // 查询对应订单Rds发货信息
        List<RdsTbTradeSendInfo> orderSendInfos = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(platOrderNos)){
            RdsTbTradeDomain rdsTbTradeDomain = new RdsTbTradeDomain(context.getMemberName());
            orderSendInfos = rdsTbTradeDomain.getOrderSendInfos(platOrderNos);
        }

        // 关联售后单和订单Rds发货信息
        if (CollectionUtils.isNotEmpty(orderSendInfos)){

        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "淘宝售后单批量查询订单推送库信息";
    }
}
