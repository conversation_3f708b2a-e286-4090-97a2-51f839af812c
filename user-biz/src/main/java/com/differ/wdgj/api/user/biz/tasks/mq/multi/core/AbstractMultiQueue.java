package com.differ.wdgj.api.user.biz.tasks.mq.multi.core;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.type.GenericTypeUtil;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.ApiMultiMQ;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.DelayGradeEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueue;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueueContext;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub.SubQueueEnum;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 多节点队列消息系统抽象类
 * 1、通过多队列注解初始化子队列链表
 * 2、支持当前子队列是否可用，需要子队列实现可用检测的逻辑
 * 3、支持子队列的限流控制，限流后转到下个子队列
 *
 * <AUTHOR>
 * @date 2024/3/14 14:30
 */
public abstract class AbstractMultiQueue<T> implements MultiQueueHandler<T>, ApplicationRunner {

    protected Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 子队列头节点
     */
    private List<SubQueue<T>> queueChain;

    /**
     * 注解属性
     */
    private ApiMultiMQ annotation;

    /**
     * 业务数据类型
     */
    private Class<T> dataClazz;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        this.init();
    }

    /**
     * 初始化，注意：有JMQ队列时，初始化会被执行2次
     */
    @Override
    public void init() {
        // 根据注解生成子队列
        this.annotation = this.getClass().getAnnotation(ApiMultiMQ.class);
        SubQueueEnum[] subQueues = annotation.subQueues();
        if (subQueues == null) {
            return;
        }
        // 初始化队列链
        Class<T> dataClazz = getDataClazz();
        List<SubQueue<T>> subs = new ArrayList<>();
        for (SubQueueEnum subQueue : subQueues) {
            if (Arrays.stream(subQueues).filter(s -> s.equals(subQueue)).count() > 1) {
                throw new RuntimeException("队列类型不能重复");
            }

            SubQueueContext context = new SubQueueContext(subQueue, annotation);
            // 过滤：既不支持发送也不支持消费的
            if(!context.receiveEnable() && !context.sendEnable()){
                continue;
            }

            SubQueue<T> queue = subQueue.createSubQueue(annotation, dataClazz);
            if(queue != null) {
                subs.add(queue);
            }
        }

        if (CollectionUtils.isNotEmpty(subs)) {
            initSubs(annotation, subs);
        }
    }

    /**
     * 发送消息
     *
     * @param message
     */
    @Override
    public void sendMulti(T message) {
        this.sendMulti(message, null);
    }

    /**
     * 发送消息
     *
     * @param message
     * @param header  业务头信息
     */
    @Override
    public void sendMulti(T message, QueueHeader header) {
        if (CollectionUtils.isEmpty(queueChain)) {
            throw new RuntimeException("子队列集合为空");
        }

        int size = queueChain.size();
        for (int i = 0; i < size; i++) {
            try {
                SubQueue<T> subQueue = queueChain.get(i);
                if (!subQueue.currentEnable()) {
                    // 当前队列不可用时，跳过
                    continue;
                }

                if (annotation.limitEnable() && subQueue.limited()) {
                    // 当前队列限流时，跳过
                    continue;
                }
                if (subQueue.sendToCurrentQueue(message, getInitQueueHeader(header))) {
                    // 发送成功，返回，否则走下个节点的队列发送
                    return;
                }
            } catch (Throwable t) {
                if (i >= size - 1) {
                    log.error(String.format("多队列发送失败:%s", JsonUtils.toJson(message)), t);
                } else {
                    log.error(String.format("子队列发送失败，转下节点队列发送:%s", JsonUtils.toJson(message)), t);
                }
            }
        }
    }

    /**
     * 初始化子队列
     *
     * @param subQueues
     */
    private void initSubs(ApiMultiMQ annotation, List<SubQueue<T>> subQueues) {
        queueChain = new ArrayList<>();
        for (SubQueue<T> subQueue : subQueues) {
            if (subQueue == null) {
                continue;
            }

            SubQueueContext context = subQueue.getContext();

            // 添加到队列链
            if (context.sendEnable()) {
                queueChain.add(subQueue);
            }

            // 注册消息处理器
            if (context.receiveEnable()) {
                // 注册函数：用于延迟重试时的取延迟时间
                subQueue.initDelayFun(this::getDelayGrade);
                // 注册消息处理函数
                subQueue.initMessageListener(this.getDataClazz(), this::receiveMulti);
            }
        }
    }

    /**
     * 获取泛型的业务数据类型
     *
     * @return
     */
    @Override
    public Class<T> getDataClazz() {
        if (dataClazz == null) {
            Class clazz = AopUtils.getTargetClass(this);
            dataClazz = (Class<T>) GenericTypeUtil.getGenericDataType(clazz, 0);
        }
        return dataClazz;
    }

    /**
     * 获取初始化的头
     *
     * @param customHeader
     * @return
     */
    private QueueHeader getInitQueueHeader(QueueHeader customHeader) {
        QueueHeader header = new QueueHeader(customHeader);
        DelayGradeEnum delayGrade = getDelayGrade(header);
        if (delayGrade!= null && delayGrade != DelayGradeEnum.GRADE_0) {
            header.setDelayGrade(delayGrade);
        }
        return header;
    }

    /**
     * 延迟消息的延迟级别，可重写来支持动态设定，默认使用注解配置的值
     *
     * @param header 队列头信息上下文
     * @return 延迟等级
     */
    protected DelayGradeEnum getDelayGrade(QueueHeader header) {
        return annotation.delayTime();
    }

    /**
     * 根据类型获取对应的子队列，用于特殊业务处理，需要由业务控制指定子队列处理逻辑的场景
     *
     * @param subQueueEnum
     * @return
     */
    protected SubQueue<T> getSubQueue(SubQueueEnum subQueueEnum) {
        for (SubQueue<T> subQueue : queueChain) {
            if (subQueue.getContext().getSubQueueEnum().equals(subQueueEnum)) {
                return subQueue;
            }
        }
        return null;
    }

}
