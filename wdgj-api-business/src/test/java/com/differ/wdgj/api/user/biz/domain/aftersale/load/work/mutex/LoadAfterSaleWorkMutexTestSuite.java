package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * LoadAfterSaleWorkMutex 测试套件
 * 统一运行所有相关的单元测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午4:00
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    LoadAfterSaleWorkMutexTest.class,                    // 第一批：基础功能测试
    LoadAfterSaleWorkMutexTimeoutTest.class,             // 第二批：超时任务检测测试
    LoadAfterSaleWorkMutexComprehensiveTest.class        // 第三批：综合场景和边界条件测试
})
public class LoadAfterSaleWorkMutexTestSuite {
    // 测试套件类，无需实现内容
}
