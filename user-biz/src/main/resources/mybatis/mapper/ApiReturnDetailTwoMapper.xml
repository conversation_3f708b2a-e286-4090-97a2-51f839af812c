<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnDetailTwoMapper">

    <!--售后单表数据映射-->
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO">
        <id property="recId" column="RecID" jdbcType="INTEGER"/>
        <result property="billId" column="BillID" jdbcType="INTEGER"/>
        <result property="goodsId" column="GoodsID" jdbcType="INTEGER"/>
        <result property="specId" column="SpecID" jdbcType="INTEGER"/>
        <result property="outerId" column="Outer_id" jdbcType="VARCHAR"/>
        <result property="goodsCount" column="GoodsCount" jdbcType="DECIMAL"/>
        <result property="price" column="Price" jdbcType="DECIMAL"/>
        <result property="remark" column="Remark" jdbcType="VARCHAR"/>
        <result property="bFit" column="bFit" jdbcType="TINYINT"/>
        <result property="sku" column="SKU" jdbcType="VARCHAR"/>
        <result property="goodsTitle" column="GoodsTitle" jdbcType="VARCHAR"/>
        <result property="jitPONO" column="JitPONO" jdbcType="VARCHAR"/>
        <result property="platGoodsId" column="PlatGoodsID" jdbcType="VARCHAR"/>
        <result property="platSkuId" column="PlatSkuID" jdbcType="VARCHAR"/>
        <result property="oid" column="Oid" jdbcType="VARCHAR"/>
        <result property="extendedField" column="ExtendedField" jdbcType="VARCHAR"/>
    </resultMap>
    <!--售后单His表数据映射-->
    <resultMap id="BaseHisResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO">
        <id property="recId" column="RecID" jdbcType="INTEGER"/>
        <result property="billId" column="BillID" jdbcType="INTEGER"/>
        <result property="goodsId" column="GoodsID" jdbcType="INTEGER"/>
        <result property="specId" column="SpecID" jdbcType="INTEGER"/>
        <result property="outerId" column="Outer_id" jdbcType="VARCHAR"/>
        <result property="goodsCount" column="GoodsCount" jdbcType="DECIMAL"/>
        <result property="price" column="Price" jdbcType="DECIMAL"/>
        <result property="remark" column="Remark" jdbcType="VARCHAR"/>
        <result property="bFit" column="bFit" jdbcType="TINYINT"/>
        <result property="sku" column="SKU" jdbcType="VARCHAR"/>
        <result property="goodsTitle" column="GoodsTitle" jdbcType="VARCHAR"/>
        <result property="jitPONO" column="JitPONO" jdbcType="VARCHAR"/>
        <result property="platGoodsId" column="PlatGoodsID" jdbcType="VARCHAR"/>
        <result property="platSkuId" column="PlatSkuID" jdbcType="VARCHAR"/>
        <result property="oid" column="Oid" jdbcType="VARCHAR"/>
        <result property="extendedField" column="ExtendedField" jdbcType="VARCHAR"/>
    </resultMap>

    <!--字段-->
    <sql id="BaseColumnList">
        recId, billId, goodsId, specId, bFit, outer_id, goodsCount,
        price, remark, platGoodsId, platSkuId, goodsTitle, sku,
        jitPONO
    </sql>

    <!-- 根据售后单id批量查询售后单换货商品 -->
    <select id="selectByBillIds" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from g_api_return_detail2
        where BillId in
        <foreach collection="billIds" item="Id" open="(" close=")" separator=",">
            #{Id}
        </foreach>
    </select>

    <!-- 根据售后单id批量查询售后单归档换货商品 -->
    <select id="selectHisByBillIds" resultMap="BaseHisResultMap">
        select *
        from g_api_return_detail2_his
        where BillId in
        <foreach collection="billIds" item="Id" open="(" close=")" separator=",">
            #{Id}
        </foreach>
    </select>

    <!-- 单个新增 -->
    <insert id="insert" parameterType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO">
        <!-- useGeneratedKeys="true" keyProperty="recId" -->
        INSERT INTO g_api_return_detail2
        (billId, goodsId, specId, bFit, outer_id, goodsCount, price, remark, platGoodsId, platSkuId, goodsTitle,
        sku, jitPONO)
        VALUES
        (#{billId}, #{goodsId}, #{specId}, #{bFit}, #{outerId}, #{goodsCount}, #{price}, #{remark}, #{platGoodsId},
        #{platSkuId}, #{goodsTitle}, #{sku}, #{jitPONO})
    </insert>

    <!-- 批量新增 -->
    <insert id="batchInsert" parameterType="java.util.List">
        <!-- useGeneratedKeys="true" keyProperty="recId" -->
        INSERT INTO g_api_return_detail2
        (billId, goodsId, specId, bFit, outer_id, goodsCount, price, remark, platGoodsId, platSkuId, goodsTitle,
        sku, jitPONO)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.billId}, #{item.goodsId}, #{item.specId}, #{item.bFit}, #{item.outerId}, #{item.goodsCount},
            #{item.price}, #{item.remark}, #{item.platGoodsId}, #{item.platSkuId}, #{item.goodsTitle}, #{item.sku},
            #{item.jitPONO})
        </foreach>
    </insert>

    <!-- 批量新增/更新 -->
    <insert id="batchInsertOrUpdate" parameterType="java.util.List">
        INSERT INTO g_api_return_detail2 (RecID, BillID, GoodsID, SpecID, Outer_id, GoodsCount, Price, Remark, bFit, SKU, GoodsTitle, JitPONO, PlatGoodsID, PlatSkuID)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.recId}, #{item.billId}, #{item.goodsId}, #{item.specId}, #{item.outerId}, #{item.goodsCount}, #{item.price}, #{item.remark}, #{item.bFit}, #{item.sku}, #{item.goodsTitle}, #{item.jitPONO}, #{item.platGoodsId}, #{item.platSkuId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        GoodsID = VALUES(GoodsID),
        SpecID = VALUES(SpecID),
        Outer_id = VALUES(Outer_id),
        GoodsCount = VALUES(GoodsCount),
        Price = VALUES(Price),
        Remark = VALUES(Remark),
        bFit = VALUES(bFit),
        SKU = VALUES(SKU),
        GoodsTitle = VALUES(GoodsTitle),
        JitPONO = VALUES(JitPONO),
        PlatGoodsID = VALUES(PlatGoodsID),
        PlatSkuID = VALUES(PlatSkuID)
    </insert>

    <!-- 单个更新 -->
    <update id="update" parameterType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO">
        UPDATE g_api_return_detail2
        SET goodsId = #{goodsId}, specId = #{specId}, bFit = #{bFit}, outer_id = #{outerId},
        goodsCount = #{goodsCount}, price = #{price}, remark = #{remark}, platGoodsId = #{platGoodsId}, platSkuId = #{platSkuId},
        goodsTitle = #{goodsTitle}, sku = #{sku}, jitPONO = #{jitPONO}
        WHERE recId = #{recId}
    </update>

    <!-- 批量更新 -->
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE g_api_return_detail2
            SET goodsId = #{item.goodsId}, specId = #{item.specId}, bFit = #{item.bFit}, outer_id = #{item.outerId},
            goodsCount = #{item.goodsCount}, price = #{item.price}, remark = #{item.remark}, platGoodsId = #{item.platGoodsId}, platSkuId = #{item.platSkuId},
            goodsTitle = #{item.goodsTitle}, sku = #{item.sku}, jitPONO = #{item.jitPONO}
            WHERE recId = #{item.recId}
        </foreach>
    </update>

    <!-- 根据主键批量删除售后单换货商品 -->
    <delete id="deleteByRecIds" parameterType="java.lang.Integer">
        delete from g_api_return_detail2
        where recId in
        <foreach collection="recIds" item="recId" open="(" separator="," close=")">
            #{recId}
        </foreach>
    </delete>

</mapper>