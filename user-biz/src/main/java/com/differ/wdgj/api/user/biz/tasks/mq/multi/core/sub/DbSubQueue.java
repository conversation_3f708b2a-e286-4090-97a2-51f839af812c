package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.sub;

import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.DelayGradeEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.MultiMessage;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueResult;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库子队列
 * 1、初始化时，注册触发入口到定时任务
 * 2、定时任务触发时，查询数据库队列表，并处理
 * 3、处理完后，删除数据库队列，
 * 4、处理失败时，记录日志，并重试
 *
 * <AUTHOR>
 * @date 2024/3/14 10:56
 */
public abstract class DbSubQueue<T> extends AbstractPullSubQueue<T> {

    /**
     * 拉取分页页面的起始位置
     */
    protected int offset = 0;
    /**
     * 拉取的页大小
     */
    protected int pageSize = 5;

    public DbSubQueue(SubQueueContext subQueueContext) {
        super(subQueueContext);
    }

    @Override
    protected boolean sendToSubQueue(String message, QueueHeader header) {
        return this.addMessage(message, header);
    }

    /**
     * 注册后，由定时任务触发，拉取消息
     */
    @Override
    protected void triggerTask() {
        List<MultiMessage> pullMessages = pullMessages();
        if (CollectionUtils.isEmpty(pullMessages)) {
            return;
        }
        List<MultiMessage> ackMessages = new ArrayList<>();
        List<MultiMessage> retryMessages = new ArrayList<>();
        for (MultiMessage pullMessage : pullMessages) {
            QueueResult result = this.execConsume(pullMessage.getData(), pullMessage.getHeader());
            if (result == QueueResult.ACK) {
                ackMessages.add(pullMessage);
            }else if(result == QueueResult.RETRY){
                retryMessages.add(pullMessage);
            }
        }

        if (CollectionUtils.isNotEmpty(ackMessages)) {
            // 正常处理后删除消息
            this.deleteMessage(ackMessages);
        }

        if (CollectionUtils.isNotEmpty(retryMessages)) {
            for (MultiMessage retryMessage : retryMessages) {
                QueueHeader header = retryMessage.getHeader();
                if(this.funGetDelay !=null) {
                    DelayGradeEnum delay = this.funGetDelay.exec(header);
                    header.setDelayGrade(delay);
                }
                this.updateRetryMessage(retryMessage.getData(), header);
            }
        }
    }

    /**
     * 添加消息
     *
     * @param message
     * @param header
     * @return
     */
    protected abstract boolean addMessage(String message, QueueHeader header);

    /**
     * 更新重试消息
     * @param message
     * @param header
     * @return
     */
    protected abstract boolean updateRetryMessage(String message, QueueHeader header);

    /**
     * 拉取消息
     *
     * @return
     */
    protected List<MultiMessage> pullMessages(){
        return null;
    }

    /**
     * 删除消息
     *
     * @param ackMessages
     */
    protected abstract void deleteMessage(List<MultiMessage> ackMessages);

    /**
     * 消费者线程池
     *
     * @return
     */
    @Override
    protected TaskEnum getConsumerPool() {
        return TaskEnum.API_MULTI_DB_CONSUMER;
    }
}
