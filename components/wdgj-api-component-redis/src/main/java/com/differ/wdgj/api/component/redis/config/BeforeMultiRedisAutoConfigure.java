package com.differ.wdgj.api.component.redis.config;

import com.differ.wdgj.api.component.redis.converter.CacheKeyToCacheTypeConverter;
import com.differ.wdgj.api.component.redis.converter.DefaultCacheKeyToCacheTypeConverter;
import com.differ.wdgj.api.component.redis.creator.DefaultMultiRedisPropertiesCreator;
import com.differ.wdgj.api.component.redis.creator.MultiRedisPropertiesCreator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自动装配redis多库
 * <AUTHOR>
 * @date 2020/12/14 11:37
 */
@Configuration
@EnableConfigurationProperties(MultiRedisProperties.class)
public class BeforeMultiRedisAutoConfigure {

    @Bean
    @ConditionalOnMissingBean
    public MultiRedisPropertiesCreator redisPropertiesCreator(MultiRedisProperties defaultProperties){
        return new DefaultMultiRedisPropertiesCreator(defaultProperties);
    }

    @Bean
    @ConditionalOnMissingBean
    public CacheKeyToCacheTypeConverter cacheKeyToCacheTypeConverter(){
        return new DefaultCacheKeyToCacheTypeConverter();
    }
}
