package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderDataResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle.IAfterSaleNotice;

import java.util.List;

/**
 * 外部消息通知售后单
 *
 * <AUTHOR>
 * @date 2024-07-04 19:56
 */
public class TargetOutNoticeItem {

    //region 属性
    /**
     * 外部消息通知类别列表
     */
    private List<IAfterSaleNotice> erpNoticeTypes;
    /**
     * 保存db结果
     */
    private List<SaveOrderDataResult> saveDbResults;
    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param outNotices    外部消息通知类别列表
     * @param saveDbResults 保存db结果
     */
    public TargetOutNoticeItem(List<IAfterSaleNotice> outNotices, List<SaveOrderDataResult> saveDbResults) {
        this.erpNoticeTypes = outNotices;
        this.saveDbResults = saveDbResults;
    }
    //endregion

    //region get/set

    public List<IAfterSaleNotice> getErpNoticeTypes() {
        return erpNoticeTypes;
    }

    public void setErpNoticeTypes(List<IAfterSaleNotice> erpNoticeTypes) {
        this.erpNoticeTypes = erpNoticeTypes;
    }

    public List<SaveOrderDataResult> getSaveDbResults() {
        return saveDbResults;
    }

    public void setSaveDbResults(List<SaveOrderDataResult> saveDbResults) {
        this.saveDbResults = saveDbResults;
    }

    //endregion
}
