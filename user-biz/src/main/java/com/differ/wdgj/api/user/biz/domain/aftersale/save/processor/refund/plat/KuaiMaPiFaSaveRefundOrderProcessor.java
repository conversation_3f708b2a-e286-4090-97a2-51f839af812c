package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;

/**
 * 快马批发 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2025/6/3 14:08
 */
public class KuaiMaPiFaSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public KuaiMaPiFaSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 是否匹配原始单货品(通常情况下子类只需要考虑商品级)
     *
     * @param apiTradeGoods 原始单货品
     * @param refundOrder   售后单
     * @param refundGoods   售后单货品
     * @return 结果
     */
    @Override
    protected boolean isMatchApiTradeGoods(ApiTradeGoodsDO apiTradeGoods, BusinessGetRefundOrderResponseOrderItem refundOrder, BusinessGetRefundResponseRefundGoodInfo refundGoods) {
        // 菠萝派返回商品信息与原始单商品对不上。
        // 退货退款单对应整个订单，因此退货退款商品直接取所有原始单商品
        return true;
    }
}
