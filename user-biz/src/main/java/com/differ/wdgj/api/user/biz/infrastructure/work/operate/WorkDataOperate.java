package com.differ.wdgj.api.user.biz.infrastructure.work.operate;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;

import java.util.List;

/**
 * 工作任务的操作
 *
 * <AUTHOR>
 * @date 2024/6/24 10:41
 */
public interface WorkDataOperate {
    /**
     * 创建工作任务
     *
     * @param workData 任务数据
     * @return 任务ID
     */
    CreateResult create(WorkData<?> workData);

    /**
     * 是否存在未完成的工作任务
     *
     * @param workData 任务数据
     * @return true:存在
     */
    boolean existsUnComplete(WorkData<?> workData);

    /**
     * 是否存在未完成的工作任务
     *
     * @param workData      任务数据
     * @param timeoutPeriod 超时时间
     * @return true:存在
     */
    boolean existsRunTimeout(WorkData<?> workData, int timeoutPeriod);

    /**
     * 查询待检测的未完成任务
     *
     * @param member   会员名
     * @param workEnum 任务类型
     * @return 未完成任务列表
     */
    List<String> listToCheckWork(String member, WorkEnum workEnum);

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    void workComplete(String member, String taskId, WorkResult workResult);

    /**
     * 清理到期的任务
     *
     * @param member   会员名
     * @param workEnum 任务类型
     */
    int cleanExpired(String member, WorkEnum workEnum);
}
