package com.differ.wdgj.api.user.biz.infrastructure.condition;

import org.springframework.context.annotation.Conditional;

import java.lang.annotation.*;

/**
 * bean条件：根据站点是否启用
 *
 * <AUTHOR>
 * @date 2021-09-18 14:00
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Conditional(OnSiteCondition.class)
public @interface ConditionalOnSite {

    /**
     * 启用的站点
     *
     * @return 启用的站点集合
     */
    String[] sites() default {};
}
