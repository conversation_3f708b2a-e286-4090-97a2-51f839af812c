package com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.heart;

import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberRunnable;

/**
 * 心跳抽象类
 *
 * <AUTHOR>
 * @date 2024/7/9 10:28
 */
public abstract class AbstractWorkHeart extends MemberRunnable implements WorkHeart {
    //region 常量
    /**
     * 工作任务操作缓存
     */
    private final TaskEnum taskEnum;
    //endregion

    //region 构造
    protected AbstractWorkHeart(String jackNo, Object argv, TaskEnum taskEnum) {
        super(jackNo, argv);
        this.taskEnum = taskEnum;
    }
    //endregion

    //region 实现接口方法
    /**
     * 开启心跳
     */
    @Override
    public void openBeat() {
        // 开启心跳
        HeartBeatCenter.singleton().register(this);
        // 并同步一次心跳
        this.run();
    }

    /**
     * 关闭心跳
     */
    @Override
    public void closeBeat() {
        HeartBeatCenter.singleton().remove(this);
    }
    //endregion

    //region 公共方法
    /**
     * 刷新心跳
     */
    public void refreshHeart() {
        this.taskEnum.execute(this);
    }
    //endregion

}
