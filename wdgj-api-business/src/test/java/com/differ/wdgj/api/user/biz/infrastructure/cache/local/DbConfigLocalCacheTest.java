package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.DbConfigLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.DbConfigLocalCacheDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyTypeEnum;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 内存缓存：配置键
 *
 * <AUTHOR>
 * @date 2024-04-09 19:04
 */
@Ignore
public class DbConfigLocalCacheTest extends AbstractSpringTest {

    /**
     * 异步获取配置键
     */
    @Test
    public void asyncGetDataTest() throws Exception{
        DbConfigLocalCacheDto dbConfigLocalCacheDto = DbConfigLocalCache.singleton().asyncGetData(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, ConfigKeyTypeEnum.WDGJ);
        Thread.sleep(10000);
        DbConfigLocalCacheDto dbConfigLocalCacheDto1 = DbConfigLocalCache.singleton().asyncGetData(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, ConfigKeyTypeEnum.WDGJ);
        DbConfigLocalCacheDto dbConfigLocalCacheDto2 = DbConfigLocalCache.singleton().asyncGetData(ConfigKeyEnum.IsAction_SyncStock_PloyCommonErrorCodeProcess, ConfigKeyTypeEnum.WDGJ);
    }
}
