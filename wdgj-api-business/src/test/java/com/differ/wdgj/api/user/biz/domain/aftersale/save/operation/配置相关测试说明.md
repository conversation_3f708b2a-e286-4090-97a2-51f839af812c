# RefundTypeCovertOperation 配置相关测试说明

## 概述

根据修改后的逻辑，重新生成了针对 `bDistinguishShippedAndUnshipped` 和 `bPolyRefundPayRegardedRefundPaySend` 两个配置的完整单元测试。

## 修改后的逻辑分析

### 第一层逻辑：bPolyRefundPayRegardedRefundPaySend（第121-123行）
```java
AfterSalesConfigContent afterSalesConfigPlatFeature = context.getAfterSalesConfigPlatFeature(shopType.getCode());
if (afterSalesConfigPlatFeature != null && afterSalesConfigPlatFeature.getBPolyRefundPayRegardedRefundPaySend()) {
    return ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
}
```
- **作用范围**: `JH_01` 和 `JH_03` 退款类型
- **优先级**: 最高，在 `polyRefundTypeCovert` 方法中处理
- **效果**: 直接返回 `REFUND_PAY_SEND`，跳过后续所有逻辑

### 第二层逻辑：bDistinguishShippedAndUnshipped（第53-66行）
```java
if (apiAfterSaleType == ApiAfterSaleTypeEnum.REFUND_PAY) {
    AfterSalesConfigContent afterSalesConfigPlatFeature = context.getAfterSalesConfigPlatFeature(shopType.getCode());
    if (afterSalesConfigPlatFeature != null && afterSalesConfigPlatFeature.getBDistinguishShippedAndUnshipped()) {
        RefundPayOrderSendStatusEnum refundPayOrderSendStatusEnum = checkIsOrderSend(ployOrder, dbOrder.getApiTrade(), dbOrder.getApiTradeGoodsList());
        switch (refundPayOrderSendStatusEnum) {
            case SEND:
                apiAfterSaleType = ApiAfterSaleTypeEnum.REFUND_PAY_SEND;
                break;
            case NOT_SEND:
                apiAfterSaleType = ApiAfterSaleTypeEnum.REFUND_PAY_NOT_SEND;
                break;
        }
    }
}
```
- **作用范围**: 基础转换结果为 `REFUND_PAY` 的所有情况
- **优先级**: 较低，在主方法 `covertApiAfterSaleType` 中处理
- **效果**: 根据发货状态进一步转换为 `REFUND_PAY_SEND` 或 `REFUND_PAY_NOT_SEND`

## 新增测试方法

### 1. bPolyRefundPayRegardedRefundPaySend 相关测试

#### `testCovertApiAfterSaleType_PolyRefundPayRegardedSend_JH03_ShouldReturnRefundPaySend()`
- **测试场景**: `bPolyRefundPayRegardedRefundPaySend=true` + `JH_03`
- **预期结果**: `REFUND_PAY_SEND`
- **验证逻辑**: 第121-123行的直接返回逻辑

#### `testCovertApiAfterSaleType_PolyRefundPayRegardedSend_JH01_ShouldReturnRefundPaySend()`
- **测试场景**: `bPolyRefundPayRegardedRefundPaySend=true` + `JH_01`
- **预期结果**: `REFUND_PAY_SEND`
- **验证逻辑**: 第121-123行的直接返回逻辑

#### `testCovertApiAfterSaleType_PolyRefundPayRegardedSend_Disabled_ShouldUseDefaultLogic()`
- **测试场景**: `bPolyRefundPayRegardedRefundPaySend=false` + `JH_03`
- **预期结果**: `REFUND_PAY`
- **验证逻辑**: 配置关闭时使用默认逻辑

### 2. bDistinguishShippedAndUnshipped 相关测试

#### `testCovertApiAfterSaleType_DistinguishShippedEnabled_RefundPayWithSendStatus()`
- **测试场景**: `bDistinguishShippedAndUnshipped=true` + 已发货 + `JH_03`
- **配置组合**: `bPolyRefundPayRegardedRefundPaySend=false`（确保不走第一层逻辑）
- **预期结果**: `REFUND_PAY_SEND`
- **验证逻辑**: 第53-66行的发货状态转换逻辑

#### `testCovertApiAfterSaleType_DistinguishShippedEnabled_RefundPayNotSend()`
- **测试场景**: `bDistinguishShippedAndUnshipped=true` + 未发货 + `JH_03`
- **配置组合**: `bPolyRefundPayRegardedRefundPaySend=false`（确保不走第一层逻辑）
- **预期结果**: `REFUND_PAY_NOT_SEND`
- **验证逻辑**: 第53-66行的发货状态转换逻辑

#### `testCovertApiAfterSaleType_DistinguishShippedDisabled_NoSendStatusConversion()`
- **测试场景**: `bDistinguishShippedAndUnshipped=false` + 已发货 + `JH_03`
- **预期结果**: `REFUND_PAY`
- **验证逻辑**: 配置关闭时不进行发货状态转换

### 3. 配置优先级和组合测试

#### `testCovertApiAfterSaleType_BothConfigsEnabled_PolyRefundPayRegardedTakesPriority()`
- **测试场景**: 两个配置都启用 + 未发货 + `JH_03`
- **预期结果**: `REFUND_PAY_SEND`
- **验证逻辑**: `bPolyRefundPayRegardedRefundPaySend` 优先级更高

#### `testCovertApiAfterSaleType_ConfigNull_ShouldUseDefaultLogic()`
- **测试场景**: 配置对象为 `null` + `JH_03`
- **预期结果**: `REFUND_PAY`
- **验证逻辑**: 配置为空时的容错处理

#### `testCovertApiAfterSaleType_NonRefundPayType_NotAffectedByConfigs()`
- **测试场景**: 所有配置启用 + `JH_04`（退货退款单）
- **预期结果**: `REFUND`
- **验证逻辑**: 非退款类型不受配置影响

## 测试数据构造

### 配置对象构造
```java
AfterSalesConfigContent configContent = new AfterSalesConfigContent();
configContent.setBDistinguishShippedAndUnshipped(true/false);
configContent.setBPolyRefundPayRegardedRefundPaySend(true/false);
```

### 发货状态数据构造
- **已发货**: `createDbOrderWithSendStatus(true)` - `synStatus=4`, `bSend=2`
- **未发货**: `createDbOrderWithSendStatus(false)` - `synStatus=1`, `bSend=1`

## 测试覆盖矩阵

| 配置1 | 配置2 | 退款类型 | 发货状态 | 预期结果 | 测试方法 |
|-------|-------|---------|---------|---------|---------|
| bPolyRefundPayRegardedRefundPaySend=true | - | JH_03 | - | REFUND_PAY_SEND | ✅ 已覆盖 |
| bPolyRefundPayRegardedRefundPaySend=true | - | JH_01 | - | REFUND_PAY_SEND | ✅ 已覆盖 |
| bPolyRefundPayRegardedRefundPaySend=false | bDistinguishShippedAndUnshipped=true | JH_03 | 已发货 | REFUND_PAY_SEND | ✅ 已覆盖 |
| bPolyRefundPayRegardedRefundPaySend=false | bDistinguishShippedAndUnshipped=true | JH_03 | 未发货 | REFUND_PAY_NOT_SEND | ✅ 已覆盖 |
| bPolyRefundPayRegardedRefundPaySend=false | bDistinguishShippedAndUnshipped=false | JH_03 | - | REFUND_PAY | ✅ 已覆盖 |
| bPolyRefundPayRegardedRefundPaySend=true | bDistinguishShippedAndUnshipped=true | JH_03 | 未发货 | REFUND_PAY_SEND | ✅ 已覆盖（优先级测试）|
| 配置为null | - | JH_03 | - | REFUND_PAY | ✅ 已覆盖 |
| 所有配置启用 | - | JH_04 | - | REFUND | ✅ 已覆盖 |

## 代码逻辑验证

### 第一层逻辑验证（polyRefundTypeCovert方法）
- ✅ **第121行**: `getBPolyRefundPayRegardedRefundPaySend()` 方法调用
- ✅ **第122行**: 直接返回 `REFUND_PAY_SEND`
- ✅ **优先级**: 在 `JH_01` 和 `JH_03` 处理中优先执行

### 第二层逻辑验证（covertApiAfterSaleType方法）
- ✅ **第55行**: `getBDistinguishShippedAndUnshipped()` 方法调用
- ✅ **第56行**: `checkIsOrderSend()` 方法调用
- ✅ **第58-64行**: 根据发货状态的switch逻辑

### 配置获取验证
- ✅ **配置获取**: `context.getAfterSalesConfigPlatFeature(shopType.getCode())`
- ✅ **空值处理**: 配置为null时的容错逻辑
- ✅ **方法名修正**: 使用 `getBDistinguishShippedAndUnshipped()` 而不是 `isBDistinguishShippedAndUnshipped()`

## 运行测试

### 运行新增的配置相关测试
```bash
# 运行所有配置相关测试
mvn test -Dtest=RefundTypeCovertOperationTest -Dtest.methods="*PolyRefundPayRegarded*,*DistinguishShipped*,*BothConfigs*,*ConfigNull*"

# 运行完整测试类
mvn test -Dtest=RefundTypeCovertOperationTest
```

## 测试价值

### 1. **配置逻辑完整性**
- 验证两个配置的独立功能
- 验证配置组合时的优先级
- 验证配置关闭时的默认行为

### 2. **业务规则准确性**
- 确保 `JH_01` 和 `JH_03` 在特定配置下的正确转换
- 验证发货状态检查逻辑的正确性
- 确保非退款类型不受配置影响

### 3. **边界条件覆盖**
- 配置为null的容错处理
- 配置组合的优先级验证
- 不同发货状态的处理验证

### 4. **回归测试保障**
- 为配置逻辑变更提供回归测试
- 确保新增配置不影响现有功能
- 验证配置的向后兼容性

## 注意事项

1. **配置优先级**: `bPolyRefundPayRegardedRefundPaySend` 优先级高于 `bDistinguishShippedAndUnshipped`
2. **方法名变更**: 使用 `getBDistinguishShippedAndUnshipped()` 而不是 `isBDistinguishShippedAndUnshipped()`
3. **测试隔离**: 每个测试独立设置配置，避免相互影响
4. **发货状态模拟**: 使用 `createDbOrderWithSendStatus()` 方法模拟不同的发货状态

这些测试确保了配置驱动的业务逻辑的正确性和稳定性，为后续的功能扩展和维护提供了可靠的保障。
