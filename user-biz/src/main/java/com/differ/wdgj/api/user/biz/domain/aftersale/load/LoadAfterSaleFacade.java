package com.differ.wdgj.api.user.biz.domain.aftersale.load;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.ExecLoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadAfterSaleParam;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out.LoadResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.WorkFacade;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.*;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.tasks.job.queue.core.AbstractQueueJob;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.AddResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.aftersale.AfterSaleLoadQueueJob;
import com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.aftersale.TbAfterSaleLoadQueueJob;
import com.differ.wdgj.api.user.biz.tasks.job.queue.plugins.aftersale.data.AfterSaleLoadQueueJobData;
import org.apache.commons.lang3.StringUtils;

/**
 * 下载售后单 - 外部调用
 *
 * <AUTHOR>
 * @date 2024/9/12 下午3:34
 */
public class LoadAfterSaleFacade {
    //region 常量
    /**
     * 任务类别
     */
    private static final WorkEnum workType = WorkEnum.LOAD_AFTER_SALE;
    //endregion

    //region 公共方法

    /**
     * 同步下载售后单
     *
     * @param param 售后单下载参数
     * @return 下载结果
     */
    public LoadResult createTaskAndExec(LoadAfterSaleParam param) {
        WorkFacade facade = new WorkFacade();
        String taskId = StringUtils.EMPTY;
        try {
            // 1、创建任务数据
            WorkContext workContext = WorkContext.of(param.getMember(), workType, param.getShopId(), param.getTriggerType(), param.getCreator());
            WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, param.getLoadArgs());
            CreateResult createResult = facade.createWork(workData);
            if (!createResult.success()) {
                return LoadResult.failedResult(createResult.formatFrontShow());
            }

            // 2、发起下载
            taskId = createResult.getTaskId();
            WorkResult workResult = facade.execWork(workData, createResult.getTaskId(), false);
            if (!workResult.success()) {
                return LoadResult.failedResult(workResult.formatFrontShow());
            }

            return LoadResult.successResult();
        } catch (Exception e) {
            return exceptionResult(param.getMember(), taskId, e);
        }
    }

    /**
     * 创建排队下载售后单
     *
     * @param param 售后单下载参数
     * @return 发起排队结果
     */
    public LoadResult createQueueTask(LoadAfterSaleParam param) {
        WorkFacade facade = new WorkFacade();
        String taskId = StringUtils.EMPTY;
        try {
            // 1、创建排队任务数据
            WorkContext workContext = WorkContext.of(param.getMember(), workType, param.getShopId(), param.getTriggerType(), param.getCreator());
            WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, param.getLoadArgs());
            CreateResult createResult = facade.createWork(workData);
            if (!createResult.success()) {
                return LoadResult.failedResult(createResult.formatFrontShow());
            }

            // 2、发起排队下载
            AfterSaleLoadQueueJobData jobData = new AfterSaleLoadQueueJobData();
            jobData.setMemberName(param.getMember());
            jobData.setShopId(param.getShopId());
            jobData.setOperatorName(param.getCreator());
            jobData.setTriggerType(param.getTriggerType());
            jobData.setLoadArgs(param.getLoadArgs());
            jobData.setTaskId(createResult.getTaskId());
            AbstractQueueJob<AfterSaleLoadQueueJobData> queueJob = getQueueJob(param.getMember(), param.getPlat());
            AddResult addResult = queueJob.addTask(jobData);
            if (!addResult.isSuccess()) {
                // 完成工作任务数据
                facade.workComplete(param.getMember(), createResult.getTaskId(), workType, new FailWorkResult(addResult.getMessage()));
                // 排队失败
                return LoadResult.failedResult(addResult.getMessage());
            }

            return LoadResult.successResult();
        } catch (Exception e) {
            return exceptionResult(param.getMember(), taskId, e);
        }
    }

    /**
     * 执行售后单任务
     *
     * @param param 售后单下载参数
     * @return 执行结果
     */
    public LoadResult execTask(ExecLoadAfterSaleParam param) {
        WorkFacade facade = new WorkFacade();
        try {
            // 1、构建任务基础信息
            WorkContext workContext = WorkContext.of(param.getMember(), workType, param.getShopId(), param.getTriggerType(), param.getCreator());
            WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, param.getLoadArgs());
            String taskId = param.getTaskId();

            // 2、执行任务
            WorkResult workResult = facade.execWork(workData, taskId, false);
            if (!workResult.success()) {
                return LoadResult.failedResult(workResult.formatFrontShow());
            }
            return LoadResult.successResult();
        } catch (Exception e) {
            return exceptionResult(param.getMember(), param.getTaskId(), e);
        }
    }
    //endregion

    //region 私有方法

    /**
     * 异常错误处理
     *
     * @param member 会员名
     * @param taskId 任务id
     * @param e      异常信息
     * @return 异常结果
     */
    private LoadResult exceptionResult(String member, String taskId, Exception e) {
        WorkFacade facade = new WorkFacade();
        FailWorkResult failWorkResult = new FailWorkResult(e);
        if (StringUtils.isNotEmpty(taskId)) {
            facade.workComplete(member, taskId, workType, failWorkResult);
        }
        return LoadResult.failedResult(failWorkResult.formatFrontShow());
    }

    /**
     * 获取排队定时任务
     *
     * @param member 会员名
     * @param plat   平台
     * @return 排队定时任务
     */
    private AbstractQueueJob<AfterSaleLoadQueueJobData> getQueueJob(String member, PolyPlatEnum plat) {
        switch (plat) {
            case BUSINESS_Taobao:
                return TbAfterSaleLoadQueueJob.singleton();
            default:
                return AfterSaleLoadQueueJob.singleton();
        }
    }
    //endregion
}
