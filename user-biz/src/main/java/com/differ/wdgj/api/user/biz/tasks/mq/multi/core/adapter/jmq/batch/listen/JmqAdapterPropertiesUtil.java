package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq.batch.listen;

import com.differ.jackyun.framework.component.jmq.core.properties.ComposePropertiesResolver;
import com.differ.jackyun.framework.component.jmq.core.properties.JMQConnection;
import com.differ.jackyun.framework.component.jmq.core.properties.JMQProperties;
import com.differ.jackyun.framework.component.jmq.core.properties.JMQQueue;
import com.differ.jackyun.framework.component.jmq.rabbit.constant.RabbitConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * JMQ的code配置拉取工具
 *
 * <AUTHOR>
 * @date 2024/6/4 16:46
 */
public class JmqAdapterPropertiesUtil {

    /**
     * 所有队列的code
     */
    private static List<String> allCodes = new ArrayList<>();

    /**
     * 拉取所有队列的code
     * @return
     */
    private static List<String> getAllQueueCodes() {
        List<String> codes = new ArrayList<>();
        ApiDefaultRabbitPropertiesPull pull = new ApiDefaultRabbitPropertiesPull();
        String property = pull.pullProperties();
        if (StringUtils.isBlank(property)){
            return null;
        }
        ComposePropertiesResolver resolver = new ComposePropertiesResolver();
        JMQProperties<JMQQueue, JMQConnection> jmqProperties = resolver.resolverProperties(RabbitConstant.RABBIT_TYPE, property );
        for (JMQQueue queue : jmqProperties.getQueues()) {
            codes.add(queue.getQueueCode());
        }
        return codes;
    }

    /**
     *  获取所有队列的code
     * @return
     */
    public static List<String> getAllCodes() {
        if(CollectionUtils.isEmpty(allCodes)){
            allCodes = getAllQueueCodes();
        }
        return allCodes;
    }

    /**
     *  判断code是否在队列的配置中
     * @param code
     * @return
     */
    public static boolean configContains(String code){
        List<String> codes = getAllCodes();
        if(CollectionUtils.isEmpty(allCodes)){
            return false;
        }
        return codes.contains(code);
    }
}
