package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor;

import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.RdsTbTradeSendInfo;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.trade.TbTradeJdpResponseDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * RdsTbOrderConvertor 单元测试
 * 专门测试 convertTbTradeSendInfo 方法
 *
 * <AUTHOR>
 * @date 2024/12/19 下午11:00
 */
public class RdsTbOrderConvertorTest {

    private RdsTbOrderConvertor convertor;

    @Before
    public void setUp() {
        convertor = new RdsTbOrderConvertor();
    }

    //region convertTbTradeSendInfo 方法测试

    /**
     * 测试正常情况：有发货时间的订单转换
     */
    @Test
    public void testConvertTbTradeSendInfo_WithConsignTime_ShouldReturnCorrectSendInfo() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        TbTradeJdpResponseDto.Order order1 = createOrderWithConsignTime(123456L, "2024-12-19 14:30:45");
        TbTradeJdpResponseDto.Order order2 = createOrderWithConsignTime(789012L, "2024-12-19 15:30:45");
        
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order1, order2);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回2个发货信息", 2, result.size());
        
        // 验证第一个发货信息
        RdsTbTradeSendInfo sendInfo1 = result.get(0);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(123456L), sendInfo1.getOid());
        Assert.assertEquals("发货时间应该正确", "2024-12-19 14:30:45", sendInfo1.getConsignTime());
        
        // 验证第二个发货信息
        RdsTbTradeSendInfo sendInfo2 = result.get(1);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(789012L), sendInfo2.getOid());
        Assert.assertEquals("发货时间应该正确", "2024-12-19 15:30:45", sendInfo2.getConsignTime());
    }

    /**
     * 测试订单没有发货时间的情况
     */
    @Test
    public void testConvertTbTradeSendInfo_WithoutConsignTime_ShouldUseTradeConsignTime() {
        // 准备测试数据：订单没有发货时间，但交易有发货时间
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        TbTradeJdpResponseDto.Order order1 = createOrderWithConsignTime(123456L, null);
        TbTradeJdpResponseDto.Order order2 = createOrderWithConsignTime(789012L, "");
        
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order1, order2);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回2个发货信息", 2, result.size());
        
        // 验证第一个发货信息（使用交易发货时间）
        RdsTbTradeSendInfo sendInfo1 = result.get(0);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(123456L), sendInfo1.getOid());
        Assert.assertEquals("发货时间应该使用交易发货时间", "2024-12-19 14:30:45", sendInfo1.getConsignTime());
        
        // 验证第二个发货信息（使用交易发货时间）
        RdsTbTradeSendInfo sendInfo2 = result.get(1);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(789012L), sendInfo2.getOid());
        Assert.assertEquals("发货时间应该使用交易发货时间", "2024-12-19 14:30:45", sendInfo2.getConsignTime());
    }

    /**
     * 测试交易和订单都没有发货时间的情况
     */
    @Test
    public void testConvertTbTradeSendInfo_NoConsignTimeAtAll_ShouldUseNull() {
        // 准备测试数据：交易和订单都没有发货时间
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime(null);
        TbTradeJdpResponseDto.Order order1 = createOrderWithConsignTime(123456L, null);
        TbTradeJdpResponseDto.Order order2 = createOrderWithConsignTime(789012L, "");
        
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order1, order2);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回2个发货信息", 2, result.size());
        
        // 验证第一个发货信息（发货时间为null）
        RdsTbTradeSendInfo sendInfo1 = result.get(0);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(123456L), sendInfo1.getOid());
        Assert.assertNull("发货时间应该为null", sendInfo1.getConsignTime());
        
        // 验证第二个发货信息（发货时间为null）
        RdsTbTradeSendInfo sendInfo2 = result.get(1);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(789012L), sendInfo2.getOid());
        Assert.assertNull("发货时间应该为null", sendInfo2.getConsignTime());
    }

    /**
     * 测试空订单列表的情况
     */
    @Test
    public void testConvertTbTradeSendInfo_EmptyOrderList_ShouldReturnEmptyList() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        List<TbTradeJdpResponseDto.Order> orders = Collections.emptyList();
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertTrue("应该返回空列表", result.isEmpty());
    }

    /**
     * 测试null订单列表的情况
     */
    @Test
    public void testConvertTbTradeSendInfo_NullOrderList_ShouldReturnEmptyList() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        List<TbTradeJdpResponseDto.Order> orders = null;
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertTrue("应该返回空列表", result.isEmpty());
    }

    /**
     * 测试null交易对象的情况
     */
    @Test
    public void testConvertTbTradeSendInfo_NullTrade_ShouldHandleGracefully() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = null;
        TbTradeJdpResponseDto.Order order = createOrderWithConsignTime(123456L, "2024-12-19 14:30:45");
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回1个发货信息", 1, result.size());
        
        // 验证发货信息（应该使用订单的发货时间）
        RdsTbTradeSendInfo sendInfo = result.get(0);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(123456L), sendInfo.getOid());
        Assert.assertEquals("发货时间应该使用订单发货时间", "2024-12-19 14:30:45", sendInfo.getConsignTime());
    }

    /**
     * 测试订单ID为null的情况
     */
    @Test
    public void testConvertTbTradeSendInfo_NullOrderId_ShouldHandleGracefully() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        TbTradeJdpResponseDto.Order order1 = createOrderWithConsignTime(null, "2024-12-19 14:30:45");
        TbTradeJdpResponseDto.Order order2 = createOrderWithConsignTime(789012L, "2024-12-19 15:30:45");
        
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order1, order2);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回2个发货信息", 2, result.size());
        
        // 验证第一个发货信息（订单ID为null）
        RdsTbTradeSendInfo sendInfo1 = result.get(0);
        Assert.assertNull("订单ID应该为null", sendInfo1.getOid());
        Assert.assertEquals("发货时间应该正确", "2024-12-19 14:30:45", sendInfo1.getConsignTime());
        
        // 验证第二个发货信息
        RdsTbTradeSendInfo sendInfo2 = result.get(1);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(789012L), sendInfo2.getOid());
        Assert.assertEquals("发货时间应该正确", "2024-12-19 15:30:45", sendInfo2.getConsignTime());
    }

    /**
     * 测试混合场景：部分订单有发货时间，部分没有
     */
    @Test
    public void testConvertTbTradeSendInfo_MixedScenario_ShouldHandleCorrectly() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        TbTradeJdpResponseDto.Order order1 = createOrderWithConsignTime(123456L, "2024-12-19 14:30:45"); // 有发货时间
        TbTradeJdpResponseDto.Order order2 = createOrderWithConsignTime(789012L, null); // 没有发货时间
        TbTradeJdpResponseDto.Order order3 = createOrderWithConsignTime(345678L, ""); // 空发货时间
        TbTradeJdpResponseDto.Order order4 = createOrderWithConsignTime(901234L, "2024-12-19 16:30:45"); // 有发货时间
        
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order1, order2, order3, order4);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回4个发货信息", 4, result.size());
        
        // 验证第一个发货信息（使用订单发货时间）
        RdsTbTradeSendInfo sendInfo1 = result.get(0);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(123456L), sendInfo1.getOid());
        Assert.assertEquals("发货时间应该使用订单发货时间", "2024-12-19 14:30:45", sendInfo1.getConsignTime());
        
        // 验证第二个发货信息（使用交易发货时间）
        RdsTbTradeSendInfo sendInfo2 = result.get(1);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(789012L), sendInfo2.getOid());
        Assert.assertEquals("发货时间应该使用交易发货时间", "2024-12-19 14:30:45", sendInfo2.getConsignTime());
        
        // 验证第三个发货信息（使用交易发货时间）
        RdsTbTradeSendInfo sendInfo3 = result.get(2);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(345678L), sendInfo3.getOid());
        Assert.assertEquals("发货时间应该使用交易发货时间", "2024-12-19 14:30:45", sendInfo3.getConsignTime());
        
        // 验证第四个发货信息（使用订单发货时间）
        RdsTbTradeSendInfo sendInfo4 = result.get(3);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(901234L), sendInfo4.getOid());
        Assert.assertEquals("发货时间应该使用订单发货时间", "2024-12-19 16:30:45", sendInfo4.getConsignTime());
    }

    /**
     * 测试空字符串发货时间的处理
     */
    @Test
    public void testConvertTbTradeSendInfo_EmptyStringConsignTime_ShouldTreatAsNull() {
        // 准备测试数据
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("   "); // 空白字符串
        TbTradeJdpResponseDto.Order order1 = createOrderWithConsignTime(123456L, "   "); // 空白字符串
        TbTradeJdpResponseDto.Order order2 = createOrderWithConsignTime(789012L, ""); // 空字符串
        
        List<TbTradeJdpResponseDto.Order> orders = Arrays.asList(order1, order2);
        
        // 执行转换
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回2个发货信息", 2, result.size());
        
        // 验证第一个发货信息（空白字符串应该被当作null处理）
        RdsTbTradeSendInfo sendInfo1 = result.get(0);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(123456L), sendInfo1.getOid());
        Assert.assertNull("发货时间应该为null", sendInfo1.getConsignTime());
        
        // 验证第二个发货信息（空字符串应该被当作null处理）
        RdsTbTradeSendInfo sendInfo2 = result.get(1);
        Assert.assertEquals("订单ID应该正确", Long.valueOf(789012L), sendInfo2.getOid());
        Assert.assertNull("发货时间应该为null", sendInfo2.getConsignTime());
    }

    /**
     * 测试大量订单的性能和正确性
     */
    @Test
    public void testConvertTbTradeSendInfo_LargeOrderList_ShouldHandleCorrectly() {
        // 准备测试数据：1000个订单
        TbTradeJdpResponseDto.Trade trade = createTradeWithConsignTime("2024-12-19 14:30:45");
        List<TbTradeJdpResponseDto.Order> orders = new java.util.ArrayList<>();
        
        for (int i = 1; i <= 1000; i++) {
            // 一半订单有发货时间，一半没有
            String consignTime = (i % 2 == 0) ? "2024-12-19 14:30:" + String.format("%02d", i % 60) : null;
            orders.add(createOrderWithConsignTime((long) i, consignTime));
        }
        
        // 执行转换
        long startTime = System.currentTimeMillis();
        List<RdsTbTradeSendInfo> result = convertor.convertTbTradeSendInfo(trade, orders);
        long endTime = System.currentTimeMillis();
        
        // 验证结果
        Assert.assertNotNull("转换结果不应为空", result);
        Assert.assertEquals("应该返回1000个发货信息", 1000, result.size());
        
        // 验证性能（应该在合理时间内完成）
        long executionTime = endTime - startTime;
        Assert.assertTrue("执行时间应该在1秒内: " + executionTime + "ms", executionTime < 1000);
        
        // 验证部分结果的正确性
        RdsTbTradeSendInfo sendInfo1 = result.get(0); // 第1个订单（奇数，没有发货时间）
        Assert.assertEquals("订单ID应该正确", Long.valueOf(1L), sendInfo1.getOid());
        Assert.assertEquals("发货时间应该使用交易发货时间", "2024-12-19 14:30:45", sendInfo1.getConsignTime());
        
        RdsTbTradeSendInfo sendInfo2 = result.get(1); // 第2个订单（偶数，有发货时间）
        Assert.assertEquals("订单ID应该正确", Long.valueOf(2L), sendInfo2.getOid());
        Assert.assertEquals("发货时间应该使用订单发货时间", "2024-12-19 14:30:02", sendInfo2.getConsignTime());
    }

    //endregion

    //region 辅助方法

    /**
     * 创建带有发货时间的交易对象
     */
    private TbTradeJdpResponseDto.Trade createTradeWithConsignTime(String consignTime) {
        TbTradeJdpResponseDto.Trade trade = new TbTradeJdpResponseDto.Trade();
        trade.setConsignTime(consignTime);
        return trade;
    }

    /**
     * 创建带有发货时间的订单对象
     */
    private TbTradeJdpResponseDto.Order createOrderWithConsignTime(Long oid, String consignTime) {
        TbTradeJdpResponseDto.Order order = new TbTradeJdpResponseDto.Order();
        order.setOid(oid);
        order.setConsignTime(consignTime);
        return order;
    }

    //endregion
}
