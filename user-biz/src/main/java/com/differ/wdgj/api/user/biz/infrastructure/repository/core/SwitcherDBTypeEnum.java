package com.differ.wdgj.api.user.biz.infrastructure.repository.core;

/**
 * @Description 数据库自动切换类型
 * <AUTHOR>
 * @Date 2020-07-10 10:38
 */
public enum SwitcherDBTypeEnum {

    /**
     * RDS推送库
     */
    RDS_PUSH("RDS推送库"),
    /**
     * 管家会员库
     */
    WDGJ("管家会员库"),
    /**
     * API数据库
     */
    API("API数据库")
    ;

    SwitcherDBTypeEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }
}
