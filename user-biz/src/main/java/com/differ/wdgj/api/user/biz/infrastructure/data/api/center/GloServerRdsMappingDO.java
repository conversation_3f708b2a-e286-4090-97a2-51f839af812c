package com.differ.wdgj.api.user.biz.infrastructure.data.api.center;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.core.StringDateTimeDeserializer;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Table(name = "glo_serverrdsmapping")
public class GloServerRdsMappingDO {
    /**
     * 服务器内网IPecs服务器（不带端口）
     */
    @Id
    @Column(name = "ServerIP")
    private String serverip;

    @Column(name = "RdsID")
    private String rdsid;

    @Column(name = "RdsServerIP")
    private String rdsserverip;

    @Column(name = "RdsServerPort")
    private String rdsserverport;

    @Column(name = "RdsUserID")
    private String rdsuserid;

    @Column(name = "RdsPwd")
    private String rdspwd;

    /**
     * 创建时间
     */

    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    @Column(name = "CreateTime")
    private LocalDateTime createtime;

    /**
     * 修改时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    @Column(name = "ModifyTime")
    private LocalDateTime modifytime;

    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    @Column(name = "DataVersion")
    private LocalDateTime dataversion;

    /**
     * 获取服务器内网IPecs服务器（不带端口）
     *
     * @return ServerIP - 服务器内网IPecs服务器（不带端口）
     */
    public String getServerIp() {
        return serverip;
    }

    /**
     * 设置服务器内网IPecs服务器（不带端口）
     *
     * @param serverIp 服务器内网IPecs服务器（不带端口）
     */
    public void setServerIp(String serverIp) {
        this.serverip = serverIp;
    }

    /**
     * @return RdsID
     */
    public String getRdsid() {
        return rdsid;
    }

    /**
     * @param rdsid
     */
    public void setRdsid(String rdsid) {
        this.rdsid = rdsid;
    }

    /**
     * @return RdsServerIP
     */
    public String getRdsserverip() {
        return rdsserverip;
    }

    /**
     * @param rdsserverip
     */
    public void setRdsserverip(String rdsserverip) {
        this.rdsserverip = rdsserverip;
    }

    /**
     * @return RdsServerPort
     */
    public String getRdsserverport() {
        return rdsserverport;
    }

    /**
     * @param rdsserverport
     */
    public void setRdsserverport(String rdsserverport) {
        this.rdsserverport = rdsserverport;
    }

    /**
     * @return RdsUserID
     */
    public String getRdsuserid() {
        return rdsuserid;
    }

    /**
     * @param rdsuserid
     */
    public void setRdsuserid(String rdsuserid) {
        this.rdsuserid = rdsuserid;
    }

    /**
     * @return RdsPwd
     */
    public String getRdspwd() {
        return rdspwd;
    }

    /**
     * @param rdspwd
     */
    public void setRdspwd(String rdspwd) {
        this.rdspwd = rdspwd;
    }

    /**
     * 获取创建时间
     *
     * @return CreateTime - 创建时间
     */
    public LocalDateTime getCreatetime() {
        return createtime;
    }

    /**
     * 设置创建时间
     *
     * @param createtime 创建时间
     */
    public void setCreatetime(LocalDateTime createtime) {
        this.createtime = createtime;
    }

    /**
     * 获取修改时间
     *
     * @return ModifyTime - 修改时间
     */
    public LocalDateTime getModifytime() {
        return modifytime;
    }

    /**
     * 设置修改时间
     *
     * @param modifytime 修改时间
     */
    public void setModifytime(LocalDateTime modifytime) {
        this.modifytime = modifytime;
    }

    /**
     * @return DataVersion
     */
    public LocalDateTime getDataversion() {
        return dataversion;
    }

    /**
     * @param dataversion
     */
    public void setDataversion(LocalDateTime dataversion) {
        this.dataversion = dataversion;
    }
}