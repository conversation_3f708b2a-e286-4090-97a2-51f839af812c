package com.differ.wdgj.api.component.util.system;

import java.util.Map;
import java.util.Set;

/**
 * 线程工具类
 *
 * <AUTHOR>
 * @date 2024/5/14 16:09
 */
public class ThreadUtil {
    //region 构造
    private ThreadUtil() {
    }
    //endregion

    /**
     * 安全线程暂停
     *
     * @param millis 暂这时间(毫秒)
     */
    public static void sleep(long millis) {
        boolean interrupted = false;
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            interrupted = true;
        } finally {
            if (interrupted) {
                Thread.currentThread().interrupt();
            }
        }

    }

    /**
     * 通过线程id和线程名称获取唯一线程
     *
     * @param threadId   线程Id
     * @param threadName 线程名称
     * @return 线程
     */
    public static Thread getThreadByThreadIdAndName(long threadId, String threadName) {
        Map<Thread, StackTraceElement[]> stackTraces = Thread.getAllStackTraces();
        Set<Thread> threads = stackTraces.keySet();
        for (Thread thread : threads) {
            if (thread.getId() == threadId && thread.getName().equals(threadName)) {
                return thread;
            }
        }
        return null;
    }

    /**
     * 中断线程
     *
     * @param thread 线程
     */
    public static void interruptThread(Thread thread) {
        if (thread != null) {
            thread.interrupt();
        }
    }

    /**
     * 中断线程（强制停止）
     *
     * @param thread 线程
     */
    public static void stopThread(Thread thread) {
        if (thread != null) {
            thread.stop();
        }
    }

    /**
     * 获取线程堆栈信息
     *
     * @param thread 线程
     * @return 堆栈信息
     */
    public static String getStackTraceStr(Thread thread) {
        if (thread == null) {
            return "";
        }
        StackTraceElement[] stackTrace = thread.getStackTrace();
        StringBuilder stringBuilder = new StringBuilder();
        for (StackTraceElement element : stackTrace) {
            stringBuilder.append(element.toString()).append("\n");
        }
        return stringBuilder.toString();
    }
}
