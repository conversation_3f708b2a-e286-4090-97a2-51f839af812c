package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * LoadAfterSaleWorkMutex 单元测试 - 第三批：综合场景和边界条件测试
 *
 * <AUTHOR>
 * @date 2024/12/19 下午3:30
 */
public class LoadAfterSaleWorkMutexComprehensiveTest extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 测试所有枚举值的覆盖
     * 确保所有AfterSaleLoadTypeEnum和TriggerTypeEnum的组合都能正常处理
     */
    @Test
    public void testCreate_AllEnumCombinations() {
        // 测试所有触发类型和加载类型的组合
        TriggerTypeEnum[] triggerTypes = {TriggerTypeEnum.HAND, TriggerTypeEnum.AUTO, TriggerTypeEnum.MESSAGE_NOTIFICATION};
        AfterSaleLoadTypeEnum[] loadTypes = {AfterSaleLoadTypeEnum.SHOP_LOAD, AfterSaleLoadTypeEnum.TIME_RANGE, AfterSaleLoadTypeEnum.AFTER_SALE_NO};
        
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            // 模拟配置值为0（不超时）
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(0);
            
            for (TriggerTypeEnum triggerType : triggerTypes) {
                for (AfterSaleLoadTypeEnum loadType : loadTypes) {
                    // 重置mock
                    Mockito.reset(mockDataOperate);
                    
                    WorkData<AfterSaleLoadArgs> workData = createWorkData(triggerType, loadType);
                    
                    if (triggerType == TriggerTypeEnum.MESSAGE_NOTIFICATION && loadType == AfterSaleLoadTypeEnum.AFTER_SALE_NO) {
                        // 消息推送且按售后单号下载，应该直接创建
                        CreateResult expectedResult = CreateResult.successResult("direct-task-id");
                        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                        
                        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                        
                        Assert.assertNotNull("消息推送+售后单号应该直接创建", result);
                        Assert.assertTrue("应该创建成功", result.success());
                        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                        // 验证没有调用父类逻辑
                        Mockito.verify(mockDataOperate, Mockito.never()).existsUnComplete(workData);
                    } else {
                        // 其他情况，模拟不存在未完成任务，应该通过父类逻辑创建
                        CreateResult expectedResult = CreateResult.successResult("super-task-id");
                        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
                        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                        
                        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                        
                        Assert.assertNotNull("其他情况应该通过父类创建", result);
                        Assert.assertTrue("应该创建成功", result.success());
                        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
                        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                    }
                }
            }
        }
    }

    /**
     * 测试父类创建逻辑返回非空结果的场景
     * 当父类create方法返回非空结果时，应该直接返回该结果，不检查超时任务
     */
    @Test
    public void testCreate_SuperCreateReturnsResult_ShouldReturnSuperResult() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            // 模拟配置值为30分钟超时
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(30);
            
            // 模拟父类方法返回结果（不存在未完成任务时返回创建结果）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
            Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNotNull("结果不应为空", result);
            Assert.assertTrue("应该创建成功", result.success());
            Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());
            
            // 验证交互
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
            Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
            
            // 验证没有检查超时任务（因为父类已经返回结果）
            Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
            
            // 验证配置方法没有被调用（因为父类已经返回结果）
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.never());
        }
    }

    /**
     * 测试存在未完成任务且不存在超时任务的完整流程
     * 验证整个方法的执行流程
     */
    @Test
    public void testCreate_CompleteFlow_ExistsUnCompleteButNoTimeout() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            // 模拟配置值为30分钟超时
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(30);
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 模拟不存在超时未运行任务
            Mockito.when(mockDataOperate.existsUnRunTimeout(workData, 30)).thenReturn(false);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("当存在未完成任务但不存在超时任务时，应该返回null", result);
            
            // 验证完整的执行流程
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, 30);
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
            
            // 验证配置方法被调用
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
        }
    }

    /**
     * 测试负数超时时间的场景
     * 当配置的超时时间为负数时，应该视为不超时
     */
    @Test
    public void testCreate_NegativeTimeoutPeriod_ShouldNotCheckTimeout() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟配置值为负数
        try (MockedStatic<LoadAfterSaleConfigKeyUtils> configMock = Mockito.mockStatic(LoadAfterSaleConfigKeyUtils.class)) {
            configMock.when(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod)
                     .thenReturn(-10);
            
            // 模拟父类方法返回null（存在未完成任务）
            Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
            
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证结果
            Assert.assertNull("当超时时间为负数时，应该返回null", result);
            
            // 验证没有检查超时任务
            Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
            Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
            
            // 验证配置方法被调用
            configMock.verify(LoadAfterSaleConfigKeyUtils::getWorkTaskUnRunTimeoutPeriod, Mockito.times(1));
        }
    }

    /**
     * 测试多次调用的一致性
     * 验证多次调用相同参数时行为一致
     */
    @Test
    public void testCreate_MultipleCallsConsistency() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        CreateResult expectedResult = CreateResult.successResult("consistent-task-id");
        
        // 模拟依赖行为
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 多次执行测试
        for (int i = 0; i < 3; i++) {
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            // 验证每次结果都一致
            Assert.assertNotNull("第" + (i + 1) + "次调用结果不应为空", result);
            Assert.assertTrue("第" + (i + 1) + "次调用应该创建成功", result.success());
            Assert.assertEquals("第" + (i + 1) + "次调用任务ID应该匹配", "consistent-task-id", result.getTaskId());
        }
        
        // 验证总共调用了3次
        Mockito.verify(mockDataOperate, Mockito.times(3)).create(workData);
    }

    /**
     * 测试WorkData为null的边界情况
     * 当WorkData为null时，应该抛出异常或者有适当的处理
     */
    @Test
    public void testCreate_WorkDataNull_ShouldHandleGracefully() {
        try {
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(null, mockDataOperate);
            
            // 如果没有抛出异常，验证结果
            Assert.assertNull("当WorkData为null时，应该返回null或抛出异常", result);
        } catch (Exception e) {
            // 如果抛出异常，验证异常类型
            Assert.assertTrue("应该抛出适当的异常", 
                e instanceof NullPointerException || e instanceof IllegalArgumentException);
        }
    }

    /**
     * 测试DataOperate为null的边界情况
     * 当DataOperate为null时，应该抛出异常
     */
    @Test
    public void testCreate_DataOperateNull_ShouldThrowException() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        
        try {
            // 执行测试
            CreateResult result = loadAfterSaleWorkMutex.create(workData, null);
            
            // 如果没有抛出异常，验证结果
            Assert.assertNull("当DataOperate为null时，应该返回null或抛出异常", result);
        } catch (Exception e) {
            // 如果抛出异常，验证异常类型
            Assert.assertTrue("应该抛出NullPointerException", e instanceof NullPointerException);
        }
    }

    /**
     * 创建测试用的WorkData对象
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            triggerType, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);
        
        return WorkData.of(workContext, args);
    }
}
