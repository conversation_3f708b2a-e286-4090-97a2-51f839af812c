package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.goods.TbGoodsJdpResponseDto;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor.RdsTbGoodsConvertor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct.BusinessDownloadProductResponseGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbGoodsRdsMapper;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 淘宝Rds - 商品相关操作（jdp_tb_item）
 * <AUTHOR>
 * @date 2024-03-20 21:05
 */
public class RdsGoodsDomain {

    //region 公共方法
    /**
     * 批量按平台商品id 获取 淘宝商品菠萝派数据
     * @param platGoodsIds 平台商品Id列表
     * @return 淘宝商品菠萝派数据列表
     */
    public List<BusinessDownloadProductResponseGoodInfo> getPolyProductByPlatGoodIds(String vipUser, List<String> platGoodsIds){
        if(CollectionUtils.isEmpty(platGoodsIds)){
            return new ArrayList<>();
        }

        // 批量获取推送库数据
        TbGoodsRdsMapper tbGoodsRdsMapper = BeanContextUtil.getBean(TbGoodsRdsMapper.class);
        List<String> jdpResponses = DBSwitchUtil.doDBWithRds(vipUser, () -> tbGoodsRdsMapper.selectByNumIids(platGoodsIds));

        // 数据转换
        List<TbGoodsJdpResponseDto> tbGoodsList = jdpResponses.stream().map(x -> JsonUtils.deJson(x, TbGoodsJdpResponseDto.class)).collect(Collectors.toList());
        return RdsTbGoodsConvertor.convertJdpResponse(tbGoodsList);
    }
    //endregion



}
