package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.core.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 保存换货单处理器
 *
 * <AUTHOR>
 * @date 2024/8/12 下午3:13
 */
@Ignore
public class BaseSaveExchangeOrderProcessorTest extends BaseAfterSaleOrderTest {
    //region 变量
    /**
     * 基础测试数据地址
     */
    private final String basePath = "D:\\esapi-java\\管家java测试数据\\AfterSaleDataOperationTest\\exchange\\%s";
    //endregion

    /**
     * 保存换货单完成流程测试
     */
    @Test
    public void saveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "BusinessGetExchangeOrderResponseOrderItem.json"))));
        List<BusinessGetExchangeOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetExchangeOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 96936);
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = SaveExchangeOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 拼多多-保存换货单完成流程测试
     */
    @Test
    public void pddSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "PddBusinessGetExchangeOrderResponseOrderItem.json"))));
        List<BusinessGetExchangeOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetExchangeOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 96936);
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = SaveExchangeOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 放心购-保存换货单完成流程测试
     */
    @Test
    public void byteDanceSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "ByteDanceBusinessGetExchangeOrderResponseOrderItem.json"))));
        List<BusinessGetExchangeOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetExchangeOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 1033);
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = SaveExchangeOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }

    /**
     * 小红书-保存换货单完成流程测试
     */
    @Test
    public void xiaoHSSaveOrderTest() throws IOException {
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "XiaoHSBusinessGetExchangeOrderResponseOrderItem.json"))));
        List<BusinessGetExchangeOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetExchangeOrderResponseOrderItem>>() {
        });
        AfterSaleSaveContext context = getContext("api2017", 96230);
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = SaveExchangeOrderFactory.createProcessor(context);
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());
    }
}
