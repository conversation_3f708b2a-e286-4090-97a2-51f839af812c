//package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.items;
//
//import com.differ.newapi.base.infrastructure.component.cache.ICacheDataItem;
//import com.differ.newapi.proxy.business.data.command.SubPlatEntity;
//
///**
// * @Description
// * <AUTHOR>
// * @date 2020/12/22 16:44
// */
//public class SubPlatCacheItem extends SubPlatEntity implements ICacheDataItem {
//    @Override
//    public String getDataVersion() {
//        return null;
//    }
//
//    @Override
//    public String getHashKey() {
//        return this.getSubPlatId().toString();
//    }
//}
