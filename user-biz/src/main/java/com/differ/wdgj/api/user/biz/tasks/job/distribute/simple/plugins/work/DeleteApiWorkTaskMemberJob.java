package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.work;

import com.differ.wdgj.api.component.task.single.core.SingleJobParameter;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.WorkFacade;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.core.AbstractUserSimpleDistributeJob;

/**
 * 自动删除过期任务数据 定时任务
 *
 * <AUTHOR>
 * @date 2025/04/16 上午11:30
 */
@SingleJobParameter(
        sitesToRun = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
        jobName = "DeleteApiWorkTaskMemberJob",
        cron = "0 0 2 * * ?" //每天凌晨2点执行一次
)
public class DeleteApiWorkTaskMemberJob extends AbstractUserSimpleDistributeJob {
    //region 变量

    /**
     * 标题
     */
    private final String CAPTION = "自动删除过期任务数据";

    /**
     * 工作任务门面
     */
    private final WorkFacade workFacade = new WorkFacade();

    // endregion

    //region 实现基类方法

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return CAPTION;
    }

    /**
     * 按会员执行任务
     *
     * @param memberName 会员名
     */
    @Override
    protected void executeByUser(String memberName) {
        //是否开启 总开关
        if (!ConfigKeyUtils.isActionApiBoolean(ConfigKeyEnum.ISACTION_DELETE_EXPIRED_DATA)) {
            return;
        }

        // 遍历 taskType 枚举
        for (WorkEnum workEnum : WorkEnum.values()) {
            try {
                workFacade.cleanComplete(memberName, workEnum);
            } catch (Throwable e) {
                LogFactory.error(CAPTION, String.format("[%s]自动删除过期任务数据异常:%s", memberName, workEnum), e);
            }
        }
    }

    //endregion
}
