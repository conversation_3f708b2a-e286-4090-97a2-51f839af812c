package com.differ.wdgj.api.user.biz.tasks.job.queue.core;

import com.differ.wdgj.api.component.task.single.core.AbstractSingleBeanJob;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.hash.membercontrol.plugins.ApiBalanceJobMemberControlCache;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.JobResult;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.UserQueueJobData;

import java.util.ArrayList;
import java.util.List;

/**
 * 动态用户队列定时任务
 * <p>
 * 说明：
 * 1、外部动态添加会员任务
 * 2、各业务实现控制单次会员任务执行完成后是否继续入队，供下轮执行
 * 3、支持 “延迟退出” 的兜底模式，避免未正常归还任务到队列而导致的整体任务中断
 * <p>
 * 适用场景：
 * 1、外部触发的长任务，如批次同步 100w 数据，可实现多副本负载均衡执行
 *
 * <AUTHOR>
 * @date 2023-12-13 10:17
 */
public abstract class AbstractDynamicUserQueueJob extends AbstractUserQueueJob {

    // region 变量

    /**
     * 会员控制缓存
     */
    private ApiBalanceJobMemberControlCache controlCache;

    // endregion

    // region 重写基类方法

    /**
     * 获取自动生成的初始化会员
     *
     * @return 全部会员
     */
    @Override
    protected List<String> getAllAutoInitUsers() {

        // 延迟退出：添加会员控制缓存中的会员
        if (this.delayQuit()) {
            return this.getControlCache().getCurrentClusterMembers();
        }

        return new ArrayList<>();
    }

    /**
     * 执行任务
     *
     * @param data 任务数据
     */
    @Override
    protected JobResult execute(UserQueueJobData data) {
        try {

            // 开始时间
            long startTimeMillis = System.currentTimeMillis();

            // 会员名
            String memberName = data.getMemberName();

            // 按会员执行
            this.executeByUser(memberName);

            // 执行完成是否重新入队
            boolean executedRequeue = this.executedRequeue(memberName);

            // 延迟退出
            if (this.delayQuit()) {
                if (executedRequeue) {

                    // 重新入队：重置会员控制缓存
                    this.getControlCache().addMember(memberName);
                } else {

                    // 不重新入队：会员控制缓存自增标记，延迟删除
                    this.getControlCache().incrementClearSign(memberName);

                    // 根据会员控制缓存判断是否重新入队
                    executedRequeue = this.getControlCache().actionMember(memberName);
                }
            }

            // 记录日志
            boolean finalExecutedRequeue = executedRequeue;
            this.tempMonitor.monitor(memberName, () ->
                    ExtUtils.stringBuilderAppend(String.format("任务执行完成，重新入队：%s", finalExecutedRequeue)), startTimeMillis);

            // 返回任务结果
            return executedRequeue ? JobResult.REQUEUE : JobResult.FINISH;
        } catch (Throwable t) {

            // 当出现异常时，返回重试，但有最大重试次数，超过重试次数后依赖兜底重新加入任务
            AbstractSingleBeanJob.log.error(String.format("%s-%s会员任务异常", this.getJobParameter().jobName(), data.getMemberName()), t);
            return JobResult.RETRY;
        }
    }

    /**
     * 延迟退出（在任务执行完成后不会立刻退出队列，会多重新入队几次后，再退出，以避免任务链断掉）
     *
     * @return 是否延迟退出
     */
    protected boolean delayQuit() {
        return true;
    }

    // endregion

    // region 公共方法

    /**
     * 会员任务入队
     *
     * @param memberName 会员名
     */
    public boolean enqueueMember(String memberName) {

        // 新建任务数据
        UserQueueJobData jobData = new UserQueueJobData();
        jobData.setMemberName(memberName);

        // 添加任务
        boolean success = this.addTask(jobData).isSuccess();
        if (!success) {
            return false;
        }

        // 延迟退出：更新会员控制缓存
        if (this.delayQuit()) {
            this.getControlCache().addMember(memberName);
        }

        // 返回成功
        return true;
    }

    // endregion

    // region 私有方法

    /**
     * 获取会员控制缓存
     *
     * @return 结果
     */
    protected ApiBalanceJobMemberControlCache getControlCache() {
        if (this.controlCache == null) {
            this.controlCache = ApiBalanceJobMemberControlCache.build(this.getJobCode());
        }

        return this.controlCache;
    }

    // endregion
}
