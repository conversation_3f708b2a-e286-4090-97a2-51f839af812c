package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.order;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractOrderConvertHandle;

/**
 * 换货单订单级转换平台级特殊处理
 *
 * <AUTHOR>
 * @date 2024/8/9 下午2:20
 */
public class ExchangePlatSpecialOrderCovertHandle extends AbstractOrderConvertHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangePlatSpecialOrderCovertHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult convertOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return String.format("平台【%s】换货单订单级转换-平台级特殊处理", context.getPlat().getName());
    }
}
