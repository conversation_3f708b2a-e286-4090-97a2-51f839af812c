package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 聚宝赞 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2025/5/27 14:56
 */
public class JvBaoZanSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public JvBaoZanSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取api售后单类型
     *
     * @param orderItem 菠萝派售后单信息
     * @param shopType  店铺类型
     * @return api售后单类型
     */
    @Override
    protected ApiAfterSaleTypeEnum getApiAfterSaleType(BusinessGetRefundOrderResponseOrderItem orderItem, ShopTypeEnum shopType) {
        // 获取菠萝派返回售后类型
        PolyRefundTypeEnum polyRefundType = PolyRefundTypeEnum.create(orderItem.getRefundType());
        if (polyRefundType != null && polyRefundType == PolyRefundTypeEnum.JH_06) {
            // 商家主动退款
            return ApiAfterSaleTypeEnum.REFUND_MERCHANT;
        }

        //普通售后类型走基类
        return super.getApiAfterSaleType(orderItem, shopType);
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        //商家主动退款 - 暂时归类为“仅退款”
        if (StringUtils.equals(ployOrder.getRefundType(), PolyRefundTypeEnum.JH_06.getCode())) {
            afterSaleOrder.setType(WdgjRefundTypeEnum.REFUND_PAY.getValue());
        }

        return AfterSaleHandleResult.success();
    }
}