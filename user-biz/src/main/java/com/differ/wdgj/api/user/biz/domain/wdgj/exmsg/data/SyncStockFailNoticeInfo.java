package com.differ.wdgj.api.user.biz.domain.wdgj.exmsg.data;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockRestrictedModeEnum;

/**
 * 库存同步失败通知对象
 *
 * <AUTHOR>
 * @since 2024-03-18 9:43
 */
public class SyncStockFailNoticeInfo {

    /**
     * 是否自动
     */
    private boolean isAuto;

    /**
     * 匹配记录Id
     */
    private int apiSysMatchId;

    /**
     * 失败信息
     */
    private String syncMsg;

    /**
     * 店铺id
     */
    private int shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 平台商品名称
     */
    private String tbName;

    /**
     * 执行模式
     */
    private SyncStockRestrictedModeEnum modeEnum;

    public boolean isAuto() {
        return isAuto;
    }

    public void setAuto(boolean auto) {
        isAuto = auto;
    }

    public int getApiSysMatchId() {
        return apiSysMatchId;
    }

    public void setApiSysMatchId(int apiSysMatchId) {
        this.apiSysMatchId = apiSysMatchId;
    }

    public String getSyncMsg() {
        return syncMsg;
    }

    public void setSyncMsg(String syncMsg) {
        this.syncMsg = syncMsg;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getTbName() {
        return tbName;
    }

    public void setTbName(String tbName) {
        this.tbName = tbName;
    }

    public SyncStockRestrictedModeEnum getModeEnum() {
        return modeEnum;
    }

    public void setModeEnum(SyncStockRestrictedModeEnum modeEnum) {
        this.modeEnum = modeEnum;
    }
}
