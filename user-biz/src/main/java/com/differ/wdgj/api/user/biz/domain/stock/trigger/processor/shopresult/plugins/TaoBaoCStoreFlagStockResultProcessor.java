package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.*;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchExtResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;

import java.time.LocalDateTime;

/**
 * 库存同步结果处理-淘宝C店数据打标
 * <AUTHOR>
 * @date 2024-03-19 20:56
 */
public class TaoBaoCStoreFlagStockResultProcessor extends AbstractStockResultProcessor {
    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "淘宝C店数据打标";
    }

    /**
     * 处理结果
     *
     * @param context          上下文
     * @param resultPackage    库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 遍历失败结果
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (composite == null) {
                return;
            }

            // 1、淘宝c店商品打标
            createCStoreFlag(composite);

            // 2、后端商品禁止库存同步
            forbidCStoreGoods(idEnhance, composite);
        });
    }

    // region 淘宝c店商品打标

    /**
     * 淘宝c店商品打标
     * @param composite 库存同步结果
     */
    public void createCStoreFlag(StockSyncResultComposite composite){

        // 1、淘宝c店商品打标
        // 根据错误匹配平台商品类型
        PlatGoodsTypeEnum platGoodsTypeEnum = getPlatGoodsType(composite);
        // 平台商品类型不为空，且当前为普通商品
        if(platGoodsTypeEnum != null){
            ApiSysMatchExtDO extEntity = composite.getMatchEnhance().getSysMatch().getExtEntity();
            boolean isNormalGoods = extEntity == null || extEntity.getPlatGoodsType().equals(PlatGoodsTypeEnum.Normal.getValue());
            if(isNormalGoods){
                // 结果持久化
                // 更新扩展表平台商品类型
                composite.setApiSysMatchExtResult(buildMatchExtResult(platGoodsTypeEnum, composite));
                // 重新发起库存同步
                composite.getApiSysMatchResult().setIsSys(SyncStockStatusEnum.WaitSync.getValue());
            }
        }
    }

    /**
     * 获取平台商品类型
     * @param composite 库存同步结果组合
     * @return 平台商品类型
     */
    private PlatGoodsTypeEnum getPlatGoodsType(StockSyncResultComposite composite){
        switch (composite.getResponseErrorCode()){
            case TAOBAO_C_CUSTOMIZE_GOODS:
                return PlatGoodsTypeEnum.CustomizeGoods;
            case TAOBAO_C_TYPE_CATEGORY_STOCK_ERROR:
                return PlatGoodsTypeEnum.CStoreEntryError;
            case TAOBAO_C_TYPE_STOCK_ERROR:
                return PlatGoodsTypeEnum.TaoTe;
            default:
                // 防止插入空扩展数据
                return null;
        }
    }

    /**
     * 构建匹配扩展结果对象
     * @param composite 库存同步结果组合
     * @return 匹配扩展结果对象
     */
    private StockSyncApiSysMatchExtResult buildMatchExtResult(PlatGoodsTypeEnum platGoodsTypeEnum, StockSyncResultComposite composite){
        StockSyncApiSysMatchExtResult apiSysMatchExtResult = composite.getApiSysMatchExtResult();
        if(apiSysMatchExtResult == null){
            apiSysMatchExtResult = new StockSyncApiSysMatchExtResult();
            apiSysMatchExtResult.setApiSysMatchId(composite.getMatchEnhance().getSysMatch().getId());
        }
        apiSysMatchExtResult.setPlatGoodsType(platGoodsTypeEnum.getValue());

        return apiSysMatchExtResult;
    }

    // endregion

    // region 后端商品禁止库存同步

    /**
     * 后端商品禁止库存同步
     * @param composite 库存同步结果
     */
    private void forbidCStoreGoods(MatchIdEnhance idEnhance, StockSyncResultComposite composite){
        if (SyncStockPolyErrorCodeEnum.TAOBAO_GOODS_LESS_REAL_ITEM_ID.equals(composite.getResponseErrorCode())){
            // 禁止时间1天
            LocalDateTime nextResetSyncTime = LocalDateTime.now().plusDays(1);
            // 降级标识
            SyncStockFlagEnum limit = SyncStockFlagEnum.Forbid;
            // 限制/禁止模式
            SyncStockRestrictedModeEnum restrictedMode = composite.getResponseErrorCode().getRestrictedMode();

            // 结果持久化
            StockSyncApiSysMatchExtResult tierDown = composite.getApiSysMatchExtResult() != null
                    ? composite.getApiSysMatchExtResult()
                    : StockSyncApiSysMatchExtResult.createDefault(idEnhance.getMatchId());
            tierDown.setIncreFlag(limit.getValue());
            tierDown.setNextResetSyncTime(nextResetSyncTime);
            tierDown.setRestrictedMode(restrictedMode.getValue());
            composite.setApiSysMatchExtResult(tierDown);
        }
    }


    // endregion
}
