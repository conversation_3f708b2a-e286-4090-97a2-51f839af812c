'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

interface ExecuteQueueMonitorCenter << 队列监控中心接口 >> {
    +void put(ExecuteEvent event) : 外部触发添加执行事件
    +void checkTimeoutAndGetTotalCount() : 定时任务触发检测超时数据
    +boolean useLocal() : 是否使用本地内存队列
}

ExecuteMonitorEnum --> TaskEnum :关联（代码体现：成员变量）
ExecuteMonitorEnum --> ExecuteQueueMonitorCenter :关联（代码体现：成员变量）

ExecuteMonitorEnum <.. DemoExecute
ExecuteMonitorEnum <.. LocalExecuteMonitorJob : 超时定时任务检测

abstract class AbstractExecuteQueueMonitorCenter << 队列监控中心抽象 >> implements ExecuteQueueMonitorCenter  {
    # Map<String, TimeoutQueue> : 监控组集合
    # abstract void doFinish(TimeoutQueue queue, boolean success) : 当完成一个任务链时，执行处理
    # abstract void doTimeout(TimeoutQueue queue) : 是否超时，用于外部定时任务检测
    # int getQueueCapacity() : 每个队列的最大容量，超过限制时，丢弃非完成状态的数据
    - void clear(String uniqueNo) : 清空队列
}

class LocalChainLogMonitorCenter << 本地链路日志执行监控中心 >> extends AbstractExecuteQueueMonitorCenter
class LocalConsumeTimeMonitorCenter << 本地耗时日志执行监控中心 >> extends AbstractExecuteQueueMonitorCenter
class LocalExecuteTimeoutAlarmMonitorCenter << 本地执行超时的实时报警监控中心 >> extends AbstractExecuteQueueMonitorCenter

class TimeoutQueue {
    - DateTime firstTime : 首个数据的时间
    - DateTime lastTime : 最新数据的时间
    - ConcurrentLinkedQueue<ExecuteEvent> queue ： 监控事件列表
    + add(ExecuteEvent event) : 外部触发添加执行事件
    + void clear() : 清空队列
    + boolean isTimeout(int timeoutSeconds) : 是否超时
    + ConcurrentLinkedQueue<ExecuteEvent> getQueue() :返回队列对象
    + int getSize() : 获取当前队列的数据数量
}
ExecuteEvent <--o TimeoutQueue :聚合（代码体现：成员集合变量）
TimeoutQueue <--o AbstractExecuteQueueMonitorCenter :聚合（代码体现：成员集合变量）

enum ExecuteMonitorEnum << 外部操作门面的枚举 >> {
    +String addFirst(String eventData) ： 添加父监控数据（首次添加）
    +void addChild(String parentUniqueNo, String eventData) ： 添加未完成状态子监控数据
    +void addChild(String parentUniqueNo, String eventData, boolean finishSuccess) : 添加完成状态的子监控数据
    +void checkTimeout(int timeoutSeconds) : 定时任务触发检测超时数据
    +boolean useLocal() : 是否使用本地内存队列
}

class ExecuteEvent <<执行监控事件>> {
    - DateTime eventTime : 事件发生时间
    - ExecuteStatus status : 监控事件状态，主要用于标识是否完成（成功，失败，执行中）
    - String eventData : 事件业务数据
    - String uniqueNo : 监控任务编号
}

@enduml