package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.common;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.member.ApiMemberCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj.WdgjYunMemberDataSourceCache;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberUserDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.member.MemberUserStatusEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.member.MemberUserTypeEnum;
import org.apache.commons.lang3.RandomUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 有效会员内存缓存
 *
 * <AUTHOR>
 * @date 2021-09-24 15:00
 */
public class ActiveMembersLocalCache extends AbstractLocalCache<String, List<String>> {

    //region 单例

    private ActiveMembersLocalCache() {
        this.cacheMaxSize = 100;
        this.timeUnit = TimeUnit.SECONDS;
        this.expire = RandomUtils.nextInt(300, 360);
    }

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static ActiveMembersLocalCache singleton() {
        return ActiveMembersLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final ActiveMembersLocalCache instance;

        private SingletonEnum() {
            instance = new ActiveMembersLocalCache();
        }
    }

    //endregion

    // region 公共方法

    /**
     * 获取有效会员（当前集群）
     *
     * @return 会员集合
     */
    public List<String> getActiveMembersCurrentCluster() {
        return super.getCacheThenSource(SystemAppConfig.getClusterNo());
    }

    // endregion

    // region 重写基类方法

    /**
     * 加载数据
     *
     * @param key 集群号
     * @return 有效会员
     */
    @Override
    protected List<String> loadSource(String key) {
        List<MemberUserDO> members = ApiMemberCache.singleton().getAll();
        List<String> hasDataSourceMembers = WdgjYunMemberDataSourceCache.singleton().getAllMembers();

        // 获取有效会员
        List<String> activeMembers = new ArrayList<>();
        for (MemberUserDO member : members) {
            // 校验会员状态
            if(member.getUserStatus() != MemberUserStatusEnum.NORMAL.getValue()){
                continue;
            }
            // 校验会员类型
            if(member.getUserType() != MemberUserTypeEnum.WDGJ_MEMBER.getValue()){
                continue;
            }
            // 获取外部会员名
            String outMemberName = OuterApiEnum.WDGJ.getOutUserName(member.getUserName());
            // 校验数据库连接是否存在
            if(hasDataSourceMembers.stream().noneMatch(x -> x.equals(outMemberName))){
                continue;
            }

            activeMembers.add(outMemberName);
        }
        return activeMembers;
    }

    // endregion
}
