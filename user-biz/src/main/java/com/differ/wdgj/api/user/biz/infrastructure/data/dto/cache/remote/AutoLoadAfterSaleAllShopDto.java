package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

import java.util.List;

/**
 * 所有支持自动下载售后单的店铺
 *
 * <AUTHOR>
 * @date 2024/11/27 下午4:02
 */
public class AutoLoadAfterSaleAllShopDto {
    /**
     * 支持自动下载售后单的店铺
     */
    private List<AutoLoadAfterSaleShopDto> autoLoadAfterSaleShops;

    //region 构造

    public AutoLoadAfterSaleAllShopDto() {
    }

    public AutoLoadAfterSaleAllShopDto(List<AutoLoadAfterSaleShopDto> autoLoadAfterSaleShops) {
        this.autoLoadAfterSaleShops = autoLoadAfterSaleShops;
    }
    //endregion

    //region get/set
    public List<AutoLoadAfterSaleShopDto> getAutoLoadAfterSaleShops() {
        return autoLoadAfterSaleShops;
    }

    public void setAutoLoadAfterSaleShops(List<AutoLoadAfterSaleShopDto> autoLoadAfterSaleShops) {
        this.autoLoadAfterSaleShops = autoLoadAfterSaleShops;
    }
    //endregion
}
