package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.core.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2024/7/25 下午12:00
 */
@Ignore
public class BaseSaveRefundOrderProcessorTest extends BaseAfterSaleOrderTest {

    //region 变量
    /**
     * 随机数生成器
     */
    private final Random random = new Random();

    /**
     * 日期时间格式化器
     */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    //endregion

    //region 测试数据常量

    /**
     * 基础测试数据 - BusinessGetRefundOrderResponseOrderItem
     */
    private static final String BASIC_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_04\",\"refundFee\":100.00,\"refundGoods\":[{\"goodsId\":\"test_goods_001\",\"goodsName\":\"测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"test_buyer\",\"sellerNick\":\"test_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 得物测试数据 - DewuBusinessGetRefundOrderResponseOrderItem
     */
    private static final String DEWU_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_03\",\"refundFee\":299.00,\"refundGoods\":[{\"goodsId\":\"dewu_goods_001\",\"goodsName\":\"得物测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"dewu_buyer\",\"sellerNick\":\"dewu_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 抖音超市测试数据 - DouDianSupermarketBusinessGetRefundOrderResponseOrderItem
     */
    private static final String DOUDIAN_SUPERMARKET_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_04\",\"refundFee\":89.90,\"refundGoods\":[{\"goodsId\":\"doudian_goods_001\",\"goodsName\":\"抖音超市测试商品\",\"refundCount\":2,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"doudian_buyer\",\"sellerNick\":\"doudian_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 天猫国际直营测试数据 - TmallGJZYBusinessGetRefundOrderResponseOrderItem
     */
    private static final String TMALL_GJZY_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_03\",\"refundFee\":199.00,\"refundGoods\":[{\"goodsId\":\"tmall_gjzy_goods_001\",\"goodsName\":\"天猫国际直营测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"tmall_gjzy_buyer\",\"sellerNick\":\"tmall_gjzy_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 诚信通测试数据 - CXTBusinessGetRefundOrderResponseOrderItem
     */
    private static final String CXT_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_04\",\"refundFee\":150.00,\"refundGoods\":[{\"goodsId\":\"cxt_goods_001\",\"goodsName\":\"诚信通测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"cxt_buyer\",\"sellerNick\":\"cxt_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 阿里健康大药房测试数据 - AliJianKangDaYaoFangBusinessGetRefundOrderResponseOrderItem
     */
    private static final String ALI_JIANKANG_DAYAOFANG_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_03\",\"refundFee\":68.80,\"refundGoods\":[{\"goodsId\":\"ali_jkdyf_goods_001\",\"goodsName\":\"阿里健康大药房测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"ali_jkdyf_buyer\",\"sellerNick\":\"ali_jkdyf_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 视频号小店测试数据 - ShiPingHaoXiaoDianBusinessGetRefundOrderResponseOrderItem
     */
    private static final String SHIPINGHAO_XIAODIAN_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_04\",\"refundFee\":39.90,\"refundGoods\":[{\"goodsId\":\"sphxd_goods_001\",\"goodsName\":\"视频号小店测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"sphxd_buyer\",\"sellerNick\":\"sphxd_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 1688数字小店测试数据 - ShuZiXiaoDian1688BusinessGetRefundOrderResponseOrderItem
     */
    private static final String SHUZI_XIAODIAN_1688_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_03\",\"refundFee\":25.50,\"refundGoods\":[{\"goodsId\":\"szxd1688_goods_001\",\"goodsName\":\"1688数字小店测试商品\",\"refundCount\":3,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"szxd1688_buyer\",\"sellerNick\":\"szxd1688_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 菠萝派商城测试数据 - PolyMallBusinessGetRefundOrderResponseOrderItem
     */
    private static final String POLY_MALL_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_04\",\"refundFee\":128.00,\"refundGoods\":[{\"goodsId\":\"poly_mall_goods_001\",\"goodsName\":\"菠萝派商城测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"poly_mall_buyer\",\"sellerNick\":\"poly_mall_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 有赞测试数据 - YouZanBusinessGetRefundOrderResponseOrderItem
     */
    private static final String YOUZAN_TEST_DATA = "[{\"refundId\":\"PLACEHOLDER_REFUND_NO\",\"orderId\":\"PLACEHOLDER_PLAT_ORDER_NO\",\"refundType\":\"JH_03\",\"refundFee\":88.88,\"refundGoods\":[{\"goodsId\":\"youzan_goods_001\",\"goodsName\":\"有赞测试商品\",\"refundCount\":1,\"goodsStatus\":\"BUYER_RECEIVED\"}],\"buyerNick\":\"youzan_buyer\",\"sellerNick\":\"youzan_seller\",\"refundStatus\":\"SUCCESS\"}]";

    /**
     * 占位符常量
     */
    private static final String PLACEHOLDER_REFUND_NO = "PLACEHOLDER_REFUND_NO";
    private static final String PLACEHOLDER_PLAT_ORDER_NO = "PLACEHOLDER_PLAT_ORDER_NO";

    //endregion

    //region 工具方法

    /**
     * 生成随机的退款单号
     * 格式：RF + 时间戳 + 4位随机数
     *
     * @return 随机退款单号
     */
    private String generateRandomRefundNo() {
        String timestamp = LocalDateTime.now().format(dateTimeFormatter);
        int randomSuffix = random.nextInt(9000) + 1000; // 1000-9999
        return "RF" + timestamp + randomSuffix;
    }

    /**
     * 生成随机的平台订单号
     * 格式：PO + 时间戳 + 4位随机数
     *
     * @return 随机平台订单号
     */
    private String generateRandomPlatOrderNo() {
        String timestamp = LocalDateTime.now().format(dateTimeFormatter);
        int randomSuffix = random.nextInt(9000) + 1000; // 1000-9999
        return "PO" + timestamp + randomSuffix;
    }

    /**
     * 生成唯一的UUID字符串（去掉横线）
     *
     * @return UUID字符串
     */
    private String generateUniqueId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 为BusinessGetRefundOrderResponseOrderItem列表随机化refundNo和platOrderNo
     *
     * @param polyOrderList 订单列表
     */
    private void randomizeOrderNumbers(List<BusinessGetRefundOrderResponseOrderItem> polyOrderList) {
        if (polyOrderList == null || polyOrderList.isEmpty()) {
            return;
        }

        for (BusinessGetRefundOrderResponseOrderItem orderItem : polyOrderList) {
            if (orderItem != null) {
                // 随机化退款单号
                String newRefundNo = generateRandomRefundNo();
                orderItem.setRefundNo(newRefundNo);

                // 随机化平台订单号
                String newPlatOrderNo = generateRandomPlatOrderNo();
                orderItem.setPlatOrderNo(newPlatOrderNo);

                // 如果有其他需要随机化的字段，可以在这里添加
                // 例如：orderItem.setOtherField(generateUniqueId());
            }
        }
    }

    /**
     * 加载测试数据并随机化关键字段
     *
     * @param jsonData JSON数据字符串
     * @return 处理后的订单列表
     */
    private List<BusinessGetRefundOrderResponseOrderItem> loadAndRandomizeTestData(String jsonData) {
        // 随机化占位符
        String randomizedJsonData = randomizePlaceholders(jsonData);

        // 反序列化为对象列表
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(
            randomizedJsonData,
            new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {}
        );

        return polyOrderList;
    }

    /**
     * 随机化JSON数据中的占位符
     *
     * @param jsonData 原始JSON数据
     * @return 随机化后的JSON数据
     */
    private String randomizePlaceholders(String jsonData) {
        String result = jsonData;

        // 随机化退款单号占位符
        result = result.replace(PLACEHOLDER_REFUND_NO, generateRandomRefundNo());

        // 随机化平台订单号占位符
        result = result.replace(PLACEHOLDER_PLAT_ORDER_NO, generateRandomPlatOrderNo());

        return result;
    }

    /**
     * 执行保存订单测试的通用方法
     *
     * @param testData JSON测试数据字符串
     * @param memberName 会员名
     * @param shopId 店铺ID
     */
    private void executeSaveOrderTest(String testData, String memberName, int shopId) {
        // 加载并随机化测试数据
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = loadAndRandomizeTestData(testData);

        // 创建上下文
        AfterSaleSaveContext context = getContext(memberName, shopId);

        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor =
            SaveRefundOrderFactory.createProcessor(context);

        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(polyOrderList);

        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("保存操作应该成功", result.isSuccess());

        // 打印随机化后的订单信息（用于调试）
        printRandomizedOrderInfo(polyOrderList);
    }

    /**
     * 打印随机化后的订单信息
     *
     * @param polyOrderList 订单列表
     */
    private void printRandomizedOrderInfo(List<BusinessGetRefundOrderResponseOrderItem> polyOrderList) {
        if (polyOrderList != null && !polyOrderList.isEmpty()) {
            System.out.println("=== 测试数据随机化信息 ===");
            for (int i = 0; i < polyOrderList.size(); i++) {
                BusinessGetRefundOrderResponseOrderItem item = polyOrderList.get(i);
                System.out.printf("订单[%d]: refundNo=%s, platOrderNo=%s%n",
                    i + 1, item.getRefundNo(), item.getPlatOrderNo());
            }
            System.out.println("=========================");
        }
    }

    //endregion

    /**
     * 保存售后单完成流程测试
     */
    @Test
    public void saveOrderTest() {
        executeSaveOrderTest(BASIC_TEST_DATA, "api2017", 1446);
    }

    /**
     * 得物保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/fMA3J8q 得物-售后菠萝派报文分析
     */
    @Test
    public void dewuSaveOrderTest() {
        executeSaveOrderTest(DEWU_TEST_DATA, "api2017", 1446);
    }

    /**
     * 抖音超市保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/MNUXnsl 抖音超市-售后菠萝派报文分析
     */
    @Test
    public void douDianSupermarketSaveOrderTest() {
        executeSaveOrderTest(DOUDIAN_SUPERMARKET_TEST_DATA, "api2017", 1360);
    }

    /**
     * 天猫国际直营保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/c4k8RXK 天猫国际直营-售后菠萝派报文分析
     */
    @Test
    public void tmallGJZYSaveOrderTest() {
        executeSaveOrderTest(TMALL_GJZY_TEST_DATA, "api2017", 1212);
    }

    /**
     * 诚信通保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/0WiqEQC 诚信通-售后菠萝派报文分析
     */
    @Test
    @Ignore
    public void cxtSaveOrderTest() {
        executeSaveOrderTest(CXT_TEST_DATA, "api2017", 96914);
    }

    /**
     * 阿里健康大药房保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/iUSVr9H 阿里健康大药房-售后菠萝派报文分析
     */
    @Test
    public void aliJianKangDaYaoFangSaveOrderTest() {
        executeSaveOrderTest(ALI_JIANKANG_DAYAOFANG_TEST_DATA, "api2017", 1280);
    }

    /**
     * 视频号小店保存售后单完成流程测试 - 测试通过
     * https://s.jkyun.biz/rXFBTAc 视频号小店-退货退款单菠萝派报文分析！
     */
    @Test
    public void shiPingHaoXiaoDianSaveOrderTest() {
        executeSaveOrderTest(SHIPINGHAO_XIAODIAN_TEST_DATA, "api2017", 1382);
    }

    /**
     * 1688数字小店保存售后单完成流程测试
     */
    @Test
    public void shuZiXiaoDian1688SaveOrderTest() {
        executeSaveOrderTest(SHUZI_XIAODIAN_1688_TEST_DATA, "api2017", 1471);
    }

    /**
     * 菠萝派商城保存售后单完成流程测试
     */
    @Test
    public void polyMallSaveOrderTest() {
        executeSaveOrderTest(POLY_MALL_TEST_DATA, "api2017", 96933);
    }

    /**
     * 有赞保存售后单完成流程测试
     */
    @Test
    public void youZanSaveOrderTest() {
        executeSaveOrderTest(YOUZAN_TEST_DATA, "api2017", 1121);
    }
}
