package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPreFiltrationOrderHandle;

/**
 * 退货退款单前置过滤平台级特殊处理
 *
 * <AUTHOR>
 * @date 2024/7/28 下午4:42
 */
public class RefundPlatSpecialFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造
    public RefundPlatSpecialFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem, TargetCovertOrderItem targetOrder) {
        // 供平台级实现
        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return String.format("平台【%s】退货退款单前置过滤平台级特殊处理", context.getPlat().getName());
    }
    //endregion
}
