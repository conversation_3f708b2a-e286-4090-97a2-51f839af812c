package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 原始单（g_api_tradelist）
 *
 * <AUTHOR>
 * @LocalDateTime 2024/7/17 下午7:04
 */
public class ApiTradeDO {
    /**
     * 计算的增长Id
     */
    private int billId;

    /**
     * 平台订单号
     */
    private String tradeNo;

    /**
     * 管家店铺Id
     */
    private Integer shopId;

    /**
     * 0 待过滤 1 等扫描 2 待递交 3 放弃 4 已递交 11 退款 13 部份退款
     */
    private Integer curStatus;

    /**
     * 平台上的订单状态
     */
    private String tradeStatus;

    /**
     * 交易时间
     */
    private LocalDateTime tradeTime;

    /**
     * 网名
     */
    private String customerID;

    /**
     * 收件人姓名
     */
    private String customerName;

    /**
     * 支付ID
     */
    private String payId;

    /**
     * 支付账号
     */
    private String payAccount;

    /**
     * 0 待同步 1 就绪 2 同步失败 3 待校验 4 成功 5 取消 8 在线下单等确认 9拆单未出库 10拆单出库 11拆单发货失败
     */
    private Integer synStatus;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地区
     */
    private String town;

    /**
     * 镇/街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String adr;

    /**
     * 邮编
     */
    private String zip;

    /**
     * 电话
     */
    private String phone;

    /**
     * 手机
     */
    private String mobile;

    /**
     * email
     */
    private String email;

    /**
     * 订单递交号，和管家的订单关联
     */
    private Integer tradeId;

    /**
     * 客户备注
     */
    private String customerRemark;

    /**
     * 客服备注
     */
    private String remark;

    /**
     * 邮资
     */
    private BigDecimal postFee;

    /**
     * 货品金额
     */
    private BigDecimal goodsFee;

    /**
     * 税额
     */
    private BigDecimal taxTotal;

    /**
     * 实际支付总金额
     */
    private BigDecimal totalMoney;

    /**
     * 优惠金额
     */
    private BigDecimal favourableMoney;

    /**
     * 平台佣金
     */
    private BigDecimal commissionValue;

    /**
     * 平台分摊金额
     */
    private BigDecimal platShareTotal;

    /**
     * 货运方式
     */
    private String sndStyle;

    /**
     * 支付方式
     */
    private String chargeType;

    /**
     * 业务员
     */
    private String seller;

    /**
     * QQ
     */
    private String qq;

    /**
     * 补偿退款金额,已废弃不用
     */
    private Double drawBack;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 下载时间
     */
    private LocalDateTime getTime;

    /**
     * 已废弃不用
     */
    private Float dTimeStamp;

    /**
     * 是否是分销，目前淘宝订单才有
     */
    private boolean bFx;

    /**
     * 0 不开票 1 开纸质发票 2 开电子发票 4 纸质专票
     */
    private byte invoiceKind;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 订单来源
     */
    private Integer fromID;

    /**
     * 发货时间
     */
    private LocalDateTime sndTime;

    /**
     * COD（快递代收货款）服务费
     */
    private BigDecimal codServiceFee;

    /**
     * 发货成功或失败写入的信息
     */
    private String synCause;

    /**
     * 等备注
     */
    private byte bDelayForRemark;

    /**
     * 等未付款
     */
    private byte bDelayForOrder;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 订单类型
     */
    private String tradeType;

    /**
     * 也代表平台,但不同于g_api_sysmatch.bTBGoods的枚举
     */
    private Integer apiType;

    /**
     * 分销Id
     */
    private String fxTId;

    /**
     * 0：普通，默认，1：菜鸟仓自动流转订单, 2：统一路由订单,3：奇门自动流转订单,4：BMS强管控业务（商家仓+菜鸟仓）, 80：菜鸟联盟当日达 81：菜鸟联盟次日达 82：预计时效订单 83：菜鸟联盟预约配送 84：菜鸟联盟多日达
     */
    private byte remind;

    /**
     * 是否淘宝物流宝：1 区域零售：2
     */
    private byte bwlb;

    /**
     * 是否特卖1
     */
    private byte bbrandSale;

    /**
     * 是否售罄
     */
    private int bSoldOver;

    /**
     * 是否聚划算
     */
    private byte bTBJHS;

    /**
     * 发货时间
     */
    private LocalDateTime apiSendTime;

    /**
     * 淘宝标记
     */
    private String tbFlag;

    /**
     * 优惠券价格
     */
    private BigDecimal couponPrice;

    /**
     * 颜色标记
     */
    private Integer flagID;

    /**
     * 是否拆分订单
     */
    private int bSplit;

    /**
     * 快递单号
     */
    private String postCode;

    /**
     * 货币
     */
    private String currencyType;

    /**
     * 1:身份证 2：护照 3：港澳通行证
     */
    private Integer cardType;

    /**
     * 证件姓名
     */
    private String cardName;

    /**
     * 证件号码
     */
    private String idCard;

    /**
     * 发货最后截止时间
     */
    private LocalDateTime sndDeadLine;

    /**
     * 支付方式代码
     */
    private String payMethod;

    /**
     * 是否删除过货品
     */
    private byte bDeleteGoods;

    /**
     * 加密类型
     */
    private Integer encryptType;

    /**
     * 追加备注
     */
    private String appendRemark;

    /**
     * 开始等待递交时间
     */
    private LocalDateTime waitPostTime;

    /**
     * 订单收件人加密信息
     */
    private String receiverMaskId;

    /**
     * 订单收件人唯一标识
     */
    private String guid;

    /**
     * oiad加解密单号
     */
    private String oaidSourceCode;

    /**
     * 卖家昵称ID
     */
    private String nickUId;

    //region get/set
    public int getBillId() {
        return billId;
    }

    public void setBillId(int billId) {
        this.billId = billId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Integer getCurStatus() {
        return curStatus;
    }

    public void setCurStatus(Integer curStatus) {
        this.curStatus = curStatus;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public LocalDateTime getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(LocalDateTime tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getCustomerID() {
        return customerID;
    }

    public void setCustomerID(String customerID) {
        this.customerID = customerID;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getPayAccount() {
        return payAccount;
    }

    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }

    public Integer getSynStatus() {
        return synStatus;
    }

    public void setSynStatus(Integer synStatus) {
        this.synStatus = synStatus;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getAdr() {
        return adr;
    }

    public void setAdr(String adr) {
        this.adr = adr;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getTradeId() {
        return tradeId;
    }

    public void setTradeId(Integer tradeId) {
        this.tradeId = tradeId;
    }

    public String getCustomerRemark() {
        return customerRemark;
    }

    public void setCustomerRemark(String customerRemark) {
        this.customerRemark = customerRemark;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getPostFee() {
        return postFee;
    }

    public void setPostFee(BigDecimal postFee) {
        this.postFee = postFee;
    }

    public BigDecimal getGoodsFee() {
        return goodsFee;
    }

    public void setGoodsFee(BigDecimal goodsFee) {
        this.goodsFee = goodsFee;
    }

    public BigDecimal getTaxTotal() {
        return taxTotal;
    }

    public void setTaxTotal(BigDecimal taxTotal) {
        this.taxTotal = taxTotal;
    }

    public BigDecimal getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(BigDecimal totalMoney) {
        this.totalMoney = totalMoney;
    }

    public BigDecimal getFavourableMoney() {
        return favourableMoney;
    }

    public void setFavourableMoney(BigDecimal favourableMoney) {
        this.favourableMoney = favourableMoney;
    }

    public BigDecimal getCommissionValue() {
        return commissionValue;
    }

    public void setCommissionValue(BigDecimal commissionValue) {
        this.commissionValue = commissionValue;
    }

    public BigDecimal getPlatShareTotal() {
        return platShareTotal;
    }

    public void setPlatShareTotal(BigDecimal platShareTotal) {
        this.platShareTotal = platShareTotal;
    }

    public String getSndStyle() {
        return sndStyle;
    }

    public void setSndStyle(String sndStyle) {
        this.sndStyle = sndStyle;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public String getSeller() {
        return seller;
    }

    public void setSeller(String seller) {
        this.seller = seller;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public Double getDrawBack() {
        return drawBack;
    }

    public void setDrawBack(Double drawBack) {
        this.drawBack = drawBack;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public LocalDateTime getGetTime() {
        return getTime;
    }

    public void setGetTime(LocalDateTime getTime) {
        this.getTime = getTime;
    }

    public Float getdTimeStamp() {
        return dTimeStamp;
    }

    public void setdTimeStamp(Float dTimeStamp) {
        this.dTimeStamp = dTimeStamp;
    }

    public boolean isbFx() {
        return bFx;
    }

    public void setbFx(boolean bFx) {
        this.bFx = bFx;
    }

    public byte getInvoiceKind() {
        return invoiceKind;
    }

    public void setInvoiceKind(byte invoiceKind) {
        this.invoiceKind = invoiceKind;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public Integer getFromID() {
        return fromID;
    }

    public void setFromID(Integer fromID) {
        this.fromID = fromID;
    }

    public LocalDateTime getSndTime() {
        return sndTime;
    }

    public void setSndTime(LocalDateTime sndTime) {
        this.sndTime = sndTime;
    }

    public BigDecimal getCodServiceFee() {
        return codServiceFee;
    }

    public void setCodServiceFee(BigDecimal codServiceFee) {
        this.codServiceFee = codServiceFee;
    }

    public String getSynCause() {
        return synCause;
    }

    public void setSynCause(String synCause) {
        this.synCause = synCause;
    }

    public byte getbDelayForRemark() {
        return bDelayForRemark;
    }

    public void setbDelayForRemark(byte bDelayForRemark) {
        this.bDelayForRemark = bDelayForRemark;
    }

    public byte getbDelayForOrder() {
        return bDelayForOrder;
    }

    public void setbDelayForOrder(byte bDelayForOrder) {
        this.bDelayForOrder = bDelayForOrder;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public Integer getApiType() {
        return apiType;
    }

    public void setApiType(Integer apiType) {
        this.apiType = apiType;
    }

    public String getFxTId() {
        return fxTId;
    }

    public void setFxTId(String fxTId) {
        this.fxTId = fxTId;
    }

    public byte getRemind() {
        return remind;
    }

    public void setRemind(byte remind) {
        this.remind = remind;
    }

    public byte getBwlb() {
        return bwlb;
    }

    public void setBwlb(byte bwlb) {
        this.bwlb = bwlb;
    }

    public byte getBbrandSale() {
        return bbrandSale;
    }

    public void setBbrandSale(byte bbrandSale) {
        this.bbrandSale = bbrandSale;
    }

    public int getbSoldOver() {
        return bSoldOver;
    }

    public void setbSoldOver(int bSoldOver) {
        this.bSoldOver = bSoldOver;
    }

    public byte getbTBJHS() {
        return bTBJHS;
    }

    public void setbTBJHS(byte bTBJHS) {
        this.bTBJHS = bTBJHS;
    }

    public LocalDateTime getApiSendTime() {
        return apiSendTime;
    }

    public void setApiSendTime(LocalDateTime apiSendTime) {
        this.apiSendTime = apiSendTime;
    }

    public String getTbFlag() {
        return tbFlag;
    }

    public void setTbFlag(String tbFlag) {
        this.tbFlag = tbFlag;
    }

    public BigDecimal getCouponPrice() {
        return couponPrice;
    }

    public void setCouponPrice(BigDecimal couponPrice) {
        this.couponPrice = couponPrice;
    }

    public Integer getFlagID() {
        return flagID;
    }

    public void setFlagID(Integer flagID) {
        this.flagID = flagID;
    }

    public int getbSplit() {
        return bSplit;
    }

    public void setbSplit(int bSplit) {
        this.bSplit = bSplit;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public LocalDateTime getSndDeadLine() {
        return sndDeadLine;
    }

    public void setSndDeadLine(LocalDateTime sndDeadLine) {
        this.sndDeadLine = sndDeadLine;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public byte getbDeleteGoods() {
        return bDeleteGoods;
    }

    public void setbDeleteGoods(byte bDeleteGoods) {
        this.bDeleteGoods = bDeleteGoods;
    }

    public Integer getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(Integer encryptType) {
        this.encryptType = encryptType;
    }

    public String getAppendRemark() {
        return appendRemark;
    }

    public void setAppendRemark(String appendRemark) {
        this.appendRemark = appendRemark;
    }

    public LocalDateTime getWaitPostTime() {
        return waitPostTime;
    }

    public void setWaitPostTime(LocalDateTime waitPostTime) {
        this.waitPostTime = waitPostTime;
    }

    public String getReceiverMaskId() {
        return receiverMaskId;
    }

    public void setReceiverMaskId(String receiverMaskId) {
        this.receiverMaskId = receiverMaskId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOaidSourceCode() {
        return oaidSourceCode;
    }

    public void setOaidSourceCode(String oaidSourceCode) {
        this.oaidSourceCode = oaidSourceCode;
    }

    public String getNickUId() {
        return nickUId;
    }

    public void setNickUId(String nickUId) {
        this.nickUId = nickUId;
    }
    //endregion
}
