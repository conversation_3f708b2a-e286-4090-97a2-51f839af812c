package com.differ.wdgj.api.user.biz.infrastructure.work.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

/**
 * 工作任务运行结果状态
 *
 * <AUTHOR>
 * @date 2024/9/6 下午3:38
 */
public enum WorkerRunStatusEnum implements ValueEnum, NameEnum {
    RUNNING("执行中", 0, -1),
    SUCCESS("任务成功", 1, 0),
    PARTSUCCESS("部分成功", 2, 1),
    FAILED("任务失败", 3, 2),
    CANCELLED("任务已取消", 4, 3),
    WAITING("排队中", 5, 4),
    BREAK("任务中断", 6, 5),
            ;

    /**
     * 构造函数
     *
     * @param caption    标题名称
     * @param value      后端值
     * @param frontValue 前端值
     */
    WorkerRunStatusEnum(String caption, int value, int frontValue) {
        this.caption = caption;
        this.value = value;
        this.frontValue = frontValue;
    }

    /**
     * 任务运行状态标题。
     */
    private String caption;

    /**
     * 后端任务运行状态值。
     */
    private int value;

    /**
     * 前端对应值。
     */
    private int frontValue;

    /**
     * 获取任务运行状态值。
     *
     * @return 任务运行状态值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取任务运行状态标题。
     *
     * @return 任务运行状态标题
     */
    @Override
    public String getName() {
        return this.caption;
    }

    /**
     * 重载toString方法。
     *
     * @return 任务运行状态值
     */
    @Override
    public String toString() {
        return this.getValue().toString();
    }

    public int getFrontValue() {
        return frontValue;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static WorkerRunStatusEnum create(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return EnumConvertCacheUtil.convert(WorkerRunStatusEnum.class, value, EnumConvertType.VALUE, EnumConvertType.NAME);
    }
}
