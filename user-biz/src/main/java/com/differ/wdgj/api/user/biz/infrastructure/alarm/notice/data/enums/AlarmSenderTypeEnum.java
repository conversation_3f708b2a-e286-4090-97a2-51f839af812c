package com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.sender.IAlarmSender;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.sender.plugins.QyWeixinTextAlarmSender;

/**
 * 报警发送方式枚举
 *
 * <AUTHOR>
 * @date 2025/3/19 下午4:14
 */
public enum AlarmSenderTypeEnum implements ValueEnum {
    /**
     * 企业微信报警
     */
    QY_WEIXIN_TEXT("企业微信Text报警", 0, QyWeixinTextAlarmSender.class),
    /**
     * 短信报警
     */
    SMS("短信报警", 1, null),
    /**
     * 邮件报警
     */
    EMAIL("邮件报警", 2, null)
    ;

    //region 常量
    /**
     * 报警方式描述
     */
    private final String caption;

    /**
     * 对应的发送器类名
     */
    private final int value;

    /**
     * 对应发送者
     */
    private final Class<? extends IAlarmSender> senderClass;
    //endregion

    //region 构造
    AlarmSenderTypeEnum(String caption, Integer value, Class<? extends IAlarmSender> senderClass) {
        this.caption = caption;
        this.value = value;
        this.senderClass = senderClass;
    }
    //endregion

    //region 公共方法

    public String getCaption() {
        return caption;
    }

    public Class<? extends IAlarmSender> getSenderClass() {
        return senderClass;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static AlarmSenderTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, AlarmSenderTypeEnum.class, EnumConvertType.VALUE);
    }
    //endregion
}
