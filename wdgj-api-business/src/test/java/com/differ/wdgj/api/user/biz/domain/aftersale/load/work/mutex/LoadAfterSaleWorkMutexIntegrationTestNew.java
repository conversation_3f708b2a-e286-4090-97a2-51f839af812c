package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.utils.LoadAfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.context.TestPropertySource;

/**
 * LoadAfterSaleWorkMutex 集成测试
 * 专门用于测试与配置键相关的完整业务流程
 * 
 * <AUTHOR>
 * @date 2024/12/19 下午5:00
 */
@TestPropertySource(properties = {
    // 模拟不同的超时配置进行测试
    "config.test.timeout.period=30"
})
public class LoadAfterSaleWorkMutexIntegrationTestNew extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 集成测试：验证消息推送不互斥的完整流程
     * 测试从参数检查到最终创建任务的完整过程
     */
    @Test
    public void integrationTest_MessageNotificationFlow_Complete() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        CreateResult expectedResult = CreateResult.successResult("integration-msg-task-id");
        
        // 模拟数据操作
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("集成测试：消息推送结果不应为空", result);
        Assert.assertTrue("集成测试：应该创建成功", result.success());
        Assert.assertEquals("集成测试：任务ID应该匹配", "integration-msg-task-id", result.getTaskId());
        
        // 验证交互：消息推送应该直接创建，跳过其他检查
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
    }

    /**
     * 集成测试：验证默认配置下的超时检测行为
     * 测试当配置键为默认值时的完整流程
     */
    @Test
    public void integrationTest_DefaultTimeoutConfig_CompleteFlow() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟存在未完成任务的情况
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果：由于默认配置超时时间为0，应该返回null
        Assert.assertNull("集成测试：默认配置下存在未完成任务时应返回null", result);
        
        // 验证完整的调用流程
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        // 验证没有检查超时任务（因为默认超时时间为0）
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
        Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
        
        // 验证配置键的实际调用
        int actualTimeoutPeriod = LoadAfterSaleConfigKeyUtils.getWorkTaskUnRunTimeoutPeriod();
        Assert.assertEquals("集成测试：默认超时时间应为0", 0, actualTimeoutPeriod);
    }

    /**
     * 集成测试：验证父类创建逻辑的完整流程
     * 测试当不存在未完成任务时的创建流程
     */
    @Test
    public void integrationTest_SuperClassCreateFlow_Complete() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        CreateResult expectedResult = CreateResult.successResult("integration-super-task-id");
        
        // 模拟父类逻辑：不存在未完成任务，直接创建
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("集成测试：父类创建结果不应为空", result);
        Assert.assertTrue("集成测试：应该创建成功", result.success());
        Assert.assertEquals("集成测试：任务ID应该匹配", "integration-super-task-id", result.getTaskId());
        
        // 验证完整的调用流程
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
        // 验证没有检查超时任务（因为父类已经返回结果）
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
    }

    /**
     * 集成测试：验证所有枚举组合的业务逻辑
     * 测试不同触发类型和加载类型组合的完整处理流程
     */
    @Test
    public void integrationTest_AllEnumCombinations_BusinessLogic() {
        TriggerTypeEnum[] triggerTypes = {
            TriggerTypeEnum.HAND, 
            TriggerTypeEnum.AUTO, 
            TriggerTypeEnum.MESSAGE_NOTIFICATION
        };
        AfterSaleLoadTypeEnum[] loadTypes = {
            AfterSaleLoadTypeEnum.SHOP_LOAD, 
            AfterSaleLoadTypeEnum.TIME_RANGE, 
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        };
        
        for (TriggerTypeEnum triggerType : triggerTypes) {
            for (AfterSaleLoadTypeEnum loadType : loadTypes) {
                // 重置mock以确保测试独立性
                Mockito.reset(mockDataOperate);
                
                WorkData<AfterSaleLoadArgs> workData = createWorkData(triggerType, loadType);
                String expectedTaskId = String.format("integration-%s-%s", 
                    triggerType.name().toLowerCase(), 
                    loadType.name().toLowerCase());
                CreateResult expectedResult = CreateResult.successResult(expectedTaskId);
                
                if (triggerType == TriggerTypeEnum.MESSAGE_NOTIFICATION && 
                    loadType == AfterSaleLoadTypeEnum.AFTER_SALE_NO) {
                    // 特殊情况：消息推送 + 售后单号，直接创建
                    Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                    
                    CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                    
                    Assert.assertNotNull(String.format("集成测试：%s+%s应该直接创建", triggerType, loadType), result);
                    Assert.assertTrue("应该创建成功", result.success());
                    Assert.assertEquals("任务ID应该匹配", expectedTaskId, result.getTaskId());
                    
                    // 验证直接创建，跳过其他检查
                    Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                    Mockito.verify(mockDataOperate, Mockito.never()).existsUnComplete(workData);
                } else {
                    // 一般情况：通过父类逻辑处理
                    Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
                    Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                    
                    CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                    
                    Assert.assertNotNull(String.format("集成测试：%s+%s应该通过父类创建", triggerType, loadType), result);
                    Assert.assertTrue("应该创建成功", result.success());
                    Assert.assertEquals("任务ID应该匹配", expectedTaskId, result.getTaskId());
                    
                    // 验证父类逻辑调用
                    Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
                    Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                }
            }
        }
    }

    /**
     * 集成测试：验证配置键的实际行为
     * 测试LoadAfterSaleConfigKeyUtils的真实调用和返回值
     */
    @Test
    public void integrationTest_ConfigKeyUtils_RealBehavior() {
        // 直接调用配置工具类，验证其真实行为
        int timeoutPeriod = LoadAfterSaleConfigKeyUtils.getWorkTaskUnRunTimeoutPeriod();
        
        // 验证默认配置值
        Assert.assertTrue("集成测试：超时时间应该是非负数", timeoutPeriod >= 0);
        
        // 准备测试数据，模拟存在未完成任务的场景
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
        
        if (timeoutPeriod > 0) {
            // 如果配置了超时时间，模拟超时任务检查
            Mockito.when(mockDataOperate.existsUnRunTimeout(workData, timeoutPeriod)).thenReturn(false);
            
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            Assert.assertNull("集成测试：存在未完成任务但无超时任务时应返回null", result);
            Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnRunTimeout(workData, timeoutPeriod);
        } else {
            // 如果超时时间为0，不应该检查超时任务
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            
            Assert.assertNull("集成测试：超时时间为0时应返回null", result);
            Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
        }
    }

    /**
     * 集成测试：验证边界条件的完整处理
     * 测试各种边界情况下的系统行为
     */
    @Test
    public void integrationTest_BoundaryConditions_CompleteHandling() {
        // 测试1：args为null的情况
        WorkContext workContext = WorkContext.of(
            "integration-test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            9999, 
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            "integration-test-creator"
        );
        WorkData<AfterSaleLoadArgs> nullArgsWorkData = WorkData.of(workContext, null);
        CreateResult expectedResult = CreateResult.successResult("integration-null-args-task");
        
        // 模拟父类逻辑
        Mockito.when(mockDataOperate.existsUnComplete(nullArgsWorkData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(nullArgsWorkData)).thenReturn(expectedResult);
        
        CreateResult result = loadAfterSaleWorkMutex.create(nullArgsWorkData, mockDataOperate);
        
        Assert.assertNotNull("集成测试：args为null时应通过父类逻辑处理", result);
        Assert.assertTrue("应该创建成功", result.success());
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(nullArgsWorkData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(nullArgsWorkData);
        
        // 重置mock
        Mockito.reset(mockDataOperate);
        
        // 测试2：WorkContext中的各种字段
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        
        // 验证WorkContext的字段
        Assert.assertEquals("会员名应该匹配", "integration-test-member", workData.getMemberName());
        Assert.assertEquals("工作类型应该匹配", WorkEnum.LOAD_AFTER_SALE, workData.getWorkType());
        Assert.assertEquals("店铺ID应该匹配", Integer.valueOf(9999), workData.getShopId());
        Assert.assertEquals("触发类型应该匹配", TriggerTypeEnum.AUTO, workData.getWorkContext().getTriggerType());
        Assert.assertEquals("创建者应该匹配", "integration-test-creator", workData.getWorkContext().getCreator());
    }

    /**
     * 集成测试：验证异常情况的处理
     * 测试系统在异常情况下的健壮性
     */
    @Test
    public void integrationTest_ExceptionHandling_Robustness() {
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 测试1：dataOperate.existsUnComplete抛出异常
        Mockito.when(mockDataOperate.existsUnComplete(workData))
               .thenThrow(new RuntimeException("数据库连接异常"));
        
        try {
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            Assert.fail("集成测试：应该抛出异常");
        } catch (RuntimeException e) {
            Assert.assertEquals("异常信息应该匹配", "数据库连接异常", e.getMessage());
        }
        
        // 重置mock
        Mockito.reset(mockDataOperate);
        
        // 测试2：dataOperate.create抛出异常
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData))
               .thenThrow(new RuntimeException("任务创建失败"));
        
        try {
            CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
            Assert.fail("集成测试：应该抛出异常");
        } catch (RuntimeException e) {
            Assert.assertEquals("异常信息应该匹配", "任务创建失败", e.getMessage());
        }
    }

    /**
     * 创建测试用的WorkData对象
     * 使用集成测试专用的标识符
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "integration-test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            9999, 
            triggerType, 
            "integration-test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);
        
        return WorkData.of(workContext, args);
    }
}
