package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.HashCodeBizTypeEnum;

import java.time.LocalDateTime;

/**
 * 业务hash校验表(g_api_tradecode)
 *
 * <AUTHOR>
 * @date 2024-07-01 19:12
 */
public class ApiTradeCodeDO {
    /**
     * 主键
     */
    private Long recId;

    /**
     * 外部店铺id
     */
    private Integer shopId;

    /**
     * 业务类型
     * {@link HashCodeBizTypeEnum}
     */
    private Integer bizType;

    /**
     * 业务主键
     */
    private int billId;

    /**
     * 业务单号
     */
    private String tradeNo;

    /**
     * hashcode
     */
    private String hashCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    //region 构造
    public ApiTradeCodeDO() {
    }

    public ApiTradeCodeDO(Integer shopId, Integer bizType, String tradeNo, String hashCode) {
        this.shopId = shopId;
        this.bizType = bizType;
        this.tradeNo = tradeNo;
        this.hashCode = hashCode;
    }
    //endregion

    //region get/set

    public Long getRecId() {
        return recId;
    }

    public void setRecId(Long recId) {
        this.recId = recId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public int getBillId() {
        return billId;
    }

    public void setBillId(int billId) {
        this.billId = billId;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    //endregion
}
