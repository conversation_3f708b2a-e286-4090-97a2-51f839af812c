'可访问性说明
'private -
'protect #
'package private ~
'public +

'类关系说明参见：类图6大关系使用说明.puml

'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

'缓存类图设计

@startuml

interface IDataCache {

}

interface IMemoryDataCache <<支持定时刷入内存的缓存>>{
    int syncAllToMemory() ：刷新数据到本地内存
    List<T> getAllFromMemory()：获取所有内存数据
    List<T> getDataFromMemory()：根据条件查询内存数据
}

interface IHashDataCache <<Hash缓存>>{
    int syncAllToCache() :自动同步所有缓存
    List<T> hashGetAll()：获取所有缓存数据
    T hashGet()：查询单个缓存数据
}

interface ISetDataCache <<Set缓存>>{
}

interface MultiRedis <<支持多库的Redis缓存组件接口>>{
}

abstract class AbstractCache <<缓存顶层抽象类>>{
    MultiRedis cacher
}

abstract class AbstractHashCache <<Hash缓存>>{

    int syncAll() :自动同步所有缓存
    #List<T> getAllSourceData(String... cacheKeyArgs)
}
abstract class AbstractSetCache <<set缓存>>{
}
abstract class AbstractMemoryHashCache <<定时刷入内存的Hash缓存>> {
    #boolean isNeedRefurData() : override
    #int refurDataToMemory() : Override
    #protected String getLastRefurTime()
    #void setLastRefurTime(String lastRefurTime)
}
class DistributedLockCache <<分布式锁>>
abstract class AbstractStringCache <<支持按需的String缓存>>

IDataCache <|.. AbstractCache
IDataCache <|.. IHashDataCache
IDataCache <|.. ISetDataCache
IDataCache <|.. IMemoryDataCache
IHashDataCache <|.. AbstractHashCache
ISetDataCache <|.. AbstractSetCache
IMemoryDataCache <|.. AbstractMemoryHashCache
AbstractHashCache<|-- AbstractMemoryHashCache


AbstractCache <|-- AbstractHashCache
AbstractCache <|-- AbstractSetCache
AbstractCache <|-- DistributedLockCache
AbstractCache <|-- AbstractStringCache

MultiRedis <-- AbstractCache :关联（代码体现：成员变量）

@enduml