package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.create;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadSubTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;

import java.util.List;

/**
 * 创建子任务接口
 *
 * <AUTHOR>
 * @date 2024/9/20 上午11:20
 */
public interface ICreateSubTask {
    /**
     * 创建子任务
     *
     * @param workData 工作任务数据
     * @return 返回子任务集合
     */
    List<AfterSaleLoadSubTask> create(WorkData<AfterSaleLoadArgs> workData);
}
