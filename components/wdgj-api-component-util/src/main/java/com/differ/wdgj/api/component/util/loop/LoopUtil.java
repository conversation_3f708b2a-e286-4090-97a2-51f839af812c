package com.differ.wdgj.api.component.util.loop;

import com.differ.wdgj.api.component.util.functional.zero.Action;
import com.differ.wdgj.api.component.util.functional.zero.Function;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 预防循环递归的工具类
 * 使用示例:LoopUtil.avoidRecursion(() -> { 业务逻辑 }, "定位相同调用源的key");
 *
 * <AUTHOR>
 * @date 2024/5/7 11:40
 */
public class LoopUtil {

    /**
     * 全局的历史调用
     */
    private static Map<Object, Boolean> globalCall = new ConcurrentHashMap<>();

    /**
     * 线程粒度的历史调用
     */
    private static ThreadLocal<Map<Object, Boolean>> historyThreadCall = ThreadLocal.withInitial(() -> new HashMap<>());

    /**
     * 避免循环递归
     *
     * @param fun     检测的方法
     * @param sources 调用源信息
     */
    public static void avoidThreadRecursion(Action fun, Object... sources) {
        // 当前线程的历史调用
        Map<Object, Boolean> called = null;
        // 当前线程是否调用fun完成
        boolean finish = false;
        // 调用源信息包装，不同的调用函数表示不同调用源
        LoopSources source = new LoopSources(AvoidLoopFun.AVOID, sources);
        try {
            called = historyThreadCall.get();

            if (!called.containsKey(source)) {
                called.put(source, true);
                // fun同步执行完成后要设置完成标记
                try {
                    fun.exec();
                    finish = true;
                    return;
                } catch (Throwable e) {
                    finish = true;
                    throw e;
                }
            }else {
                System.err.println("线程内循环递归调用："+ source);
            }
        } finally {
            if (finish) {
                // 当前线程调用fun完成后才能释放
                if (called != null) {
                    called.remove(source);
                }
                if (MapUtils.isEmpty(called)) {
                    historyThreadCall.remove();
                }
            }
        }
    }

    /**
     * 避免循环递归
     *
     * @param fun          检测的方法
     * @param defaultValue 检测到循环递归时的默认返回值
     * @param sources      调用源信息
     * @param <R>
     * @return fun的返回值
     */
    public static <R> R avoidThreadRecursion(Function<R> fun, R defaultValue, Object... sources) {
        // 当前线程的历史调用
        Map<Object, Boolean> called = null;
        // 当前线程是否调用fun完成
        boolean finish = false;
        // 调用源信息包装，不同的调用函数表示不同调用源
        LoopSources source = new LoopSources(AvoidLoopFun.AVOID_DEFAULT, sources);

        try {
            called = historyThreadCall.get();

            if (!called.containsKey(source)) {
                called.put(source, true);
                // fun同步执行完成后要设置完成标记
                try {
                    R r = fun.exec();
                    finish = true;
                    return r;
                } catch (Throwable e) {
                    finish = true;
                    throw e;
                }
            }
            System.err.println("单线程内检测到循环调用："+ source+"，默认返回："+defaultValue);
            return defaultValue;
        } finally {
            if (finish) {
                // 当前线程调用fun完成后才能释放
                if (called != null) {
                    called.remove(source);
                }
                if (MapUtils.isEmpty(called)) {
                    historyThreadCall.remove();
                }
            }
        }
    }

    /**
     * 避免循环递归
     *
     * @param fun     检测的方法
     * @param sources 调用源信息
     * @param <R>
     * @return fun的返回值
     */
    public static <R> R avoidThreadRecursionThrowable(Function<R> fun, Object... sources) {
        // 当前线程的历史调用
        Map<Object, Boolean> called = null;
        // 当前线程是否调用fun完成
        boolean finish = false;
        // 调用源信息包装，不同的调用函数表示不同调用源
        LoopSources source = new LoopSources(AvoidLoopFun.AVOID_THROWABLE, sources);

        try {
            called = historyThreadCall.get();

            if (!called.containsKey(source)) {
                called.put(source, true);
                // fun同步执行完成后要设置完成标记
                try {
                    R r = fun.exec();
                    finish = true;
                    return r;
                } catch (Throwable e) {
                    finish = true;
                    throw e;
                }
            }

            System.err.println("单线程内检测到循环调用，默认异常："+ source);
            throw new RuntimeException("单线程内检测到循环调用");
        } finally {
            if (finish) {
                // 当前线程调用fun完成后才能释放
                if (called != null) {
                    called.remove(source);
                }
                if (MapUtils.isEmpty(called)) {
                    historyThreadCall.remove();
                }
            }
        }
    }

    /**
     * 避免循环递归
     *
     * @param fun     检测的方法
     * @param sources 调用源信息
     */
    public static void avoidGlobalRecursion(Action fun, Object... sources) {
        // 当前线程的历史调用
        Map<Object, Boolean> called = null;
        // 当前线程是否调用fun完成
        boolean finish = false;
        // 调用源信息包装，不同的调用函数表示不同调用源
        LoopSources source = new LoopSources(AvoidLoopFun.AVOID, sources);
        try {
            called = globalCall;

            if (!called.containsKey(source)) {
                called.put(source, true);
                // fun同步执行完成后要设置完成标记
                try {
                    fun.exec();
                    finish = true;
                    return;
                } catch (Throwable e) {
                    finish = true;
                    throw e;
                }
            }else {
                System.err.println("全局线程内循环递归调用："+ source);
            }
        } finally {
            if (finish) {
                // 当前线程调用fun完成后才能释放
                if (called != null) {
                    called.remove(source);
                }
            }
        }
    }

    /**
     * 避免循环递归
     *
     * @param fun          检测的方法
     * @param defaultValue 检测到循环递归时的默认返回值
     * @param sources      调用源信息
     * @param <R>
     * @return fun的返回值
     */
    public static <R> R avoidGlobalRecursion(Function<R> fun, R defaultValue, Object... sources) {
        // 当前线程的历史调用
        Map<Object, Boolean> called = null;
        // 当前线程是否调用fun完成
        boolean finish = false;
        // 调用源信息包装，不同的调用函数表示不同调用源
        LoopSources source = new LoopSources(AvoidLoopFun.AVOID_DEFAULT, sources);

        try {
            called = globalCall;

            if (!called.containsKey(source)) {
                called.put(source, true);
                // fun同步执行完成后要设置完成标记
                try {
                    R r = fun.exec();
                    finish = true;
                    return r;
                } catch (Throwable e) {
                    finish = true;
                    throw e;
                }
            }

            System.err.println("全局线程内循环递归调用："+ source+"，默认返回："+defaultValue);
            return defaultValue;
        } finally {
            if (finish) {
                // 当前线程调用fun完成后才能释放
                if (called != null) {
                    called.remove(source);
                }
            }
        }
    }

    /**
     * 避免循环递归
     *
     * @param fun     检测的方法
     * @param sources 调用源信息
     * @param <R>
     * @return fun的返回值
     */
    public static <R> R avoidGlobalRecursionThrowable(Function<R> fun, Object... sources) {
        // 当前线程的历史调用
        Map<Object, Boolean> called = null;
        // 当前线程是否调用fun完成
        boolean finish = false;
        // 调用源信息包装，不同的调用函数表示不同调用源
        LoopSources source = new LoopSources(AvoidLoopFun.AVOID_THROWABLE, sources);

        try {
            called = globalCall;

            if (!called.containsKey(source)) {
                called.put(source, true);
                // fun同步执行完成后要设置完成标记
                try {
                    R r = fun.exec();
                    finish = true;
                    return r;
                } catch (Throwable e) {
                    finish = true;
                    throw e;
                }
            }

            System.err.println("全局线程范围内检测到循环调用，默认异常："+ source);
            throw new RuntimeException("全局线程范围内检测到循环调用");
        } finally {
            if (finish) {
                // 当前线程调用fun完成后才能释放
                if (called != null) {
                    called.remove(source);
                }
            }
        }
    }

    public static void main(String[] args) {
        Map<Object, Boolean> called = new HashMap<>();
        Object log = new Object();

        called.put(new LoopSources(AvoidLoopFun.AVOID_THROWABLE, new Object[]{log}), true);
        Object[] objects = {log};
        System.out.println(called.containsKey(new LoopSources(AvoidLoopFun.AVOID_THROWABLE, objects)));
    }
}


