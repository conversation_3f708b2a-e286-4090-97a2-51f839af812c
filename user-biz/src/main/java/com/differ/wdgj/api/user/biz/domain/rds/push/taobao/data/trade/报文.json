{"trade_fullinfo_get_response": {"trade": {"buyer_open_uid": "AAHk5d-EAAeGwJedwSFu0XXX", "title": "麦包包", "type": "fixed(一口价)", "created": "2000-01-01 00:00:00", "sid": "2231958349", "tid": 2231958349, "acookie_id": "1a11a0987628772c002ec7d0eec2a352", "seller_rate": true, "buyer_rate": true, "status": "TRADE_NO_CREATE_PAY", "payment": "200.07", "discount_fee": "200.07", "adjust_fee": "200.07", "post_fee": "200.07", "total_fee": "200.07", "pay_time": "2000-01-01 00:00:00", "end_time": "2000-01-01 00:00:00", "modified": "2000-01-01 00:00:00", "consign_time": "2000-01-01 00:00:00", "received_payment": "200.07", "commission_fee": "200.07", "buyer_memo": "上衣要大一号", "seller_memo": "好的", "buyer_area": "浙江省杭州市", "alipay_no": "2009112081173831", "buyer_message": "要送的礼物的，不要忘记的哦", "pic_path": "http://img08.taobao.net/bao/uploaded/i8/T1jVXXXePbXXaoPB6a_091917.jpg", "num_iid": 3424234, "price": "200.07", "cod_fee": "12.07", "cod_status": "EW_CREATED(订单已创建)", "buyer_cod_fee": "12.07", "seller_cod_fee": "12.07", "express_agency_fee": "212.07", "shipping_type": "free", "num": 1, "point_fee": 0, "real_point_fee": 0, "buyer_obtain_point_fee": 0, "buyer_alipay_no": "<EMAIL>", "receiver_name": "**东方", "receiver_country": "中国", "receiver_state": "浙江省", "receiver_city": "杭州市", "receiver_district": "西湖区", "receiver_town": "三墎镇", "receiver_address": "***淘宝城911号", "receiver_zip": "223700", "receiver_mobile": "****1826", "receiver_phone": "****5372", "buyer_email": "<EMAIL>", "seller_alipay_no": "<EMAIL>", "seller_mobile": "13512501826", "seller_phone": "13512501826", "seller_name": "我在测试", "seller_email": "<EMAIL>", "available_confirm_fee": "200.07", "has_post_fee": true, "timeout_action_time": "2000-01-01 00:00:00", "snapshot_url": "T1mURbXopZXXXe3rLI.1257513712679_snap", "trade_memo": "这是一次成功的交易", "promotion": "hello world", "is_3D": true, "is_lgtype": true, "is_brand_sale": true, "is_force_wlb": true, "buyer_flag": 1, "seller_flag": 1, "credit_card_fee": "30.5", "step_trade_status": "FRONT_NOPAID_FINAL_NOPAID", "step_paid_fee": "525.70", "eticket_ext": "525.70", "mark_desc": "该订单需要延长收货时间", "has_yfx": true, "yfx_fee": "0.50", "yfx_id": "135751716473138", "yfx_type": "<PERSON><PERSON><PERSON><PERSON>", "send_time": "2000-01-01", "can_rate": true, "seller_can_rate": true, "is_part_consign": true, "is_daixiao": true, "is_wt": true, "arrive_interval": 1, "arrive_cut_time": "16:00", "consign_interval": 48, "o2o": "crm", "o2o_guide_id": "123456", "o2o_guide_name": "西湖门店导购员1", "o2o_shop_id": "123456", "o2o_shop_name": "西湖门店", "o2o_delivery": "inshop", "o2o_out_trade_id": "123456", "shop_code": "demo", "hk_en_name": "chen<PERSON><PERSON>", "hk_flight_no": "GZ0022", "hk_china_name": "陈小", "hk_card_code": "G0000000001", "hk_card_type": "证件类型", "hk_flight_date": "2014-12-01 15:50", "hk_gender": "M", "hk_birthday": "1988-01-12", "hk_pickup": "某地点", "hk_pickup_id": "3", "est_con_time": "付款后30天内", "orders": {"order": [{"title": "山寨版测试机器", "pic_path": "http://img08.taobao.net/bao/uploaded/i8/T1jVXXXePbXXaoPB6a_091917.jpg", "price": "200.07", "num_iid": 2342344, "sku_id": "5937146", "outer_iid": "152e442aefe88dd41cb0879232c0dcb0", "outer_sku_id": "81893848", "refund_status": "SUCCESS(退款成功)", "status": "TRADE_NO_CREATE_PAY", "oid": 2231958349, "total_fee": "200.07", "payment": "200.07", "discount_fee": "200.07", "adjust_fee": "1.01", "divide_order_fee": "21.00", "part_mjz_discount": "21.00", "sku_properties_name": "颜色:桔色;尺码:M", "item_meal_id": 2564854632, "item_meal_name": "M8原装电池:便携支架:M8专用座充:莫凡保护袋", "num": 1, "snapshot_url": "T1mURbXopZXXXe3rLI.1257513712679_snap", "timeout_action_time": "2000-01-01 00:00:00", "item_memo": "这是商品备注", "buyer_rate": true, "seller_rate": true, "refund_id": "2231958349", "seller_type": "B（商城商家）", "cid": 123456, "is_oversold": true, "end_time": "2012-04-07 00:00:00", "order_from": "jhs", "is_service_order": true, "consign_time": "2013-01-13 15:23:00", "shipping_type": "post", "logistics_company": "顺风快递", "invoice_no": "05432465", "bind_oid": 23194074143138, "is_daixiao": true, "ticket_outer_id": "123456abcd", "ticket_expdate_key": "100FFFFFF02374020000002001020304000A", "is_www": true, "store_code": "南京QDHEWL-0004", "tmser_spu_code": "家装干支装服务", "sub_order_tax_fee": "0", "sub_order_tax_rate": "0", "et_ser_time": "2015-04-15 13:00", "et_shop_name": "测试预约门店地址", "et_verified_shop_name": "测试核销门店地址", "et_plate_number": "浙A11111", "estimate_con_time": "demo", "bind_oids": "***************,***************", "zhengji_status": "1", "md_qualification": "true_免单原因", "md_fee": "999", "customization": "{   \"itemId\": 123,   \"skuId\": 123,   \"text\": [     {       \"id\": 44,       \"content\": \"home\"     }   ],   \"pic\": [     {       \"id\": 44,       \"url\": \"sn\"     }   ] ,\"dingzhi\":\";pluginId:182;dingzhiId:157886;\"}", "inv_type": "6", "is_sh_ship": true, "shipper": "cn", "f_type": "jsd", "f_status": "分单完成", "f_term": "storeId", "combo_id": "1923423423", "order_attr": "{\"isFreeDownPayment\":\"true\",\"backPayment\":\"3000.00\",\"refActivityId\":\"*********\"}", "assembly_rela": "123123", "assembly_price": "*********", "assembly_item": "123,456", "sub_order_tax_promotion_fee": "0", "down_payment": "1000", "down_payment_ratio": "1000", "month_payment": "1000", "tail_payment": "1000", "installment_num": "1000", "penalty": "1000", "service_fee": "1000", "car_taker": "1000", "car_taker_phone": "1000", "car_taker_id": "1000", "car_store_code": "1000", "car_store_name": "1000", "out_unique_id": "1000", "ws_bank_apply_no": "1000", "oid_str": "123", "fqg_num": 12, "is_fqg_s_fee": true, "tax_free": true, "tax_coupon_discount": "0", "nr_reduce_inv_fail": "1", "nr_outer_iid": "**********", "bind_oids_all_status": "***************,***************", "sort_info": {"day_sort_val": 100, "hour_sort_val": 100}, "o2o_guide_id": "12345", "o2o_guide_name": "abc", "o2o_shop_id": "12345", "o2o_shop_name": "abc", "biz_code": "tmall.daogoubao.cloudstore", "cloud_store": "1", "hj_settle_no_commission": "1", "order_taking": "接单标记", "cloud_store_token": "11111", "cloud_store_bind_pos": "12345", "retail_store_id": "123415", "out_item_id": "12345", "rt_omni_outer_sc_id": "12345", "rt_omni_sc_id": "12345", "modify_address": "1", "ti_modify_address_time": "2019-02-18 11:32:53", "credit_buy": "3", "s_tariff_fee": "0", "timing_promise": "tmallPromise", "promise_service": "tmallpromise.arrival.timing,tmallpromise.xx.timing", "es_date": "格式yyyy-MM-dd", "es_range": "格式 hh:mm-hh:mm", "os_date": "格式yyyy-MM-dd", "os_range": "格式 hh:mm-hh:mm", "cutoff_minutes": "660", "es_time": "1", "delivery_time": "2019-04-12 16:00:00", "collect_time": "2019-04-12 16:00:00", "dispatch_time": "2019-04-12 16:00:00", "sign_time": "2019-04-12 16:00:00", "promise_end_time": "格式yyyy-MM-dd HH:mm", "os_activity_id": "2734957495", "os_fg_item_id": "58901376862", "os_gift_count": "1", "os_sort_num": "10", "propoint": "10", "is_kaola": true, "special_refund_type": "2", "extend_info": "{\"itemTag\":\"100\"}", "is_devalue_fee": true, "brand_light_shop_store_id": "123456", "brand_light_shop_source": "scode", "service_order_type": "1", "service_outer_id": "sunin_outid_324324324", "expand_card_expand_price_used_suborder": "10.01", "expand_card_basic_price_used_suborder": "10.01", "lijian": "10.01", "auto_flow": "0/1", "trade_fulfillment_type": 1, "distribute_status": "distribute", "is_idle": "1", "gift_mids": "22331764490705", "is_free_gift": true, "has_gift": true, "promise_collect_time": "2019-04-12 16:00:00", "is_force_dc": true, "b2b_daixiao": "1", "ship_info": {"ship_info": [{"transport_type": "express,seller,instant等"}]}, "combine_item_info": {"combine_sub_item_d_o": [{"item_id": 123123, "sku_id": 22331764490705, "quantity": 1, "item_name": "商品名称", "outer_iid": "152e442aefe88dd41cb0879232c0dcb0", "sku_title": "16g", "origin_fee": 123, "combine_sub_item_fee": 123, "ismain": true, "estcon_time": "7 / 2023-02-15", "outer_sku_id": "123", "edu_original_fee": 123, "apple_cc": "{}"}]}, "need_return": true, "bundle_id": "123", "bundle_out_name": "Apple", "bundle_type": 1, "one_yuan_reservation_orders": "3330286058340981615,3330286058340981614", "is_jzfx": true, "jewcc_no": "TBJA34876579730796583190", "is_bybt_order": false, "apple_cc": "{}", "bybt_sn_code_tag": "{\"t\":1, \"s\":[]}", "recycleOrderId": "4057146831098598118", "gov_zhaoshangpi": "558715580168_N_gradeOne_02", "consign_due_time": "2_7", "ypds_platform_order_id": "4129314015003153704", "ypds_platform_pay_order_id": "2024111523001107821426437126", "ypds_order_supply_price": "0.10", "ypds_order_type": "1", "use_gov_subsidy_new": "1", "gov_subsidy_amount_new": "230", "is_inds_qyg": true, "gov_sn_check": "1_1_0", "qn_distr": "1", "gov_energy_level": "1", "gov_main_subject": "测试公司", "czhm": "123abc", "is_apple_channel_edu_order": true}]}, "promotion_details": {"promotion_detail": [{"id": 22331764490705, "promotion_name": "满就减钱", "discount_fee": "10.00", "gift_item_name": "晨光签字笔", "gift_item_id": "13233791195", "gift_item_num": "1", "promotion_desc": "双11促销，满就送", "promotion_id": "mjs", "kd_discount_fee": "10.00", "kd_child_discount_fee": "22331764490703|5.00,22331764490704|5.00"}]}, "service_tags": {"logistics_tag": [{"order_id": "123456", "logistic_service_tag_list": {"logistic_service_tag": [{"service_tag": "comFee=1211;comTim=1;companyCode=SF;", "service_type": "FAST"}]}}]}, "service_orders": {"service_order": [{"oid": 110770592823138, "item_oid": 110770592803138, "service_id": 2342344, "service_detail_url": "http://wt.taobao.com/plan.htm?plan_id=planId", "num": 1, "price": "39.09", "payment": "29.98", "title": "滚筒洗衣机", "total_fee": "39.09", "buyer_nick": "小倩2005", "refund_id": "2231958349", "seller_nick": "麦包包", "pic_path": "http://img08.taobao.com/bao/upload/i8/T1jVXXXePb_a0908.jpg", "tmser_spu_code": "家装干支装服务", "et_ser_time": "2015-04-15 13:00", "et_shop_name": "测试预约门店地址", "et_verified_shop_name": "测试核销门店地址", "et_plate_number": "浙A11111", "oid_str": "110770592823138", "apple_care_email": "1", "apple_care_mpn": "1", "service_outer_id": "suni_outid_23432", "service_order_type": "1", "ext_service_biz_id": "23423_324234,2324_342342", "combine_sub_item_id": 123, "combine_sub_item_sku_id": 123, "comm_amount_unit": "123"}]}, "trade_from": "WAP,JHS", "trade_source": "ownshop", "zero_purchase": true, "alipay_point": 1, "pcc_af": 1, "trade_ext": {"before_enable_flag": 1, "before_close_flag": 1, "before_pay_flag": 1, "before_ship_flag": 1, "before_confirm_flag": 1, "before_rate_flag": 1, "before_refund_flag": 1, "before_modify_flag": 1, "third_party_status": 1, "extra_data": "abc", "ext_attributes": "abc"}, "order_tax_fee": "0", "et_ser_time": "2015-04-15 13:00", "et_shop_name": "接口测试新增门店_0304055043555", "et_verified_shop_name": "测试核销门店地址", "et_plate_number": "浙A11111", "o2o_snatch_status": "1", "is_sh_ship": true, "eticket_service_addr": "地址信息", "et_type": "bs_common", "market": "eticket", "obs": "1", "et_shop_id": 66749068, "paid_coupon_fee": "10.00", "shop_pick": "1", "rx_audit_status": "0", "es_date": "直接下单预计时间", "es_range": "直接下单预计时间", "os_date": "预约下单时间", "os_range": "预约下单时间", "coupon_fee": 1.111, "post_gate_declare": true, "cross_bonded_declare": true, "trade_attr": "{\\\"payCurrency\\\":\\\"HKD\\\"}", "top_hold": 0, "omni_attr": "{}", "omni_param": "{}", "assembly": "1", "forbid_consign": 1, "identity": "EPP", "team_buy_hold": 1, "share_group_hold": 1, "ofp_hold": 1, "o2o_step_trade_detail": "3_1_100_paid", "o2o_step_order_id": "2505550397798727", "o2o_et_order_id": "2505550397798727", "o2o_voucher_price": "1000", "order_tax_promotion_fee": "0", "delay_create_delivery": 1, "tid_str": "123", "toptype": 1, "service_type": "timedd,jiaju", "o2o_service_mobile": "17802118895", "o2o_service_name": "衍一", "o2o_service_state": "浙江省", "o2o_service_city": "杭州市", "o2o_service_district": "余杭区", "o2o_service_town": "五常街道", "o2o_service_address": "杭州市余杭区文一西路969号", "o2o_step_trade_detail_new": "bizType:分阶段付款交易,orderPayChannel:支付宝在线支付,subOrderPayTime:step 0: payTime Sun Jul 30 17:08:50 CST 2017    step 1: payTime Sat Aug 05 04:07:14 CST 2017    ,totalStep:2,currStep:2,fee:60300,payStatus:paid", "o2o_xiaopiao": "123", "o2o_contract": "3213", "recharge_fee": "100", "retail_store_code": "123", "retail_out_order_id": "123", "platform_subsidy_fee": "100.00", "nr_offline": "1", "wtt_param": "json格式", "logistics_infos": {"logistics_info": [{"trade_id": 1100070860172581613, "sub_trade_id": 1100070860173581613, "item_id": "2323232", "item_code": "323232", "need_consign_num": 5, "store_code": "2223", "type": "00", "sku_id": "213", "num_iid": 12222, "consign_type": "cn", "combine_item_id": "1231234", "combine_item_code": "13123", "item_ratio": 1, "bar_code": "691234", "delivery_cps": "STO", "biz_delivery_code": "e_delivery_code", "biz_store_code": "e_store_code", "biz_sd_type": "0", "send_country": "中国", "send_state": "浙江省", "send_city": "杭州市", "send_district": "西湖区", "send_town": "三墎镇", "send_division_code": "330110005", "black_delivery_cps": "STO,SF", "white_delivery_cps": "STO,SF", "biz_delivery_type": 1, "unused_warehouse_error_msg": "当前订单在猫淘平台对消费者进行了服务承诺：当日发次日达，建议按照平台要求进行择仓，否则存在赔付风险。", "unused_delivery_error_msg": "当前订单在猫淘平台对消费者进行了服务承诺，建议按照平台要求进行择配，否则存在赔付风险”/“平台建议当前线路您只能选择的配品牌，存在电子面单无法打印风险", "used_black_delivery_error_msg": "平台建议当前线路不要使用您选择的配品牌，存在电子面单无法打印风险", "promise_outbound_time": "2019-04-12 16:00:00", "promise_collect_time": "2019-04-12 16:00:00"}]}, "seller_nick": "我在测试", "buyer_nick": "我在测试", "nr_store_order_id": "下发鲜生活订单号", "nr_shop_id": "1", "nr_shop_name": "1", "nr_shop_guide_id": "1", "nr_shop_guide_name": "1", "sort_info": {"day_sort_val": 100, "hour_sort_val": 100}, "sorted": 1, "nr_no_handle": "1", "biz_code": "tmall.daogoubao.cloudstore", "cloud_store": "1", "is_gift": 0, "donee_nick": "暂不公开", "donee_open_uid": "暂不公开", "suning_shop_code": "123", "suning_shop_valid": 1, "allow_appkeys": "123,456", "new_presell": true, "you_xiang": true, "retail_store_id": "1", "is_istore": true, "ua": "ap", "linkedmall_ext_info": "{}", "pay_channel": "0", "rt_omni_send_type": "pickUp", "rt_omni_store_id": "12345", "rt_omni_outer_store_id": "12345", "tcps_start": "2018-10-10 11:00:00", "tcps_code": "com.tmall.dsd", "tcps_end": "2018-10-10 13:00:00", "m_tariff_fee": "0", "timing_promise": "tmallPromise", "promise_service": "aa,bb,cc", "cutoff_minutes": "660", "es_time": "1", "delivery_time": "2019-04-12 16:00:00", "collect_time": "2019-04-12 16:00:00", "dispatch_time": "2019-04-12 16:00:00", "sign_time": "2019-04-12 16:00:00", "outer_partner_member_id": "123456", "root_cat": "1234567", "gifting": "1", "gifting_takeout": "1", "oi_date": "8:00-9:00", "oi_range": "2019-08-01", "hold_install": "暂不安装", "app_name": "tmall", "easy_home_city_type": "deposit：预约，tail：尾款，direct：直接购买", "nr_deposit_order_id": "123456", "nr_store_code": "123456", "propoint": "10", "zqs_order_tag": "1", "txp_freezer_id": "1234", "txp_receive_method": "post", "expandcard_info": {"basic_price": "0.01", "expand_price": "0.01", "basic_price_used": "0.01", "expand_price_used": "0.01"}, "extend_info": "{\"itemTag\":\"100\"}", "lm": "1", "brand_light_shop_source": "scode", "brand_light_shop_store_id": "123456", "is_wmly": "1", "omni_package": "[{\"city_token\":\"155774003320052624\",\"pickup_code\":\"059006\",\"latest_prepare_time\":\"2020-05-26 13:00:00\",\"select_time\":\"2020-05-26 13:00-17:00\",\"sub_order_ids\":\"1234,2345\"}]", "ncz_ext_attr": "{}", "invoice_detail_pay": "{\"result\":{\"$ref\":\"@\"},\"1100453005045981418\":[{\"details\":[{\"amount\":0.50,\"name\":\"信用购\"}],\"type\":\"消费积分\"}],\"success\":true,\"errorCode\":\"\"}", "invoice_detail_mid_refund": "{\"result\":{\"$ref\":\"@\"},\"1100453005045981418\":[{\"details\":[{\"amount\":0.50,\"name\":\"信用购\"}],\"type\":\"消费积分\"}],\"success\":true,\"errorCode\":\"\"}", "invoice_detail_after_refund": "{\"result\":{\"$ref\":\"@\"},\"1100453005045981418\":[{\"details\":[{\"amount\":0.50,\"name\":\"信用购\"}],\"type\":\"消费积分\"}],\"success\":true,\"errorCode\":\"\"}", "expand_card_basic_price": "0.01", "expand_card_expand_price": "0.01", "expand_card_basic_price_used": "0.01", "expand_card_expand_price_used": "0.01", "delivery_cps": "SF", "asdp_biz_type": "logistics_upgrade", "is_openmall": false, "v_logistics_create": false, "q_r_pay": false, "order_follow_id": "1", "asdp_ads": "201", "ob_tag": "lg", "general_new_presell": true, "drug_register": "1", "step_pay_details": {"step_pay_detail": [{"id": 1, "pay_status": 2, "step_no": 1, "step_channel_no": "2021111222001188701414727247", "step_instrument_code": "ALIPAY_BATCH_ESCROW_APP", "step_actual_pay_fee": "10.00"}]}, "agree_refund_checks": {"agree_refund_check": [{"delivery_tips": "demo", "delivery_process": "demo", "detail_order_id": "demo"}]}, "address_details": {"address_detail": [{"detailed_address": "demo", "division_code": "demo", "area": "demo", "telephone": "demo", "city": "demo", "prov": "demo", "country": "demo", "contact_name": "demo", "biz_type": "demo"}]}, "stage_address_type": "identifyService", "og_id": "3628641205792", "promise_sign_time": "1", "tmall_coupon_fee": 100, "delivery_plan": {"delivery_plan": [{"plan_id": 100, "order_id": 100, "curr_phase": 100, "out_logistics_id": "demo", "prepare_time_begin": "2015-01-01 00:00:00", "ship_time_begin": "2015-01-01 00:00:00", "actual_ship_time": "2015-01-01 00:00:00", "goods_num": 100, "plan_status": 100, "plan_refund_status": 100, "receiver_name": "1", "receiver_mobile": "1", "receiver_phone": "1", "receiver_address": "1", "oaid": "demo", "privacy_num": "demo", "privacy_expire_time": "time", "receiver_town": "1", "receiver_district": "1", "receiver_city": "1", "receiver_state": "1", "receiver_country": "1"}]}, "omnichannel_param": "orderState:X_SHOP_HANDLED,orderType:STORE_DELIVER,acceptExpirationTime:2016-09-05 23:41:59,targetCode:34510274,allocationCode:339001,subOrderCode:194497830335000,targetType:STORE;", "oaid": "2w2RYE45iahnF4aiaJ7pHKCJ3Hwnbgnq2PH3AfpQVyWZNHKS9wNgAAOUfCVt9XZMetogNHwc", "is_cycle_buy": true, "scenario_group": "TXD_B2C_FRESH_HALFDAY", "play_type": "group_buy", "is_force_dc": true, "combine_logistics_details": {"combine_logistics_detail": [{"invoice_no": "*********", "logistics_company": "xx物流公司", "sub_order_id": "*********", "send_goods_details": {"send_goods_detail": [{"type": 1, "consign_status": 1, "amount": 3, "goods_details": {"package_goods_detail": [{"item_id": 123123, "sku_id": 123123, "amount": 123}]}}]}}]}, "identify_info": {"identify_logistics_infos": {"identify_logistics_info": [{"detail_order_id": 123123, "logistics_company": "xx物流公司", "stage_no": 2, "invoice_no": "123123", "refund_id": "123123", "refund": false}]}, "identify_service_infos": {"identify_service_info": [{"detail_order_id": 123123, "unit_id": "123123", "service_id": "123123"}]}}, "logistics_consign_info": {"logistics_consign_info": [{"related_id": "1234567", "consign_time": "付款后30天内", "render_consign_time": "付款后30天内", "combine_item": {"combine_consign_info": [{"comb_id": "56789", "item_id": "23456789", "sku_id": "678998", "consign_time": "付款后30天内", "render_consign_time": "付款后30天内"}]}}]}, "logistics_modify_info": {"logistics_modify_info": [{"related_id": "1234567", "consign_time": "付款后30天内", "origin_consign_time": "付款后32天内", "modify_time": "2023-04-01 11:11:11", "modify_reason": "提前发货时效", "item_id": "124556", "sku_id": "56789", "comb_id": "567890"}]}, "priority_consign_time": "2023-04-26 03:26", "logistics_agreement": {"logistics_service_msg": "送货上门", "asdp_biz_type": "logistics_upgrade", "asdp_ads": "201", "sign_time": "2019-04-12 16:00:00", "promise_sign_time": "2019-04-12 16:00:00", "push_time": "2019-04-12 16:00:00", "no_detail_part_consign": "1", "sink_type": "1"}, "real_name_auth_status": "0", "third_party_customs_declaration": "0", "receipt_type": "ITEM", "seller_flag_tag": "红色旗子", "seller_open_uid": "AAHk5d-EAAeGwJedwSFu0XXX", "education_receiver_mobile": "11111111111", "is_gfjy": true, "memo_operator_infos": {"memo_operator_info": [{"operator_id": 100001, "operator_nick": "这是一个昵称"}]}, "use_gov_subsidy": "1", "gov_subsidy_amount": "100", "is_zk_identify": true, "use_gov_predict": "1", "gov_subsidy_amount_exact": "330", "oldfornew": "1", "gov_store": "1", "use_gov_subsidy_new": "1", "gov_subsidy_amount_new": "321", "is_inds_qyg": true, "payment_method": "wx", "gov_sn_check": "1_1_0", "real_receiver_open_id": "AAEnVKOdAOIduo9TH9hjf-zb", "real_receiver_display_nick": "这是一个昵称", "post_fee_type": "giftprepaid", "real_post_fee": "11", "refund_post_fee": "8", "gift_post_fee_role": "0", "qn_distr": "1", "gov_main_subject": "测试公司", "platform_gold_subsidy": 100, "tmg_wx_channel_id": "439087655780000", "cc_tax_promotion_fee": 100, "is_mss": true, "gov_pay_type": "1", "gov_subsidy_type": "1", "gov_subsidy_type_extra": "4"}}}