package com.differ.wdgj.api.user.biz.domain.aftersale.load.utils;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.mchange.lang.IntegerUtils;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:30
 */
public class LoadAfterSaleConfigKeyUtils {
    //region 构造
    private LoadAfterSaleConfigKeyUtils() {
    }
    //endregion

    /**
     * 获取 售后单待执行下载任务超时时间
     *
     * @return 超时时间
     */
    public static int getWorkTaskUnRunTimeoutPeriod() {
        return IntegerUtils.parseInt(ConfigKeyUtils.getApiConfigValue(ConfigKeyEnum.LOAD_AFTERSALE_WORK_TASK_UNRUN_TIMEOUT_PERIOD), 0);
    }
}
