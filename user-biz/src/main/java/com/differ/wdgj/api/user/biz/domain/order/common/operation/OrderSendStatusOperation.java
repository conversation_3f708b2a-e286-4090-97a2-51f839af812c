package com.differ.wdgj.api.user.biz.domain.order.common.operation;

import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderGoodsSendInfoDto;
import com.differ.wdgj.api.user.biz.domain.order.common.data.OrderSendInfoDto;
import com.differ.wdgj.api.user.biz.domain.order.common.data.enums.OrderGoodsSendStatusEnum;
import com.differ.wdgj.api.user.biz.domain.order.common.data.enums.OrderSendStatusEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单发货状态操作者
 *
 * <AUTHOR>
 * @date 2025/6/19 14:04
 */
public class OrderSendStatusOperation {

    /**
     * 获取订单发货同步结果
     *
     * @param apiTrade 订单信息
     * @param apiTradeGoodsList 订单商品信息
     * @return 订单发货同步结果
     */
    public static OrderSendInfoDto getOrderSendStatus(ApiTradeDO apiTrade, List<ApiTradeGoodsDO> apiTradeGoodsList) {
        // 基础返回结果构建
        OrderSendInfoDto orderSendInfoDto = new OrderSendInfoDto();
        orderSendInfoDto.setBillId(apiTrade.getBillId());
        orderSendInfoDto.setTradeNo(apiTrade.getTradeNo());
        orderSendInfoDto.setShopId(apiTrade.getShopId());
        orderSendInfoDto.setOrderGoodsSendStatusList(new ArrayList<>());

        // 拆单发货场景
        if (BooleanUtils.toBoolean(apiTrade.getbSplit())){
            List<OrderGoodsSendInfoDto> orderGoodsSendInfoList = new ArrayList<>();
            apiTradeGoodsList.forEach(x ->{
                OrderGoodsSendInfoDto orderGoodsSendInfo = new OrderGoodsSendInfoDto();
                orderGoodsSendInfo.setRecId(x.getRecId());
                OrderGoodsSendStatusEnum orderGoodsSendStatusEnum = OrderGoodsSendStatusEnum.create(x.getbSend());
                switch (orderGoodsSendStatusEnum){
                    case WAIT_SYNC:
                    case PARTIAL_SUCCESS:
                        orderGoodsSendInfo.setWaitSendCount(x.getGoodsCount());
                        break;
                    case SUCCESS:
                        orderGoodsSendInfo.setbAllSend(true);
                        orderGoodsSendInfo.setSuccessSendCount(x.getGoodsCount());
                        break;
                    case Failed:
                        orderGoodsSendInfo.setFailSendCount(x.getGoodsCount());
                        break;
                }

                orderGoodsSendInfoList.add(orderGoodsSendInfo);
            });

            // 订单级是否全部发货
            orderSendInfoDto.setbAllSend(orderGoodsSendInfoList.stream().allMatch(OrderGoodsSendInfoDto::isbAllSend));
            orderSendInfoDto.setOrderGoodsSendStatusList(orderGoodsSendInfoList);

            return orderSendInfoDto;
        }

        // 整单发货场景
        OrderSendStatusEnum orderSynStatus = OrderSendStatusEnum.create(apiTrade.getSynStatus());
        if(OrderSendStatusEnum.getSuccessSyncStatusList().contains(orderSynStatus)){
            orderSendInfoDto.setbAllSend(true);
            // 构建成功的商品级结果
            apiTradeGoodsList.forEach(x ->{
                OrderGoodsSendInfoDto orderGoodsSendInfo = new OrderGoodsSendInfoDto();
                orderGoodsSendInfo.setRecId(x.getRecId());
                orderGoodsSendInfo.setbAllSend(true);
                orderGoodsSendInfo.setSuccessSendCount(x.getGoodsCount());
                orderSendInfoDto.getOrderGoodsSendStatusList().add(orderGoodsSendInfo);
            });
        }
        else if(OrderSendStatusEnum.getFailedSyncStatusList().contains(orderSynStatus)){
            // 构建失败的商品级结果
            apiTradeGoodsList.forEach(x -> {
                OrderGoodsSendInfoDto orderGoodsSendInfo = new OrderGoodsSendInfoDto();
                orderGoodsSendInfo.setRecId(x.getRecId());
                orderGoodsSendInfo.setFailSendCount(x.getGoodsCount());
                orderSendInfoDto.getOrderGoodsSendStatusList().add(orderGoodsSendInfo);
            });
        }
        else{
            // 构建待发货的商品级结果
            apiTradeGoodsList.forEach(x -> {
                OrderGoodsSendInfoDto orderGoodsSendInfo = new OrderGoodsSendInfoDto();
                orderGoodsSendInfo.setRecId(x.getRecId());
                orderGoodsSendInfo.setWaitSendCount(x.getGoodsCount());
                orderSendInfoDto.getOrderGoodsSendStatusList().add(orderGoodsSendInfo);
            });
        }
        return orderSendInfoDto;
    }
}
