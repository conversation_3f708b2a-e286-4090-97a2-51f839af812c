package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.adapter;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleWorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiWorkTaskDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiWorkTaskMapper;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;

/**
 * 上下文适配器
 *
 * <AUTHOR>
 * @date 2024/9/19 下午3:38
 */
public class AfterSaleLoadTaskContextAdapter {
    /**
     * 上下文转换
     *
     * @param workData 任务数据
     * @return 上下文
     */
    public AfterSaleLoadTaskContext toLoadTaskContext(WorkData<AfterSaleLoadArgs> workData){
        // 基础数据
        WorkContext workContext = workData.getWorkContext();
        AfterSaleLoadArgs loadArgs = workData.getData();

        // 工作任务上下文
        String memberName = workContext.getMember();
        Integer shopId = workContext.getShopId();

        // 获取店铺基础信息
        ApiShopBaseDto apiShopBase = ShopInfoUtils.singleByOutShopId(memberName, shopId);
        // 获取售后店铺配置
        AfterSalesShopConfig afterSalesShopConfig = AfterSalesShopConfigUtils.singleByShopId(memberName, shopId);

        // 查询上一次成功的自动间隔下载任务
        LoadAfterSaleWorkResult lastWorkResult = null;
        if (workContext.getTriggerType() == TriggerTypeEnum.AUTO){
            ApiWorkTaskDO bizWorkTask = DBSwitchUtil.doDBWithUser(memberName, () -> BeanContextUtil.getBean(ApiWorkTaskMapper.class).singleAutoShopLastFinishTask(shopId, WorkEnum.LOAD_AFTER_SALE.getValue(), TriggerTypeEnum.AUTO.getByteValue()));
            if(bizWorkTask != null){
                lastWorkResult = JsonUtils.deJson(bizWorkTask.getJsonResult(), LoadAfterSaleWorkResult.class);
            }
        }

        AfterSaleLoadTaskContext context = new AfterSaleLoadTaskContext();
        context.setMemberName(memberName);
        context.setShopId(shopId);
        context.setOperatorName(workContext.getCreator());
        context.setTriggerType(loadArgs.getTriggerType());
        context.setApiShopBaseInfo(apiShopBase);
        context.setAfterSalesConfig(afterSalesShopConfig);
        context.setLastWorkResult(lastWorkResult);
        return context;
    }
}
