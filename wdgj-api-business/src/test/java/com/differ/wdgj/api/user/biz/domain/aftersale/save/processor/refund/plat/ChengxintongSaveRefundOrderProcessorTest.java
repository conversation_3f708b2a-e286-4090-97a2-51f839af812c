package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.SaveRefundOrderFactory;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 诚信通-保存退货退款单特殊处理
 * https://s.jkyun.biz/0WiqEQC 诚信通-售后菠萝派报文分析
 *
 * <AUTHOR>
 * @date 2025/3/5 上午11:40
 */
public class ChengxintongSaveRefundOrderProcessorTest extends BaseAfterSaleOrderTest {
    //region 变量
    /**
     * 基础测试数据地址
     */
    private final String basePath = "D:\\esapi-java\\管家java测试数据\\AfterSaleDataOperationTest\\refund\\%s";
    //endregion

    /**
     * 诚信通保存售后单 - 存在TQ单号
     */
    @Test
    public void cxtSaveOrderByTQTest() throws IOException {
        AfterSaleSaveContext context = getContext("api2017", 96914);
        String str = new String(Files.readAllBytes(Paths.get(String.format(basePath, "CXTBusinessGetRefundOrderResponseOrderItem.json"))));
        List<BusinessGetRefundOrderResponseOrderItem> polyOrderList = JsonUtils.deJson(str, new TypeReference<List<BusinessGetRefundOrderResponseOrderItem>>() {
        });
        ISaveAfterSaleOrder<BusinessGetRefundOrderResponseOrderItem> processor = SaveRefundOrderFactory.createProcessor(context);

        // 非TQ保存
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResult = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResult);
        Assert.assertTrue(listSaveAfterSaleResult.isSuccess());

        // TQ保存
        polyOrderList.forEach(x -> {
            x.setRefundNo(x.getRefundNo().startsWith("TQ") ? x.getRefundNo() : "TQ" + x.getRefundNo());
            x.setReason("TQ测试");
        });
        SaveAfterSaleResult<List<SaveOrderResultComposite>> listSaveAfterSaleResultTQ = processor.saveOrder(polyOrderList);
        Assert.assertNotNull(listSaveAfterSaleResultTQ);
        Assert.assertTrue(listSaveAfterSaleResultTQ.isSuccess());
    }
}
