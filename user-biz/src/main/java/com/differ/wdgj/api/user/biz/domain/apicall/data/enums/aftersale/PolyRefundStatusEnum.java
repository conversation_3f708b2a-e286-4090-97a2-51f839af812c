package com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 菠萝派退款状态
 *
 * <AUTHOR>
 * @date 2024/7/17 下午5:08
 */
public enum PolyRefundStatusEnum implements ValueEnum, CodeEnum {
    /**
     * 买家已经申请退款等待卖家同意
     */
    BUYER_APPLIED_REFUND_WAITING_SELLER_AGREE("买家已经申请退款等待卖家同意", 1, "JH_01"),

    /**
     * 卖家已经同意退款等待买家退货
     */
    SELLER_AGREED_REFUND_WAITING_BUYER_RETURN("卖家已经同意退款等待买家退货", 2, "JH_02"),

    /**
     * 买家已经退货等待卖家确认收货
     */
    BUYER_RETURNED_WAITING_SELLER_CONFIRM("买家已经退货等待卖家确认收货", 3, "JH_03"),

    /**
     * 卖家拒绝退款
     */
    SELLER_REFUSED_REFUND("卖家拒绝退款", 4, "JH_04"),

    /**
     * 退款关闭
     */
    REFUND_CLOSED("退款关闭", 5, "JH_05"),

    /**
     * 退款成功
     */
    REFUND_SUCCESSFUL("退款成功", 6, "JH_06"),

    /**
     * 没有退款
     */
    NO_REFUND("没有退款", 7, "JH_07"),

    /**
     * 退款中
     */
    REFUND_IN_PROGRESS("退款中", 8, "JH_08"),

    /**
     * 部分退款
     */
    PARTIAL_REFUND("部分退款", 9, "JH_09"),

    /**
     * 待审核
     */
    PENDING_REVIEW("待审核", 10, "JH_10"),

    /**
     * 商家收货拒绝
     */
    SELLER_REFUSED_CONFIRM_RECEIPT("商家收货拒绝", 11, "JH_11"),

    /**
     * 等待卖家发出补寄货品
     */
    WAITING_SELLER_SEND_REPLACEMENT("等待卖家发出补寄货品", 12, "JH_12"),

    /**
     * 补寄成功
     */
    REPLACEMENT_SUCCESSFUL("补寄成功", 13, "JH_13"),

    /**
     * 待买家确认收货
     */
    PENDING_BUYER_CONFIRMATION_OF_RECEIPT("待买家确认收货", 14, "JH_14"),

    /**
     * 全部状态
     */
    ALL_STATUSES("全部状态", 98, "JH_98"),

    /**
     * 其他
     */
    OTHER("其他", 99, "JH_99");


    /**
     * 网店管家业务值
     */
    private final String wdgjValue;

    /**
     * API dotNet对应BusinessRefundStatus枚举值（兼容逻辑）
     */
    private final Integer dotValue;

    /**
     * 菠萝派JH值
     */
    private final String code;


    PolyRefundStatusEnum(String wdgjValue, Integer dotValue, String code) {
        this.wdgjValue = wdgjValue;
        this.dotValue = dotValue;
        this.code = code;
    }

    //region 公共方法
    @Override
    public String getCode() {
        return code;
    }

    @Override
    public Integer getValue() {
        return dotValue;
    }

    public String getWdgjValue() {
        return wdgjValue;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static PolyRefundStatusEnum create(String value) {
        return EnumConvertCacheUtil.convert(PolyRefundStatusEnum.class, value, EnumConvertType.CODE, EnumConvertType.VALUE);
    }
    //endregion
}
