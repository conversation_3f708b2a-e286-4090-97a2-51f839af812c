package com.differ.wdgj.api.component.redis.limtier;

/**
 * 限流结果
 * <AUTHOR>
 * @date 2022/9/16 11:13
 */
public class LimitResult {

    /**
     * 是否限流，true：限流，false：不限流
     */
    private boolean limit;
    /**
     * 剩余可通过数
     */
    private Integer available;
    /**
     * 可用数未知
     */
    private static final int AVAILABLE_UN_KNOWN = -1;

    public boolean isLimit() {
        return limit;
    }

    public void setLimit(boolean limit) {
        this.limit = limit;
    }

    public Integer getAvailable() {
        return available;
    }

    public void setAvailable(Integer available) {
        this.available = available;
    }

    /**
     * 创建限流结果
     * @param limit 是否限流
     * @param available 剩余可用数
     * @return
     */
    public static LimitResult of(boolean limit,Integer available){
        LimitResult result = new LimitResult();
        result.setLimit(limit);
        result.setAvailable(available);
        return result;
    }

    /**
     * 创建限流结果,可用数未知的情况（-1）
     * @param limit 是否限流
     * @return
     */
    public static LimitResult of(boolean limit){
        LimitResult result = new LimitResult();
        result.setLimit(limit);
        result.setAvailable(AVAILABLE_UN_KNOWN);
        return result;
    }
}
