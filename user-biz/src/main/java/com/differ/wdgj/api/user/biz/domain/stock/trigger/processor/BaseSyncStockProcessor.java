package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor;

import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncResultDetail;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation.StockSyncResponseOperation;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.ISyncStockShopResultProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存同步-业务处理基础实现
 *
 * <AUTHOR>
 * @date 2024-02-26 9:31
 */
public class BaseSyncStockProcessor implements ISyncStockProcessor {

    // region 构造方法

    /**
     * 构造函数
     * @param context 全局上下文
     */
    public BaseSyncStockProcessor(StockSyncContext context){
        this.context = context;
        syncDetails = new HashMap<>();
        this.stockSyncResponseOperation = new StockSyncResponseOperation(context, this);
    }

    // endregion

    //region 变量

    /**
     * 全局上下文
     */
    protected final StockSyncContext context;

    /**
     * StockSyncResponseOperation
     */
    protected final StockSyncResponseOperation stockSyncResponseOperation;

    /**
     * 库存同步详情
     */
    protected final Map<MatchIdEnhance, StockContentResult<StockSyncResultDetail>> syncDetails;

    //endregion

    //region 库存同步核心步骤

    /**
     * 1、构建库存同步请求
     */
    @Override
    public final void buildSyncStockRequests() {
        // 1、店铺级前置处理 - 插件

        // 2、库存量计算

        // 3、基础请求构建
    }

    /**
     * 2、发起菠萝派库存同步
     */
    @Override
    public final void doRequest() {
        // 1、发起库存同步请求

        // 2、菠萝派结果处理
    }

    /**
     * 3、保存库存同步结果
     * @param responseComposites 库存同步结果组合列表
     * @return 结果
     */
    @Override
    public final Map<MatchIdEnhance, StockContentResult<StockSyncResultDetail>> saveSyncStockResult(
            Map<MatchIdEnhance, StockSyncResultComposite> responseComposites
    ) {

        // 构建库存同步结果
        stockSyncResponseOperation.buildStockSyncResult(responseComposites);

        // 封装库存同步结果包
        StockSyncResultPackage resultPackage = StockSyncResultPackage.create(responseComposites);

        // 库存同步结果处理
        this.getResultProcessors().forEach(t -> t.postProcess(context, resultPackage));

        return this.syncDetails;
    }

    //endregion

    //region 私有方法
    /**
     * 获取库存同步结果处理器
     *
     * @return 结果
     */
    private List<ISyncStockShopResultProcessor> getResultProcessors() {
        List<ISyncStockShopResultProcessor> resultProcessors = new ArrayList<>();

        // 错误预处理 - 必须放第一个
        resultProcessors.add(new PerErrorStockResultProcessor());
        // 错误码 - 无效货品
        resultProcessors.add(new InvalidGoodsStockResultProcessor());
        // 错误码 - 停用库存同步
        resultProcessors.add(new DisableStockResultProcessor());
        // 错误码 - 降级
        resultProcessors.add(new TierDownStockResultProcessor());
        // 上下架相关
        resultProcessors.add(new UpDownGoodsResultProcess());

        // 平台级处理
        resultProcessors.addAll(getPlatResultProcessors());

        // 结果持久化 - 必须放最后一个
        resultProcessors.add(new PersistStockResultProcessor());

        return resultProcessors;
    }
    //endregion

    //region 供子类重写方法

    /**
     * 扩展特殊匹配平台响应
     *
     * @param resultComposite 结果组合
     */
    public void specialProcessSyncResults(StockSyncResultComposite resultComposite) { }

    /**
     * 获取 平台级库存同步结果处理器
     * @return 结果
     */
    protected List<ISyncStockShopResultProcessor> getPlatResultProcessors(){
        return new ArrayList<>();
    }

    //endregion

}
