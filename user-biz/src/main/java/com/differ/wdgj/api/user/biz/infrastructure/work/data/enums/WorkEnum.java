package com.differ.wdgj.api.user.biz.infrastructure.work.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.LoadAfterSaleWorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.demo.TestExecWorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.WorkFactory;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

/**
 * 工作任务类型
 *
 * <AUTHOR>
 * @date 2024/6/25 10:50
 */
public enum WorkEnum implements ValueEnum {
    /**
     * 样例测试
     */
    DEMO_TEST(1, "样例测试", TaskEnum.API_SYNC, TestExecWorkFactory.class),
    /**
     * 下载售后单
     */
    LOAD_AFTER_SALE(2, "下载售后单", TaskEnum.API_AFTER_SALE_LOAD, LoadAfterSaleWorkFactory.class),
    ;

    //region 变量
    /**
     * 类型值
     */
    private final Integer value;

    /**
     * 任务类型描述
     */
    private final String caption;

    /**
     * 工作任务的执行线程池
     */
    private final TaskEnum taskEnum;

    /**
     * 抽象工厂的bean名
     */
    private final Class<? extends WorkFactory<?>> workFactoryClass;

    /**
     * 工作任务工厂
     */
    private WorkFactory<?> workFactory;

    //endregion

    //region 构造
    private WorkEnum(Integer value, String caption, TaskEnum taskEnum, Class<? extends WorkFactory<?>> workFactoryClass) {
        this.value = value;
        this.caption = caption;
        this.taskEnum = taskEnum;
        this.workFactoryClass = workFactoryClass;
    }
    //endregion

    //region 公共方法

    /**
     * 获取工作工厂
     *
     * @return 工作工厂
     */
    public WorkFactory<?> getWorkFactory() {
        if (workFactory == null) {
            try {
                workFactory = workFactoryClass.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return workFactory;
    }

    /**
     * 获取类型值
     *
     * @return 类型值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 获取工作任务的执行线程池
     *
     * @return 工作任务的执行线程池
     */
    public TaskEnum getTaskEnum() {
        return taskEnum;
    }

    /**
     * 获得枚举对应的描述
     *
     * @return 枚举对应的描述
     */
    public String getCaption() {
        return caption;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static WorkEnum create(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return EnumConvertCacheUtil.convert(value, WorkEnum.class, EnumConvertType.VALUE);
    }
    //endregion
}
