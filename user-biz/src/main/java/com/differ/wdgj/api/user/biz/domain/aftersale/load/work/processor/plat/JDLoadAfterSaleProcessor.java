package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.*;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.BaseLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * 京东 - 售后单下载
 *
 * <AUTHOR>
 * @date 2025/4/8 上午10:31
 */
public class JDLoadAfterSaleProcessor extends BaseLoadAfterSaleProcessor {
    //region 构造
    public JDLoadAfterSaleProcessor(AfterSaleLoadTaskContext context) {
        super(context);
    }
    //endregion

    //region 常量

    /*
     自营店铺特殊售后单类型ordertype传参值
     */
    protected static  final  String SUPPLIER_SPECIAL_ORDERTYPE = "JH_01";

    //endregion

    /**
     * 子任务平台级特殊处理
     *
     * @param workData 工作任务数据
     * @param subTask  子任务
     * @return 特殊处理后子任务
     */
    @Override
    protected LoadAfterSaleWorkResult subTaskCreatePlatProcess(WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
        //VC供应商退货单 和 厂直退款单下载orderType需传"JH_01"
        if(ShopTypeEnum.JD_SUPPLIER.getPolyCode().equalsIgnoreCase(subTask.getShopType())){
            if(PolyRefundTypeEnum.JH_04.getCode().equalsIgnoreCase(subTask.getAfterSaleType()) || StringUtils.isEmpty(subTask.getAfterSaleType())){
                subTask.setOrderType(SUPPLIER_SPECIAL_ORDERTYPE);
            }
        }
        return LoadAfterSaleWorkResult.onSuccess();
    }
}
