package com.differ.wdgj.api.user.biz.infrastructure.work.template;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;

/**
 * 工作任务执行模板
 *
 * <AUTHOR>
 * @date 2024/6/24 11:36
 */
public interface WorkExecTemplate<T extends WorkData<?>> {
    /**
     * 模板执行工作任务,只带存在的任务ID
     *
     * @param member 会员名
     * @param taskId 任务id
     */
    WorkResult run(String member, String taskId);

    /**
     * 模板执行工作任务,带具体的任务数据
     *
     * @param workData 任务数据
     * @param taskId   任务id
     */
    WorkResult run(T workData, String taskId);

}
