package com.differ.wdgj.api.component.util.http.core;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;

import javax.net.ssl.SSLContext;

/**
 * TLS 安全传输层协议版本
 *
 * <AUTHOR>
 * @date 2024/8/12 11:12
 */
public enum HttpTlsEnum implements CodeEnum {
    DEFAULT("", new DefaultTrustAllStrategy()),
    TLS0("TLSv1.0", new IgnoreX509TrustStrategy()),
    TLS1("TLSv1.1", new IgnoreX509TrustStrategy()),
    TLS2("TLSv1.2", new IgnoreX509TrustStrategy()),
    TLS3("TLSv1.3", new IgnoreX509TrustStrategy()),
    ;
    private final String code;

    private SSLContextTrustStrategy SSLContextTrustStrategy;

    HttpTlsEnum(String code, SSLContextTrustStrategy SSLContextTrustStrategy) {
        this.code = code;
        this.SSLContextTrustStrategy = SSLContextTrustStrategy;
    }

    /**
     * 获取枚举代码
     *
     * @return
     */
    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String toString() {
        return this.code;
    }

    /**
     * 获取证书信任策略
     *
     * @return
     */
    public SSLContextTrustStrategy getApiX509TrustManager() {
        return SSLContextTrustStrategy;
    }

    /**
     * 创建SSLContext
     *
     * @return
     */
    public SSLContext createSslContext() {
        return this.getApiX509TrustManager().createSslContext(this);
    }

    /**
     * typeCode转换SubQueueEnum
     *
     * @param code
     * @return
     */
    public static HttpTlsEnum convert(String code) {
        HttpTlsEnum httpTlsEnum = EnumConvertCacheUtil.convert(code, HttpTlsEnum.class, EnumConvertType.CODE);
        if (httpTlsEnum == null) {
            httpTlsEnum = HttpTlsEnum.DEFAULT;
        }
        return httpTlsEnum;
    }
}
