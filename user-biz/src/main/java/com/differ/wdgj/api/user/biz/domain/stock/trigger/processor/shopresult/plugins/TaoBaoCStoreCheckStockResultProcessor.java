package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.plugins;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchOperationEnhance;
import com.differ.wdgj.api.user.biz.domain.goods.match.subdomain.impl.BasicsGoodsMatchServiceImpl;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.RdsGoodsDomain;
import com.differ.wdgj.api.user.biz.domain.stock.data.MatchIdEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockPolyErrorCodeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct.BusinessDownloadProductResponseGoodInfo;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultComposite;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.shopresult.AbstractStockResultProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.goods.match.ApiSysMatchExtJsonParamsDto;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchExtMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存同步结果处理-淘宝C店商品审核变更处理
 *
 * <AUTHOR>
 * @date 2024-03-20 20:36
 */
public class TaoBaoCStoreCheckStockResultProcessor extends AbstractStockResultProcessor {
    /**
     * 仅当存在失败结果时运行
     *
     * @return 结果
     */
    @Override
    protected boolean justRunWhenFailed() {
        return true;
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "淘宝C店商品审核变更处理";
    }

    /**
     * 处理结果
     *
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    @Override
    protected void processResults(StockSyncContext context, StockSyncResultPackage resultPackage) {
        // 获取需要处理的匹配数据
        List<ApiSysMatchDO> needDealMatches = getNeedDealMatches(resultPackage);
        if (CollectionUtils.isEmpty(needDealMatches)) {
            return;
        }

        // 批量获取商品推送库数据
        Map<String, String> rdsResult = this.queryRdsAndConvert(context.getVipUser(), context.getShopId(), needDealMatches);
        if (MapUtils.isEmpty(rdsResult)) {
            return;
        }

        // 待删除的商品匹配唯一键
        List<ApiSysMatchOperationEnhance> waitDeleteMatchIds = new ArrayList<>();
        // 待更新规格表的数据
        Map<Integer, String> waitUpdateMap = new HashMap<>();

        // 构建更新/删除数据
        doGoodsCheck(needDealMatches, rdsResult, waitDeleteMatchIds, waitUpdateMap);

        // 批量数据库操作
        doDataOperate(context.getVipUser(), resultPackage, waitDeleteMatchIds, waitUpdateMap);

    }

    /**
     * 获取需要处理的匹配数据
     *
     * @param resultPackage 库存同步结果包
     * @return 需要处理的匹配数据
     */
    private List<ApiSysMatchDO> getNeedDealMatches(StockSyncResultPackage resultPackage) {
        List<ApiSysMatchDO> needDealMatches = new ArrayList<>();
        // 遍历失败结果
        resultPackage.getFailedItems().forEach(idEnhance -> {
            // 结果
            StockSyncResultComposite composite = resultPackage.getComposites().get(idEnhance);
            if (composite == null) {
                return;
            }

            if (SyncStockPolyErrorCodeEnum.TAOBAO_C_GOODS_NEED_CHECK.equals(composite.getResponseErrorCode())) {
                needDealMatches.add(composite.getMatchEnhance().getSysMatch());
            }
        });

        return needDealMatches;
    }

    /**
     * 查询RDS并转换结果
     *
     * @param needDealMatches 需要处理的匹配信息
     * @return 结果
     */
    private Map<String, String> queryRdsAndConvert(String vipUser, Integer shopId, List<ApiSysMatchDO> needDealMatches) {
        if (CollectionUtils.isEmpty(needDealMatches)) {
            return new HashMap<>(0);
        }

        // 获取淘宝商品RDS推送数据
        List<String> platGoodsIds = needDealMatches.stream().map(ApiSysMatchDO::getNumiid).collect(Collectors.toList());
        List<BusinessDownloadProductResponseGoodInfo> polyGoodsList = new RdsGoodsDomain().getPolyProductByPlatGoodIds(vipUser, platGoodsIds);

        log.info("淘宝C店商品审核变更处理-推送库数据，platGoodsId：{}; rdsResult：{}", JsonUtils.toJson(platGoodsIds), JsonUtils.toJson(polyGoodsList));
        Map<String, String> result = new HashMap<>();
        polyGoodsList.forEach(goodInfo -> {
            if(CollectionUtils.isNotEmpty(goodInfo.getSkus())){
                // 规格级赋值
                goodInfo.getSkus().forEach(sku -> {
                    result.put(String.format("%s_%s_%s", goodInfo.getPlatProductId(), sku.getSkuId(), shopId), sku.getSkuProperty());
                });
            }
        });

        return result;
    }

    /**
     * 执行商品具体校验
     *
     * @param needDealMatches    待处理的商品
     * @param waitDeleteMatchIds 删除的商品匹配唯一键
     * @param waitUpdateMap      待更新规格表的数据
     */
    private void doGoodsCheck(List<ApiSysMatchDO> needDealMatches, Map<String, String> rdsResult,
                              List<ApiSysMatchOperationEnhance> waitDeleteMatchIds, Map<Integer, String> waitUpdateMap) {
        needDealMatches.forEach(needDealMatch -> {
            // 仅处理多规格数据
            if (StringUtils.isNotEmpty(needDealMatch.getSkuID())) {
                String key = String.format("%s_%s_%s", needDealMatch.getNumiid(), needDealMatch.getSkuID(), needDealMatch.getShopId());

                log.info("淘宝C店商品审核变更处理-校验执行，key：{}; rdsResult：{}", key, JsonUtils.toJson(rdsResult.keySet()));

                // 推送库对应规格不存在，删除匹配
                if (!rdsResult.containsKey(key)) {
                    waitDeleteMatchIds.add(ApiSysMatchOperationEnhance.create(needDealMatch, "淘宝C店无效货品"));
                    return;
                }
                // 比较推送库中的规格编码和表中的规格编码是否一致，不一致则更新
                String rdsProperty = rdsResult.get(key);
                if (needDealMatch.getExtEntity() != null && StringUtils.isNotEmpty(needDealMatch.getExtEntity().getJsonParams())) {
                    waitUpdateMap.put(needDealMatch.getId(), rdsProperty);
                }
            }
        });
    }


    /**
     * 执行数据更新
     *
     * @param vipUser            会员名
     * @param waitDeleteMatchIds 删除的商品匹配唯一键
     * @param waitUpdateMap      待更新规格表的数据
     */
    private void doDataOperate(String vipUser, StockSyncResultPackage resultPackage, List<ApiSysMatchOperationEnhance> waitDeleteMatchIds, Map<Integer, String> waitUpdateMap) {
        // 删除匹配
        if (CollectionUtils.isNotEmpty(waitDeleteMatchIds)) {
            BasicsGoodsMatchServiceImpl basicsGoodsMatchService = new BasicsGoodsMatchServiceImpl();
            basicsGoodsMatchService.delGoodsMatch(vipUser, "新API", waitDeleteMatchIds);
        }

        // 更新货品匹配扩展数据
        if (MapUtils.isNotEmpty(waitUpdateMap)) {
            ApiSysMatchExtMapper apiSysMatchExtMapper = BeanContextUtil.getBean(ApiSysMatchExtMapper.class);
            // 批量获取货品匹配扩展数据
            Set<Integer> keySet = waitUpdateMap.keySet();
            List<ApiSysMatchExtDO> apiSysMatchExtDoS = DBSwitchUtil.doDBWithUser(vipUser, () -> apiSysMatchExtMapper.selectByIds(new ArrayList<>(keySet)));
            // 构建匹配扩展数据 - 扩展字段
            Map<Integer, String> waitUpdateJsonParamsMap = new HashMap<>();
            Set<Integer> needSyncIdList = new HashSet<>();
            for (ApiSysMatchExtDO apiSysMatchExtDo : apiSysMatchExtDoS) {
                String skuProperty = waitUpdateMap.get(apiSysMatchExtDo.getId());
                ApiSysMatchExtJsonParamsDto jsonParamsDto = JsonUtils.deJson(apiSysMatchExtDo.getJsonParams(), ApiSysMatchExtJsonParamsDto.class);
                if (jsonParamsDto != null && !StringUtils.equals(skuProperty, jsonParamsDto.getSkuProperty())) {
                    // 待更新匹配表数据
                    jsonParamsDto.setSkuProperty(skuProperty);
                    String waitUpdateJsonParams = JsonUtils.toJson(jsonParamsDto);
                    waitUpdateJsonParamsMap.put(apiSysMatchExtDo.getId(), waitUpdateJsonParams);

                    // 待触发匹配表数据
                    needSyncIdList.add(apiSysMatchExtDo.getId());
                }
            }


            // 更新货品匹配扩展数据
            if(MapUtils.isNotEmpty(waitUpdateJsonParamsMap)){
                DBSwitchUtil.doDBWithUser(vipUser, () -> apiSysMatchExtMapper.batchUpdateJsonParams(waitUpdateJsonParamsMap));
            }
            // 更新触发状态
            needSyncIdList.forEach(matchId -> {
                Map<MatchIdEnhance, StockSyncResultComposite> composites = resultPackage.getComposites();
                composites.keySet().stream().filter(x -> x.getMatchId().equals(matchId)).forEach(x -> {
                    StockSyncResultComposite stockSyncResultComposite = composites.get(x);
                    stockSyncResultComposite.getApiSysMatchResult().setIsSys(SyncStockStatusEnum.WaitSync.getValue());
                });
            });


            log.info("淘宝C店商品审核变更处理-结果更新，结果：{}", JsonUtils.toJson(resultPackage));
        }
    }
}
