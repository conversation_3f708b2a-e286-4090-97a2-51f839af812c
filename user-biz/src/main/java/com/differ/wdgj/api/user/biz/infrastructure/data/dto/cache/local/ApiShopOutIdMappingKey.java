package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

/**
 *  内存缓存：Api店铺数据 outShopId + outAccount 映射 shopId Key
 *
 * <AUTHOR>
 * @date 2024-03-21 20:40
 */
public class ApiShopOutIdMappingKey {

    /**
     * 构造
     * @param outAccount 外部会员名
     * @param outShopId 外部店铺Id
     */
    public ApiShopOutIdMappingKey(String outAccount, String outShopId) {
        this.outAccount = outAccount;
        this.outShopId = outShopId;
    }

    /**
     * 外部会员名
     */
    private String outAccount;

    /**
     * 外部店铺Id
     */
    private String outShopId;


    //region get/set
    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public String getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(String outShopId) {
        this.outShopId = outShopId;
    }
    //endregion
}
