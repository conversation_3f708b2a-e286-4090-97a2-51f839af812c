package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiShopCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevShopDO;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 店铺基础数据Redis缓存 测试
 * <AUTHOR>
 * @date 2024-03-21 18:21
 */
@Ignore
public class ApiDevShopCacheTest extends AbstractSpringTest {

    /**
     * 获取 店铺基础数据
     */
    @Test
    public void getTest(){

        DevShopDO api2017 = ApiShopCache.create("api2017").get(2715);
    }
}
