package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 下载订单翻页方式枚举
 *
 * <AUTHOR>
 * @date 2024-06-24 14:43
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum LoadOrderPageTypeEnum implements ValueEnum {
    /**
     * 正序
     */
    ASC("正序", 1),

    /**
     * 倒序
     */
    DESC("倒序", 2),

    /**
     * 下一页（token）
     */
    NEXT("下一页", 3);

    /**
     * 枚举项的名称
     */
    private final String name;

    /**
     * 枚举项的值
     */
    private final Integer value;

    /**
     * 构造方法
     *
     * @param name  枚举项的名称
     * @param value 枚举项的值
     */
    LoadOrderPageTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 获取枚举项的名称
     *
     * @return 枚举项的名称
     */
    public String getName() {
        return name;
    }

    /**
     * 获取枚举项的值
     *
     * @return 枚举项的值
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static LoadOrderPageTypeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, LoadOrderPageTypeEnum.class, EnumConvertType.VALUE);
    }
}
