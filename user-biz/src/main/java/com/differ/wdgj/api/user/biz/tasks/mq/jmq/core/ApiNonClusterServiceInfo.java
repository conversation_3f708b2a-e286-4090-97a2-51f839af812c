package com.differ.wdgj.api.user.biz.tasks.mq.jmq.core;

import com.differ.jackyun.framework.component.jmq.core.ServiceInfo;
import com.differ.jackyun.framework.component.jmq.core.ServiceInfoI;
import com.differ.jackyun.framework.component.jmq.core.constant.SpringEnvConstant;
import com.differ.wdgj.api.user.biz.infrastructure.condition.ConditionalOnSite;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.SiteTypeCodeConst;
import org.springframework.stereotype.Component;

/**
 * 非集群服务信息
 * <p>
 * 1、旨在解决非集群服务配置了集群号问题
 * 2、默认集群号 common
 *
 * <AUTHOR>
 * @date 2022/10/27 13:50
 */
@Component
@ConditionalOnSite(sites = {
        SiteTypeCodeConst.WDGJ_API_BUSINESS}
)
public class ApiNonClusterServiceInfo implements ServiceInfoI {

    /**
     * 创建服务信息
     *
     * @return 结果
     */
    @Override
    public ServiceInfo createServiceInfo() {

        // 配置文件读取当前站点类型
        String serviceCode = SystemAppConfig.get().getSiteTypeEnum().getServiceCode();

        // 非集群服务默认集群号：common
        return new ServiceInfo(serviceCode, SpringEnvConstant.DEFAULT_GROUP_ID);
    }
}
