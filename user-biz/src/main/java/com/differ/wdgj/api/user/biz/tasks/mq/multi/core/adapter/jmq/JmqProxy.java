package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq;

import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.ApiMultiMQ;
import net.bytebuddy.ByteBuddy;
import net.bytebuddy.description.annotation.AnnotationDescription;

/**
 * JMQ创建动态实例的代理类
 *
 * <AUTHOR>
 * @date 2024/3/19 17:54
 */
public class JmqProxy<T> {

    /**
     * 代理目标基类
     */
    private final Class<T> jmqBaseClass;

    /**
     * 多队列注解属性
     */
    private final ApiMultiMQ multiMQProperties;

    /**
     * 代理队列JMQ代码
     */
    private final String jmqCode;

    public JmqProxy(Class<T> jmqBaseClass, String jmqCode, ApiMultiMQ multiMQProperties) {
        this.jmqBaseClass = jmqBaseClass;
        this.jmqCode = jmqCode;
        this.multiMQProperties = multiMQProperties;
    }

    /**
     * 创建动态代理实例
     *
     * @return
     * @throws Exception
     */
    public T getInstance() throws Exception {
        Class dynamicClass = getDynamicClass(jmqBaseClass);
        return (T) dynamicClass.newInstance();
    }

    /**
     * 创建动态代理类
     *
     * @param superClass
     * @return
     */
    private Class getDynamicClass(Class superClass) {
        // 使用ByteBuddy创建一个新的类，并继承原始类
        Class dynamicClass = new ByteBuddy()
                // 父类
                .subclass(superClass)
                // 类添加ApiMultiMQ注解
                .annotateType(AnnotationDescription.Builder.ofType(JmqAdapterAnnotation.class)
                        .define("code", jmqCode)
                        .defineArray("sitesToSend", multiMQProperties.sitesToSend())
                        .defineArray("sitesToReceive", multiMQProperties.sitesToReceive())
                        .define("queueEnabled", multiMQProperties.queueEnabled())
                        .define("consumerFailCallback", multiMQProperties.consumerFailCallback())
                        .build())
                // 将已经定义好的类转换为 Java 类对象
                .make()
                // 加载类
                .load(superClass.getClassLoader())
                // 获取class
                .getLoaded();
        return dynamicClass;
    }
}
