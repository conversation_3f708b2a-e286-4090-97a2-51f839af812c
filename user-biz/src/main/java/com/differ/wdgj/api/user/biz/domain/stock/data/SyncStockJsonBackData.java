package com.differ.wdgj.api.user.biz.domain.stock.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.core.StringDateTimeDeserializer;

import java.time.LocalDateTime;

/***
 * 库存同步结果jsonBack
 *
 * <AUTHOR>
 * @date 2024-03-04 11:54
 */
public class SyncStockJsonBackData {

    /**
    * 获取或设置 操作者
    */
    private String operatorUser;

    /**
     * 获取或设置 是否手动，默认false：自动 。
     */
    private Boolean isHand;

    /**
     * 电商平台枚举
     */
    private int businessPlat;

    /**
     * 店铺id
     */
    private Integer shopId;

    /**
    * 获取或设置 平台货品ID 。
    */
    private String platProductId;

    /**
    * 获取或设置 平台规格ID 。
    */
    private String skuId;

    /**
    * 请求的库存同步数量
    */
    private Integer requestCount;

    /**
    * 实际库存数量
    */
    private Integer realQuantity;

    /**
    * 库存量详情
    */
    private String detailCount;

    /**
    * 同步开始时间
    */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 上下架状态
     */
    private String status;

    /**
     * 店铺下的门店Id
     */
    private String storeId;

    /**
     * 平台门店Id/Code
     */
    private String platStoreId;

    /**
     * 平台门店Id/Code
     */
    private String storeType;

    //region get/set

    public String getOperatorUser() {
        return operatorUser;
    }

    public void setOperatorUser(String operatorUser) {
        this.operatorUser = operatorUser;
    }

    public Boolean getHand() {
        return isHand;
    }

    public void setHand(Boolean hand) {
        isHand = hand;
    }

    public int getBusinessPlat() {
        return businessPlat;
    }

    public void setBusinessPlat(int businessPlat) {
        this.businessPlat = businessPlat;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getPlatProductId() {
        return platProductId;
    }

    public void setPlatProductId(String platProductId) {
        this.platProductId = platProductId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Integer getRequestCount() {
        return requestCount;
    }

    public void setRequestCount(Integer requestCount) {
        this.requestCount = requestCount;
    }

    public Integer getRealQuantity() {
        return realQuantity;
    }

    public void setRealQuantity(Integer realQuantity) {
        this.realQuantity = realQuantity;
    }

    public String getDetailCount() {
        return detailCount;
    }

    public void setDetailCount(String detailCount) {
        this.detailCount = detailCount;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getPlatStoreId() {
        return platStoreId;
    }

    public void setPlatStoreId(String platStoreId) {
        this.platStoreId = platStoreId;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }


    //endregion
}
