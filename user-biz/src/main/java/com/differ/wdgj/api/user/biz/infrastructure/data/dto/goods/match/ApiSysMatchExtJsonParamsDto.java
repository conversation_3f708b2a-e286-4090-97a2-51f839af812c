package com.differ.wdgj.api.user.biz.infrastructure.data.dto.goods.match;

/**
 * 匹配实体扩展表json字段参数实体类
 * 对应表g_api_sysMatchExt字段JsonParams
 * <AUTHOR>
 * @date 2024-03-22 15:09
 */
public class ApiSysMatchExtJsonParamsDto {
    /**
    * 商品所在仓库名称。
    */
    private String whseName;

    /**
    * 供应商编码
    */
    private String suppliercode;

    /**
    * 商品链接
    */
    private String detailUrl;

    /**
    * 商品类型
    */
    private String productType;

    /**
    * 类目ID
    */
    private String categoryID;

    /**
    * 仓库编码标识 0：全国逻辑仓或7大仓；1：省仓 默认为全国逻辑仓或7大仓
    */
    private int warehouseFlag;

    /**
    * 店铺类型
    */
    private String shopType;

    /**
    * 平台店铺ID
    */
    private String platShopId;

    /**
    * 规格属性
    */
    private String skuProperty;

    //region get/set

    public String getWhseName() {
        return whseName;
    }

    public void setWhseName(String whseName) {
        this.whseName = whseName;
    }

    public String getSuppliercode() {
        return suppliercode;
    }

    public void setSuppliercode(String suppliercode) {
        this.suppliercode = suppliercode;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getCategoryID() {
        return categoryID;
    }

    public void setCategoryID(String categoryID) {
        this.categoryID = categoryID;
    }

    public int getWarehouseFlag() {
        return warehouseFlag;
    }

    public void setWarehouseFlag(int warehouseFlag) {
        this.warehouseFlag = warehouseFlag;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public String getPlatShopId() {
        return platShopId;
    }

    public void setPlatShopId(String platShopId) {
        this.platShopId = platShopId;
    }

    public String getSkuProperty() {
        return skuProperty;
    }

    public void setSkuProperty(String skuProperty) {
        this.skuProperty = skuProperty;
    }

    //endregion
}


