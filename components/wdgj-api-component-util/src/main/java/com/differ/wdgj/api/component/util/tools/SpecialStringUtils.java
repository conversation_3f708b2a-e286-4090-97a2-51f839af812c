package com.differ.wdgj.api.component.util.tools;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 特殊字符串处理
 *
 * <AUTHOR>
 * @date 2024/12/9 下午7:23
 */
public class SpecialStringUtils {
    //region 构造
    private SpecialStringUtils() {
    }
    //endregion

    //region emoji表情字符
    /**
     * 将emoji表情字符(四个字节 utf8mb4格式)替换为空
     *
     * @param source 原始数据字符串
     * @return 过滤后的字符串
     */
    public static String filterEmoji(String source) {
        if (StringUtils.isNotEmpty(source)) {
            return source.replaceAll("[\ud800\udc00-\udbff\udfff\ud800-\udfff]", "");
        } else {
            return source;
        }
    }

    /**
     * 将emoji表情字符(四个字节 utf8mb4格式)替换为别的字符串，默认为空字符串
     *
     * @param source     原始数据字符串
     * @param replaceStr 替换字符串
     * @return 替换后的字符串
     */
    public static String replaceEmoji(String source, String replaceStr) {
        if (StringUtils.isEmpty(replaceStr)) {
            replaceStr = StringUtils.EMPTY;
        }

        if (StringUtils.isNotEmpty(source)) {
            return source.replaceAll("[\ud800\udc00-\udbff\udfff\ud800-\udfff]", replaceStr);
        } else {
            return source;
        }
    }

    /**
     * 字符串是否包含标签符号
     *
     * @param source 原始数据字符串
     * @return true:包含标签符号
     */
    public static Boolean isHasEmoji(String source) {
        boolean result = false;
        if (StringUtils.isNotEmpty(source)) {
            Pattern pattern = Pattern.compile("[\ud800\udc00-\udbff\udfff\ud800-\udfff]");
            Matcher matcher = pattern.matcher(source);
            result = matcher.find();
        }
        return result;
    }

    /**
     * 字符串末尾追加新字符串
     *
     * @param oldString 原始字符串
     * @param newString 要追加的字符串
     * @param connectChar 拼接字符，为空时默认英文逗号 ","
     * @return 拼接字符串结果
     */
    public static String AddStringToEnd(String oldString, String newString, String connectChar) {
        String newStringResult = StringUtils.isEmpty (newString) ? "" : newString;

        //老字符串为空时
        if (StringUtils.isEmpty(oldString)) {
            return newStringResult;
        }
        else if (StringUtils.isEmpty(newStringResult)) {
            //老字符串不为空，新字符串为空时
            return oldString;
        }

        if (StringUtils.isEmpty(connectChar)) {
            //默认用逗号拼接
            connectChar = ",";
        }

        //新老字符串都不为空时，用拼接符拼接
        String result = StringUtils.removeEnd(oldString, connectChar) + connectChar + newStringResult;
        return result;
    }
    //endregion
}
