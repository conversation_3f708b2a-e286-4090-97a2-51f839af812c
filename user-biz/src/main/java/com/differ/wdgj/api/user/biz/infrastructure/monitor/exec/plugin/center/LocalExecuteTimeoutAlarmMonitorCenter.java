package com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.plugin.center;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.event.AlarmIntervalEvent;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.AbstractExecuteQueueMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.TimeoutQueue;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * @Description 本地执行超时的实时报警监控中心, 相关定时任务:LocalExecuteMonitorJob，设计：https://s.jkyun.biz/PtwivXO 执行监控基础组件
 * <AUTHOR>
 * @Date 2023/12/14 17:14
 */
public class LocalExecuteTimeoutAlarmMonitorCenter extends AbstractExecuteQueueMonitorCenter {

    private static final Logger LOG = LoggerFactory.getLogger(LocalExecuteTimeoutAlarmMonitorCenter.class);

    /**
     * 当完成一个任务链时，执行处理
     *
     * @param queue
     * @param success
     */
    @Override
    protected void doFinish(TimeoutQueue queue, boolean success) {
        // 报警不作业务处理
    }

    /**
     * 是否超时，用于外部定时任务检测
     * 注意：当超时的时候，如果不想清除队列中的数据，业务处理中不要使用queue.getQueue().poll(),应使用queue.getQueue().toArray()处理数据
     *
     * @param queue             执行队列
     * @param maxTimeoutSeconds 超时的阈值
     */
    @Override
    protected void doTimeout(TimeoutQueue queue, int maxTimeoutSeconds) {
        if (queue == null) {
            return;
        }
        LocalDateTime firstTime = queue.getFirstTime();
        if (firstTime == null) {
            return;
        }
        String alarmText = String.format("执行超时报警（第%d次）,耗时：%d秒，超过报警值：%d", queue.getTimeoutCount(), Duration.between(firstTime, LocalDateTime.now()).getSeconds(), maxTimeoutSeconds);
        BeanContextUtil.publishEvent(new AlarmIntervalEvent(AlarmIntervalTypeEnum.LOCAL_EXEC_TIMEOUT, this.monitorCode, alarmText));
    }

    /**
     * 超时的时候是否清理数据，默认清理
     *
     * @param queue 要清除的队列
     * @return
     */
    @Override
    protected boolean needClearOnTimeout(TimeoutQueue queue) {
        // 报警多少次后清理数据
        return queue.getTimeoutCount() > NumberUtils.toInt(ConfigKeyUtils.getConfigValue(ConfigKeyEnum.EXEC_MONITOR_TIMEOUT_ALARM_MAX_COUNT), 100);
    }
}
