package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.plat;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.plat.CallSplitServiceCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.CallSplitServiceKey;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.CallSplitServiceLocalCacheDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.GloSplitServiceDto;
import org.apache.commons.lang3.RandomUtils;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 菠萝派网关配置内存缓存
 * </p>对应表 glo_splitService
 * </p>对应Admin运维功能-菠萝接口拆分服务
 *
 * <AUTHOR>
 * @date 2024/8/19 下午2:32
 */
public class CallSplitServiceLocalCache extends AbstractLocalCache<CallSplitServiceKey, CallSplitServiceLocalCacheDto> {
    //region 构造
    private CallSplitServiceLocalCache() {
        this.expire = RandomUtils.nextInt(30, 60);
        timeUnit = TimeUnit.SECONDS;
    }
    //endregion

    //region 单例

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static CallSplitServiceLocalCache singleton() {
        return CallSplitServiceLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final CallSplitServiceLocalCache instance;

        private SingletonEnum() {
            instance = new CallSplitServiceLocalCache();
        }
    }

    //endregion

    // region 重写基类方法
    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key 键
     * @return 值
     */
    @Override
    protected CallSplitServiceLocalCacheDto loadSource(CallSplitServiceKey key) {
        // 查询所有菠萝派网关配置
        List<GloSplitServiceDto> allCallSplitServices = CallSplitServiceCache.singleton().getAll();
        // 启用的菠萝派网关配置
        List<GloSplitServiceDto> enableCallSplitServices = allCallSplitServices.stream().filter(GloSplitServiceDto::isEnable).collect(Collectors.toList());
        // 排序
        enableCallSplitServices.sort(Comparator.comparingInt(GloSplitServiceDto::getPriority).reversed());
        // 构建内存缓存传输对象
        CallSplitServiceLocalCacheDto callSplitServiceLocalCacheDto = new CallSplitServiceLocalCacheDto();
        callSplitServiceLocalCacheDto.setCallSplitServices(enableCallSplitServices);
        return callSplitServiceLocalCacheDto;
    }
    // endregion
}
