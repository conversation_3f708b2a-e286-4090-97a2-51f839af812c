package com.differ.wdgj.api.user.biz.infrastructure.common;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * 日志工厂
 *
 * <AUTHOR>
 * @date 2024/4/8 15:50
 */
public class LogFactory {

    /**
     * 日志对象缓存
     */
    private static Map<String, Logger> loggerMap = new ConcurrentHashMap<>();

    /**
     * 获取日志对象
     *
     * @param obj
     * @return
     */
    public static Logger get(Object obj) {
        return get(obj.getClass());
    }

    /**
     * 获取日志对象
     *
     * @param clazz
     * @return
     */
    public static Logger get(Class clazz) {
        return get(clazz.getName());
    }

    /**
     * 获取日志对象
     *
     * @param caption
     * @return
     */
    public static Logger get(String caption) {
        return loggerMap.computeIfAbsent(caption, k -> LoggerFactory.getLogger(k));
    }


    public static boolean isInfoEnabled(String member, String caption) {
        Logger logger = get(caption);
        return logger.isInfoEnabled();
    }

    public static boolean isInfoEnabled(String caption) {
        Logger logger = get(caption);
        return logger.isInfoEnabled();
    }

    //region info日志

    /**
     * info日志
     *
     * @param caption 标题
     */
    public static void info(String caption, String logContent) {
        Logger logger = get(caption);
        logger.info(logContent);
    }

    /**
     * info日志
     *
     * @param caption 标题
     */
    public static void info(String caption, String format, Object... arguments) {
        Logger logger = get(caption);
        logger.info(format, arguments);
    }

    /**
     * info日志
     *
     * @param caption 标题
     */
    public static void info(String caption, Supplier<StringBuilder> funLogContent) {
        Logger logger = get(caption);
        if (logger.isInfoEnabled()) {
            logger.info(funLogContent.get().toString());
        }
    }

    /**
     * info日志
     *
     * @param caption       标题
     * @param key           日志灰度标识
     * @param funLogContent 日志内容
     */
    public static void info(String caption, String key, Supplier<StringBuilder> funLogContent) {
        Logger logger = get(caption);
        if (logger.isInfoEnabled() && ConfigKeyUtils.isActionWdgjMultipleMatchValue(ConfigKeyEnum.IsAction_JavaElkLog_Member_Info, new String[]{"@"}, caption, key)) {
            logger.info(String.format("【%s】%s", key, funLogContent.get().toString()));
        }
    }
    //endregion

    //region warn日志

    /**
     * warn日志
     *
     * @param caption
     * @return
     */
    public static void warn(String caption, String logContent) {
        Logger logger = get(caption);
        logger.warn(logContent);
    }

    /**
     * warn日志
     *
     * @param caption
     * @return
     */
    public static void warn(String caption, String format, Object... arguments) {
        Logger logger = get(caption);
        logger.warn(format, arguments);
    }

    /**
     * warn日志
     *
     * @param caption
     * @return
     */
    public static void warn(String caption, String logContent, Throwable th) {
        Logger logger = get(caption);
        logger.warn(logContent, th);
    }
    //endregion

    //region error日志

    /**
     * error日志
     *
     * @param caption
     * @return
     */
    public static void error(String caption, String logContent) {
        Logger logger = get(caption);
        logger.info(logContent);
    }

    /**
     * error日志
     *
     * @param caption
     * @return
     */
    public static void error(String caption, String format, Object... arguments) {
        Logger logger = get(caption);
        logger.error(format, arguments);
    }

    /**
     * error日志
     *
     * @param caption
     * @return
     */
    public static void error(String caption, String logContent, Throwable th) {
        Logger logger = get(caption);
        logger.error(logContent, th);
    }
    //endregion
}
