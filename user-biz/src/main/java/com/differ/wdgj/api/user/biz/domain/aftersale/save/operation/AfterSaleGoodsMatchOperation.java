package com.differ.wdgj.api.user.biz.domain.aftersale.save.operation;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.AfterSaleGoodsMatchResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleGoodsSysMatchItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.wdgj.MatchOrderGoodsResultDto;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.WdgjStoredProcedureMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 售后商品匹配操作类
 *
 * <AUTHOR>
 * @date 2024/7/24 下午2:10
 */
public class AfterSaleGoodsMatchOperation {
    //region 常量
    /**
     * 日志
     */
    private static final Logger log = LoggerFactory.getLogger(AfterSaleGoodsMatchOperation.class);
    //endregion

    //region 构造
    private AfterSaleGoodsMatchOperation() {
    }
    //endregion

    //region erp商品匹配

    /**
     * 退货/退款商品匹配
     *
     * @param context       上下文
     * @param afterSaleNo   售后单号
     * @param apiTradeGoods 原始单商品
     * @param refundGoods   退货/退款商品
     * @return 匹配结果
     */
    public static AfterSaleGoodsMatchResult refundGoodsErpMatch(AfterSaleSaveContext context, String afterSaleNo, ApiTradeGoodsDO apiTradeGoods, ApiReturnDetailDO refundGoods) {
        try {
            // 优先使用原始单匹配数据
            MatchOrderGoodsResultDto goodsMatchResult = new MatchOrderGoodsResultDto();
            if (apiTradeGoods != null && apiTradeGoods.getGoodsId() > 0) {
                goodsMatchResult.setGoodsId(apiTradeGoods.getGoodsId());
                goodsMatchResult.setSpecId(apiTradeGoods.getSpecId());
                goodsMatchResult.setbFit(apiTradeGoods.getbFit());

                return AfterSaleGoodsMatchResult.success(goodsMatchResult);
            }
            // 基于存储过程匹配
            goodsMatchResult = goodsMatchStoredProcedure(context.getMemberName(), context.getShopId(), refundGoods.getOuterId(), refundGoods.getGoodsTitle(), refundGoods.getSku());

            return AfterSaleGoodsMatchResult.success(goodsMatchResult);
        } catch (Exception e) {
            // 错误日志
            String logContent = String.format("【%s】【%s】售后退货商品匹配失败，售后单号：%s, 失败原因：%s", context.getMemberName(), context.getShopId(), afterSaleNo, e.getMessage());
            log.error(logContent, e);

            return AfterSaleGoodsMatchResult.failed(logContent);
        }
    }

    /**
     * 换货/补寄商品匹配
     *
     * @param context           上下文
     * @param afterSaleNo       售后单号
     * @param goodsSysMatchItem 商品匹配表数据
     * @param exchangeGoods     换货/补寄商品
     * @return 匹配结果
     */
    public static AfterSaleGoodsMatchResult exchangeGoodsErpMatch(AfterSaleSaveContext context, String afterSaleNo, AfterSaleGoodsSysMatchItem goodsSysMatchItem, ApiReturnDetailTwoDO exchangeGoods) {
        try {
            // 优先使用商品匹配表数据
            MatchOrderGoodsResultDto goodsMatchResult = new MatchOrderGoodsResultDto();
            if (goodsSysMatchItem != null && goodsSysMatchItem.getGoodsID() > 0) {
                goodsMatchResult.setGoodsId(goodsSysMatchItem.getGoodsID());
                goodsMatchResult.setSpecId(goodsSysMatchItem.getSpecID());
                goodsMatchResult.setbFit(goodsSysMatchItem.getGoodsType());

                return AfterSaleGoodsMatchResult.success(goodsMatchResult);
            }
            // 基于存储过程匹配
            goodsMatchResult = goodsMatchStoredProcedure(context.getMemberName(), context.getShopId(), exchangeGoods.getOuterId(), exchangeGoods.getGoodsTitle(), exchangeGoods.getSku());

            return AfterSaleGoodsMatchResult.success(goodsMatchResult);
        } catch (Exception e) {
            // 错误日志
            String logContent = String.format("【%s】【%s】售后换货商品匹配失败，售后单号：%s, 失败原因：%s", context.getMemberName(), context.getShopId(), afterSaleNo, e.getMessage());
            log.error(logContent, e);

            return AfterSaleGoodsMatchResult.failed(logContent);
        }
    }
    //endregion

    //region 历史售后商品匹配

    /**
     * 是否匹配历史退货/退款商品匹配
     *
     * @return 结果
     */
    public static boolean isMatchHistoryRefundGoods(ApiReturnDetailDO newReturnGoods, ApiReturnDetailDO oldReturnGoods) {
        // 使用平台商品Id+平台规格Id匹配
        if (StringUtils.isNotEmpty(newReturnGoods.getPlatGoodsId())) {
            return StringUtils.equals(newReturnGoods.getPlatGoodsId(), oldReturnGoods.getPlatGoodsId()) && StringUtils.equals(newReturnGoods.getPlatSkuId(), oldReturnGoods.getPlatSkuId());
        }
        // 使用平台规格Id匹配
        if (StringUtils.isNotEmpty(newReturnGoods.getPlatSkuId())) {
            return StringUtils.equals(newReturnGoods.getPlatSkuId(), oldReturnGoods.getPlatSkuId());
        }
        // 使用子订单号进行匹配
        if (StringUtils.isNotEmpty(newReturnGoods.getOid())) {
            return StringUtils.equals(newReturnGoods.getOid(), oldReturnGoods.getOid());
        }

        return false;
    }

    /**
     * 是否匹配历史换货/补寄商品匹配
     *
     * @return 结果
     */
    public static boolean isMatchHistoryExchangeGoods(ApiReturnDetailTwoDO newExchangeGoods, ApiReturnDetailTwoDO oldExchangeGoods) {
        // 使用平台商品Id+平台规格Id匹配
        if (StringUtils.isNotEmpty(newExchangeGoods.getPlatGoodsId())) {
            return StringUtils.equals(newExchangeGoods.getPlatGoodsId(), oldExchangeGoods.getPlatGoodsId()) && StringUtils.equals(newExchangeGoods.getPlatSkuId(), oldExchangeGoods.getPlatSkuId());
        }
        // 使用平台规格Id匹配
        if (StringUtils.isNotEmpty(newExchangeGoods.getPlatSkuId())) {
            return StringUtils.equals(newExchangeGoods.getPlatSkuId(), oldExchangeGoods.getPlatSkuId());
        }

        return false;
    }
    //endregion

    //region 原始单商品匹配

    /**
     * 是否匹配原始单货品(历史逻辑兼容，子类禁止使用，一般只需要考虑商品级)
     *
     * @param apiTradeGoods 原始的那货品
     * @param refundOrder   售后单
     * @param refundGoods   退货退款单退货货品
     * @return 结果
     */
    public static boolean isRefundMatchApiTradeGoods(ApiTradeGoodsDO apiTradeGoods, BusinessGetRefundOrderResponseOrderItem refundOrder, BusinessGetRefundResponseRefundGoodInfo refundGoods) {
        // 匹配商品级
        if (refundGoods != null) {
            // 平台商品id不为空，优先使用平台商品Id+平台规格Id匹配
            String tradeGoodsSkuId = StringUtils.defaultIfEmpty(apiTradeGoods.getPlatSkuID(), StringUtils.EMPTY);
            String refundGoodsSkuId = StringUtils.defaultIfEmpty(refundGoods.getSku(), StringUtils.EMPTY);
            if (StringUtils.isNotEmpty(refundGoods.getPlatProductId())) {
                return StringUtils.equals(apiTradeGoods.getPlatGoodsID(), refundGoods.getPlatProductId()) &&
                        StringUtils.equals(tradeGoodsSkuId, refundGoodsSkuId);
            }
            // 如果平台商品Id为空，平台规格Id不为空，依赖平台规格Id匹配
            if (StringUtils.isNotEmpty(refundGoods.getSku())) {
                return StringUtils.equals(apiTradeGoods.getPlatSkuID(), refundGoods.getSku());
            }
            // 使用子订单号进行匹配
            if (StringUtils.isNotEmpty(refundGoods.getSubTradeNo())) {
                return StringUtils.equals(apiTradeGoods.getOid(), refundGoods.getSubTradeNo());
            }
        }
        // 匹配订单级
        if (refundOrder != null) {
            // 平台商品id不为空，优先使用平台商品Id匹配
            if (StringUtils.isNotEmpty(refundOrder.getPlatProductId())) {
                return StringUtils.equals(apiTradeGoods.getPlatGoodsID(), refundOrder.getPlatProductId());
            }
            // 使用子订单号进行匹配
            if (StringUtils.isNotEmpty(refundOrder.getSubPlatOrderNo())) {
                return StringUtils.equals(apiTradeGoods.getOid(), refundOrder.getSubPlatOrderNo());
            }
        }

        return false;
    }

    /**
     * 是否匹配原始单货品(历史逻辑兼容，子类禁止使用，一般只需要考虑商品级)
     *
     * @param apiTradeGoods 原始的那货品
     * @param refundGoods   换货单退货货品
     * @return 结果
     */
    public static boolean isExchangeMatchApiTradeGoods(ApiTradeGoodsDO apiTradeGoods, BusinessGetExchangeResponseRefundGoodInfo refundGoods) {
        // 匹配商品级
        if (refundGoods != null) {
            // 平台商品id不为空，优先使用平台商品Id+平台规格Id匹配
            String tradeGoodsSkuId = StringUtils.defaultIfEmpty(apiTradeGoods.getPlatSkuID(), StringUtils.EMPTY);
            String refundGoodsSkuId = StringUtils.defaultIfEmpty(refundGoods.getSku(), StringUtils.EMPTY);
            if (StringUtils.isNotEmpty(refundGoods.getPlatProductId())) {
                return StringUtils.equals(apiTradeGoods.getPlatGoodsID(), refundGoods.getPlatProductId()) &&
                        StringUtils.equals(tradeGoodsSkuId, refundGoodsSkuId);
            }
            // 如果平台商品Id为空，平台规格Id不为空，依赖平台规格Id匹配
            if (StringUtils.isNotEmpty(refundGoods.getSku())) {
                return StringUtils.equals(apiTradeGoods.getPlatSkuID(), refundGoods.getSku());
            }
            // 使用子订单号进行匹配
            if (StringUtils.isNotEmpty(refundGoods.getSubTradeNo())) {
                return StringUtils.equals(apiTradeGoods.getOid(), refundGoods.getSubTradeNo());
            }
        }

        return false;
    }

    //endregion

    //region 商品匹配数据
    /**
     * 是否匹配原始单货品(历史逻辑兼容，子类禁止使用，一般只需要考虑商品级)
     *
     * @param goodsSysMatchItem 商品匹配数据
     * @param exchangeGoods     换货商品
     * @return 结果
     */
    public static boolean isExchangeMatchGoodsSysMatch(AfterSaleGoodsSysMatchItem goodsSysMatchItem, BusinessGetExchangeResponseExchangeGoodInfo exchangeGoods) {
        // 匹配商品级
        if (exchangeGoods != null) {
            // 平台商品id不为空，优先使用平台商品Id+平台规格Id匹配
            String goodsSysMatchSkuId = StringUtils.defaultIfEmpty(goodsSysMatchItem.getSkuID(), StringUtils.EMPTY);
            String exchangeGoodsSkuId = StringUtils.defaultIfEmpty(exchangeGoods.getSku(), StringUtils.EMPTY);
            String exchangeGoodsSkuIdDefaultZero = StringUtils.defaultIfEmpty(exchangeGoods.getSku(), NumberUtils.INTEGER_ZERO.toString());
            if (StringUtils.isNotEmpty(exchangeGoods.getPlatProductId())) {
                return StringUtils.equals(goodsSysMatchItem.getNumiid(), exchangeGoods.getPlatProductId()) &&
                        (StringUtils.equals(goodsSysMatchSkuId, exchangeGoodsSkuId) ||
                         StringUtils.equals(goodsSysMatchSkuId, exchangeGoodsSkuIdDefaultZero));
            }
            // 如果平台商品Id为空，平台规格Id不为空，依赖平台规格Id匹配
            if (StringUtils.isNotEmpty(exchangeGoods.getSku())) {
                return StringUtils.equals(goodsSysMatchItem.getSkuID(), exchangeGoods.getSku());
            }
        }

        return false;
    }
    //endregion

    //region 私有方法

    /**
     * 调用存储过程进行商品匹配
     *
     * @param vipUser      会员名
     * @param outShopId    外部店铺Id
     * @param tradeGoodsNo 商品编码
     * @param goodsName    商品名称
     * @param specName     规格名称
     * @return 匹配结果
     */
    private static MatchOrderGoodsResultDto goodsMatchStoredProcedure(String vipUser, int outShopId, String tradeGoodsNo, String goodsName, String specName) {
        final MatchOrderGoodsResultDto[] matchOrderGoodsResults = new MatchOrderGoodsResultDto[1];
        WdgjStoredProcedureMapper wdgjStoredProcedureMapper = BeanContextUtil.getBean(WdgjStoredProcedureMapper.class);
        DBSwitchUtil.doDBWithUser(vipUser, () -> matchOrderGoodsResults[0] = wdgjStoredProcedureMapper.matchOrderGoods(outShopId, tradeGoodsNo, goodsName, specName));
        return matchOrderGoodsResults[0];
    }
    //endregion
}
