package com.differ.wdgj.api.user.biz.domain.apicall.plugins.aftersale;

import com.differ.wdgj.api.user.biz.domain.apicall.AbstractApiCall;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.PolyAPIBusinessGetRefundOrderRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.PolyAPIBusinessGetRefundOrderResponseBizData;

/**
 * 下载退货退款单
 *
 * <AUTHOR>
 * @date 2024/8/26 下午6:25
 */
public class GetRefundOrderApiCall extends AbstractApiCall<PolyAPIBusinessGetRefundOrderRequestBizData, PolyAPIBusinessGetRefundOrderResponseBizData> {
    /**
     * 获取接口类型
     *
     * @return 接口类型
     */
    @Override
    protected PolyAPITypeEnum getApiType() {
        return PolyAPITypeEnum.BUSINESS_GETREFUND;
    }
}
