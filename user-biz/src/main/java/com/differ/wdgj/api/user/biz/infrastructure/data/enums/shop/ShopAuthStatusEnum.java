package com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 店铺授权状态
 *
 * <AUTHOR>
 * @date 2024/9/18 下午2:54
 */
public enum ShopAuthStatusEnum implements ValueEnum {
    /**
     * 正常授权状态
     */
    AUTHORIZED(0),

    /**
     * 未授权
     */
    UNAUTHORIZED(1),

    /**
     * 授权失效
     */
    INVALID(2),

    /**
     * 授权取消
     */
    CANCEL(3);

    // 定义枚举值对应的字段
    private final int value;

    // 构造方法
    ShopAuthStatusEnum(int value) {
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据平台值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static ShopAuthStatusEnum create(int value) {
        return EnumConvertCacheUtil.convert(value, ShopAuthStatusEnum.class, EnumConvertType.VALUE);
    }
}
