package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;

import java.util.Map;

/**
 * HASH缓存增强
 *
 * <AUTHOR>
 * @date 2021-12-16 16:41
 */
public interface IHashEnhanceCache<T> {

    /**
     * 查询缓存
     * <p>
     * 1、单个查询
     * 2、缓存不存在时从数据源获取并同步到缓存
     *
     * @param hashField 字段
     * @return 值
     */
    T getAndSyncIfAbsent(String hashField);

    /**
     * 查询缓存
     * <p>
     * 1、批量查询
     * 2、缓存不存在时从数据源获取并同步到缓存
     *
     * @param hashFields 字段集合
     * @return 值集合
     */
    Map<String, T> getAndSyncIfAbsent(String... hashFields);
}
