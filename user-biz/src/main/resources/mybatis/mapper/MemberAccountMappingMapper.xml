<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.MemberAccountMappingMapper" >
  <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberAccountMappingDO" >
    <!--
      WARNING - @mbg.generated
    -->
    <id column="UserName" property="userName" jdbcType="INTEGER" />
    <result column="OutPlatTypes" property="outPlatTypes" jdbcType="VARCHAR" />
  </resultMap>

  <!-- 查询基本列 -->
  <sql id="select_base_column">
    UserName, OutPlatTypes, OutAccount, OutAccountCode, SessionKey, ServerName, ServerIPPort, IsActived, CreateTime, LastModifyTime, DataVersion, ChangeServer
  </sql>

  <select id="selectByUserName"
            resultMap="BaseResultMap"
            parameterType="java.lang.String">
    select <include refid="select_base_column"/> from  member_accountmapping where UserName = #{userName} limit 1</select>
</mapper>