package com.differ.wdgj.api.component.wait;

import com.differ.wdgj.api.component.wait.data.*;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 排队接口抽象
 * <AUTHOR>
 * @Date 2021/9/10 14:26
 */
public interface WaitLine {
    /**
     * 初始化，任务执行方法
     *
     * @param context
     */
    void init(WaitContext context);

    /**
     * 初始化，任务执行方法
     *
     * @param context
     * @param waitStrategy
     */
    void init(WaitContext context, WaitStrategy waitStrategy);

    /**
     * 获取上下文
     *
     * @return
     */
    WaitContext getWaitContext();

    /**
     * 刷新配置
     *
     * @param args
     */
    void refreshArgs(WaitConfigArgs args);

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return true:放入排队, false:已直接执行
     */
    boolean putWaitOrExec(AbstractWaitEntity entity);

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return 排队状态 {@link WaitStatusEnum}
     */
    WaitStatusEnum putAndGetStatus(AbstractWaitEntity entity);

    /**
     * 取消排队
     *
     * @param waitUniqueId 任务ID
     * @return 返回是否有任务被取消掉
     */
    boolean cancel(String waitUniqueId);

    /**
     * 取消排队
     *
     * @param waitUniqueIds 任务IDs
     * @return
     */
    int cancel(List<String> waitUniqueIds);

    /**
     * 是否排队中或查询排队状态
     *
     * @param waitUniqueId
     * @return
     */
    boolean isWaiting(String waitUniqueId);

    /**
     * 是否排队中或查询排队状态
     *
     * @param waitUniqueIds
     * @return
     */
    Map<String, Boolean> isWaiting(List<String> waitUniqueIds);

    /**
     * 收到消息：处理下一个MQ事件
     * @return true:取到任务并且处理，false:没有取到任务
     */
    boolean receiveMessageHandleNext();

    /**
     * 执行异步任务完成后回调结果
     *
     * @param waitUniqueId 任务唯一ID
     */
    void callbackResultOnAsync(String waitUniqueId);

    /**
     * 排队监控信息查询
     *
     * @param queryInfo
     * @return
     */
    WaitMonitorResult getMonitorInfo(MonitorQueryArgs queryInfo);

    /**
     * 检查并修复数据容错
     *
     * @return 修复的记录数
     */
    int checkAndRepair();

    /**
     * 获取排队组件
     *
     * @param key 排队键
     * @return
     */
    WaitLine getHandler(String key);

    /**
     * 获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    Map<String, String> getWaitData(List<String> waitUniqueIds);

    /**
     * 获取排队任务总数
     *
     * @return
     */
    Long getWaitSum();

    /**
     * 获取执行中任务总数
     *
     * @return
     */
    Long getExecSum();

    /**
     * 获取排队任务数据
     *
     * @param bizData 请求参数
     * @return
     */
    Map<String, String> getWaitData(WaitQueryRequestBiaData bizData);

    /**
     * 倒序获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    LinkedHashMap<String, String> getWaitDataDesc(List<String> waitUniqueIds);

    /**
     * 获取排队任务key
     *
     * @return
     */
    List<String> getWaitKeys(long start, long end);

    /**
     * 获取执行中任务key
     *
     * @return
     */
    List<String> getExecKeys();

    /**
     * 删除排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    boolean deleteWaitData(List<String> waitUniqueIds);
}
