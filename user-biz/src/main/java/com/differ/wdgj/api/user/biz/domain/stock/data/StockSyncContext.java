package com.differ.wdgj.api.user.biz.domain.stock.data;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;

/**
 * 库存同步上下文
 *
 * <AUTHOR>
 * @date 2024-02-26 14:09
 */
public class StockSyncContext {

    /**
     * 会员名
     */
    private String vipUser;

    /**
     * 店铺Id
     */
    private Integer shopId;

    /**
     * 平台
     */
    private PolyPlatEnum plat;

    /**
     * 店铺基础信息
     */
    private ApiShopBaseDto shopBase;

    /**
     * 库存同步配置
     * todo:预留，对象二期建
     */
    private String stockConfig;

    /**
     * 触发类型
     */
    private StockSyncTriggerTypeEnum triggerType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 上下文 ID
     */
    private Integer contextId;

    //region get/set

    public String getVipUser() {
        return vipUser;
    }

    public void setVipUser(String vipUser) {
        this.vipUser = vipUser;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    public ApiShopBaseDto getShopBase() {
        return shopBase;
    }

    public void setShopBase(ApiShopBaseDto shopBase) {
        this.shopBase = shopBase;
    }

    public String getStockConfig() {
        return stockConfig;
    }

    public void setStockConfig(String stockConfig) {
        this.stockConfig = stockConfig;
    }

    public StockSyncTriggerTypeEnum getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(StockSyncTriggerTypeEnum triggerType) {
        this.triggerType = triggerType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Integer getContextId() {
        return contextId;
    }

    public void setContextId(Integer contextId) {
        this.contextId = contextId;
    }
    //endregion
}
