package com.differ.wdgj.api.user.biz.tasks.job.cluster.core;

import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.jackyun.framework.component.utils.id.IdWorkerUtil;
import com.differ.wdgj.api.component.task.single.core.AbstractSingleBeanJob;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.tasks.job.cluster.strategy.ClusterJobExecStrategy;
import com.differ.wdgj.api.user.biz.tasks.job.cluster.strategy.JobNameHashExecStrategy;
import org.quartz.JobExecutionContext;

/**
 * @Description 集群任务基类，在一个集群下的所有副本中，只有一个副本会执行定时任务
 * <AUTHOR>
 * @Date 2023/11/21 13:58
 */
public abstract class BaseClusterJob extends AbstractSingleBeanJob {

    /**
     * 日志标题
     */
    protected String logCaption = "集群单体任务";
    /**
     * 执行策略
     */
    private ClusterJobExecStrategy strategy;

    /**
     * 是否支持在本站点运行
     *
     * @param sitesToRun 可运行的站点集合，由注解参数设置
     * @return
     */
    @Override
    public boolean runOnSite(String[] sitesToRun) {
        return SystemAppConfig.get().runOnSite(sitesToRun);
    }

    /**
     * 业务处理
     *
     * @param context
     */
    @Override
    protected void doWork(JobExecutionContext context) {

        // 根据策略判断：是否本副本执行任务
        ClusterJobExecStrategy jobExecStrategy = getJobExecStrategy();
        if (jobExecStrategy == null) {
            return;
        }
        if (!jobExecStrategy.executable(this.getJobParameter().jobName())) {
            return;
        }
        // 重置日志ID
        CommonContextHolder.setLoggerSn(IdWorkerUtil.getId());
        // 执行任务
        this.doWork();
    }

    /**
     * 获取执行策略
     *
     * @return
     */
    private ClusterJobExecStrategy getJobExecStrategy() {
        if (strategy == null) {
            // 从注解上获取指定策略
            ClusterJobParameter[] jobParameters = getClass().getAnnotationsByType(ClusterJobParameter.class);
            Class<? extends ClusterJobExecStrategy> jobExecStrategy = JobNameHashExecStrategy.class;
            if (jobParameters.length > 0) {
                jobExecStrategy = jobParameters[0].jobExecStrategy();
            }
            try {
                strategy = jobExecStrategy.newInstance();
            } catch (Throwable t) {
                log.error("集群单体任务执行策略创建失败", t);
            }
        }
        return strategy;
    }
}
