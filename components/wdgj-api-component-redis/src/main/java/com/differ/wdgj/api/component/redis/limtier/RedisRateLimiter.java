package com.differ.wdgj.api.component.redis.limtier;

/**
 * 基于redis的限流接口
 * <AUTHOR>
 * @date 2021/6/16 11:27
 */
public interface RedisRateLimiter {

    /**
     * 限流方法,请求个数为1(限流关键字)
     *
     * @param key 限流关键字
     * @return 允许通过的请求个数，0：不通过，大于0表示通过
     */
    int acquire(String key);

    /**
     * 限流方法(限流关键字,请求个数)
     *
     * @param key          限流关键字
     * @param requestCount 请求个数
     * @return 允许通过的请求个数，0：不通过，大于0表示通过
     */
    int acquire(String key, int requestCount);

    /**
     * 限流方法(限流关键字,请求个数)
     *
     * @param key 限流关键字
     * @param requestCount 请求个数
     * @return 限流结果：是否限流和剩余可通过数，true：限流，false：不限流
     */
    LimitResult checkLimit(String key, int requestCount);

}
