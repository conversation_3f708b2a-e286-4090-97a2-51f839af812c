package com.differ.wdgj.api.user.biz.infrastructure.work;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.demo.data.TestWorkArgs;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 工作任务
 *
 * <AUTHOR>
 * @date 2024/9/11 上午11:26
 */
@Ignore
public class WorkTest extends AbstractSpringTest {
    /**
     * 创建工作任务
     */
    @Test
    public void createWorkTest() {
        WorkFacade facade = new WorkFacade();
        TestWorkArgs d = new TestWorkArgs();
        WorkContext workContext = new WorkContext();
        workContext.setMember("api2017");
        workContext.setShopId(1360);
        workContext.setTriggerType(TriggerTypeEnum.AUTO);
        workContext.setCreator("新API");
        workContext.setWorkType(WorkEnum.DEMO_TEST);

        WorkData<TestWorkArgs> workData = WorkData.of(workContext, d);
        CreateResult createResult = facade.createWork(workData);
    }

/**
     * 执行工作任务
     */
    @Test
    public void execWorkTest() {
        WorkFacade facade = new WorkFacade();
        TestWorkArgs d = new TestWorkArgs();
        WorkContext workContext = new WorkContext();
        workContext.setMember("api2017");
        workContext.setShopId(1360);
        workContext.setTriggerType(TriggerTypeEnum.AUTO);
        workContext.setCreator("新API");
        workContext.setWorkType(WorkEnum.DEMO_TEST);
        WorkData<TestWorkArgs> workData = WorkData.of(workContext, d);

        facade.execWork(workData, "-5444670628447173360", false);
    }
}
