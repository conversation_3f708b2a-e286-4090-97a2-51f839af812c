package com.differ.wdgj.api.user.biz.tasks.mq.jmq.core;

import com.differ.jackyun.framework.component.jmq.core.AbstractJMQSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * API JMQ 发送者
 *
 * <AUTHOR>
 * @date 2022-06-10 15:19
 */
public abstract class ApiJMQSender extends AbstractJMQSender<String> {

    // region 变量 & 常量

    protected Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 日志标题
     */
    protected final static String LOG_CAP = "[JMQ发送消息]";

    /**
     * 信号量
     */
    protected final Semaphore semaphore = new Semaphore(400, true);

    // endregion

    // region 公共方法

    /**
     * 发送消息
     *
     * @param message 消息
     */
    public void sendMessage(String message) {

        // 解析队列code
        String code = this.resolveCode();

        // 开启信号量限制
        this.sendBySemaphoreLimit(code, message, 500);
    }

    /**
     * 发送消息到延时队列
     * <p>
     * 1、取注解 ApiJMQ 上的延时时间
     * 2、延时时间必须在运维后台配置过
     *
     * @param message 消息
     */
    public void sendToDelay(String message) {

        // 解析延时时间
        long delayTime = this.resolveDelayTime();

        // 延时时间 = 0，发普通队列
        if (delayTime == 0L) {
            this.sendMessage(message);
        }
        // 延时时间 > 0，发延时队列
        else {
            this.send(message, delayTime);
        }
    }

    /**
     * 发送消息到延时队列
     * <p>
     * 1、业务端指定延时时间
     * 2、延时时间必须在运维后台配置过
     *
     * @param message 消息
     */
    public void sendToDelay(String message, long delayTime) {
        this.send(message, delayTime);
    }

    // endregion

    // region 私有方法

    /**
     * 发送消息
     *
     * @param code           队列code
     * @param message        消息
     * @param acquireTimeout 信号量申请超时时间（单位：毫秒）
     */
    private void sendBySemaphoreLimit(String code, String message, int acquireTimeout) {

        // 是否申请成功
        boolean acquireSuccess = false;
        try {

            // 申请信号量（上游业务层未做持久化处理，为防止消息丢失，申请失败仍然使其通过，仅记录日志）
            acquireSuccess = this.semaphore.tryAcquire(acquireTimeout, TimeUnit.MILLISECONDS);

            // 发送消息
            super.send(message);
        } catch (Exception e) {
            log.error(String.format("%s队列%s发送消息失败，消息：%s", LOG_CAP, code, message), e);
        } finally {

            // 释放信号量
            if (acquireSuccess) {
                this.semaphore.release();
            } else {
                log.error(String.format("%s队列%s发送消息时获取信号量失败，消息：%s", LOG_CAP, code, message));
            }
        }
    }

    /**
     * 解析队列code
     *
     * @return 队列code
     */
    private String resolveCode() {
        try {

            // 获取注解
            ApiJMQ[] annotations = this.getClass().getAnnotationsByType(ApiJMQ.class);
            if (annotations.length == 0) {
                return "";
            }

            // 获取code
            ApiJMQ mqParameter = annotations[0];
            return mqParameter.code();
        } catch (Exception e) {
            log.error("JMQ解析队列code失败", e);
            return "";
        }
    }

    /**
     * 解析延时时间
     *
     * @return 延时时间
     */
    private long resolveDelayTime() {

        // 获取注解
        ApiJMQ[] annotations = this.getClass().getAnnotationsByType(ApiJMQ.class);
        if (annotations.length == 0) {
            return 0L;
        }

        // 获取延时时间（单位：秒）
        long delayTime = annotations[0].delayTime();

        // 转换为毫秒
        return delayTime * 1000;
    }

    // endregion
}
