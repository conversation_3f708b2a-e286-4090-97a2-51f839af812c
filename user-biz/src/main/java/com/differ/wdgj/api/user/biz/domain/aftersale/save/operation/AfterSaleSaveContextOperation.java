package com.differ.wdgj.api.user.biz.domain.aftersale.save.operation;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleOutResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleParam;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.DownloadOrderShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.DownloadOrderShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;

/**
 * 售后上下文操作类
 *
 * <AUTHOR>
 * @date 2024-06-27 14:20
 */
public class AfterSaleSaveContextOperation {

    /**
     * 转换售后上下文
     * @param param 入参
     * @param context 售后上下文
     * @return 转换结果
     */
    public SaveAfterSaleOutResult covert(SaveAfterSaleParam param, AfterSaleSaveContext context) {
        ApiShopBaseDto apiShopBaseDto = ShopInfoUtils.singleByOutShopId(param.getMemberName(), param.getOutShopId());
        if (apiShopBaseDto == null) {
            return SaveAfterSaleOutResult.failed("查询店铺基本信息失败");
        }
        AfterSalesShopConfig afterSalesConfig = AfterSalesShopConfigUtils.singleByShopId(param.getMemberName(), param.getOutShopId());
        if (afterSalesConfig == null) {
            return SaveAfterSaleOutResult.failed("查询售后店铺配置失败");
        }
        DownloadOrderShopConfig downloadOrderConfig = DownloadOrderShopConfigUtils.singleByShopId(param.getMemberName(), param.getOutShopId());
        if (downloadOrderConfig == null) {
            return SaveAfterSaleOutResult.failed("查询下载订单店铺配置失败");
        }

        if (context == null) {
            context = new AfterSaleSaveContext();
        }
        context.setMemberName(param.getMemberName());
        context.setShopId(param.getOutShopId());
        context.setOrderTriggerType(param.getOrderTriggerType());
        context.setOperatorName(param.getOperatorName());
        context.setPolyApiRequestId(param.getPolyApiRequestId());
        context.setLoadTime(param.getLoadTime());
        context.setApiShopBaseInfo(apiShopBaseDto);
        context.setPlat(apiShopBaseDto.getPlat());
        context.setAfterSalesShopConfig(afterSalesConfig);
        context.setDownLoadOrderShopConfig(downloadOrderConfig);

        return SaveAfterSaleOutResult.success();
    }
}
