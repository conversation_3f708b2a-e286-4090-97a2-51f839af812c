package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj;

import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashReadOnlyCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WdgjYunDataSourceDto;

/**
 * 网店管家云端 - 数据库链接缓存 - 只读
 *
 * <AUTHOR>
 * @date 2024-03-26 14:32
 */
public class WdgjYunDataSourceCache extends AbstractHashReadOnlyCache<WdgjYunDataSourceDto> {

    //region 单例
    /**
     * 构造方法
     */
    private WdgjYunDataSourceCache() {
        super(DataCacheKeyEnum.WDGJYUN_BASICCFG_DATASOURCE.getOriginalCode());
    }

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static WdgjYunDataSourceCache singleton() {
        return WdgjYunDataSourceCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final WdgjYunDataSourceCache instance;

        private SingletonEnum() {
            instance = new WdgjYunDataSourceCache();
        }
    }
    //endregion

    //region 重写基类方法
    /**
     * 获取值类型
     *
     * @return 值类型
     */
    @Override
    public Class<WdgjYunDataSourceDto> getValueClazz() {
        return WdgjYunDataSourceDto.class;
    }
    //endregion

    /**
     * 获取 数据库链接缓存
     * @param rdsId 数据库链接Id
     * @return 数据库链接缓存实体
     */
    public WdgjYunDataSourceDto getDataSource(String rdsId){
        return getCache(cacheKey, rdsId);
    }
}
