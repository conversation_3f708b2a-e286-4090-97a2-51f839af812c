package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

import java.util.List;

/**
 * 会员级所有店铺信息
 *
 * <AUTHOR>
 * @date 2024/9/18 上午11:30
 */
public class ApiAllShopDto {
    /**
     * 店铺信息列表
     */
    private List<ShopInfo> shopInfoList;

    //region get/set
    public List<ShopInfo> getShopInfoList() {
        return shopInfoList;
    }

    public void setShopInfoList(List<ShopInfo> shopInfoList) {
        this.shopInfoList = shopInfoList;
    }
    //endregion

    //region 内部类
    public static class ShopInfo{
        /**
         * 管家店铺Id
         */
        private int shopId;

        /**
         * api店铺Id
         */
        private int apiShopId;

        public int getShopId() {
            return shopId;
        }

        public void setShopId(int shopId) {
            this.shopId = shopId;
        }

        public int getApiShopId() {
            return apiShopId;
        }

        public void setApiShopId(int apiShopId) {
            this.apiShopId = apiShopId;
        }
    }
    //endregion
}
