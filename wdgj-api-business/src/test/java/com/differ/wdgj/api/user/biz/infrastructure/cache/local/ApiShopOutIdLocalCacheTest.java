package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopOutIdLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.OutShopInfoDto;
import org.junit.Assert;
import org.junit.Test;

/**
 * 内存缓存：Api店铺数据 outShopId + outAccount 映射 shopId
 *
 * <AUTHOR>
 * @date 2024/12/4 下午3:24
 */
public class ApiShopOutIdLocalCacheTest extends AbstractSpringTest {
    /**
     * 查找店铺Id
     */
    @Test
    public void findShopIdTest(){
        OutShopInfoDto outShopInfoDto = ApiShopOutIdLocalCache.singleton().findShopId("api2017", 1382);
        Assert.assertTrue(outShopInfoDto.apiShopId > 0);
        OutShopInfoDto outShopInfoDtoTwo = ApiShopOutIdLocalCache.singleton().findShopId("api2017", 1002);
        Assert.assertTrue(outShopInfoDtoTwo.apiShopId > 0);
    }
}
