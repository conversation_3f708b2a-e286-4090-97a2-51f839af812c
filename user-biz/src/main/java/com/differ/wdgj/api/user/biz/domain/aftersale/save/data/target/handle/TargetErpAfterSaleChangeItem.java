package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.handle;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleChangeTypeEnum;

import java.util.Objects;

/**
 * 售后单变更通知erp项
 *
 * <AUTHOR>
 * @date 2024/7/30 下午2:53
 */
public class TargetErpAfterSaleChangeItem {

    /**
     * 变更类别
     */
    private AfterSaleChangeTypeEnum changeType;

    /**
     * 变更信息
     */
    private String changeInfo;

    //region 构造

    /**
     * 构造
     *
     * @param changeType 变更类别
     * @param changeInfo 变更信息
     */
    public TargetErpAfterSaleChangeItem(AfterSaleChangeTypeEnum changeType, String changeInfo) {
        this.changeType = changeType;
        this.changeInfo = changeInfo;
    }
    //endregion

    //region 重写基类方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TargetErpAfterSaleChangeItem that = (TargetErpAfterSaleChangeItem) o;
        return changeType == that.changeType;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(changeType);
    }
    //endregion

    //region get/set
    public AfterSaleChangeTypeEnum getChangeType() {
        return changeType;
    }

    public void setChangeType(AfterSaleChangeTypeEnum changeType) {
        this.changeType = changeType;
    }

    public String getChangeInfo() {
        return changeInfo;
    }

    public void setChangeInfo(String changeInfo) {
        this.changeInfo = changeInfo;
    }
    //endregion
}
