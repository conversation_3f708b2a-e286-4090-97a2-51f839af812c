package com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.core;

/**
 * 平台业务特性 - 配置约束 - 允许下载的类型+状态
 *
 * <AUTHOR>
 * @date 2024/8/14 下午3:10
 */
public class PlatSourceOrderTypeDto {
    /**
     * Api 类型值
     */
    private String apiTypeValue;

    /**
     * 菠萝派类型值
     */
    private String polyTypeValue;

    /**
     * 类型描述
     */
    private String typeDesc;

    //region get/set
    public String getApiTypeValue() {
        return apiTypeValue;
    }

    public void setApiTypeValue(String apiTypeValue) {
        this.apiTypeValue = apiTypeValue;
    }

    public String getPolyTypeValue() {
        return polyTypeValue;
    }

    public void setPolyTypeValue(String polyTypeValue) {
        this.polyTypeValue = polyTypeValue;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }
    //endregion
}
