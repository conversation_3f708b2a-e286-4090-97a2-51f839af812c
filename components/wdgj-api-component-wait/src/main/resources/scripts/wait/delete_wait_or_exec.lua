-- 删除排队，用于取消或完成任务
-- User: chuff
-- Date: 2021/9/13 10:45
--


-- 排队任务数据（Hash）
local contentWaitKey = KEYS[1]
-- 任务排队队列
local contentWaitListKey = KEYS[2]
-- 排队任务执行中的临时队列
local executeKey = KEYS[3]
-- 执行中数量的Key
local execCountKey = KEYS[4]

-- 任务内容hashkey
local contentHashKey = ARGV[1]
-- 是否取消
local cancel = tonumber(ARGV[2])
-- 执行中的key后缀
local executeKeyTail = tonumber(ARGV[3])
local executeKeyTailBefore = executeKeyTail - 1

local count = redis.call("hdel", contentWaitKey, contentHashKey)
-- 不管是否存在任务数据时，都删除排队任务
if cancel == 1 then
    -- 取消时，删除排队任务
    redis.call('lrem', contentWaitListKey, 1, contentHashKey)
else
    -- 完成时，删除排队执行中任务,先删早期的，再删当前的
    local deleteCount = redis.call('hdel', executeKey .. executeKeyTailBefore, contentHashKey)
    if deleteCount == 0 then
        redis.call('hdel', executeKey .. executeKeyTail, contentHashKey)
    end
    -- 计数减1，这里不判断删除数>0，因为有可能过期了
    local deCount = redis.call("decr", execCountKey)
    if deCount < 0 then
        -- 容错，计数不小于0
        redis.call("del", execCountKey)
    end
end
return count