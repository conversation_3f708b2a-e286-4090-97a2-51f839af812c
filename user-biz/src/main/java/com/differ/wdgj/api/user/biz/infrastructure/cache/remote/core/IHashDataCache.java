/**
 * Copyright(C) 2018 Hangzhou Differsoft Co., Ltd. All rights reserved.
 */
package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;

import java.util.Map;

/**
 * Hash存储的缓存
 *
 * <AUTHOR>
 * @since 2019-12-24  15:01
 */
public interface IHashDataCache<T> extends IDataCache{
    /**
     * 自动同步所有缓存
     *
     * @return 同步成功数量
     */
    int syncAllToCache();

    /**
     * 根据构成缓存键的参数同步缓存。
     *
     * @param isClearOldCacheData 是否清除旧的缓存数据
     * @param cacheKeyArgs        缓存键拆分所需相关参数(和枚举DataCacheKeyEnuma项caption相关)
     * @return
     */
    int syncAllToCache(boolean isClearOldCacheData, String... cacheKeyArgs);

    /**
     * 获取某个Hash下所有缓存
     *
     * @param args 动态缓存键参数集
     * @return
     */
    Map<String,T> hashGetAll(Object... args);

    /**
     * 获取某一条Hash缓存
     *
     * @param hashKey hash键
     * @param args    动态缓存键参数集
     * @return
     */
    T hashGet(String hashKey, Object... args);
}
