'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

class ClassA {
}

class ClassB {
}

class ClassC {
}

class ClassD {
}

class ClassE {
}

class ClassF {
}

class ClassG {
}

class ClassH {
}

class ClassI {
}

class ClassJ {
}

class ClassK {
}

class ClassL {
}

ClassA <|.. ClassB :继承（接口）（实现）（B is a A）

ClassC <|-- ClassD :继承（类）（泛化）（D is a C）

ClassE <.. ClassF :依赖（代码体现：方法参数、返回值，局部变量）（F use a E）

ClassG <-- ClassH :关联（代码体现：成员变量）（H has a G）

ClassI <--* ClassJ :组合（代码体现：成员集合变量）（J contains many I)

ClassK <--o ClassL :聚合（代码体现：成员集合变量）（L has many K）


@enduml