package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashReadOnlyCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevShopDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 店铺基础数据Redis缓存 - 只读
 *
 * <AUTHOR>
 * @date 2024-03-21 15:48
 */
public class ApiShopCache extends AbstractHashReadOnlyCache<DevShopDO> {

    //region 构造

    /**
     * 构造方法
     * @param outAccount 外部用户名
     */
    private ApiShopCache(String outAccount) {
        super(String.format(DataCacheKeyEnum.DT_OPERATION_SHOP.getOriginalCode(), OuterApiEnum.WDGJ.getValue(), outAccount));
        this.outAccount = outAccount;
    }

    public static ApiShopCache create(String outAccount) {
        return new ApiShopCache(outAccount);
    }

    //endregion

    //region 变量

    /**
     * 外部会员名
     */
    private final String outAccount;

    //endregion

    //region 重写基类方法
    /**
     * 获取值类型
     *
     * @return 值类型
     */
    @Override
    public Class<DevShopDO> getValueClazz() {
        return DevShopDO.class;
    }
    //endregion

    //region 增强方法
    /**
     * 获取 店铺基础数据
     * @param shopId 店铺Id
     * @return 店铺基础数据
     */
    public DevShopDO get(int shopId){
        return super.getCache(cacheKey, createHashKey(shopId));
    }

    /**
     * 查询某一会员的店铺列表（存在明显性能问题）
     * @return 某一会员的店铺列表
     */
    public List<DevShopDO> getVipUserShopList(){
        // 查询缓存
        List<DevShopDO> shopList = new ArrayList<>();
        this.cacher.hashScanAll(cacheKey, 20, (key,value)->{
            DevShopDO shop = JsonUtils.deJson(value, DevShopDO.class);
            shopList.add(shop);
        });

        return shopList;
    }

    //endregion

    //region 私有方法
    /**
     * 构建HashKey
     * @param shopId 店铺Id
     * @return HashKey
     */
    private String createHashKey(int shopId){
        return String.format("%013d", shopId);
    }

    //endregion
}
