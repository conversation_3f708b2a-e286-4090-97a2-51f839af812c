package com.differ.wdgj.api.user.biz.domain.apicall.plugins;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.PolyAPIBusinessGetRefundOrderRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.PolyAPIBusinessGetRefundOrderResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.plugins.aftersale.GetRefundOrderApiCall;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 下载退货退款单
 *
 * <AUTHOR>
 * @date 2024/8/27 下午6:41
 */
@Ignore
public class GetRefundOrderApiCallTest extends AbstractSpringTest {
    /**
     * 下载退货退款单
     */
    @Test
    public void apiCallTest(){
        GetRefundOrderApiCall getRefundOrderApiCall = new GetRefundOrderApiCall();
        PolyAPIBusinessGetRefundOrderRequestBizData request = new PolyAPIBusinessGetRefundOrderRequestBizData();
        request.setReturn_sn("111111");
        ApiCallResponse<PolyAPIBusinessGetRefundOrderResponseBizData> response = getRefundOrderApiCall.apiCall("api2017", 1446, request);
    }
}
