package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.enums.AfterSaleLoadTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * LoadAfterSaleWorkMutex 集成测试
 * 使用真实的配置键值，不使用MockedStatic
 *
 * <AUTHOR>
 * @date 2024/12/19 下午4:00
 */
public class LoadAfterSaleWorkMutexTest extends AbstractSpringTest {

    @Mock
    private WorkDataOperate mockDataOperate;

    private LoadAfterSaleWorkMutex loadAfterSaleWorkMutex;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        loadAfterSaleWorkMutex = new LoadAfterSaleWorkMutex();
    }

    /**
     * 测试默认配置下的行为
     * 当配置键为默认值（空字符串，解析为0）时，不应该检查超时任务
     */
    @Test
    public void testCreate_DefaultConfig_ShouldNotCheckTimeout() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.AUTO, 
            AfterSaleLoadTypeEnum.TIME_RANGE
        );
        
        // 模拟父类方法返回null（存在未完成任务）
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(true);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果 - 由于默认配置超时时间为0，不会检查超时任务，直接返回null
        Assert.assertNull("当存在未完成任务且超时时间为0时，应该返回null", result);
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        // 由于超时时间为0，不应该检查超时任务
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
        Mockito.verify(mockDataOperate, Mockito.never()).create(workData);
    }

    /**
     * 测试消息推送不互斥的完整流程
     * 验证消息推送场景下的行为
     */
    @Test
    public void testCreate_MessageNotificationFlow() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            AfterSaleLoadTypeEnum.AFTER_SALE_NO
        );
        CreateResult expectedResult = CreateResult.successResult("message-task-id");
        
        // 模拟依赖行为
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("消息推送场景结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "message-task-id", result.getTaskId());
        
        // 验证交互 - 消息推送场景应该直接创建，不检查父类逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
    }

    /**
     * 测试父类创建逻辑的完整流程
     * 验证非特殊场景下的父类逻辑调用
     */
    @Test
    public void testCreate_SuperClassFlow() {
        // 准备测试数据
        WorkData<AfterSaleLoadArgs> workData = createWorkData(
            TriggerTypeEnum.HAND, 
            AfterSaleLoadTypeEnum.SHOP_LOAD
        );
        CreateResult expectedResult = CreateResult.successResult("super-task-id");
        
        // 模拟父类逻辑：不存在未完成任务，创建新任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果
        Assert.assertNotNull("父类逻辑结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "super-task-id", result.getTaskId());
        
        // 验证交互
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
        // 由于父类已经返回结果，不应该检查超时任务
        Mockito.verify(mockDataOperate, Mockito.never()).existsUnRunTimeout(Mockito.any(), Mockito.anyInt());
    }

    /**
     * 测试所有枚举组合的基本功能
     * 确保所有枚举值组合都能正常处理
     */
    @Test
    public void testCreate_AllEnumCombinations() {
        TriggerTypeEnum[] triggerTypes = {TriggerTypeEnum.HAND, TriggerTypeEnum.AUTO, TriggerTypeEnum.MESSAGE_NOTIFICATION};
        AfterSaleLoadTypeEnum[] loadTypes = {AfterSaleLoadTypeEnum.SHOP_LOAD, AfterSaleLoadTypeEnum.TIME_RANGE, AfterSaleLoadTypeEnum.AFTER_SALE_NO};
        
        for (TriggerTypeEnum triggerType : triggerTypes) {
            for (AfterSaleLoadTypeEnum loadType : loadTypes) {
                // 重置mock
                Mockito.reset(mockDataOperate);
                
                WorkData<AfterSaleLoadArgs> workData = createWorkData(triggerType, loadType);
                
                if (triggerType == TriggerTypeEnum.MESSAGE_NOTIFICATION && loadType == AfterSaleLoadTypeEnum.AFTER_SALE_NO) {
                    // 消息推送且按售后单号下载，应该直接创建
                    CreateResult expectedResult = CreateResult.successResult("direct-task-id");
                    Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                    
                    CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                    
                    Assert.assertNotNull("消息推送+售后单号应该直接创建", result);
                    Assert.assertTrue("应该创建成功", result.success());
                    Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                    // 验证没有调用父类逻辑
                    Mockito.verify(mockDataOperate, Mockito.never()).existsUnComplete(workData);
                } else {
                    // 其他情况，模拟不存在未完成任务，应该通过父类逻辑创建
                    CreateResult expectedResult = CreateResult.successResult("super-task-id");
                    Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
                    Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
                    
                    CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
                    
                    Assert.assertNotNull("其他情况应该通过父类创建", result);
                    Assert.assertTrue("应该创建成功", result.success());
                    Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
                    Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
                }
            }
        }
    }

    /**
     * 测试边界条件
     * 验证null参数的处理
     */
    @Test
    public void testCreate_BoundaryConditions() {
        // 测试args为null的情况
        WorkContext workContext = WorkContext.of(
            "test-member", 
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            TriggerTypeEnum.MESSAGE_NOTIFICATION, 
            "test-creator"
        );
        WorkData<AfterSaleLoadArgs> workData = WorkData.of(workContext, null);
        CreateResult expectedResult = CreateResult.successResult("null-args-task-id");
        
        // 模拟父类逻辑：不存在未完成任务，创建新任务
        Mockito.when(mockDataOperate.existsUnComplete(workData)).thenReturn(false);
        Mockito.when(mockDataOperate.create(workData)).thenReturn(expectedResult);
        
        // 执行测试
        CreateResult result = loadAfterSaleWorkMutex.create(workData, mockDataOperate);
        
        // 验证结果 - args为null时应该跳过消息推送检查，走父类逻辑
        Assert.assertNotNull("args为null时结果不应为空", result);
        Assert.assertTrue("应该创建成功", result.success());
        Assert.assertEquals("任务ID应该匹配", "null-args-task-id", result.getTaskId());
        
        // 验证调用了父类的逻辑
        Mockito.verify(mockDataOperate, Mockito.times(1)).existsUnComplete(workData);
        Mockito.verify(mockDataOperate, Mockito.times(1)).create(workData);
    }

    /**
     * 创建测试用的WorkData对象
     *
     * @param triggerType 触发类型
     * @param loadType    加载类型
     * @return WorkData对象
     */
    private WorkData<AfterSaleLoadArgs> createWorkData(TriggerTypeEnum triggerType, AfterSaleLoadTypeEnum loadType) {
        WorkContext workContext = WorkContext.of(
            "api2017",
            WorkEnum.LOAD_AFTER_SALE, 
            1001, 
            triggerType, 
            "test-creator"
        );
        
        AfterSaleLoadArgs args = new AfterSaleLoadArgs();
        args.setLoadType(loadType);
        
        return WorkData.of(workContext, args);
    }
}
