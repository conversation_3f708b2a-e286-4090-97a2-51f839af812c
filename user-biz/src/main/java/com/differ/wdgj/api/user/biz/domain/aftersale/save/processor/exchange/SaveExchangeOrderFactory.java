package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat.ByteDanceExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat.PddSaveExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat.TaoBaoExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat.XiaoHSSaveExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.plat.KuaiShouShopSaveExchangeOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;

/**
 * 保存换货单 - 业务工厂
 *
 * <AUTHOR>
 * @date 2024-08-07 14:05
 */
public class SaveExchangeOrderFactory {
    //region 构造
    private SaveExchangeOrderFactory() {
    }
    //endregion

    /**
     * 创建保存退货退款单处理器
     *
     * @param context 上下文
     * @return 实例
     */
    public static ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> createProcessor(AfterSaleSaveContext context) {
        switch (context.getPlat()) {
            case BUSINESS_Yangkeduo:
                return new PddSaveExchangeOrderProcessor(context);
            case BUSINESS_FangXinGou:
            case BUSINESS_LuBan:
                return new ByteDanceExchangeOrderProcessor(context);
            case BUSINESS_XiaoHS:
                return new XiaoHSSaveExchangeOrderProcessor(context);
            case BUSINESS_Taobao:
                return new TaoBaoExchangeOrderProcessor(context);
            case BUSINESS_KuaiShouShop:
                return new KuaiShouShopSaveExchangeOrderProcessor(context);
            default:
                return new BaseSaveExchangeOrderProcessor(context);
        }
    }
}
