package com.differ.wdgj.api.user.biz.domain.apicall.processor.http;

import com.differ.wdgj.api.component.util.http.HttpTools;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.adapter.ApiCallGatewayAdapter;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallGateway;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.processor.IApiCallProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.processor.profile.IApiPostProfile;
import com.differ.wdgj.api.user.biz.domain.apicall.processor.profile.PolyApiPostProfile;
import com.differ.wdgj.api.user.biz.domain.apicall.utils.ApiCallLogUtils;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

import java.util.SortedMap;

/**
 * API调用处理器 - HTTP
 *
 * <AUTHOR>
 * @date 2022-03-09 18:09
 */
public class HttpApiCallProcessor<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData>
        implements IApiCallProcessor<RequestBizData, ResponseBizData> {
    //region 常量
    /**
     * 日志标题
     */
    private final String CAPTION = "HTTP调用处理器";
    //endregion

    // region 变量

    /**
     * API调用网关地址适配器
     */
    private final ApiCallGatewayAdapter gatewayAdapter;

    // endregion

    //region 构造
    public HttpApiCallProcessor() {
        this.gatewayAdapter = new ApiCallGatewayAdapter();
    }
    //endregion

    // region 接口实现

    /**
     * 请求
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param responseBizType  响应业务数据类型
     * @param callExtendedInfo 请求扩展信息
     * @return 响应
     */
    @Override
    public ApiCallResponse<ResponseBizData> call(ApiCallContext context, RequestBizData requestBizData, Class<? extends ResponseBizData> responseBizType, ApiCallExtendedInfo callExtendedInfo) {
        // 响应
        ApiCallResponse<ResponseBizData> response = new ApiCallResponse<>();

        // 网关
        ApiCallGateway gateway = null;

        // 返回报文
        String returnMsg = "";
        try {
            // 获取网关地址
            gateway = this.gatewayAdapter.getGateway(context);

            // 创建请求实例
            IApiPostProfile<RequestBizData, ResponseBizData> httpPost = this.createInstance(gateway);

            // 封装请求数据
            SortedMap<String, String> postData = httpPost.createPostData(context, requestBizData, callExtendedInfo);

            // 请求接口
            returnMsg = HttpTools.postData(gateway.getGateway(), postData, gateway.getTls());

            // 解析返回报文
            response = httpPost.resolveResponse(context, returnMsg, responseBizType);

            // 记录跟踪日志（默认不开启）
            ApiCallGateway finalGateway = gateway;
            String finalReturnMsg = returnMsg;
            ApiCallLogUtils.writeRequestInfoLog(context, CAPTION, () -> {
                String gatewayName = finalGateway.getType().getName();
                String gatewayUrl = finalGateway.getGateway();
                String requestJsonStr = JsonUtils.toJson(postData);
                return ExtUtils.stringBuilderAppend(String.format("网关：%s-%s;请求参数：%s；返回结果：%s。", gatewayName, gatewayUrl, requestJsonStr, finalReturnMsg));
            });
        } catch (Exception ex) {
            // 处理异常
            ApiCallLogUtils.processException(response, ex);
            // 记录日志
            ApiCallLogUtils.writeRequestErrorLog(context, gateway, returnMsg, StringUtils.EMPTY, ex);
        }

        return response;
    }

    // endregion

    // region 私有方法

    /**
     * 创建实例
     *
     * @param gateway 网关
     * @return 实例
     */
    private IApiPostProfile<RequestBizData, ResponseBizData> createInstance(ApiCallGateway gateway) {
        switch (gateway.getType()) {
            case YUN:
                //todo return SpringResolveManager.resolve(YunApiPostProfile.class);
            case POLY_API:
            default:
                return new PolyApiPostProfile<>();
        }
    }

    // endregion
}
