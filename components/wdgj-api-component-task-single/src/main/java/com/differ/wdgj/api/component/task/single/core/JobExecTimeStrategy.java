package com.differ.wdgj.api.component.task.single.core;

/**
 * @Description 任务执行时间策略，非线程安全
 * <AUTHOR>
 * @Date 2021/11/4 10:54
 */
public interface JobExecTimeStrategy {

    /**
     * 是否可以在本站点运行
     *
     * @param sitesToRun 可运行的站点集合，由注解参数设置
     * @return
     */
    default boolean runOnSite(String[] sitesToRun) {
        return true;
    }

    /**
     * 是否当前可以执行任务
     * @return true:可以执行
     */
    boolean enableRunCurrent();

    /**
     * 是否当前可以执行任务,传入的时间戳和上次执行的时间戳相同时，认为可执行，非线程安全
     * @param currentTimeMillis 当时任务的时间戳(毫秒)
     * @return true:可以执行
     */
    boolean enableRunOnReEntry(long currentTimeMillis);
}
