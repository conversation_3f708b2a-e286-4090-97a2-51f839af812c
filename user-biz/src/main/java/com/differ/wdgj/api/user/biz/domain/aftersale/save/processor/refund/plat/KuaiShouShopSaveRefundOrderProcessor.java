package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolySpecialRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 京东 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2025/5/20 上午15:11
 */
public class KuaiShouShopSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public KuaiShouShopSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取api售后单类型
     * @param orderItem 菠萝派售后单信息
     * @return api售后单类型
     */
    @Override
    protected ApiAfterSaleTypeEnum getApiAfterSaleType(BusinessGetRefundOrderResponseOrderItem orderItem, ShopTypeEnum shopType) {
        // 保价单
        if (StringUtils.isNotEmpty(orderItem.getSpecialRefundType())
                && PolySpecialRefundTypeEnum.create(orderItem.getSpecialRefundType()) == PolySpecialRefundTypeEnum.JH_PRICE_PROTECT) {
            return ApiAfterSaleTypeEnum.REFUND_BJ;
        }

        //其他场景走基类
        return super.getApiAfterSaleType(orderItem, shopType);
    }
}
