package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.create.plugins.time;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.BaseLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;

import java.util.Collections;
import java.util.List;

/**
 * 按时间创建子任务
 *
 * <AUTHOR>
 * @date 2024/9/20 上午11:43
 */
public class TimeRangeCreateSubTaskOperator extends BaseTimeCreateSubTaskOperator {
    //region 构造
    public TimeRangeCreateSubTaskOperator(AfterSaleLoadTaskContext context, BaseLoadAfterSaleProcessor loadProcessor) {
        super(context, loadProcessor);
    }
    //endregion

    //region 实现基类方法

    /**
     * 构建店铺类型-售后类型列表
     *
     * @param loadArgs 基础入参
     * @return 店铺类型-售后类型列表
     */
    @Override
    protected List<AfterSaleBizType> buildShopRefundTypes(AfterSaleLoadArgs loadArgs) {
        // 外部传入，以外部为准
        ShopTypeEnum argShopType = loadArgs.getShopType();
        ApiAfterSaleTypeEnum argRefundType = loadArgs.getRefundType();
        if (argShopType != null && argRefundType != null) {
            return Collections.singletonList(new AfterSaleBizType(argShopType, argRefundType));
        }

        // 根据店铺配置创建
        return super.buildShopRefundTypes(loadArgs);
    }
    //endregion
}
