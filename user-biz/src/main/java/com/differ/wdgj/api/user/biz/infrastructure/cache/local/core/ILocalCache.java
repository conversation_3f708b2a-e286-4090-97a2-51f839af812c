package com.differ.wdgj.api.user.biz.infrastructure.cache.local.core;

/**
 * 网店管家数据键缓存
 * <AUTHOR>
 * @date 2021/1/13 13:49
 */
public interface ILocalCache<K,V> {

    /**
     * 获取会员的数据，优先取缓存的有效数据
     * @param key
     * @param wdgjUser
     * @return
     */
     V getData(String wdgjUser,K key);

    /**
     * 刷新本地缓存
     * @param wdgjUser
     * @param key
     */
    void refresh(String wdgjUser,K key);
}
