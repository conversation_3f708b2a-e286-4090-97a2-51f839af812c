package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloDbConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.DbConfigMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnLogMapper;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.Collections;

/**
 * 事务测试
 *
 * <AUTHOR>
 * @date 2024/8/7 上午10:09
 */
@Ignore
public class TransactionTest extends AbstractSpringTest {
    @Autowired
    ApiReturnLogMapper mapper;
    @Autowired
    DbConfigMapper DbConfigMapper;

    /**
     * 事务保存验证
     */
    @Test
    public void doTransactionTest() {
        DBSwitchUtil.doTransaction("api2017", () -> {
            // 插入1
            ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
            apiReturnLogOne.setBillId(12922);
            apiReturnLogOne.setOperator("[新API]");
            apiReturnLogOne.setLogDetail("测试1");
            apiReturnLogOne.setLogTime(LocalDateTime.now());
            mapper.batchInsert(Collections.singletonList(apiReturnLogOne));

            return false;
        });

        DBSwitchUtil.doTransaction("api2017", () -> {
            // 插入1
            ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
            apiReturnLogOne.setBillId(12922);
            apiReturnLogOne.setOperator("[新API-2]");
            apiReturnLogOne.setLogDetail("测试1");
            apiReturnLogOne.setLogTime(LocalDateTime.now());
            mapper.batchInsert(Collections.singletonList(apiReturnLogOne));

            return true;
        });
    }

    /**
     /**
     * 多数据库连接事务验证
     */
    @Test
    public void doubleConTransactionTest(){
        DBSwitchUtil.doTransaction("api2017", () -> {

            GloDbConfigDO api2017 = DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(), () -> {
                return DbConfigMapper.selectByConfigKey("", 0, "");
            });

            // 插入1
            ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
            apiReturnLogOne.setBillId(12922);
            apiReturnLogOne.setOperator("[新API]");
            apiReturnLogOne.setLogDetail("测试1");
            apiReturnLogOne.setLogTime(LocalDateTime.now());
            mapper.batchInsert(Collections.singletonList(apiReturnLogOne));

            //LogFactory.error("sql事务异常回滚", "", new RuntimeException("cxd测试异常"));

            return false;
        }, true);
    }

    /*
     * 多事务嵌套验证
     */
    @Test
    public void doubleTransactionTest(){
        DBSwitchUtil.doTransaction("api2017", () -> {
            DBSwitchUtil.doTransaction("api2017", () -> {
                // 插入1
                ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
                apiReturnLogOne.setBillId(12922);
                apiReturnLogOne.setOperator("[新API]");
                apiReturnLogOne.setLogDetail("测试1");
                apiReturnLogOne.setLogTime(LocalDateTime.now());
                mapper.batchInsert(Collections.singletonList(apiReturnLogOne));
                return true;
            }, true);

            // 插入2
            ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
            apiReturnLogOne.setBillId(12923);
            apiReturnLogOne.setOperator("[新API]");
            apiReturnLogOne.setLogDetail("测试2");
            apiReturnLogOne.setLogTime(LocalDateTime.now());
            mapper.batchInsert(Collections.singletonList(apiReturnLogOne));
            return true;
        }, true);
    }

    /*
     * 事务嵌套普通验证
     */
    @Test
    public void TransactionAndNormalTest(){
        DBSwitchUtil.doTransaction("api2017", () -> {
            DBSwitchUtil.doDBWithUser("api2017", () -> {
                // 插入1
                ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
                apiReturnLogOne.setBillId(12922);
                apiReturnLogOne.setOperator("[新API]");
                apiReturnLogOne.setLogDetail("测试1");
                apiReturnLogOne.setLogTime(LocalDateTime.now());
                mapper.batchInsert(Collections.singletonList(apiReturnLogOne));
            });

            // 插入2
            ApiReturnLogDO apiReturnLogOne = new ApiReturnLogDO();
            apiReturnLogOne.setBillId(12923);
            apiReturnLogOne.setOperator("[新API]");
            apiReturnLogOne.setLogDetail("测试2");
            apiReturnLogOne.setLogTime(LocalDateTime.now());
            mapper.batchInsert(Collections.singletonList(apiReturnLogOne));
            return true;
        }, true);
    }
}
