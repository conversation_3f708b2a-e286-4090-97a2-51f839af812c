package com.differ.wdgj.api.user.biz.infrastructure.utils.plat;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadAfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature.PlatBizFeatureTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;

/**
 * 平台业务特性工具类 - 售后下载
 *
 * <AUTHOR>
 * @date 2024-06-27 14:42
 */
public class LoadAfterSaleBizFeatureUtils {
    //region 常量
    /**
     * 业务类型
     */
    private static final PlatBizFeatureTypeEnum TYPE = PlatBizFeatureTypeEnum.LOAD_AFTER_SALES;
    //endregion

    //region 构造
    private LoadAfterSaleBizFeatureUtils() {
    }
    //endregion

    /**
     * 获取业务特性
     *
     * @param memberName   会员名
     * @param outShopId    外部店铺Id
     * @param restraintKey 特殊键约束
     * @return 结果
     */
    public static LoadAfterSalesConfigContent getFeature(String memberName, int outShopId, String restraintKey) {
        ApiShopBaseDto apiShopBaseDto = ShopInfoUtils.singleByOutShopId(memberName, outShopId);
        if (apiShopBaseDto == null) {
            return null;
        }

        return getFeature(apiShopBaseDto, restraintKey);
    }

    /**
     * 获取业务特性
     *
     * @param shopBase     店铺基础信息
     * @param restraintKey 特殊键约束
     * @return 结果
     */
    public static LoadAfterSalesConfigContent getFeature(ApiShopBaseDto shopBase, String restraintKey) {
        return getFeature(shopBase.getPlat(), shopBase.getOutAccount(), shopBase.getToken(), restraintKey);
    }

    /**
     * 获取业务特性
     *
     * @param plat         平台
     * @param memberName   会员名
     * @param shopToken    店铺Token
     * @param restraintKey 特殊键约束
     * @return 结果
     */
    public static LoadAfterSalesConfigContent getFeature(PolyPlatEnum plat, String memberName, String shopToken, String restraintKey) {
        return (LoadAfterSalesConfigContent) PlatBizFeatureUtils.getFeature(TYPE, plat, memberName, shopToken, restraintKey);
    }
}
