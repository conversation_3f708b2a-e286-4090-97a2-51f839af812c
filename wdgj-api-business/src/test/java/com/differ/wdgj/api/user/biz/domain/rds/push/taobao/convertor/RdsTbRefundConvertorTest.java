package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.convertor;

import com.alibaba.fastjson.JSON;
import com.differ.wdgj.api.component.util.tools.DateTimeUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbRefundJdpResponseDto;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * 淘宝Rds - 退货退款单转换器
 *
 * <AUTHOR>
 * @date 2025/4/10 下午1:39
 */
@Ignore
public class RdsTbRefundConvertorTest {

    /**
     * 测试 convertTbRefundStatus 方法
     */
    @Test
    public void testConvertTbRefundStatus() {
        // 测试已知状态
        Assert.assertEquals(PolyRefundStatusEnum.BUYER_APPLIED_REFUND_WAITING_SELLER_AGREE,
                RdsTbRefundConvertor.convertTbRefundStatus("WAIT_SELLER_AGREE"));
        Assert.assertEquals(PolyRefundStatusEnum.SELLER_AGREED_REFUND_WAITING_BUYER_RETURN,
                RdsTbRefundConvertor.convertTbRefundStatus("WAIT_BUYER_RETURN_GOODS"));
        Assert.assertEquals(PolyRefundStatusEnum.BUYER_RETURNED_WAITING_SELLER_CONFIRM,
                RdsTbRefundConvertor.convertTbRefundStatus("WAIT_SELLER_CONFIRM_GOODS"));
        Assert.assertEquals(PolyRefundStatusEnum.SELLER_REFUSED_REFUND,
                RdsTbRefundConvertor.convertTbRefundStatus("SELLER_REFUSE_BUYER"));
        Assert.assertEquals(PolyRefundStatusEnum.REFUND_CLOSED,
                RdsTbRefundConvertor.convertTbRefundStatus("CLOSED"));
        Assert.assertEquals(PolyRefundStatusEnum.REFUND_SUCCESSFUL,
                RdsTbRefundConvertor.convertTbRefundStatus("SUCCESS"));
        Assert.assertEquals(PolyRefundStatusEnum.REFUND_IN_PROGRESS,
                RdsTbRefundConvertor.convertTbRefundStatus("WAIT_BUYER_CONFIRM_EXCHANGE_SELLER_SEND_GOODS"));

        // 测试未知状态
        Assert.assertEquals(PolyRefundStatusEnum.OTHER,
                RdsTbRefundConvertor.convertTbRefundStatus("UNKNOWN_STATUS"));

        // 测试空值
        Assert.assertEquals(PolyRefundStatusEnum.OTHER,
                RdsTbRefundConvertor.convertTbRefundStatus(null));
    }

    /**
     * 测试 convertTbRefundGoodsStatus 方法
     */
    @Test
    public void testConvertTbRefundGoodsStatus() {
        // 测试已知状态
        Assert.assertEquals(PolyRefundGoodsStatusEnum.BUYER_NOT_RECEIVED,
                RdsTbRefundConvertor.convertTbRefundGoodsStatus("BUYER_NOT_RECEIVED"));
        Assert.assertEquals(PolyRefundGoodsStatusEnum.BUYER_RECEIVED,
                RdsTbRefundConvertor.convertTbRefundGoodsStatus("BUYER_RECEIVED"));
        Assert.assertEquals(PolyRefundGoodsStatusEnum.BUYER_RETURNED,
                RdsTbRefundConvertor.convertTbRefundGoodsStatus("BUYER_RETURNED_GOODS"));

        // 测试未知状态
        Assert.assertEquals(PolyRefundGoodsStatusEnum.OTHER,
                RdsTbRefundConvertor.convertTbRefundGoodsStatus("UNKNOWN_STATUS"));

        // 测试空值
        Assert.assertEquals(PolyRefundGoodsStatusEnum.OTHER,
                RdsTbRefundConvertor.convertTbRefundGoodsStatus(null));
    }

    /**
     * 测试 convertTbRefundType 方法
     */
    @Test
    public void testConvertTbRefundType() {
        // 测试已知类型
        Assert.assertEquals(PolyRefundTypeEnum.JH_03,
                RdsTbRefundConvertor.convertTbRefundType("REFUND"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_03,
                RdsTbRefundConvertor.convertTbRefundType("RETURN_GOODS_POSTAGE"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_04,
                RdsTbRefundConvertor.convertTbRefundType("REFUND_AND_RETURN"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_WEIXIU,
                RdsTbRefundConvertor.convertTbRefundType("REPAIR"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_SUPPLEMENT_SEND,
                RdsTbRefundConvertor.convertTbRefundType("RESHIPPING"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_05,
                RdsTbRefundConvertor.convertTbRefundType("TAOBAO_EXCHANGE"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_05,
                RdsTbRefundConvertor.convertTbRefundType("TMALL_EXCHANGE"));
        Assert.assertEquals(PolyRefundTypeEnum.JH_99,
                RdsTbRefundConvertor.convertTbRefundType("OTHERS"));

        // 测试未知类型
        Assert.assertEquals(PolyRefundTypeEnum.JH_99,
                RdsTbRefundConvertor.convertTbRefundType("UNKNOWN_TYPE"));

        // 测试空值
        Assert.assertEquals(PolyRefundTypeEnum.JH_99,
                RdsTbRefundConvertor.convertTbRefundType(null));
    }

    /**
     * 测试 convertPolyRefundOrder 方法 - 使用JSON字符串构建测试数据
     */
    @Test
    public void testConvertPolyRefundOrder() {
        //region 构建测试数据JSON字符串
        String jsonStr = "{\n" +
                "  \"refund_get_response\": {\n" +
                "    \"refund\": {\n" +
                "      \"refund_id\": 123456789,\n" +
                "      \"created\": \"2025-03-28 10:00:00\",\n" +
                "      \"modified\": \"2025-03-28 11:00:00\",\n" +
                "      \"reason\": \"商品质量问题\",\n" +
                "      \"desc\": \"商品有划痕\",\n" +
                "      \"status\": \"WAIT_SELLER_AGREE\",\n" +
                "      \"good_status\": \"BUYER_RECEIVED\",\n" +
                "      \"dispute_type\": \"REFUND_AND_RETURN\",\n" +
                "      \"tid\": *********,\n" +
                "      \"oid\": *********,\n" +
                "      \"order_status\": \"WAIT_BUYER_CONFIRM_GOODS\",\n" +
                "      \"total_fee\": \"100.00\",\n" +
                "      \"refund_fee\": \"50.00\",\n" +
                "      \"payment\": \"100.00\",\n" +
                "      \"buyer_nick\": \"买家昵称\",\n" +
                "      \"seller_nick\": \"卖家昵称\",\n" +
                "      \"has_good_return\": true,\n" +
                "      \"company_name\": \"顺丰快递\",\n" +
                "      \"sid\": \"SF1234567890\",\n" +
                "      \"refund_phase\": \"aftersale\",\n" +
                "      \"refund_version\": 1,\n" +
                "      \"buyer_open_uid\": \"buyer_open_uid_123\",\n" +
                "      \"num_iid\": 123456,\n" +
                "      \"outer_id\": \"OUTER_ID_123\",\n" +
                "      \"sku\": \"30004447689|颜色分类:军绿色;尺码:XS\",\n" +
                "      \"title\": \"测试商品\",\n" +
                "      \"num\": 2,\n" +
                "      \"attribute\": \"prime:1;autoInterceptAgree:1;interceptInvestor:1;autoInterceptStatus:[{\\\"subBizOrderId\\\"#3B\\\"*********\\\",\\\"interceptDate\\\"#3B\\\"1616832000000\\\",\\\"logisticInterceptEnum\\\"#3B\\\"SUCCESS\\\",\\\"interceptFailCode\\\"#3B\\\"\\\",\\\"mailNo\\\"#3B\\\"SF1234567890\\\",\\\"cpCode\\\"#3B\\\"SF\\\",\\\"cpName\\\"#3B\\\"顺丰快递\\\"}]\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
        //endregion

        jsonStr = "{\"refund_get_response\":{\"refund\":{\"seller_open_uid\":\"AAFjxRZIAAYx7hc2iXI6lVM7\",\"buyer_open_uid\":\"AAH9xRZIAAYx7hc2iXVKlBv7\",\"refund_id\":\"215757087464338564\",\"status\":\"WAIT_SELLER_CONFIRM_GOODS\",\"seller_nick\":\"nulad个护海外旗舰店\",\"buyer_nick\":\"t**\",\"tid\":2579672822012336485,\"oid\":2579672822012336485,\"created\":\"2025-06-01 18:32:49\",\"modified\":\"2025-06-03 17:21:06\",\"address\":\"售后， 15360424277， 广东省广州市从化区 江埔街道 广东省广州市从化区江浦街道环市东路192号天马科创谷7号门（禁止放园区门口/快递柜）， null\",\"advance_status\":0,\"alipay_no\":\"2025052422001198811445692723\",\"attribute\":\";carriageRiskTrackingorder:1_1;bizCode:tmall.hk.refund;disputeRequest:3;leavesCat:50011993;7d:1;sellerDoRefundId:2219223540429;apply_reason_text:使用后过敏;itemBuyAmount:1;interceptItemListResult:[{\\\"subBizOrderId\\\"#3B2579672822012336485,\\\"logisticInterceptEnum\\\"#3B\\\"INTERCEPT_NOT_APPLY\\\"}];seller_batch:true;clj_zero_second_refund:1;cPartRefund:0;_F_tlmType:B_taobao;selfReturnShowType:normal;sku:5938149855767|颜色分类#3B【促生胶原】KO暗黄·垮脸·油痘;sgr:1;bgmtc:2025-05-24 19#3B06#3B13;jibuBizCode:tmall.jibu;sellerDoRefundNick:nulad个护海外旗舰店#3B岩;shop_name:nulad个护海外旗舰店;ttid:227200@taobao_android_10.49.10;nrp:1;agreeRefundApplyTime:2025-06-01 20#3B17#3B37;rp3:1;seller_agreed_refund_fee:18600;sars:skip;isVirtual:0;EXmrf:18600;price_protection:2025-05-24 19#3B06#3B28~2025-07-05 23#3B59#3B59;enfunddetail:1;lastOrder:0;tod:864000000;newRefund:rp2;intentReturnGoodsType:RETURN_BY_SELF;opRole:buyer;newUltron:3;prepaidFailure:TMALL_GLOBE;products:timeoutrefund^;apply_init_refund_fee:18600;apply_text_id:600690;userCredit:2;sdkCode:ali.china.tmall.tmallhk;b2c:1;interceptStatus:0;restartForXiaoer:1;rootCat:1801;selfReturnSourceTtid:227200@taobao_android_10.49.10;tos:1;ol_tf:18600;part_refund:0;chfr:1;selfReturnBucketInfo:AB_202409191733_306#828635#2;payMode:alipay;appName:refundface2;workflowName:return_and_refund;rightsSuspend:1;priceFeeDisplayTips:CASH^ali^支付宝^16300|OTHER_ASSETS^OTHER_ASSETS_GROUP^优惠^2300;seller_audit:0;returnGoodsLogisticsStatus:3;apply_biz_type:3;itemPrice:40000;\",\"company_name\":\"中通快递\",\"cs_status\":1,\"desc\":\"\",\"good_return_time\":\"2025-06-03 16:07:02\",\"good_status\":\"BUYER_RETURNED_GOODS\",\"has_good_return\":true,\"num\":1,\"num_iid\":909670872047,\"operation_contraint\":\"null\",\"order_status\":\"WAIT_BUYER_CONFIRM_GOODS\",\"outer_id\":\"NLD-52496 *1+ NLD-52521*1+NLD-52373*1\",\"payment\":\"0.00\",\"price\":\"400.00\",\"reason\":\"使用后过敏\",\"refund_fee\":\"186.00\",\"refund_phase\":\"onsale\",\"refund_remind_timeout\":{\"exist_timeout\":true,\"remind_type\":8,\"timeout\":\"2025-06-13 16:07:02\"},\"refund_version\":1748773969794,\"shipping_type\":\"express\",\"sid\":\"73556972926157\",\"sku\":\"5938149855767|颜色分类:【促生胶原】KO暗黄·垮脸·油痘\",\"title\":\"【狂欢价】双抗水乳套装抗皱紧致衰老补水保湿提亮去黄护肤品正品官方旗舰店\",\"total_fee\":\"186.00\",\"dispute_type\":\"REFUND_AND_RETURN\"}}}";

        // 将JSON字符串反序列化为TbRefundJdpResponseDto对象
        TbRefundJdpResponseDto entity = JSON.parseObject(jsonStr, TbRefundJdpResponseDto.class);

        // 调用被测试方法
        BusinessGetRefundOrderResponseOrderItem result = RdsTbRefundConvertor.convertPolyRefundOrder(entity);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals("123456789", result.getRefundNo());
        Assert.assertEquals(DateTimeUtils.stringToTime("2025-03-28 10:00:00"), result.getCreateTime());
        Assert.assertEquals(DateTimeUtils.stringToTime("2025-03-28 11:00:00"), result.getUpdateTime());
        Assert.assertEquals("商品质量问题", result.getReason());
        Assert.assertEquals("商品有划痕", result.getDesc());
        Assert.assertEquals(PolyRefundStatusEnum.BUYER_APPLIED_REFUND_WAITING_SELLER_AGREE.getCode(), result.getRefundStatus());
        Assert.assertEquals(PolyRefundGoodsStatusEnum.BUYER_RECEIVED.getCode(), result.getGoodsStatus());
        Assert.assertEquals(PolyRefundTypeEnum.JH_04.getCode(), result.getRefundType());
        Assert.assertEquals("*********", result.getPlatOrderNo());
        Assert.assertEquals("*********", result.getSubPlatOrderNo());
        Assert.assertEquals(new BigDecimal("100.00"), result.getTotalAmount());
        Assert.assertEquals(new BigDecimal("50.00"), result.getRefundAmount());
        Assert.assertEquals(new BigDecimal("100.00"), result.getPayAmount());
        Assert.assertEquals("买家昵称", result.getBuyerNick());
        Assert.assertEquals("卖家昵称", result.getSellerNick());
        Assert.assertTrue(result.getIsHasGoodsReturn());
        Assert.assertEquals("顺丰快递", result.getLogisticName());
        Assert.assertEquals("SF1234567890", result.getLogisticNo());
        Assert.assertEquals("aftersale", result.getRefundPhase());
        Assert.assertEquals("1", result.getRefundVersion());
        Assert.assertEquals("buyer_open_uid_123", result.getBuyerOpenUid());
        Assert.assertEquals("1", result.getIsPrime());

        // 验证物流拦截信息
        Assert.assertNotNull(result.getTbRefundLogisticsIntercept());
        Assert.assertTrue(result.getTbRefundLogisticsIntercept().getBexInterceptAuto());
        Assert.assertNotNull(result.getTbRefundLogisticsIntercept().getInterceptStatusList());
        Assert.assertEquals(1, result.getTbRefundLogisticsIntercept().getInterceptStatusList().size());

        // 验证商品信息
        Assert.assertNotNull(result.getRefundGoods());
        Assert.assertEquals(1, result.getRefundGoods().size());
        Assert.assertEquals("123456", result.getRefundGoods().get(0).getPlatProductId());
        Assert.assertEquals("OUTER_ID_123", result.getRefundGoods().get(0).getOuterId());
        Assert.assertEquals("军绿色XS", result.getRefundGoods().get(0).getSkuSpec());
        Assert.assertEquals("30004447689", result.getRefundGoods().get(0).getSku());
        Assert.assertEquals("测试商品", result.getRefundGoods().get(0).getProductName());
        Assert.assertEquals(new BigDecimal("50.00"), result.getRefundGoods().get(0).getPrice());
        Assert.assertEquals(new BigDecimal("25.00"), result.getRefundGoods().get(0).getRefundAmount());
        Assert.assertEquals(2, result.getRefundGoods().get(0).getProductNum());
        Assert.assertEquals(2, result.getRefundGoods().get(0).getRefundProductNum());
        Assert.assertEquals("商品质量问题", result.getRefundGoods().get(0).getReason());
        Assert.assertEquals(PolyRefundStatusEnum.BUYER_APPLIED_REFUND_WAITING_SELLER_AGREE.getCode(), result.getRefundGoods().get(0).getRefundStatus());
    }

    /**
     * 测试 convertPolyRefundOrder 方法 - 空值测试
     */
    @Test
    public void testConvertPolyRefundOrderWithNull() {
        // 测试null值
        BusinessGetRefundOrderResponseOrderItem result1 = RdsTbRefundConvertor.convertPolyRefundOrder(null);
        Assert.assertNotNull(result1);
        Assert.assertNull(result1.getRefundNo());

        // 测试空对象
        TbRefundJdpResponseDto entity2 = new TbRefundJdpResponseDto();
        BusinessGetRefundOrderResponseOrderItem result2 = RdsTbRefundConvertor.convertPolyRefundOrder(entity2);
        Assert.assertNotNull(result2);
        Assert.assertNull(result2.getRefundNo());

        // 测试空RefundGetResponse
        TbRefundJdpResponseDto entity3 = new TbRefundJdpResponseDto();
        TbRefundJdpResponseDto.RefundGetResponse refundGetResponse = new TbRefundJdpResponseDto.RefundGetResponse();
        entity3.setRefundGetResponse(refundGetResponse);
        BusinessGetRefundOrderResponseOrderItem result3 = RdsTbRefundConvertor.convertPolyRefundOrder(entity3);
        Assert.assertNotNull(result3);
        Assert.assertNull(result3.getRefundNo());

        // 测试空Refund
        TbRefundJdpResponseDto entity4 = new TbRefundJdpResponseDto();
        TbRefundJdpResponseDto.RefundGetResponse refundGetResponse2 = new TbRefundJdpResponseDto.RefundGetResponse();
        TbRefundJdpResponseDto.Refund refund = new TbRefundJdpResponseDto.Refund();
        refundGetResponse2.setRefund(refund);
        entity4.setRefundGetResponse(refundGetResponse2);
        BusinessGetRefundOrderResponseOrderItem result4 = RdsTbRefundConvertor.convertPolyRefundOrder(entity4);
        Assert.assertNotNull(result4);
        Assert.assertNull(result4.getRefundNo());
    }
}
